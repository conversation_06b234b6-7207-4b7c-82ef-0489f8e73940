{% extends 'base.html' %}

{% block title %}إعدادات المظهر والثيم{% endblock %}

{% block extra_css %}
<style>
.theme-settings-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: calc(100vh - 80px);
    padding: 20px;
}

.settings-card {
    background: var(--theme-bg-primary);
    border-radius: 20px;
    box-shadow: var(--theme-shadow-lg);
    overflow: hidden;
    border: 1px solid var(--theme-border-color);
}

.settings-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.settings-header h2 {
    margin: 0;
    font-size: 2rem;
    font-weight: 600;
}

.settings-header p {
    margin: 10px 0 0 0;
    opacity: 0.9;
}

.settings-body {
    padding: 40px;
    background: var(--theme-bg-primary);
    color: var(--theme-text-primary);
}

.theme-option {
    background: var(--theme-bg-secondary);
    border: 2px solid var(--theme-border-color);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.theme-option:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: var(--theme-shadow-lg);
}

.theme-option.active {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.theme-preview {
    width: 100%;
    height: 80px;
    border-radius: 10px;
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
}

.theme-preview.light {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #dee2e6;
}

.theme-preview.dark {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%);
    border: 1px solid #4a5568;
}

.theme-preview.auto {
    background: linear-gradient(135deg, #ffffff 0%, #1a1a1a 100%);
    border: 1px solid #667eea;
}

.theme-preview::before {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #667eea;
}

.theme-preview::after {
    content: '';
    position: absolute;
    bottom: 10px;
    right: 10px;
    left: 10px;
    height: 20px;
    border-radius: 5px;
    background: rgba(102, 126, 234, 0.3);
}

.settings-section {
    margin-bottom: 40px;
    padding: 25px;
    background: var(--theme-bg-secondary);
    border-radius: 15px;
    border: 1px solid var(--theme-border-color);
}

.settings-section h4 {
    color: var(--theme-text-primary);
    margin-bottom: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.settings-section h4 i {
    color: #667eea;
}

.form-range {
    background: var(--theme-bg-tertiary);
}

.form-range::-webkit-slider-thumb {
    background: #667eea;
}

.form-range::-moz-range-thumb {
    background: #667eea;
    border: none;
}

.preview-text {
    padding: 15px;
    background: var(--theme-bg-tertiary);
    border-radius: 10px;
    border: 1px solid var(--theme-border-color);
    color: var(--theme-text-primary);
    margin-top: 10px;
}

.btn-theme {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-theme:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
}

.btn-reset {
    background: transparent;
    border: 2px solid var(--theme-border-color);
    color: var(--theme-text-secondary);
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-reset:hover {
    border-color: #dc3545;
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.alert-success {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1) 0%, rgba(46, 204, 113, 0.1) 100%);
    border: 1px solid #27ae60;
    color: #27ae60;
}
</style>
{% endblock %}

{% block content %}
<div class="theme-settings-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="settings-card">
                    <div class="settings-header">
                        <h2><i class="fa fa-palette me-3"></i>إعدادات المظهر والثيم</h2>
                        <p>اختر المظهر والألوان والخطوط التي تناسبك لتجربة مثالية</p>
                    </div>
                    
                    <div class="settings-body">
                        <!-- رسالة النجاح -->
                        <div id="successMessage" class="alert alert-success d-none">
                            <i class="fa fa-check-circle me-2"></i>
                            <span>تم حفظ الإعدادات بنجاح!</span>
                        </div>

                        <!-- اختيار الثيم الأساسي -->
                        <div class="settings-section">
                            <h4><i class="fa fa-swatchbook"></i>اختيار الثيم الأساسي</h4>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="theme-option" data-theme="light">
                                        <div class="theme-preview light"></div>
                                        <h6 class="text-center mb-1">الثيم الفاتح</h6>
                                        <small class="text-muted d-block text-center">مناسب للاستخدام النهاري والمكاتب المضيئة</small>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="theme-option" data-theme="dark">
                                        <div class="theme-preview dark"></div>
                                        <h6 class="text-center mb-1">الثيم المظلم</h6>
                                        <small class="text-muted d-block text-center">مناسب للاستخدام الليلي ويقلل إجهاد العين</small>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="theme-option" data-theme="auto">
                                        <div class="theme-preview auto"></div>
                                        <h6 class="text-center mb-1">تلقائي</h6>
                                        <small class="text-muted d-block text-center">يتبع إعدادات نظام التشغيل تلقائياً</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات الخط -->
                        <div class="settings-section">
                            <h4><i class="fa fa-font"></i>إعدادات الخط والنص</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="fontSizeSlider" class="form-label">حجم الخط: <span id="fontSizeValue">16</span>px</label>
                                    <input type="range" class="form-range" id="fontSizeSlider" min="12" max="24" value="16" step="1">
                                    <div class="preview-text" id="fontPreview">
                                        هذا نص تجريبي لمعاينة حجم الخط المختار. يمكنك تجربة أحجام مختلفة لتجد الأنسب لك.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="fontFamily" class="form-label">نوع الخط</label>
                                    <select class="form-select" id="fontFamily">
                                        <option value="Cairo">Cairo (افتراضي)</option>
                                        <option value="Amiri">Amiri</option>
                                        <option value="Noto Sans Arabic">Noto Sans Arabic</option>
                                        <option value="Tajawal">Tajawal</option>
                                        <option value="Almarai">Almarai</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات متقدمة -->
                        <div class="settings-section">
                            <h4><i class="fa fa-cogs"></i>إعدادات متقدمة</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="animationsEnabled" checked>
                                        <label class="form-check-label" for="animationsEnabled">
                                            تفعيل الحركات والانتقالات
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="soundEnabled" checked>
                                        <label class="form-check-label" for="soundEnabled">
                                            تفعيل الأصوات
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="highContrastMode">
                                        <label class="form-check-label" for="highContrastMode">
                                            وضع التباين العالي
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="reducedMotion">
                                        <label class="form-check-label" for="reducedMotion">
                                            تقليل الحركة (لذوي الاحتياجات الخاصة)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="text-center">
                            <button type="button" class="btn btn-theme me-3" id="saveSettings">
                                <i class="fa fa-save me-2"></i>حفظ الإعدادات
                            </button>
                            <button type="button" class="btn btn-reset" id="resetSettings">
                                <i class="fa fa-undo me-2"></i>إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // متغيرات العناصر
    const themeOptions = document.querySelectorAll('.theme-option');
    const fontSizeSlider = document.getElementById('fontSizeSlider');
    const fontSizeValue = document.getElementById('fontSizeValue');
    const fontPreview = document.getElementById('fontPreview');
    const fontFamily = document.getElementById('fontFamily');
    const saveButton = document.getElementById('saveSettings');
    const resetButton = document.getElementById('resetSettings');
    const successMessage = document.getElementById('successMessage');

    let currentSettings = {
        theme: 'light',
        fontSize: 16,
        fontFamily: 'Cairo',
        animationsEnabled: true,
        soundEnabled: true,
        highContrastMode: false,
        reducedMotion: false
    };

    // تحميل الإعدادات المحفوظة
    loadSettings();

    // معالجات الأحداث
    themeOptions.forEach(option => {
        option.addEventListener('click', function() {
            const theme = this.dataset.theme;
            selectTheme(theme);
        });
    });

    fontSizeSlider.addEventListener('input', function() {
        const size = this.value;
        fontSizeValue.textContent = size;
        fontPreview.style.fontSize = size + 'px';
        currentSettings.fontSize = parseInt(size);
    });

    fontFamily.addEventListener('change', function() {
        const family = this.value;
        fontPreview.style.fontFamily = family;
        currentSettings.fontFamily = family;
    });

    saveButton.addEventListener('click', saveSettings);
    resetButton.addEventListener('click', resetSettings);

    // وظائف مساعدة
    function selectTheme(theme) {
        themeOptions.forEach(option => {
            option.classList.remove('active');
        });

        const selectedOption = document.querySelector(`[data-theme="${theme}"]`);
        if (selectedOption) {
            selectedOption.classList.add('active');
            currentSettings.theme = theme;
            applyTheme(theme);
        }
    }

    function applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        if (theme === 'auto') {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            document.documentElement.setAttribute('data-bs-theme', prefersDark ? 'dark' : 'light');
        } else {
            document.documentElement.setAttribute('data-bs-theme', theme);
        }

        // حفظ في localStorage
        localStorage.setItem('theme', theme);
    }

    function loadSettings() {
        fetch('/api/theme/load')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentSettings.theme = data.theme || 'light';
                    currentSettings.fontSize = data.font_size || 16;

                    // تطبيق الإعدادات
                    selectTheme(currentSettings.theme);
                    fontSizeSlider.value = currentSettings.fontSize;
                    fontSizeValue.textContent = currentSettings.fontSize;
                    fontPreview.style.fontSize = currentSettings.fontSize + 'px';
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل الإعدادات:', error);
                // تحميل من localStorage كبديل
                const savedTheme = localStorage.getItem('theme') || 'light';
                selectTheme(savedTheme);
            });
    }

    function saveSettings() {
        const settingsData = {
            theme: currentSettings.theme,
            font_size: currentSettings.fontSize,
            font_family: currentSettings.fontFamily,
            animations_enabled: document.getElementById('animationsEnabled').checked,
            sound_enabled: document.getElementById('soundEnabled').checked,
            high_contrast_mode: document.getElementById('highContrastMode').checked,
            reduced_motion: document.getElementById('reducedMotion').checked
        };

        fetch('/api/theme/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(settingsData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessMessage('تم حفظ الإعدادات بنجاح!');
                // تطبيق الإعدادات على الصفحة
                applyAllSettings();
                // حفظ في localStorage أيضاً
                localStorage.setItem('theme', currentSettings.theme);
                localStorage.setItem('fontSize', currentSettings.fontSize);
            } else {
                alert('خطأ في حفظ الإعدادات: ' + data.message);
            }
        })
        .catch(error => {
            console.error('خطأ في حفظ الإعدادات:', error);
            // حفظ في localStorage كبديل
            localStorage.setItem('theme', currentSettings.theme);
            localStorage.setItem('fontSize', currentSettings.fontSize);
            showSuccessMessage('تم حفظ الإعدادات محلياً!');
        });
    }

    function resetSettings() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
            // إعادة تعيين القيم الافتراضية
            selectTheme('light');
            fontSizeSlider.value = 16;
            fontSizeValue.textContent = '16';
            fontPreview.style.fontSize = '16px';
            fontFamily.value = 'Cairo';
            fontPreview.style.fontFamily = 'Cairo';

            document.getElementById('animationsEnabled').checked = true;
            document.getElementById('soundEnabled').checked = true;
            document.getElementById('highContrastMode').checked = false;
            document.getElementById('reducedMotion').checked = false;

            currentSettings = {
                theme: 'light',
                fontSize: 16,
                fontFamily: 'Cairo',
                animationsEnabled: true,
                soundEnabled: true,
                highContrastMode: false,
                reducedMotion: false
            };

            // مسح localStorage
            localStorage.removeItem('theme');
            localStorage.removeItem('fontSize');

            showSuccessMessage('تم إعادة تعيين الإعدادات بنجاح!');
        }
    }

    function applyAllSettings() {
        // تطبيق جميع الإعدادات على الصفحة
        if (currentSettings.reducedMotion) {
            document.documentElement.style.setProperty('--animation-duration', '0s');
        } else {
            document.documentElement.style.removeProperty('--animation-duration');
        }
    }

    function showSuccessMessage(message) {
        successMessage.querySelector('span').textContent = message;
        successMessage.classList.remove('d-none');

        setTimeout(() => {
            successMessage.classList.add('d-none');
        }, 3000);
    }

    // تطبيق الثيم عند تحميل الصفحة
    const savedTheme = localStorage.getItem('theme') || 'light';
    selectTheme(savedTheme);
});
</script>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // متغيرات العناصر
    const themeOptions = document.querySelectorAll('.theme-option');
    const fontSizeSlider = document.getElementById('fontSizeSlider');
    const fontSizeValue = document.getElementById('fontSizeValue');
    const fontPreview = document.getElementById('fontPreview');
    const fontFamily = document.getElementById('fontFamily');
    const saveButton = document.getElementById('saveSettings');
    const resetButton = document.getElementById('resetSettings');
    const successMessage = document.getElementById('successMessage');

    let currentSettings = {
        theme: 'light',
        fontSize: 16,
        fontFamily: 'Cairo',
        animationsEnabled: true,
        soundEnabled: true,
        highContrastMode: false,
        reducedMotion: false
    };

    // تحميل الإعدادات المحفوظة
    loadSettings();

    // معالجات الأحداث
    themeOptions.forEach(option => {
        option.addEventListener('click', function() {
            const theme = this.dataset.theme;
            selectTheme(theme);
        });
    });

    fontSizeSlider.addEventListener('input', function() {
        const size = this.value;
        fontSizeValue.textContent = size;
        fontPreview.style.fontSize = size + 'px';
        currentSettings.fontSize = parseInt(size);
    });

    fontFamily.addEventListener('change', function() {
        const family = this.value;
        fontPreview.style.fontFamily = family;
        currentSettings.fontFamily = family;
    });

    saveButton.addEventListener('click', saveSettings);
    resetButton.addEventListener('click', resetSettings);

    // وظائف مساعدة
    function selectTheme(theme) {
        themeOptions.forEach(option => {
            option.classList.remove('active');
        });

        const selectedOption = document.querySelector(`[data-theme="${theme}"]`);
        if (selectedOption) {
            selectedOption.classList.add('active');
            currentSettings.theme = theme;
            applyTheme(theme);
        }
    }

    function applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        if (theme === 'auto') {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            document.documentElement.setAttribute('data-bs-theme', prefersDark ? 'dark' : 'light');
        } else {
            document.documentElement.setAttribute('data-bs-theme', theme);
        }
    }

    function loadSettings() {
        fetch('/api/theme/load')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentSettings.theme = data.theme || 'light';
                    currentSettings.fontSize = data.font_size || 16;

                    // تطبيق الإعدادات
                    selectTheme(currentSettings.theme);
                    fontSizeSlider.value = currentSettings.fontSize;
                    fontSizeValue.textContent = currentSettings.fontSize;
                    fontPreview.style.fontSize = currentSettings.fontSize + 'px';
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل الإعدادات:', error);
            });
    }

    function saveSettings() {
        const settingsData = {
            theme: currentSettings.theme,
            font_size: currentSettings.fontSize,
            font_family: currentSettings.fontFamily,
            animations_enabled: document.getElementById('animationsEnabled').checked,
            sound_enabled: document.getElementById('soundEnabled').checked,
            high_contrast_mode: document.getElementById('highContrastMode').checked,
            reduced_motion: document.getElementById('reducedMotion').checked
        };

        fetch('/api/theme/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(settingsData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessMessage('تم حفظ الإعدادات بنجاح!');
                // تطبيق الإعدادات على الصفحة
                applyAllSettings();
            } else {
                alert('خطأ في حفظ الإعدادات: ' + data.message);
            }
        })
        .catch(error => {
            console.error('خطأ في حفظ الإعدادات:', error);
            alert('حدث خطأ في حفظ الإعدادات');
        });
    }

    function resetSettings() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
            // إعادة تعيين القيم الافتراضية
            selectTheme('light');
            fontSizeSlider.value = 16;
            fontSizeValue.textContent = '16';
            fontPreview.style.fontSize = '16px';
            fontFamily.value = 'Cairo';
            fontPreview.style.fontFamily = 'Cairo';

            document.getElementById('animationsEnabled').checked = true;
            document.getElementById('soundEnabled').checked = true;
            document.getElementById('highContrastMode').checked = false;
            document.getElementById('reducedMotion').checked = false;

            currentSettings = {
                theme: 'light',
                fontSize: 16,
                fontFamily: 'Cairo',
                animationsEnabled: true,
                soundEnabled: true,
                highContrastMode: false,
                reducedMotion: false
            };

            showSuccessMessage('تم إعادة تعيين الإعدادات بنجاح!');
        }
    }

    function applyAllSettings() {
        // تطبيق جميع الإعدادات على الصفحة
        document.documentElement.style.fontSize = currentSettings.fontSize + 'px';
        document.documentElement.style.fontFamily = currentSettings.fontFamily;

        if (currentSettings.reducedMotion) {
            document.documentElement.style.setProperty('--animation-duration', '0s');
        } else {
            document.documentElement.style.removeProperty('--animation-duration');
        }
    }

    function showSuccessMessage(message) {
        successMessage.querySelector('span').textContent = message;
        successMessage.classList.remove('d-none');

        setTimeout(() => {
            successMessage.classList.add('d-none');
        }, 3000);
    }

    // تطبيق الثيم عند تحميل الصفحة
    const savedTheme = localStorage.getItem('theme') || 'light';
    selectTheme(savedTheme);
});
</script>
{% endblock %}
