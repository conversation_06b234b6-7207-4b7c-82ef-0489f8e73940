from app import db
from datetime import datetime
from sqlalchemy import text

class ArchivedCase(db.Model):
    """نموذج القضايا المؤرشفة"""
    __tablename__ = 'archived_cases'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # بيانات القضية الأصلية
    original_case_id = db.Column(db.Integer, nullable=False)
    case_number = db.Column(db.String(100), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    case_type = db.Column(db.String(100))
    status = db.Column(db.String(50))
    
    # بيانات العميل
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    client_name = db.Column(db.String(200))
    
    # بيانات الأرشفة
    archived_date = db.Column(db.DateTime, default=datetime.utcnow)
    archive_reason = db.Column(db.String(100))  # completed, cancelled, transferred, inactive
    archived_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # بيانات إضافية
    original_created_date = db.Column(db.DateTime)
    original_updated_date = db.Column(db.DateTime)
    case_data_json = db.Column(db.Text)  # JSON backup of original case data
    
    # العلاقات
    client = db.relationship('Client', backref='archived_cases')
    archived_by_user = db.relationship('User', backref='archived_cases')
    
    def __repr__(self):
        return f'<ArchivedCase {self.case_number}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'original_case_id': self.original_case_id,
            'case_number': self.case_number,
            'title': self.title,
            'description': self.description,
            'case_type': self.case_type,
            'status': self.status,
            'client_name': self.client_name,
            'archived_date': self.archived_date.isoformat() if self.archived_date else None,
            'archive_reason': self.archive_reason,
            'original_created_date': self.original_created_date.isoformat() if self.original_created_date else None
        }

class ArchivedClient(db.Model):
    """نموذج العملاء المؤرشفين"""
    __tablename__ = 'archived_clients'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # بيانات العميل الأصلية
    original_client_id = db.Column(db.Integer, nullable=False)
    name = db.Column(db.String(200), nullable=False)
    id_number = db.Column(db.String(50))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    
    # بيانات الأرشفة
    archived_date = db.Column(db.DateTime, default=datetime.utcnow)
    archive_reason = db.Column(db.String(100))
    archived_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # إحصائيات
    cases_count = db.Column(db.Integer, default=0)
    total_payments = db.Column(db.Float, default=0.0)
    
    # بيانات إضافية
    client_data_json = db.Column(db.Text)  # JSON backup of original client data
    
    # العلاقات
    archived_by_user = db.relationship('User', backref='archived_clients')
    
    def __repr__(self):
        return f'<ArchivedClient {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'original_client_id': self.original_client_id,
            'name': self.name,
            'id_number': self.id_number,
            'phone': self.phone,
            'email': self.email,
            'archived_date': self.archived_date.isoformat() if self.archived_date else None,
            'archive_reason': self.archive_reason,
            'cases_count': self.cases_count,
            'total_payments': self.total_payments
        }

class ArchivedDocument(db.Model):
    """نموذج المستندات المؤرشفة"""
    __tablename__ = 'archived_documents'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # بيانات المستند الأصلية
    original_document_id = db.Column(db.Integer, nullable=False)
    name = db.Column(db.String(200), nullable=False)
    file_path = db.Column(db.String(500))
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)
    
    # ارتباط بالقضية
    case_id = db.Column(db.Integer)
    case_title = db.Column(db.String(200))
    
    # بيانات الأرشفة
    archived_date = db.Column(db.DateTime, default=datetime.utcnow)
    archive_reason = db.Column(db.String(100))
    archived_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # مسار الأرشيف
    archive_path = db.Column(db.String(500))
    
    # العلاقات
    archived_by_user = db.relationship('User', backref='archived_documents')
    
    def __repr__(self):
        return f'<ArchivedDocument {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'original_document_id': self.original_document_id,
            'name': self.name,
            'file_type': self.file_type,
            'file_size': self.file_size,
            'case_title': self.case_title,
            'archived_date': self.archived_date.isoformat() if self.archived_date else None,
            'archive_reason': self.archive_reason
        }

class SystemSettings(db.Model):
    """نموذج إعدادات النظام"""
    __tablename__ = 'system_settings'

    id = db.Column(db.Integer, primary_key=True)
    setting_key = db.Column(db.String(100), unique=True, nullable=False)
    setting_value = db.Column(db.Text)
    setting_type = db.Column(db.String(50), default='string')  # string, integer, boolean, json
    category = db.Column(db.String(50), default='general')  # general, archive, backup, security, ui, notifications, reports
    description = db.Column(db.Text)
    is_system = db.Column(db.Boolean, default=False)  # إعدادات النظام الأساسية
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    updated_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    updated_by_user = db.relationship('User', backref='updated_settings')

    def get_value(self):
        """إرجاع القيمة بالنوع المناسب"""
        if self.setting_type == 'boolean':
            return self.setting_value.lower() in ['true', '1', 'yes'] if self.setting_value else False
        elif self.setting_type == 'integer':
            try:
                return int(self.setting_value) if self.setting_value else 0
            except (ValueError, TypeError):
                return 0
        elif self.setting_type == 'json':
            try:
                import json
                return json.loads(self.setting_value) if self.setting_value else {}
            except (ValueError, TypeError):
                return {}
        else:
            return self.setting_value or ''

    def set_value(self, value):
        """تعيين القيمة بالنوع المناسب"""
        if self.setting_type == 'boolean':
            self.setting_value = str(bool(value)).lower()
        elif self.setting_type == 'integer':
            self.setting_value = str(int(value))
        elif self.setting_type == 'json':
            import json
            self.setting_value = json.dumps(value, ensure_ascii=False)
        else:
            self.setting_value = str(value)
    
    def __repr__(self):
        return f'<SystemSettings {self.setting_key}>'
    
    @staticmethod
    def get_setting(key, default=None):
        """الحصول على قيمة إعداد"""
        setting = SystemSettings.query.filter_by(setting_key=key).first()
        if setting:
            if setting.setting_type == 'boolean':
                return setting.setting_value.lower() == 'true'
            elif setting.setting_type == 'integer':
                return int(setting.setting_value)
            elif setting.setting_type == 'json':
                import json
                return json.loads(setting.setting_value)
            else:
                return setting.setting_value
        return default
    
    @staticmethod
    def set_setting(key, value, setting_type='string', description=None, user_id=None):
        """تعيين قيمة إعداد"""
        setting = SystemSettings.query.filter_by(setting_key=key).first()
        if not setting:
            setting = SystemSettings(setting_key=key)
            db.session.add(setting)
        
        if setting_type == 'json':
            import json
            setting.setting_value = json.dumps(value)
        else:
            setting.setting_value = str(value)
        
        setting.setting_type = setting_type
        if description:
            setting.description = description
        if user_id:
            setting.updated_by = user_id
        
        db.session.commit()
        return setting

class ArchiveRule(db.Model):
    """نموذج قواعد الأرشفة التلقائية"""
    __tablename__ = 'archive_rules'
    
    id = db.Column(db.Integer, primary_key=True)
    rule_name = db.Column(db.String(100), nullable=False)
    rule_type = db.Column(db.String(50), nullable=False)  # case, client, document
    condition_field = db.Column(db.String(100))  # status, last_activity, etc.
    condition_operator = db.Column(db.String(20))  # equals, greater_than, less_than
    condition_value = db.Column(db.String(200))
    archive_after_days = db.Column(db.Integer, default=365)
    is_active = db.Column(db.Boolean, default=True)
    
    # بيانات الإنشاء والتحديث
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    updated_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # العلاقات
    created_by_user = db.relationship('User', backref='archive_rules')
    
    def __repr__(self):
        return f'<ArchiveRule {self.rule_name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'rule_name': self.rule_name,
            'rule_type': self.rule_type,
            'condition_field': self.condition_field,
            'condition_operator': self.condition_operator,
            'condition_value': self.condition_value,
            'archive_after_days': self.archive_after_days,
            'is_active': self.is_active,
            'created_date': self.created_date.isoformat() if self.created_date else None
        }

class BackupSchedule(db.Model):
    """نموذج جدولة النسخ الاحتياطي"""
    __tablename__ = 'backup_schedules'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    frequency = db.Column(db.String(20), nullable=False)  # daily, weekly, monthly
    frequency_value = db.Column(db.Integer, default=1)  # كل كم يوم/أسبوع/شهر
    backup_time = db.Column(db.Time, default=datetime.now().time())  # وقت النسخ الاحتياطي
    backup_types = db.Column(db.Text)  # JSON: ['database', 'files', 'documents']
    is_active = db.Column(db.Boolean, default=True)
    last_backup = db.Column(db.DateTime)
    next_backup = db.Column(db.DateTime)
    retention_days = db.Column(db.Integer, default=30)  # مدة الاحتفاظ بالنسخ
    compression_enabled = db.Column(db.Boolean, default=True)
    encryption_enabled = db.Column(db.Boolean, default=False)
    backup_location = db.Column(db.String(500))  # مسار حفظ النسخ
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    updated_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    creator = db.relationship('User', backref='backup_schedules')

    def get_backup_types(self):
        """إرجاع أنواع النسخ الاحتياطي كقائمة"""
        import json
        return json.loads(self.backup_types) if self.backup_types else []

    def set_backup_types(self, types_list):
        """تعيين أنواع النسخ الاحتياطي"""
        import json
        self.backup_types = json.dumps(types_list, ensure_ascii=False)

    def calculate_next_backup(self):
        """حساب موعد النسخة الاحتياطية التالية"""
        from datetime import timedelta

        if not self.last_backup:
            # أول نسخة احتياطية
            next_date = datetime.now().replace(
                hour=self.backup_time.hour,
                minute=self.backup_time.minute,
                second=0,
                microsecond=0
            )
            if next_date <= datetime.now():
                next_date += timedelta(days=1)
        else:
            if self.frequency == 'daily':
                next_date = self.last_backup + timedelta(days=self.frequency_value)
            elif self.frequency == 'weekly':
                next_date = self.last_backup + timedelta(weeks=self.frequency_value)
            elif self.frequency == 'monthly':
                next_date = self.last_backup + timedelta(days=30 * self.frequency_value)
            else:
                next_date = self.last_backup + timedelta(days=1)

        self.next_backup = next_date
        return next_date

    def __repr__(self):
        return f'<BackupSchedule {self.name}: {self.frequency}>'

class BackupLog(db.Model):
    """نموذج سجل النسخ الاحتياطية"""
    __tablename__ = 'backup_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    backup_type = db.Column(db.String(50), nullable=False)  # full, incremental, manual
    backup_path = db.Column(db.String(500))
    backup_size = db.Column(db.BigInteger)  # بالبايت
    backup_date = db.Column(db.DateTime, default=datetime.utcnow)
    backup_status = db.Column(db.String(50), default='completed')  # completed, failed, in_progress
    error_message = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # إحصائيات النسخة الاحتياطية
    tables_count = db.Column(db.Integer)
    records_count = db.Column(db.Integer)
    files_count = db.Column(db.Integer)
    
    # العلاقات
    created_by_user = db.relationship('User', backref='backup_logs')
    
    def __repr__(self):
        return f'<BackupLog {self.backup_type} - {self.backup_date}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'backup_type': self.backup_type,
            'backup_path': self.backup_path,
            'backup_size': self.backup_size,
            'backup_date': self.backup_date.isoformat() if self.backup_date else None,
            'backup_status': self.backup_status,
            'tables_count': self.tables_count,
            'records_count': self.records_count,
            'files_count': self.files_count
        }

# دوال مساعدة للأرشفة
def get_archive_statistics():
    """الحصول على إحصائيات الأرشيف"""
    stats = {
        'archived_cases': ArchivedCase.query.count(),
        'archived_clients': ArchivedClient.query.count(),
        'archived_documents': ArchivedDocument.query.count(),
        'archive_size': 0  # سيتم حسابه من أحجام الملفات
    }
    
    # حساب حجم الأرشيف
    total_size = db.session.query(db.func.sum(ArchivedDocument.file_size)).scalar()
    stats['archive_size'] = total_size or 0
    
    return stats

def get_system_info():
    """الحصول على معلومات النظام"""
    info = {
        'database_size': 0,
        'last_backup': None,
        'active_cases_count': 0,
        'total_clients_count': 0
    }
    
    # حساب حجم قاعدة البيانات (تقريبي)
    try:
        result = db.session.execute(text("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()"))
        db_size = result.fetchone()
        if db_size:
            info['database_size'] = db_size[0]
    except:
        pass
    
    # آخر نسخة احتياطية
    last_backup = BackupLog.query.filter_by(backup_status='completed').order_by(BackupLog.backup_date.desc()).first()
    if last_backup:
        info['last_backup'] = last_backup.backup_date
    
    # إحصائيات أخرى
    from app.models import Case, Client
    info['active_cases_count'] = Case.query.filter_by(status='active').count()
    info['total_clients_count'] = Client.query.count()
    
    return info
