#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def fix_client_table():
    """إضافة الحقول المفقودة لجدول العملاء"""
    
    db_path = 'instance/law_office.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الحقول الموجودة
        cursor.execute("PRAGMA table_info(clients)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"📊 الحقول الموجودة: {columns}")
        
        # إضافة الحقول المفقودة
        fields_to_add = [
            ('birth_date', 'DATE'),
            ('occupation', 'VARCHAR(100)'),
            ('notes', 'TEXT'),
            ('role', 'VARCHAR(50) DEFAULT "موكل"')
        ]
        
        for field_name, field_type in fields_to_add:
            if field_name not in columns:
                try:
                    cursor.execute(f"ALTER TABLE clients ADD COLUMN {field_name} {field_type}")
                    print(f"✅ تم إضافة حقل {field_name}")
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة حقل {field_name}: {e}")
        
        conn.commit()
        conn.close()
        
        print("✅ تم تحديث جدول العملاء بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    print("🔧 إصلاح جدول العملاء...")
    print("=" * 40)
    
    success = fix_client_table()
    
    print("=" * 40)
    if success:
        print("🎉 تم إصلاح جدول العملاء بنجاح!")
    else:
        print("💥 فشل في إصلاح جدول العملاء")
