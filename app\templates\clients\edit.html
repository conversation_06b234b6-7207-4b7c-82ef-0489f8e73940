{% extends 'base.html' %}

{% block title %}تعديل عميل{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-user-edit"></i> تعديل بيانات العميل</h1>
                    <p>تعديل بيانات العميل المحدد</p>
                </div>
                <div class="page-actions">
                    <a href="{{ url_for('clients_list') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>رجوع
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit text-primary me-2"></i>
                        تعديل بيانات العميل
                    </h5>
                </div>
                <div class="card-body">
    <form method="post">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <div class="mb-3">
            <label for="name" class="form-label">الاسم الكامل</label>
            <input type="text" class="form-control" id="name" name="name" value="{{ client.name }}" required>
        </div>
        <div class="mb-3">
            <label for="national_id" class="form-label">رقم الهوية</label>
            <input type="text" class="form-control" id="national_id" name="national_id" value="{{ client.national_id }}">
        </div>
        <div class="mb-3">
            <label for="phone" class="form-label">رقم الهاتف</label>
            <input type="text" class="form-control" id="phone" name="phone" value="{{ client.phone }}">
        </div>
        <div class="mb-3">
            <label for="email" class="form-label">البريد الإلكتروني</label>
            <input type="email" class="form-control" id="email" name="email" value="{{ client.email }}">
        </div>
        <div class="mb-3">
            <label for="address" class="form-label">العنوان</label>
            <input type="text" class="form-control" id="address" name="address" value="{{ client.address }}">
        </div>
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                            <a href="{{ url_for('clients_list') }}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-arrow-right me-2"></i>
                                رجوع
                            </a>
                        </div>
                    </div>
                </form>
                </div>
            </div>

    <div class="card mt-4">
        <div class="card-header bg-dark text-white"><i class="fa fa-paperclip"></i> مستندات العميل</div>
        <div class="card-body">
            <form method="post" action="{{ url_for('upload_client_document', client_id=client.id) }}" enctype="multipart/form-data" class="mb-3">
                <div class="row g-2 align-items-center">
                    <div class="col-auto">
                        <input type="file" name="document" class="form-control" required>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-success"><i class="fa fa-upload"></i> رفع مستند</button>
                    </div>
                </div>
            </form>
            <div class="table-responsive">
                <table class="table table-bordered text-center">
                    <thead class="table-light">
                        <tr><th>اسم الملف</th><th>تاريخ الرفع</th><th>تحميل</th><th>حذف</th></tr>
                    </thead>
                    <tbody>
                    {% for doc in client.documents %}
                        <tr>
                            <td>{{ doc.filename }}</td>
                            <td>{{ doc.upload_date.strftime('%Y-%m-%d %H:%M') if doc.upload_date else '' }}</td>
                            <td><a href="{{ url_for('download_document', filename=doc.filename) }}" class="btn btn-info btn-sm"><i class="fa fa-download"></i> تحميل</a></td>
                            <td><a href="{{ url_for('delete_client_document', client_id=client.id, doc_id=doc.id) }}" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف المستند؟');"><i class="fa fa-trash"></i> حذف</a></td>
                        </tr>
                    {% else %}
                        <tr><td colspan="4">لا توجد مستندات مرفوعة</td></tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
