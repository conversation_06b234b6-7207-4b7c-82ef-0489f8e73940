<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}طباعة - نظام إدارة مكتب المحاماة{% endblock %}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Noto Sans Arabic', sans-serif;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 20px;
                font-size: 12pt;
                line-height: 1.4;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .page-break-inside-avoid {
                page-break-inside: avoid;
            }
            
            .print-header {
                border-bottom: 3px solid #007bff;
                margin-bottom: 30px;
                padding-bottom: 20px;
            }
            
            .print-footer {
                border-top: 2px solid #6c757d;
                margin-top: 30px;
                padding-top: 15px;
                font-size: 10pt;
                color: #6c757d;
            }
            
            table {
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 20px;
            }
            
            table, th, td {
                border: 1px solid #000;
            }
            
            th, td {
                padding: 8px;
                text-align: right;
            }
            
            th {
                background-color: #f8f9fa !important;
                font-weight: bold;
            }
            
            .signature-section {
                margin-top: 50px;
                display: flex;
                justify-content: space-between;
            }
            
            .signature-box {
                width: 200px;
                text-align: center;
                border-top: 1px solid #000;
                padding-top: 10px;
            }
        }
        
        @media screen {
            body {
                background-color: #f8f9fa;
                padding: 20px;
            }
            
            .print-container {
                background: white;
                max-width: 210mm;
                margin: 0 auto;
                padding: 40px;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
                border-radius: 10px;
            }
            
            .print-actions {
                text-align: center;
                margin-bottom: 30px;
            }
            
            .print-actions .btn {
                margin: 0 10px;
                padding: 10px 30px;
            }
        }
        
        .letterhead {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .letterhead h1 {
            color: #007bff;
            font-size: 24pt;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .letterhead .subtitle {
            font-size: 14pt;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .letterhead .contact-info {
            font-size: 11pt;
            color: #495057;
            line-height: 1.6;
        }
        
        .document-title {
            text-align: center;
            font-size: 18pt;
            font-weight: bold;
            color: #007bff;
            margin: 30px 0;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 10px;
        }
        
        .document-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .document-info .row {
            margin-bottom: 10px;
        }
        
        .document-info .label {
            font-weight: bold;
            color: #495057;
        }
        
        .document-info .value {
            color: #212529;
        }
        
        .content-section {
            margin-bottom: 30px;
        }
        
        .content-section h3 {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .financial-summary {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid #007bff;
        }
        
        .financial-summary h4 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .amount {
            font-weight: bold;
            color: #28a745;
        }
        
        .amount.negative {
            color: #dc3545;
        }
        
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 72pt;
            color: rgba(0, 123, 255, 0.1);
            z-index: -1;
            pointer-events: none;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="print-container">
        <!-- أزرار الطباعة (تظهر على الشاشة فقط) -->
        <div class="print-actions no-print">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fa fa-print"></i> طباعة
            </button>
            <button onclick="generatePDF()" class="btn btn-success">
                <i class="fa fa-file-pdf"></i> تصدير PDF
            </button>
            <button onclick="window.close()" class="btn btn-secondary">
                <i class="fa fa-times"></i> إغلاق
            </button>
        </div>
        
        <!-- رأس المكتب -->
        <div class="letterhead print-header">
            <h1>مكتب المحامي سامح للاستشارات القانونية</h1>
            <div class="subtitle">مكتب محاماة واستشارات قانونية</div>
            <div class="contact-info">
                <div>العنوان: فلسطين - غزة - شارع الجلاء</div>
                <div>الهاتف: +970-8-123-4567 | الجوال: +970-59-123-4567</div>
                <div>البريد الإلكتروني: <EMAIL> | الموقع: www.lawyersameh.ps</div>
                <div>رقم الترخيص: 12345 | نقابة المحامين الفلسطينية</div>
            </div>
        </div>
        
        <!-- محتوى المستند -->
        {% block content %}{% endblock %}
        
        <!-- تذييل المستند -->
        <div class="print-footer">
            <div class="row">
                <div class="col-md-6">
                    <small>تاريخ الطباعة: {{ current_date }}</small>
                </div>
                <div class="col-md-6 text-end">
                    <small>مكتب المحامي سامح للاستشارات القانونية</small>
                </div>
            </div>
        </div>
        
        <!-- التوقيعات -->
        {% block signatures %}
        <div class="signature-section no-print-break">
            <div class="signature-box">
                <div>المحامي</div>
                <div style="margin-top: 40px;">سامح أبو عودة</div>
            </div>
            <div class="signature-box">
                <div>التاريخ</div>
                <div style="margin-top: 40px;">{{ current_date }}</div>
            </div>
        </div>
        {% endblock %}
    </div>
    
    <!-- العلامة المائية -->
    {% if watermark %}
    <div class="watermark">{{ watermark }}</div>
    {% endif %}
    
    <!-- JavaScript للطباعة وتصدير PDF -->
    <script>
        function generatePDF() {
            // إخفاء أزرار الطباعة
            document.querySelector('.print-actions').style.display = 'none';
            
            // طباعة كـ PDF
            window.print();
            
            // إظهار أزرار الطباعة مرة أخرى
            setTimeout(() => {
                document.querySelector('.print-actions').style.display = 'block';
            }, 1000);
        }
        
        // تحسين الطباعة
        window.addEventListener('beforeprint', function() {
            document.body.classList.add('printing');
        });
        
        window.addEventListener('afterprint', function() {
            document.body.classList.remove('printing');
        });
        
        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
