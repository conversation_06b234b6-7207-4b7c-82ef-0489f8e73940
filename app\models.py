from . import db
from flask_login import UserMixin
from datetime import datetime, date

class User(db.Model, UserMixin):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(150), unique=True, nullable=False)
    password = db.Column(db.String(256), nullable=False)
    role = db.Column(db.String(50), default='موظف')
    last_login = db.Column(db.DateTime)
    cases = db.relationship('Case', backref='lawyer', lazy=True)

    def __repr__(self):
        return f'<User {self.username}>'

class Client(db.Model):
    __tablename__ = 'clients'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(150), nullable=False)
    national_id = db.Column(db.String(50))
    phone = db.Column(db.String(50))
    email = db.Column(db.String(120))
    address = db.Column(db.String(200))
    birth_date = db.Column(db.Date)
    occupation = db.Column(db.String(100))
    notes = db.Column(db.Text)
    role = db.Column(db.String(50), default='موكل')
    cases = db.relationship('Case', backref='client', lazy=True)
    documents = db.relationship('ClientDocument', backref='client', lazy=True)

class Case(db.Model):
    __tablename__ = 'cases'

    id = db.Column(db.Integer, primary_key=True)
    case_number = db.Column(db.String(100))  # رقم القضية في المحكمة
    office_case_number = db.Column(db.String(100))  # رقم القضية في المكتب
    title = db.Column(db.String(200), nullable=False)
    type = db.Column(db.String(100))
    status = db.Column(db.String(50), default='جارية')
    court = db.Column(db.String(100))
    opponent = db.Column(db.String(150))
    description = db.Column(db.Text)
    open_date = db.Column(db.Date)  # تاريخ فتح القضية

    # الحقول المالية للأتعاب
    fees_total = db.Column(db.Float, default=0.0)  # إجمالي الأتعاب
    fees_paid = db.Column(db.Float, default=0.0)   # المدفوع من الأتعاب
    fees_remaining = db.Column(db.Float, default=0.0)  # المتبقي من الأتعاب
    fees_currency = db.Column(db.String(20), default='شيكل')  # عملة الأتعاب

    # الحقول المالية للرسوم
    court_fees_total = db.Column(db.Float, default=0.0)  # إجمالي الرسوم
    court_fees_paid = db.Column(db.Float, default=0.0)   # المدفوع من الرسوم
    court_fees_remaining = db.Column(db.Float, default=0.0)  # المتبقي من الرسوم

    # حقول إضافية للقضية
    priority = db.Column(db.String(50), default='متوسطة')  # أولوية القضية
    next_session_date = db.Column(db.DateTime)  # موعد الجلسة القادمة
    case_value = db.Column(db.Float, default=0.0)  # قيمة القضية
    currency = db.Column(db.String(20), default='شيكل')  # العملة

    # صفة الموكل في القضية
    client_role = db.Column(db.String(50), default='مدعي')  # صفة الموكل: مدعي، مدعى عليه، محكوم له، محكوم عليه، مشتكي، متهم، مستأنف، مستأنف ضده

    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    lawyer_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    sessions = db.relationship('Session', backref='case', lazy=True)
    documents = db.relationship('Document', backref='case', lazy=True)
    invoices = db.relationship('Invoice', backref='case', lazy=True)
    financial_transactions = db.relationship('FinancialTransaction', back_populates='case', lazy=True)

    def calculate_fees_remaining(self):
        """حساب المتبقي من الأتعاب"""
        self.fees_remaining = self.fees_total - self.fees_paid

    def calculate_court_fees_remaining(self):
        """حساب المتبقي من الرسوم"""
        self.court_fees_remaining = self.court_fees_total - self.court_fees_paid

class Session(db.Model):
    __tablename__ = 'sessions'

    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.DateTime, nullable=False)
    location = db.Column(db.String(150))
    notes = db.Column(db.Text)
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'), nullable=False)

class Document(db.Model):
    __tablename__ = 'documents'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(200), nullable=False)
    upload_date = db.Column(db.DateTime)
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))

class Invoice(db.Model):
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Float, nullable=False)
    issue_date = db.Column(db.DateTime)
    paid = db.Column(db.Boolean, default=False)
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))
    payments = db.relationship('Payment', backref='invoice', lazy=True)

class Payment(db.Model):
    __tablename__ = 'payments'

    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Float, nullable=False)
    payment_date = db.Column(db.DateTime)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'))

class Task(db.Model):
    __tablename__ = 'tasks'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    due_date = db.Column(db.DateTime)
    status = db.Column(db.String(50), default='معلقة')  # معلقة، قيد التنفيذ، مكتملة، ملغاة
    priority = db.Column(db.String(20), default='متوسطة')  # عالية، متوسطة، منخفضة
    category = db.Column(db.String(50))  # قانونية، إدارية، مالية، أخرى
    progress = db.Column(db.Integer, default=0)  # نسبة الإنجاز 0-100
    estimated_hours = db.Column(db.Float)  # الساعات المقدرة
    actual_hours = db.Column(db.Float)  # الساعات الفعلية
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    completed_date = db.Column(db.DateTime)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))

    # العلاقات
    user = db.relationship('User', backref='tasks')
    client = db.relationship('Client', backref='tasks')
    case = db.relationship('Case', backref='tasks')

class Event(db.Model):
    __tablename__ = 'events'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    start_date = db.Column(db.DateTime, nullable=False)
    end_date = db.Column(db.DateTime)
    all_day = db.Column(db.Boolean, default=False)
    location = db.Column(db.String(200))
    event_type = db.Column(db.String(50))  # موعد، جلسة، اجتماع، مهمة، تذكير
    status = db.Column(db.String(30), default='مجدول')  # مجدول، مؤكد، ملغى، مكتمل
    reminder_minutes = db.Column(db.Integer, default=30)  # تذكير قبل بالدقائق
    color = db.Column(db.String(7), default='#007bff')  # لون الحدث
    recurring = db.Column(db.String(20))  # يومي، أسبوعي، شهري، سنوي
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))

    # العلاقات
    user = db.relationship('User', backref='events')
    client = db.relationship('Client', backref='events')
    case = db.relationship('Case', backref='events')

class Appointment(db.Model):
    __tablename__ = 'appointments'

    id = db.Column(db.Integer, primary_key=True)
    subject = db.Column(db.String(200), nullable=False)
    date = db.Column(db.DateTime, nullable=False)
    location = db.Column(db.String(150))
    notes = db.Column(db.Text)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))

class Property(db.Model):
    __tablename__ = 'properties'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(150), nullable=False)
    type = db.Column(db.String(50))  # محل، مكتب، شقة...
    address = db.Column(db.String(200))
    description = db.Column(db.Text)

    # تفاصيل العقار المحسنة
    area = db.Column(db.Float)  # المساحة
    rooms_count = db.Column(db.Integer)  # عدد الغرف
    bathrooms_count = db.Column(db.Integer)  # عدد الحمامات
    floor_number = db.Column(db.Integer)  # رقم الطابق
    building_age = db.Column(db.Integer)  # عمر المبنى
    furnished = db.Column(db.Boolean, default=False)  # مفروش
    parking = db.Column(db.Boolean, default=False)  # موقف سيارة
    elevator = db.Column(db.Boolean, default=False)  # مصعد

    # معلومات الإيجار
    monthly_rent = db.Column(db.Float, default=0.0)  # الإيجار الشهري
    currency = db.Column(db.String(20), default='شيكل')  # العملة
    deposit_amount = db.Column(db.Float, default=0.0)  # مبلغ التأمين
    commission_rate = db.Column(db.Float, default=0.0)  # نسبة العمولة

    # حالة العقار
    status = db.Column(db.String(50), default='متاح')  # متاح، مؤجر، تحت الصيانة
    availability_date = db.Column(db.DateTime)  # تاريخ التوفر

    # معلومات المالك
    owner_name = db.Column(db.String(150))  # اسم المالك
    owner_phone = db.Column(db.String(50))  # هاتف المالك
    owner_email = db.Column(db.String(120))  # إيميل المالك

    # ملاحظات
    notes = db.Column(db.Text)  # ملاحظات إضافية

    # تواريخ
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_date = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    leases = db.relationship('Lease', backref='property', lazy=True)

    def is_available(self):
        """فحص توفر العقار"""
        return self.status == 'متاح'

    def get_current_lease(self):
        """الحصول على عقد الإيجار الحالي"""
        return Lease.query.filter_by(property_id=self.id, status='نشط').first()

    def calculate_total_income(self):
        """حساب إجمالي الدخل من العقار"""
        return sum(income.net_amount for income in self.rental_incomes if income.status == 'مدفوع')

class Tenant(db.Model):
    __tablename__ = 'tenants'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(150), nullable=False)
    phone = db.Column(db.String(50))
    email = db.Column(db.String(120))
    address = db.Column(db.String(200))

    # معلومات إضافية للمستأجر
    national_id = db.Column(db.String(50))  # رقم الهوية
    occupation = db.Column(db.String(100))  # المهنة
    work_place = db.Column(db.String(200))  # مكان العمل
    monthly_income = db.Column(db.Float)  # الدخل الشهري
    emergency_contact = db.Column(db.String(150))  # جهة اتصال طوارئ
    emergency_phone = db.Column(db.String(50))  # هاتف الطوارئ

    # تقييم المستأجر
    credit_score = db.Column(db.Integer, default=0)  # التقييم الائتماني
    payment_history = db.Column(db.String(50), default='جيد')  # تاريخ الدفع
    notes = db.Column(db.Text)  # ملاحظات

    # حالة المستأجر
    status = db.Column(db.String(50), default='نشط')  # نشط، غير نشط، محظور
    registration_date = db.Column(db.DateTime, default=db.func.current_timestamp())

    leases = db.relationship('Lease', backref='tenant', lazy=True)

    def get_active_lease(self):
        """الحصول على عقد الإيجار النشط"""
        return Lease.query.filter_by(tenant_id=self.id, status='نشط').first()

    def calculate_total_payments(self):
        """حساب إجمالي المدفوعات"""
        total = 0
        for lease in self.leases:
            total += sum(income.net_amount for income in lease.property.rental_incomes
                        if income.tenant_id == self.id and income.status == 'مدفوع')
        return total

    def get_payment_status(self):
        """حالة الدفع الحالية"""
        active_lease = self.get_active_lease()
        if not active_lease:
            return 'لا يوجد عقد نشط'

        # فحص آخر دفعة
        from datetime import datetime
        current_month = datetime.now().month
        current_year = datetime.now().year

        last_payment = RentalIncome.query.filter_by(
            tenant_id=self.id,
            month=current_month,
            year=current_year,
            status='مدفوع'
        ).first()

        return 'مدفوع' if last_payment else 'متأخر'

class Lease(db.Model):
    __tablename__ = 'leases'

    id = db.Column(db.Integer, primary_key=True)

    # معرفات الربط
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'), nullable=False)
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenants.id'), nullable=False)

    # تواريخ العقد
    start_date = db.Column(db.DateTime, nullable=False)
    end_date = db.Column(db.DateTime, nullable=False)
    contract_date = db.Column(db.DateTime, default=db.func.current_timestamp())

    # تفاصيل مالية
    rent_amount = db.Column(db.Float, nullable=False)  # الإيجار الشهري
    annual_rent = db.Column(db.Float)  # الأجرة السنوية
    deposit = db.Column(db.Float, default=0.0)  # مبلغ التأمين
    commission = db.Column(db.Float, default=0.0)  # العمولة
    currency = db.Column(db.String(20), default='شيكل')

    # شروط العقد
    contract_duration = db.Column(db.Integer)  # مدة العقد بالأشهر
    payment_day = db.Column(db.Integer, default=1)  # يوم الدفع من كل شهر
    payment_frequency = db.Column(db.String(50), default='شهري')  # شهري، كل شهرين، ربع سنوي، نصف سنوي، سنوي
    late_fee_rate = db.Column(db.Float, default=0.0)  # نسبة غرامة التأخير
    renewal_option = db.Column(db.Boolean, default=True)  # خيار التجديد
    auto_renewal = db.Column(db.Boolean, default=False)  # التجديد التلقائي

    # حالة العقد
    status = db.Column(db.String(50), default='نشط')  # نشط، منتهي، ملغي، معلق
    termination_date = db.Column(db.DateTime)  # تاريخ الإنهاء
    termination_reason = db.Column(db.Text)  # سبب الإنهاء

    # ملاحظات وشروط
    terms_conditions = db.Column(db.Text)  # الشروط والأحكام
    special_terms = db.Column(db.Text)  # شروط خاصة
    notes = db.Column(db.Text)  # ملاحظات

    installments = db.relationship('Installment', backref='lease', lazy=True)

    def is_active(self):
        """فحص نشاط العقد"""
        from datetime import datetime
        now = datetime.now()
        return (self.status == 'نشط' and
                self.start_date <= now <= self.end_date)

    def days_remaining(self):
        """عدد الأيام المتبقية على انتهاء العقد"""
        from datetime import datetime
        if self.end_date:
            return (self.end_date - datetime.now()).days
        return 0

    def calculate_installment_amount(self):
        """حساب مبلغ القسط حسب تكرار الدفع"""
        frequency_multipliers = {
            'شهري': 1,
            'كل شهرين': 2,
            'ربع سنوي': 3,
            'نصف سنوي': 6,
            'سنوي': 12
        }
        multiplier = frequency_multipliers.get(self.payment_frequency, 1)
        return self.rent_amount * multiplier

    def get_next_payment_date(self):
        """حساب تاريخ الدفعة القادمة"""
        from datetime import datetime, timedelta
        from dateutil.relativedelta import relativedelta

        if not self.installments:
            return self.start_date

        last_installment = max(self.installments, key=lambda x: x.due_date)

        frequency_deltas = {
            'شهري': relativedelta(months=1),
            'كل شهرين': relativedelta(months=2),
            'ربع سنوي': relativedelta(months=3),
            'نصف سنوي': relativedelta(months=6),
            'سنوي': relativedelta(years=1)
        }

        delta = frequency_deltas.get(self.payment_frequency, relativedelta(months=1))
        return last_installment.due_date + delta

    def generate_installments(self):
        """توليد الأقساط للعقد"""
        from datetime import datetime
        from dateutil.relativedelta import relativedelta

        # حذف الأقساط الموجودة غير المدفوعة
        Installment.query.filter_by(lease_id=self.id, paid=False).delete()

        current_date = self.start_date
        installment_number = 1

        while current_date <= self.end_date:
            installment = Installment(
                lease_id=self.id,
                due_date=current_date,
                amount=self.calculate_installment_amount(),
                installment_number=installment_number,
                installment_type='إيجار'
            )
            db.session.add(installment)

            # حساب التاريخ التالي
            frequency_deltas = {
                'شهري': relativedelta(months=1),
                'كل شهرين': relativedelta(months=2),
                'ربع سنوي': relativedelta(months=3),
                'نصف سنوي': relativedelta(months=6),
                'سنوي': relativedelta(years=1)
            }

            delta = frequency_deltas.get(self.payment_frequency, relativedelta(months=1))
            current_date += delta
            installment_number += 1

        db.session.commit()

    def check_renewal_needed(self):
        """فحص الحاجة للتجديد"""
        from datetime import datetime, timedelta

        # إذا كان العقد ينتهي خلال 30 يوم
        if self.end_date and (self.end_date - datetime.now()).days <= 30:
            return True
        return False

    def auto_renew_contract(self, new_duration_months=12):
        """التجديد التلقائي للعقد"""
        from dateutil.relativedelta import relativedelta

        if self.auto_renewal and self.check_renewal_needed():
            # تمديد تاريخ انتهاء العقد
            self.end_date += relativedelta(months=new_duration_months)
            self.contract_duration += new_duration_months

            # توليد أقساط جديدة للفترة المجددة
            self.generate_installments()

            db.session.commit()
            return True
        return False

    def is_expiring_soon(self, days=30):
        """فحص إذا كان العقد سينتهي قريباً"""
        return 0 <= self.days_remaining() <= days

    def calculate_total_rent(self):
        """حساب إجمالي الإيجار للعقد"""
        if self.contract_duration:
            return self.rent_amount * self.contract_duration
        return 0

    def get_payment_status_for_month(self, month, year):
        """حالة الدفع لشهر معين"""
        payment = RentalIncome.query.filter_by(
            property_id=self.property_id,
            tenant_id=self.tenant_id,
            month=month,
            year=year
        ).first()

        return payment.status if payment else 'غير مدفوع'

class Installment(db.Model):
    __tablename__ = 'installments'

    id = db.Column(db.Integer, primary_key=True)
    lease_id = db.Column(db.Integer, db.ForeignKey('leases.id'), nullable=False)
    due_date = db.Column(db.DateTime, nullable=False)
    amount = db.Column(db.Float, nullable=False)
    paid = db.Column(db.Boolean, default=False)
    payment_date = db.Column(db.DateTime)

    # تفاصيل إضافية للقسط
    installment_number = db.Column(db.Integer)  # رقم القسط
    installment_type = db.Column(db.String(50), default='إيجار')  # إيجار، تأمين، عمولة
    late_fee = db.Column(db.Float, default=0.0)  # غرامة التأخير
    discount = db.Column(db.Float, default=0.0)  # خصم
    notes = db.Column(db.Text)  # ملاحظات

    # معلومات الدفع
    payment_method = db.Column(db.String(50))  # نقداً، شيك، تحويل
    payment_reference = db.Column(db.String(100))  # رقم الشيك أو التحويل

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_date = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    def is_overdue(self):
        """فحص تأخر القسط"""
        from datetime import datetime
        return not self.paid and self.due_date < datetime.now()

    def days_overdue(self):
        """عدد أيام التأخير"""
        from datetime import datetime
        if self.is_overdue():
            return (datetime.now() - self.due_date).days
        return 0

class FinancialTransaction(db.Model):
    __tablename__ = 'financial_transactions'

    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Float, nullable=False)
    date = db.Column(db.DateTime, nullable=False)
    type = db.Column(db.String(50), nullable=False)  # قبض/صرف/رسوم/دفعة/أتعاب/تحصيل إيجار...
    description = db.Column(db.Text)
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'))
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenants.id'))
    currency = db.Column(db.String(20), nullable=False)  # العملة
    case = db.relationship('Case', back_populates='financial_transactions')
    client = db.relationship('Client', backref='financial_transactions', lazy=True)
    property = db.relationship('Property', backref='financial_transactions', lazy=True)
    tenant = db.relationship('Tenant', backref='financial_transactions', lazy=True)

# نموذج إدارة الأتعاب المحسن
class Fee(db.Model):
    __tablename__ = 'fees'

    id = db.Column(db.Integer, primary_key=True)
    fee_number = db.Column(db.String(50), unique=True, nullable=False)
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'), nullable=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)

    # تفاصيل الخدمة
    fee_type = db.Column(db.String(100), nullable=False)  # استشارة، ترافع، صياغة عقد، إلخ
    service_category = db.Column(db.String(100), nullable=False)  # قانون مدني، جنائي، تجاري، إلخ
    description = db.Column(db.Text, nullable=False)
    service_date = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())
    due_date = db.Column(db.DateTime, nullable=False)

    # المبالغ المالية
    base_amount = db.Column(db.Float, nullable=False)  # المبلغ الأساسي
    additional_fees = db.Column(db.Float, default=0.0)  # رسوم إضافية
    discount_amount = db.Column(db.Float, default=0.0)  # خصم
    tax_amount = db.Column(db.Float, default=0.0)  # ضريبة
    total_amount = db.Column(db.Float, nullable=False)  # المجموع الإجمالي

    # حالة الدفع
    payment_status = db.Column(db.String(50), default='غير مدفوع')  # غير مدفوع، مدفوع جزئياً، مدفوع كاملاً
    paid_amount = db.Column(db.Float, default=0.0)
    remaining_amount = db.Column(db.Float, nullable=False)

    # معلومات الدفع
    payment_method = db.Column(db.String(50))  # نقداً، شيك، تحويل بنكي
    payment_reference = db.Column(db.String(100))  # رقم الشيك أو التحويل
    payment_terms = db.Column(db.String(200))  # شروط الدفع

    # معلومات إضافية
    priority = db.Column(db.String(50), default='متوسطة')  # عالية، متوسطة، منخفضة
    currency = db.Column(db.String(20), default='شيكل')
    notes = db.Column(db.Text)
    is_recurring = db.Column(db.Boolean, default=False)  # أتعاب دورية
    recurring_period = db.Column(db.String(50))  # شهري، ربع سنوي، سنوي

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_date = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    # العلاقات
    case = db.relationship('Case', backref='enhanced_fees')
    client = db.relationship('Client', backref='enhanced_fees')
    payments = db.relationship('FeePayment', backref='fee', lazy='dynamic', cascade='all, delete-orphan')

    def __init__(self, **kwargs):
        super(Fee, self).__init__(**kwargs)
        if not self.fee_number:
            self.fee_number = self.generate_fee_number()
        self.calculate_totals()

    def generate_fee_number(self):
        """توليد رقم الأتعاب تلقائياً"""
        from datetime import datetime
        year = datetime.now().year
        count = Fee.query.filter(Fee.fee_number.like(f'FEE-{year}-%')).count() + 1
        return f'FEE-{year}-{count:04d}'

    def calculate_totals(self):
        """حساب المجاميع"""
        self.total_amount = (self.base_amount or 0) + (self.additional_fees or 0) - (self.discount_amount or 0) + (self.tax_amount or 0)
        self.remaining_amount = self.total_amount - (self.paid_amount or 0)

        # تحديث حالة الدفع
        if self.paid_amount == 0:
            self.payment_status = 'غير مدفوع'
        elif self.paid_amount >= self.total_amount:
            self.payment_status = 'مدفوع كاملاً'
        else:
            self.payment_status = 'مدفوع جزئياً'

    def add_payment(self, amount, payment_method='نقداً', reference=None, notes=None):
        """إضافة دفعة جديدة"""
        payment = FeePayment(
            fee_id=self.id,
            amount=amount,
            payment_method=payment_method,
            reference=reference,
            notes=notes
        )
        db.session.add(payment)

        # تحديث المبلغ المدفوع
        self.paid_amount = (self.paid_amount or 0) + amount
        self.calculate_totals()

        return payment

    def is_overdue(self):
        """التحقق من تأخر الدفع"""
        from datetime import datetime
        return self.due_date < datetime.now() and self.remaining_amount > 0

    def days_overdue(self):
        """عدد أيام التأخير"""
        if self.is_overdue():
            from datetime import datetime
            return (datetime.now() - self.due_date).days
        return 0

    def calculate_remaining(self):
        """حساب المبلغ المتبقي - للتوافق مع النموذج القديم"""
        self.calculate_totals()

    def __repr__(self):
        return f'<Fee {self.fee_number}>'

# نموذج دفعات الأتعاب
class FeePayment(db.Model):
    __tablename__ = 'fee_payments'

    id = db.Column(db.Integer, primary_key=True)
    fee_id = db.Column(db.Integer, db.ForeignKey('fees.id'), nullable=False)
    payment_date = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(50), nullable=False, default='نقداً')
    reference = db.Column(db.String(100))  # رقم الشيك أو التحويل
    notes = db.Column(db.Text)
    receipt_number = db.Column(db.String(50))

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_date = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    def __init__(self, **kwargs):
        super(FeePayment, self).__init__(**kwargs)
        if not self.receipt_number:
            self.receipt_number = self.generate_receipt_number()

    def generate_receipt_number(self):
        """توليد رقم الإيصال تلقائياً"""
        from datetime import datetime
        year = datetime.now().year
        count = FeePayment.query.filter(FeePayment.receipt_number.like(f'REC-{year}-%')).count() + 1
        return f'REC-{year}-{count:04d}'

    def __repr__(self):
        return f'<FeePayment {self.receipt_number}>'

# نموذج إدارة الرسوم القضائية
class CourtFee(db.Model):
    __tablename__ = 'court_fees'

    id = db.Column(db.Integer, primary_key=True)
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'), nullable=False)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)  # مبلغ الرسوم
    paid_amount = db.Column(db.Float, default=0.0)  # المبلغ المدفوع
    remaining_amount = db.Column(db.Float, default=0.0)  # المبلغ المتبقي
    fee_type = db.Column(db.String(50), default='رسوم قضائية')  # نوع الرسوم
    payment_method = db.Column(db.String(50))  # طريقة الدفع
    due_date = db.Column(db.DateTime)  # تاريخ الاستحقاق
    status = db.Column(db.String(50), default='مستحقة')  # الحالة
    notes = db.Column(db.Text)  # ملاحظات
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())

    case = db.relationship('Case', backref='court_fees', lazy=True)
    client = db.relationship('Client', backref='court_fees', lazy=True)

    def calculate_remaining(self):
        """حساب المبلغ المتبقي"""
        self.remaining_amount = self.amount - self.paid_amount

# نموذج إدارة الديون المفصل
class Debt(db.Model):
    __tablename__ = 'debts'

    id = db.Column(db.Integer, primary_key=True)
    creditor_name = db.Column(db.String(150), nullable=False)  # اسم الدائن
    amount = db.Column(db.Float, nullable=False)  # مبلغ الدين
    paid_amount = db.Column(db.Float, default=0.0)  # المبلغ المدفوع
    remaining_amount = db.Column(db.Float, default=0.0)  # المبلغ المتبقي
    debt_type = db.Column(db.String(50), nullable=False)  # نوع الدين
    description = db.Column(db.Text)  # وصف الدين
    due_date = db.Column(db.DateTime)  # تاريخ الاستحقاق
    status = db.Column(db.String(50), default='مستحق')  # الحالة
    priority = db.Column(db.String(50), default='متوسطة')  # الأولوية
    currency = db.Column(db.String(20), default='شيكل')  # العملة
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())

    # ربط اختياري بالقضية أو العميل
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))

    case = db.relationship('Case', backref='debts', lazy=True)
    client = db.relationship('Client', backref='debts', lazy=True)
    payments = db.relationship('DebtPayment', backref='debt', lazy=True)

    def calculate_remaining(self):
        """حساب المبلغ المتبقي"""
        self.remaining_amount = self.amount - self.paid_amount

    def update_status(self):
        """تحديث حالة الدين"""
        if self.remaining_amount <= 0:
            self.status = 'مسدد'
        elif self.due_date and self.due_date < db.func.current_timestamp():
            self.status = 'متأخر'
        else:
            self.status = 'مستحق'

# نموذج دفعات الديون
class DebtPayment(db.Model):
    __tablename__ = 'debt_payments'

    id = db.Column(db.Integer, primary_key=True)
    debt_id = db.Column(db.Integer, db.ForeignKey('debts.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)  # مبلغ الدفعة
    payment_date = db.Column(db.DateTime, default=db.func.current_timestamp())  # تاريخ الدفع
    payment_method = db.Column(db.String(50))  # طريقة الدفع
    notes = db.Column(db.Text)  # ملاحظات
    currency = db.Column(db.String(20), default='شيكل')  # العملة

# نموذج إيرادات الإيجارات المفصل
class RentalIncome(db.Model):
    __tablename__ = 'rental_incomes'

    id = db.Column(db.Integer, primary_key=True)
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'), nullable=False)
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenants.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)  # مبلغ الإيجار
    month = db.Column(db.Integer, nullable=False)  # الشهر
    year = db.Column(db.Integer, nullable=False)  # السنة
    due_date = db.Column(db.DateTime, nullable=False)  # تاريخ الاستحقاق
    paid_date = db.Column(db.DateTime)  # تاريخ الدفع
    status = db.Column(db.String(50), default='مستحق')  # الحالة
    payment_method = db.Column(db.String(50))  # طريقة الدفع
    late_fee = db.Column(db.Float, default=0.0)  # غرامة التأخير
    discount = db.Column(db.Float, default=0.0)  # خصم
    net_amount = db.Column(db.Float)  # المبلغ الصافي
    currency = db.Column(db.String(20), default='شيكل')  # العملة
    notes = db.Column(db.Text)  # ملاحظات
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())

    property = db.relationship('Property', backref='rental_incomes', lazy=True)
    tenant = db.relationship('Tenant', backref='rental_incomes', lazy=True)

    def calculate_net_amount(self):
        """حساب المبلغ الصافي"""
        self.net_amount = self.amount + self.late_fee - self.discount

    def update_status(self):
        """تحديث حالة الإيجار"""
        if self.paid_date:
            self.status = 'مدفوع'
        elif self.due_date < db.func.current_timestamp():
            self.status = 'متأخر'
        else:
            self.status = 'مستحق'

# نموذج المصروفات المفصل
class Expense(db.Model):
    __tablename__ = 'expenses'

    id = db.Column(db.Integer, primary_key=True)
    category = db.Column(db.String(100), nullable=False)  # فئة المصروف
    subcategory = db.Column(db.String(100))  # فئة فرعية
    amount = db.Column(db.Float, nullable=False)  # المبلغ
    description = db.Column(db.Text, nullable=False)  # الوصف
    expense_date = db.Column(db.DateTime, default=db.func.current_timestamp())  # تاريخ المصروف
    payment_method = db.Column(db.String(50))  # طريقة الدفع
    vendor = db.Column(db.String(150))  # المورد
    receipt_number = db.Column(db.String(100))  # رقم الإيصال
    currency = db.Column(db.String(20), default='شيكل')  # العملة
    is_recurring = db.Column(db.Boolean, default=False)  # مصروف متكرر
    recurring_period = db.Column(db.String(50))  # فترة التكرار
    notes = db.Column(db.Text)  # ملاحظات
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())

    # ربط اختياري بالقضية أو العقار
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'))

    case = db.relationship('Case', backref='expenses', lazy=True)
    property = db.relationship('Property', backref='expenses', lazy=True)

class ClientDocument(db.Model):
    __tablename__ = 'client_documents'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(200), nullable=False)
    upload_date = db.Column(db.DateTime)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))


# تم نقل نماذج الإعدادات إلى archive_models.py لتجنب التضارب


# تم نقل نماذج النسخ الاحتياطي إلى archive_models.py لتجنب التضارب
