from . import db
from flask_login import UserMixin
from datetime import datetime, date

class User(db.Model, UserMixin):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(150), unique=True, nullable=False)
    password = db.Column(db.String(256), nullable=False)
    role = db.Column(db.String(50), default='موظف')
    last_login = db.Column(db.DateTime)
    cases = db.relationship('Case', backref='lawyer', lazy=True)

    def __repr__(self):
        return f'<User {self.username}>'

class Client(db.Model):
    __tablename__ = 'clients'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(150), nullable=False)
    national_id = db.Column(db.String(50))
    phone = db.Column(db.String(50))
    email = db.Column(db.String(120))
    address = db.Column(db.String(200))
    birth_date = db.Column(db.Date)
    occupation = db.Column(db.String(100))
    notes = db.Column(db.Text)
    role = db.Column(db.String(50), default='موكل')
    cases = db.relationship('Case', backref='client', lazy=True)
    documents = db.relationship('ClientDocument', backref='client', lazy=True)

class Case(db.Model):
    __tablename__ = 'cases'

    id = db.Column(db.Integer, primary_key=True)
    case_number = db.Column(db.String(100))  # رقم القضية في المحكمة
    office_case_number = db.Column(db.String(100))  # رقم القضية في المكتب
    title = db.Column(db.String(200), nullable=False)
    type = db.Column(db.String(100))
    status = db.Column(db.String(50), default='جارية')
    court = db.Column(db.String(100))
    opponent = db.Column(db.String(150))
    description = db.Column(db.Text)
    open_date = db.Column(db.Date)  # تاريخ فتح القضية

    # الحقول المالية للأتعاب
    fees_total = db.Column(db.Float, default=0.0)  # إجمالي الأتعاب
    fees_paid = db.Column(db.Float, default=0.0)   # المدفوع من الأتعاب
    fees_remaining = db.Column(db.Float, default=0.0)  # المتبقي من الأتعاب
    fees_currency = db.Column(db.String(20), default='شيكل')  # عملة الأتعاب

    # الحقول المالية للرسوم
    court_fees_total = db.Column(db.Float, default=0.0)  # إجمالي الرسوم
    court_fees_paid = db.Column(db.Float, default=0.0)   # المدفوع من الرسوم
    court_fees_remaining = db.Column(db.Float, default=0.0)  # المتبقي من الرسوم

    # حقول إضافية للقضية
    priority = db.Column(db.String(50), default='متوسطة')  # أولوية القضية
    next_session_date = db.Column(db.DateTime)  # موعد الجلسة القادمة
    case_value = db.Column(db.Float, default=0.0)  # قيمة القضية
    currency = db.Column(db.String(20), default='شيكل')  # العملة

    # صفة الموكل في القضية
    client_role = db.Column(db.String(50), default='مدعي')  # صفة الموكل: مدعي، مدعى عليه، محكوم له، محكوم عليه، مشتكي، متهم، مستأنف، مستأنف ضده

    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    lawyer_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    sessions = db.relationship('Session', backref='case', lazy=True)
    documents = db.relationship('Document', backref='case', lazy=True)
    invoices = db.relationship('Invoice', backref='case', lazy=True)
    financial_transactions = db.relationship('FinancialTransaction', back_populates='case', lazy=True)

    def calculate_fees_remaining(self):
        """حساب المتبقي من الأتعاب"""
        self.fees_remaining = self.fees_total - self.fees_paid

    def calculate_court_fees_remaining(self):
        """حساب المتبقي من الرسوم"""
        self.court_fees_remaining = self.court_fees_total - self.court_fees_paid

class Session(db.Model):
    __tablename__ = 'sessions'

    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.DateTime, nullable=False)
    location = db.Column(db.String(150))
    notes = db.Column(db.Text)
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'), nullable=False)

class Document(db.Model):
    __tablename__ = 'documents'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(200), nullable=False)
    upload_date = db.Column(db.DateTime)
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))

class Invoice(db.Model):
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Float, nullable=False)
    issue_date = db.Column(db.DateTime)
    paid = db.Column(db.Boolean, default=False)
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))
    payments = db.relationship('Payment', backref='invoice', lazy=True)

class Payment(db.Model):
    __tablename__ = 'payments'

    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Float, nullable=False)
    payment_date = db.Column(db.DateTime)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'))

class Task(db.Model):
    __tablename__ = 'tasks'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    due_date = db.Column(db.DateTime)
    status = db.Column(db.String(50), default='معلقة')  # معلقة، قيد التنفيذ، مكتملة، ملغاة
    priority = db.Column(db.String(20), default='متوسطة')  # عالية، متوسطة، منخفضة
    category = db.Column(db.String(50))  # قانونية، إدارية، مالية، أخرى
    progress = db.Column(db.Integer, default=0)  # نسبة الإنجاز 0-100
    estimated_hours = db.Column(db.Float)  # الساعات المقدرة
    actual_hours = db.Column(db.Float)  # الساعات الفعلية
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    completed_date = db.Column(db.DateTime)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))

    # العلاقات
    user = db.relationship('User', backref='tasks')
    client = db.relationship('Client', backref='tasks')
    case = db.relationship('Case', backref='tasks')

class Event(db.Model):
    __tablename__ = 'events'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    start_date = db.Column(db.DateTime, nullable=False)
    end_date = db.Column(db.DateTime)
    all_day = db.Column(db.Boolean, default=False)
    location = db.Column(db.String(200))
    event_type = db.Column(db.String(50))  # موعد، جلسة، اجتماع، مهمة، تذكير
    status = db.Column(db.String(30), default='مجدول')  # مجدول، مؤكد، ملغى، مكتمل
    reminder_minutes = db.Column(db.Integer, default=30)  # تذكير قبل بالدقائق
    color = db.Column(db.String(7), default='#007bff')  # لون الحدث
    recurring = db.Column(db.String(20))  # يومي، أسبوعي، شهري، سنوي
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))

    # العلاقات
    user = db.relationship('User', backref='events')
    client = db.relationship('Client', backref='events')
    case = db.relationship('Case', backref='events')

class Appointment(db.Model):
    __tablename__ = 'appointments'

    id = db.Column(db.Integer, primary_key=True)
    subject = db.Column(db.String(200), nullable=False)
    date = db.Column(db.DateTime, nullable=False)
    location = db.Column(db.String(150))
    notes = db.Column(db.Text)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))

class Property(db.Model):
    """نموذج العقار المحسن والمتطور"""
    __tablename__ = 'properties'

    id = db.Column(db.Integer, primary_key=True)

    # المعلومات الأساسية
    name = db.Column(db.String(200), nullable=False, index=True)
    property_code = db.Column(db.String(50), unique=True, index=True)  # رمز العقار
    type = db.Column(db.String(50), nullable=False, index=True)  # شقة، فيلا، مكتب، محل، مستودع، أرض
    category = db.Column(db.String(50))  # سكني، تجاري، صناعي، إداري

    # العنوان والموقع
    address = db.Column(db.String(300))
    city = db.Column(db.String(100), index=True)
    district = db.Column(db.String(100))  # الحي
    street = db.Column(db.String(200))
    building_number = db.Column(db.String(20))
    floor_number = db.Column(db.Integer)
    apartment_number = db.Column(db.String(20))
    postal_code = db.Column(db.String(20))

    # إحداثيات GPS
    latitude = db.Column(db.Float)
    longitude = db.Column(db.Float)

    # تفاصيل العقار
    area = db.Column(db.Float)  # المساحة بالمتر المربع
    land_area = db.Column(db.Float)  # مساحة الأرض
    built_area = db.Column(db.Float)  # المساحة المبنية
    rooms_count = db.Column(db.Integer, default=0)
    bedrooms_count = db.Column(db.Integer, default=0)
    bathrooms_count = db.Column(db.Integer, default=0)
    kitchens_count = db.Column(db.Integer, default=0)
    living_rooms_count = db.Column(db.Integer, default=0)

    # تفاصيل المبنى
    building_age = db.Column(db.Integer)  # عمر المبنى بالسنوات
    construction_year = db.Column(db.Integer)
    building_condition = db.Column(db.String(50), default='جيد')  # ممتاز، جيد، متوسط، يحتاج صيانة
    renovation_year = db.Column(db.Integer)  # سنة آخر تجديد

    # المرافق والخدمات
    furnished = db.Column(db.Boolean, default=False)
    furnished_level = db.Column(db.String(50))  # مفروش بالكامل، مفروش جزئياً، غير مفروش
    parking_spaces = db.Column(db.Integer, default=0)
    garage = db.Column(db.Boolean, default=False)
    elevator = db.Column(db.Boolean, default=False)
    balcony = db.Column(db.Boolean, default=False)
    garden = db.Column(db.Boolean, default=False)
    swimming_pool = db.Column(db.Boolean, default=False)
    security_system = db.Column(db.Boolean, default=False)
    air_conditioning = db.Column(db.Boolean, default=False)
    central_heating = db.Column(db.Boolean, default=False)

    # الخدمات العامة
    electricity = db.Column(db.Boolean, default=True)
    water = db.Column(db.Boolean, default=True)
    gas = db.Column(db.Boolean, default=False)
    internet = db.Column(db.Boolean, default=False)
    cable_tv = db.Column(db.Boolean, default=False)

    # معلومات الإيجار
    monthly_rent = db.Column(db.Float, default=0.0)
    annual_rent = db.Column(db.Float, default=0.0)
    currency = db.Column(db.String(20), default='JOD')
    deposit_amount = db.Column(db.Float, default=0.0)
    commission_rate = db.Column(db.Float, default=0.0)
    service_charge = db.Column(db.Float, default=0.0)  # رسوم الخدمات
    maintenance_fee = db.Column(db.Float, default=0.0)  # رسوم الصيانة

    # حالة العقار
    status = db.Column(db.String(50), default='متاح', index=True)  # متاح، مؤجر، تحت الصيانة، محجوز، غير متاح
    availability_date = db.Column(db.DateTime)
    last_maintenance_date = db.Column(db.DateTime)
    next_maintenance_date = db.Column(db.DateTime)

    # معلومات المالك
    owner_name = db.Column(db.String(150))
    owner_phone = db.Column(db.String(50))
    owner_email = db.Column(db.String(120))
    owner_address = db.Column(db.String(300))
    owner_id_number = db.Column(db.String(50))  # رقم هوية المالك

    # معلومات مالية إضافية
    purchase_price = db.Column(db.Float)  # سعر الشراء
    current_value = db.Column(db.Float)  # القيمة الحالية
    insurance_amount = db.Column(db.Float)  # مبلغ التأمين
    insurance_expiry = db.Column(db.DateTime)  # انتهاء التأمين
    property_tax = db.Column(db.Float)  # ضريبة العقار

    # تقييم العقار
    rating = db.Column(db.Float, default=0.0)  # تقييم من 1-5
    energy_rating = db.Column(db.String(10))  # تصنيف الطاقة A, B, C, D

    # ملاحظات ووصف
    description = db.Column(db.Text)
    features = db.Column(db.Text)  # المميزات الخاصة
    nearby_facilities = db.Column(db.Text)  # المرافق القريبة
    notes = db.Column(db.Text)
    internal_notes = db.Column(db.Text)  # ملاحظات داخلية

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_date = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    leases = db.relationship('Lease', backref='property', lazy=True, cascade='all, delete-orphan')
    documents = db.relationship('PropertyDocument', backref='property', lazy=True, cascade='all, delete-orphan')
    images = db.relationship('PropertyImage', backref='property', lazy=True, cascade='all, delete-orphan')
    maintenance_records = db.relationship('PropertyMaintenance', backref='property', lazy=True, cascade='all, delete-orphan')
    valuations = db.relationship('PropertyValuation', backref='property', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Property {self.name}>'

    def is_available(self):
        """فحص توفر العقار"""
        return self.status == 'متاح'

    def get_current_lease(self):
        """الحصول على عقد الإيجار الحالي"""
        return Lease.query.filter_by(property_id=self.id, status='نشط').first()

    def calculate_total_income(self):
        """حساب إجمالي الدخل من العقار"""
        total = 0
        for lease in self.leases:
            if lease.status == 'نشط':
                total += lease.rent_amount
        return total

    def get_occupancy_rate(self):
        """حساب معدل الإشغال"""
        from datetime import datetime, timedelta
        year_ago = datetime.now() - timedelta(days=365)

        total_days = 365
        occupied_days = 0

        for lease in self.leases:
            if lease.end_date >= year_ago:
                start = max(lease.start_date, year_ago)
                end = min(lease.end_date, datetime.now())
                occupied_days += (end - start).days

        return min(100, (occupied_days / total_days) * 100)

    def needs_maintenance(self):
        """فحص الحاجة للصيانة"""
        from datetime import datetime
        if self.next_maintenance_date:
            return self.next_maintenance_date <= datetime.now()
        return False

    def get_property_age(self):
        """حساب عمر العقار"""
        from datetime import datetime
        if self.construction_year:
            return datetime.now().year - self.construction_year
        return self.building_age or 0

    def calculate_roi(self):
        """حساب العائد على الاستثمار"""
        if self.purchase_price and self.purchase_price > 0:
            annual_income = self.monthly_rent * 12 if self.monthly_rent else 0
            return (annual_income / self.purchase_price) * 100
        return 0

    def to_dict(self):
        """تحويل إلى قاموس للـ API"""
        return {
            'id': self.id,
            'name': self.name,
            'property_code': self.property_code,
            'type': self.type,
            'category': self.category,
            'address': self.address,
            'city': self.city,
            'district': self.district,
            'area': self.area,
            'rooms_count': self.rooms_count,
            'monthly_rent': self.monthly_rent,
            'currency': self.currency,
            'status': self.status,
            'rating': self.rating,
            'created_date': self.created_date.isoformat() if self.created_date else None
        }

    # العلاقات مع النماذج المساعدة
    images = db.relationship('PropertyImage', backref='property', lazy='dynamic', cascade='all, delete-orphan')
    documents = db.relationship('PropertyDocument', backref='property', lazy='dynamic', cascade='all, delete-orphan')
    valuations = db.relationship('PropertyValuation', backref='property', lazy='dynamic', cascade='all, delete-orphan')
    maintenance_records = db.relationship('PropertyMaintenance', backref='property', lazy='dynamic', cascade='all, delete-orphan')

class Tenant(db.Model):
    """نموذج المستأجر المحسن والمتطور"""
    __tablename__ = 'tenants'

    id = db.Column(db.Integer, primary_key=True)

    # المعلومات الشخصية الأساسية
    tenant_code = db.Column(db.String(50), unique=True, index=True)  # رمز المستأجر
    first_name = db.Column(db.String(100), nullable=False)
    middle_name = db.Column(db.String(100))
    last_name = db.Column(db.String(100), nullable=False)
    full_name = db.Column(db.String(300), nullable=False, index=True)  # الاسم الكامل

    # معلومات الهوية
    national_id = db.Column(db.String(50), unique=True, index=True)
    passport_number = db.Column(db.String(50))
    nationality = db.Column(db.String(100))
    date_of_birth = db.Column(db.DateTime)
    place_of_birth = db.Column(db.String(200))
    gender = db.Column(db.String(10))  # ذكر، أنثى
    marital_status = db.Column(db.String(50))  # أعزب، متزوج، مطلق، أرمل

    # معلومات الاتصال
    phone_primary = db.Column(db.String(50), nullable=False)
    phone_secondary = db.Column(db.String(50))
    email_primary = db.Column(db.String(120), index=True)
    email_secondary = db.Column(db.String(120))

    # العنوان الحالي
    current_address = db.Column(db.String(300))
    current_city = db.Column(db.String(100))
    current_district = db.Column(db.String(100))
    current_postal_code = db.Column(db.String(20))

    # العنوان الدائم
    permanent_address = db.Column(db.String(300))
    permanent_city = db.Column(db.String(100))
    permanent_district = db.Column(db.String(100))
    permanent_postal_code = db.Column(db.String(20))

    # معلومات العمل
    occupation = db.Column(db.String(150))
    employer_name = db.Column(db.String(200))
    employer_address = db.Column(db.String(300))
    employer_phone = db.Column(db.String(50))
    work_position = db.Column(db.String(150))
    employment_start_date = db.Column(db.DateTime)
    employment_type = db.Column(db.String(50))  # دوام كامل، دوام جزئي، مؤقت، متعاقد

    # المعلومات المالية
    monthly_income = db.Column(db.Float)
    additional_income = db.Column(db.Float)
    total_monthly_income = db.Column(db.Float)
    bank_name = db.Column(db.String(150))
    bank_account_number = db.Column(db.String(100))
    iban = db.Column(db.String(50))

    # معلومات الطوارئ
    emergency_contact_name = db.Column(db.String(150))
    emergency_contact_relationship = db.Column(db.String(100))
    emergency_contact_phone = db.Column(db.String(50))
    emergency_contact_address = db.Column(db.String(300))

    # معلومات إضافية للطوارئ
    emergency_contact_2_name = db.Column(db.String(150))
    emergency_contact_2_relationship = db.Column(db.String(100))
    emergency_contact_2_phone = db.Column(db.String(50))

    # المراجع
    reference_1_name = db.Column(db.String(150))
    reference_1_phone = db.Column(db.String(50))
    reference_1_relationship = db.Column(db.String(100))
    reference_2_name = db.Column(db.String(150))
    reference_2_phone = db.Column(db.String(50))
    reference_2_relationship = db.Column(db.String(100))

    # تقييم المستأجر
    credit_score = db.Column(db.Integer, default=0)  # من 0-1000
    payment_history_rating = db.Column(db.String(50), default='جيد')  # ممتاز، جيد، متوسط، ضعيف
    reliability_score = db.Column(db.Float, default=0.0)  # من 0-5
    previous_landlord_reference = db.Column(db.Text)

    # التاريخ الإيجاري
    previous_rental_history = db.Column(db.Text)
    eviction_history = db.Column(db.Text)
    legal_issues = db.Column(db.Text)

    # معلومات الأسرة
    family_size = db.Column(db.Integer, default=1)
    children_count = db.Column(db.Integer, default=0)
    pets = db.Column(db.Boolean, default=False)
    pets_details = db.Column(db.Text)

    # التفضيلات والمتطلبات
    preferred_property_type = db.Column(db.String(100))
    max_rent_budget = db.Column(db.Float)
    preferred_location = db.Column(db.String(200))
    special_requirements = db.Column(db.Text)

    # حالة المستأجر
    status = db.Column(db.String(50), default='نشط', index=True)  # نشط، غير نشط، محظور، تحت المراجعة
    blacklisted = db.Column(db.Boolean, default=False)
    blacklist_reason = db.Column(db.Text)

    # ملاحظات
    notes = db.Column(db.Text)
    internal_notes = db.Column(db.Text)  # ملاحظات داخلية
    special_instructions = db.Column(db.Text)

    # تواريخ النظام
    registration_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    last_contact_date = db.Column(db.DateTime)
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_date = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    leases = db.relationship('Lease', backref='tenant', lazy=True, cascade='all, delete-orphan')
    documents = db.relationship('TenantDocument', backref='tenant', lazy=True, cascade='all, delete-orphan')
    payment_history = db.relationship('PaymentRecord', backref='tenant', lazy=True, cascade='all, delete-orphan')
    communications = db.relationship('TenantCommunication', backref='tenant', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Tenant {self.full_name}>'

    def get_active_lease(self):
        """الحصول على عقد الإيجار النشط"""
        return Lease.query.filter_by(tenant_id=self.id, status='نشط').first()

    def get_current_property(self):
        """الحصول على العقار الحالي"""
        active_lease = self.get_active_lease()
        return active_lease.property if active_lease else None

    def calculate_total_payments(self):
        """حساب إجمالي المدفوعات"""
        total = 0
        for payment in self.payment_history:
            if payment.status == 'مدفوع':
                total += payment.amount
        return total

    def get_payment_status(self):
        """حالة الدفع الحالية"""
        active_lease = self.get_active_lease()
        if not active_lease:
            return 'لا يوجد عقد نشط'

        from datetime import datetime, timedelta
        today = datetime.now()
        current_month_start = today.replace(day=1)

        # البحث عن دفعة الشهر الحالي
        current_payment = PaymentRecord.query.filter(
            PaymentRecord.tenant_id == self.id,
            PaymentRecord.payment_date >= current_month_start,
            PaymentRecord.status == 'مدفوع'
        ).first()

        if current_payment:
            return 'مدفوع'
        elif today.day > active_lease.payment_day:
            return 'متأخر'
        else:
            return 'مستحق'

    def get_outstanding_balance(self):
        """حساب الرصيد المستحق"""
        active_lease = self.get_active_lease()
        if not active_lease:
            return 0

        from datetime import datetime
        total_due = 0

        # حساب الأقساط المستحقة
        for installment in active_lease.installments:
            if installment.due_date <= datetime.now() and installment.status != 'مدفوع':
                total_due += installment.amount

        return total_due

    def calculate_credit_score(self):
        """حساب النقاط الائتمانية"""
        score = 500  # نقطة البداية

        # تقييم تاريخ الدفع
        if self.payment_history_rating == 'ممتاز':
            score += 200
        elif self.payment_history_rating == 'جيد':
            score += 150
        elif self.payment_history_rating == 'متوسط':
            score += 50

        # تقييم الدخل
        active_lease = self.get_active_lease()
        if active_lease and self.total_monthly_income:
            income_ratio = active_lease.rent_amount / self.total_monthly_income
            if income_ratio <= 0.3:
                score += 100
            elif income_ratio <= 0.4:
                score += 50
            elif income_ratio > 0.5:
                score -= 50

        # تقييم مدة الإقامة
        if self.registration_date:
            from datetime import datetime
            months_with_us = (datetime.now() - self.registration_date).days / 30
            if months_with_us > 24:
                score += 100
            elif months_with_us > 12:
                score += 50

        # خصم للمشاكل
        if self.blacklisted:
            score -= 300
        if self.eviction_history:
            score -= 200
        if self.legal_issues:
            score -= 150

        return max(0, min(1000, score))

    def get_age(self):
        """حساب العمر"""
        if self.date_of_birth:
            from datetime import datetime
            return (datetime.now() - self.date_of_birth).days // 365
        return None

    def is_eligible_for_lease(self, property_rent):
        """فحص الأهلية للإيجار"""
        if self.blacklisted:
            return False, "المستأجر في القائمة السوداء"

        if self.status != 'نشط':
            return False, "حالة المستأجر غير نشطة"

        if self.total_monthly_income and property_rent:
            income_ratio = property_rent / self.total_monthly_income
            if income_ratio > 0.5:
                return False, "الدخل غير كافي (يجب أن يكون الإيجار أقل من 50% من الدخل)"

        credit_score = self.calculate_credit_score()
        if credit_score < 300:
            return False, "النقاط الائتمانية منخفضة جداً"

        return True, "مؤهل للإيجار"

    def to_dict(self):
        """تحويل إلى قاموس للـ API"""
        return {
            'id': self.id,
            'tenant_code': self.tenant_code,
            'full_name': self.full_name,
            'phone_primary': self.phone_primary,
            'email_primary': self.email_primary,
            'national_id': self.national_id,
            'occupation': self.occupation,
            'monthly_income': self.total_monthly_income,
            'status': self.status,
            'credit_score': self.calculate_credit_score(),
            'payment_status': self.get_payment_status(),
            'registration_date': self.registration_date.isoformat() if self.registration_date else None
        }

    # العلاقات مع النماذج المساعدة
    documents = db.relationship('TenantDocument', backref='tenant', lazy='dynamic', cascade='all, delete-orphan')
    payment_records = db.relationship('PaymentRecord', backref='tenant', lazy='dynamic', cascade='all, delete-orphan')
    communications = db.relationship('TenantCommunication', backref='tenant', lazy='dynamic', cascade='all, delete-orphan')


class Lease(db.Model):
    """نموذج عقد الإيجار المحسن والمتطور"""
    __tablename__ = 'leases'

    id = db.Column(db.Integer, primary_key=True)

    # معرف العقد
    lease_code = db.Column(db.String(50), unique=True, index=True)  # رمز العقد
    contract_number = db.Column(db.String(100), unique=True)  # رقم العقد الرسمي

    # معرفات الربط
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'), nullable=False, index=True)
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenants.id'), nullable=False, index=True)

    # تواريخ العقد
    contract_date = db.Column(db.DateTime, nullable=False)  # تاريخ توقيع العقد
    start_date = db.Column(db.DateTime, nullable=False, index=True)  # تاريخ بداية الإيجار
    end_date = db.Column(db.DateTime, nullable=False, index=True)  # تاريخ انتهاء الإيجار
    notice_period_days = db.Column(db.Integer, default=30)  # فترة الإشعار بالأيام

    # تفاصيل مالية أساسية
    monthly_rent = db.Column(db.Float, nullable=False)  # الإيجار الشهري
    annual_rent = db.Column(db.Float)  # الإيجار السنوي
    currency = db.Column(db.String(20), default='JOD')  # العملة

    # المبالغ الإضافية
    security_deposit = db.Column(db.Float, default=0.0)  # مبلغ التأمين
    key_deposit = db.Column(db.Float, default=0.0)  # تأمين المفاتيح
    commission_amount = db.Column(db.Float, default=0.0)  # مبلغ العمولة
    commission_rate = db.Column(db.Float, default=0.0)  # نسبة العمولة

    # الرسوم والخدمات
    service_charges = db.Column(db.Float, default=0.0)  # رسوم الخدمات
    maintenance_fee = db.Column(db.Float, default=0.0)  # رسوم الصيانة
    utilities_included = db.Column(db.Boolean, default=False)  # المرافق مشمولة
    electricity_included = db.Column(db.Boolean, default=False)
    water_included = db.Column(db.Boolean, default=False)
    gas_included = db.Column(db.Boolean, default=False)
    internet_included = db.Column(db.Boolean, default=False)

    # شروط الدفع
    payment_frequency = db.Column(db.String(50), default='شهري')  # شهري، ربع سنوي، نصف سنوي، سنوي
    payment_due_day = db.Column(db.Integer, default=1)  # يوم استحقاق الدفع
    payment_method = db.Column(db.String(50), default='نقداً')  # نقداً، شيك، تحويل بنكي
    advance_payment_months = db.Column(db.Integer, default=0)  # عدد أشهر الدفع المقدم

    # الغرامات والخصومات
    late_fee_rate = db.Column(db.Float, default=0.0)  # نسبة غرامة التأخير يومياً
    late_fee_grace_days = db.Column(db.Integer, default=5)  # أيام السماح قبل الغرامة
    early_payment_discount = db.Column(db.Float, default=0.0)  # خصم الدفع المبكر

    # شروط التجديد
    renewal_option = db.Column(db.Boolean, default=True)  # خيار التجديد
    auto_renewal = db.Column(db.Boolean, default=False)  # التجديد التلقائي
    renewal_notice_days = db.Column(db.Integer, default=60)  # أيام إشعار التجديد
    rent_increase_rate = db.Column(db.Float, default=0.0)  # نسبة زيادة الإيجار عند التجديد

    # حالة العقد
    status = db.Column(db.String(50), default='مسودة', index=True)  # مسودة، نشط، منتهي، ملغي، معلق، مجدد
    approval_status = db.Column(db.String(50), default='في انتظار الموافقة')  # في انتظار الموافقة، موافق عليه، مرفوض

    # تواريخ مهمة
    activation_date = db.Column(db.DateTime)  # تاريخ تفعيل العقد
    termination_date = db.Column(db.DateTime)  # تاريخ الإنهاء الفعلي
    termination_notice_date = db.Column(db.DateTime)  # تاريخ إشعار الإنهاء
    last_renewal_date = db.Column(db.DateTime)  # تاريخ آخر تجديد

    # أسباب الإنهاء والتغييرات
    termination_reason = db.Column(db.String(200))  # سبب الإنهاء
    termination_initiated_by = db.Column(db.String(50))  # من بدأ الإنهاء: مالك، مستأجر، إدارة
    termination_notes = db.Column(db.Text)  # ملاحظات الإنهاء

    # الشروط والأحكام
    terms_conditions = db.Column(db.Text)  # الشروط والأحكام العامة
    special_conditions = db.Column(db.Text)  # الشروط الخاصة
    property_usage_terms = db.Column(db.Text)  # شروط استخدام العقار
    maintenance_responsibility = db.Column(db.Text)  # مسؤوليات الصيانة

    # قيود وقوانين
    pets_allowed = db.Column(db.Boolean, default=False)  # السماح بالحيوانات الأليفة
    smoking_allowed = db.Column(db.Boolean, default=False)  # السماح بالتدخين
    subletting_allowed = db.Column(db.Boolean, default=False)  # السماح بالتأجير من الباطن
    max_occupants = db.Column(db.Integer)  # الحد الأقصى للسكان

    # معلومات قانونية
    legal_representative = db.Column(db.String(200))  # الممثل القانوني
    witness_1_name = db.Column(db.String(150))  # اسم الشاهد الأول
    witness_1_id = db.Column(db.String(50))  # هوية الشاهد الأول
    witness_2_name = db.Column(db.String(150))  # اسم الشاهد الثاني
    witness_2_id = db.Column(db.String(50))  # هوية الشاهد الثاني

    # ملاحظات
    notes = db.Column(db.Text)  # ملاحظات عامة
    internal_notes = db.Column(db.Text)  # ملاحظات داخلية
    tenant_notes = db.Column(db.Text)  # ملاحظات المستأجر

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_date = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    installments = db.relationship('LeaseInstallment', backref='lease', lazy=True, cascade='all, delete-orphan')
    payments = db.relationship('LeasePayment', backref='lease', lazy=True, cascade='all, delete-orphan')
    documents = db.relationship('LeaseDocument', backref='lease', lazy=True, cascade='all, delete-orphan')
    renewals = db.relationship('LeaseRenewal', backref='lease', lazy=True, cascade='all, delete-orphan')
    violations = db.relationship('LeaseViolation', backref='lease', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Lease {self.lease_code}>'

    def is_active(self):
        """فحص نشاط العقد"""
        from datetime import datetime
        now = datetime.now()
        return (self.status == 'نشط' and
                self.start_date <= now <= self.end_date)

    def is_expired(self):
        """فحص انتهاء العقد"""
        from datetime import datetime
        return datetime.now() > self.end_date

    def days_remaining(self):
        """عدد الأيام المتبقية على انتهاء العقد"""
        from datetime import datetime
        if self.end_date:
            remaining = (self.end_date - datetime.now()).days
            return max(0, remaining)
        return 0

    def is_expiring_soon(self, days=30):
        """فحص إذا كان العقد سينتهي قريباً"""
        return 0 <= self.days_remaining() <= days

    def calculate_total_contract_value(self):
        """حساب إجمالي قيمة العقد"""
        from datetime import datetime
        if self.start_date and self.end_date:
            months = ((self.end_date.year - self.start_date.year) * 12 +
                     (self.end_date.month - self.start_date.month))
            return self.monthly_rent * months
        return 0

    def calculate_installment_amount(self):
        """حساب مبلغ القسط حسب تكرار الدفع"""
        frequency_multipliers = {
            'شهري': 1,
            'ربع سنوي': 3,
            'نصف سنوي': 6,
            'سنوي': 12
        }
        multiplier = frequency_multipliers.get(self.payment_frequency, 1)
        return self.monthly_rent * multiplier

    def get_next_payment_date(self):
        """حساب تاريخ الدفعة القادمة"""
        from datetime import datetime
        from dateutil.relativedelta import relativedelta

        # البحث عن آخر دفعة
        last_payment = LeasePayment.query.filter_by(
            lease_id=self.id,
            status='مدفوع'
        ).order_by(LeasePayment.payment_date.desc()).first()

        if last_payment:
            base_date = last_payment.payment_date
        else:
            base_date = self.start_date

        # حساب التاريخ التالي حسب تكرار الدفع
        frequency_deltas = {
            'شهري': relativedelta(months=1),
            'ربع سنوي': relativedelta(months=3),
            'نصف سنوي': relativedelta(months=6),
            'سنوي': relativedelta(years=1)
        }

        delta = frequency_deltas.get(self.payment_frequency, relativedelta(months=1))
        next_date = base_date + delta

        # التأكد من أن التاريخ لا يتجاوز نهاية العقد
        return min(next_date, self.end_date) if self.end_date else next_date

    def calculate_outstanding_amount(self):
        """حساب المبلغ المستحق"""
        total_due = 0
        for installment in self.installments:
            if installment.status == 'مستحق' and not installment.is_paid:
                total_due += installment.amount
        return total_due

    def calculate_late_fees(self):
        """حساب غرامات التأخير"""
        from datetime import datetime
        total_late_fees = 0

        for installment in self.installments:
            if installment.is_overdue():
                days_late = installment.days_overdue()
                if days_late > self.late_fee_grace_days:
                    late_days = days_late - self.late_fee_grace_days
                    late_fee = installment.amount * (self.late_fee_rate / 100) * late_days
                    total_late_fees += late_fee

        return total_late_fees

    def generate_installments(self):
        """توليد الأقساط للعقد"""
        from datetime import datetime
        from dateutil.relativedelta import relativedelta

        # حذف الأقساط غير المدفوعة
        LeaseInstallment.query.filter_by(
            lease_id=self.id,
            is_paid=False
        ).delete()

        current_date = self.start_date
        installment_number = 1

        # تحديد الفترة الزمنية بين الأقساط
        frequency_deltas = {
            'شهري': relativedelta(months=1),
            'ربع سنوي': relativedelta(months=3),
            'نصف سنوي': relativedelta(months=6),
            'سنوي': relativedelta(years=1)
        }

        delta = frequency_deltas.get(self.payment_frequency, relativedelta(months=1))
        installment_amount = self.calculate_installment_amount()

        while current_date <= self.end_date:
            # تعديل يوم الاستحقاق
            due_date = current_date.replace(day=min(self.payment_due_day, 28))

            installment = LeaseInstallment(
                lease_id=self.id,
                due_date=due_date,
                amount=installment_amount,
                installment_number=installment_number,
                installment_type='إيجار',
                status='مستحق'
            )
            db.session.add(installment)

            current_date += delta
            installment_number += 1

        db.session.commit()

    def check_renewal_eligibility(self):
        """فحص أهلية التجديد"""
        if not self.renewal_option:
            return False, "العقد لا يحتوي على خيار التجديد"

        if self.status != 'نشط':
            return False, "العقد غير نشط"

        # فحص تاريخ الدفع
        outstanding = self.calculate_outstanding_amount()
        if outstanding > 0:
            return False, f"يوجد مبلغ مستحق: {outstanding}"

        # فحص المخالفات
        active_violations = LeaseViolation.query.filter_by(
            lease_id=self.id,
            status='نشط'
        ).count()

        if active_violations > 0:
            return False, f"يوجد {active_violations} مخالفة نشطة"

        return True, "مؤهل للتجديد"

    def auto_renew_if_eligible(self, new_duration_months=12):
        """التجديد التلقائي إذا كان مؤهلاً"""
        if not self.auto_renewal:
            return False, "التجديد التلقائي غير مفعل"

        eligible, message = self.check_renewal_eligibility()
        if not eligible:
            return False, message

        if not self.is_expiring_soon(self.renewal_notice_days):
            return False, "لم يحن وقت التجديد بعد"

        # إنشاء تجديد جديد
        renewal = LeaseRenewal(
            lease_id=self.id,
            old_end_date=self.end_date,
            new_end_date=self.end_date + relativedelta(months=new_duration_months),
            old_rent=self.monthly_rent,
            new_rent=self.monthly_rent * (1 + self.rent_increase_rate / 100),
            renewal_type='تلقائي',
            status='مفعل'
        )

        # تحديث العقد
        self.end_date = renewal.new_end_date
        self.monthly_rent = renewal.new_rent
        self.last_renewal_date = datetime.now()

        db.session.add(renewal)

        # توليد أقساط جديدة
        self.generate_installments()

        db.session.commit()
        return True, "تم التجديد التلقائي بنجاح"

    def terminate_lease(self, reason, initiated_by, termination_date=None, notes=None):
        """إنهاء العقد"""
        from datetime import datetime

        if termination_date is None:
            termination_date = datetime.now()

        self.status = 'منتهي'
        self.termination_date = termination_date
        self.termination_reason = reason
        self.termination_initiated_by = initiated_by
        self.termination_notes = notes

        # تحديث حالة العقار
        if self.property:
            self.property.status = 'متاح'

        db.session.commit()

    def to_dict(self):
        """تحويل إلى قاموس للـ API"""
        return {
            'id': self.id,
            'lease_code': self.lease_code,
            'contract_number': self.contract_number,
            'property_name': self.property.name if self.property else None,
            'tenant_name': self.tenant.full_name if self.tenant else None,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'monthly_rent': self.monthly_rent,
            'currency': self.currency,
            'status': self.status,
            'days_remaining': self.days_remaining(),
            'is_expiring_soon': self.is_expiring_soon(),
            'outstanding_amount': self.calculate_outstanding_amount()
        }

    # العلاقات مع النماذج المساعدة
    installments = db.relationship('LeaseInstallment', backref='lease', lazy='dynamic', cascade='all, delete-orphan')
    payments = db.relationship('LeasePayment', backref='lease', lazy='dynamic', cascade='all, delete-orphan')
    documents = db.relationship('LeaseDocument', backref='lease', lazy='dynamic', cascade='all, delete-orphan')
    renewals = db.relationship('LeaseRenewal', backref='lease', lazy='dynamic', cascade='all, delete-orphan')
    violations = db.relationship('LeaseViolation', backref='lease', lazy='dynamic', cascade='all, delete-orphan')


# ==================== النماذج المساعدة لإدارة العقارات ====================

class LeaseInstallment(db.Model):
    """نموذج أقساط عقد الإيجار"""
    __tablename__ = 'lease_installments'

    id = db.Column(db.Integer, primary_key=True)
    lease_id = db.Column(db.Integer, db.ForeignKey('leases.id'), nullable=False, index=True)

    # تفاصيل القسط
    installment_number = db.Column(db.Integer, nullable=False)
    due_date = db.Column(db.DateTime, nullable=False, index=True)
    amount = db.Column(db.Float, nullable=False)
    installment_type = db.Column(db.String(50), default='إيجار')  # إيجار، تأمين، عمولة، خدمات

    # حالة الدفع
    is_paid = db.Column(db.Boolean, default=False, index=True)
    payment_date = db.Column(db.DateTime)
    payment_amount = db.Column(db.Float)
    payment_method = db.Column(db.String(50))  # نقداً، شيك، تحويل بنكي
    payment_reference = db.Column(db.String(100))  # رقم الشيك أو التحويل

    # الغرامات والخصومات
    late_fee = db.Column(db.Float, default=0.0)
    discount = db.Column(db.Float, default=0.0)
    net_amount = db.Column(db.Float)  # المبلغ الصافي بعد الخصم والغرامة

    # الحالة
    status = db.Column(db.String(50), default='مستحق')  # مستحق، مدفوع، متأخر، ملغي

    # ملاحظات
    notes = db.Column(db.Text)

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_date = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    def __repr__(self):
        return f'<LeaseInstallment {self.installment_number} - {self.amount}>'

    def is_overdue(self):
        """فحص تأخر القسط"""
        from datetime import datetime
        return not self.is_paid and self.due_date < datetime.now()

    def days_overdue(self):
        """عدد أيام التأخير"""
        from datetime import datetime
        if self.is_overdue():
            return (datetime.now() - self.due_date).days
        return 0

    def calculate_late_fee(self, daily_rate=0.0):
        """حساب غرامة التأخير"""
        if self.is_overdue() and daily_rate > 0:
            days_late = self.days_overdue()
            return self.amount * (daily_rate / 100) * days_late
        return 0

    def mark_as_paid(self, payment_amount, payment_method, payment_reference=None, payment_date=None):
        """تسجيل دفع القسط"""
        from datetime import datetime

        self.is_paid = True
        self.payment_amount = payment_amount
        self.payment_method = payment_method
        self.payment_reference = payment_reference
        self.payment_date = payment_date or datetime.now()
        self.status = 'مدفوع'

        # حساب المبلغ الصافي
        self.net_amount = payment_amount - self.discount + self.late_fee

        db.session.commit()


class LeasePayment(db.Model):
    """نموذج مدفوعات عقد الإيجار"""
    __tablename__ = 'lease_payments'

    id = db.Column(db.Integer, primary_key=True)
    lease_id = db.Column(db.Integer, db.ForeignKey('leases.id'), nullable=False, index=True)
    installment_id = db.Column(db.Integer, db.ForeignKey('lease_installments.id'), index=True)

    # تفاصيل الدفع
    payment_date = db.Column(db.DateTime, nullable=False, index=True)
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(50), nullable=False)  # نقداً، شيك، تحويل بنكي، بطاقة ائتمان
    payment_reference = db.Column(db.String(100))  # رقم الشيك أو التحويل

    # تفاصيل إضافية
    payment_for_month = db.Column(db.Integer)  # الشهر المدفوع عنه
    payment_for_year = db.Column(db.Integer)  # السنة المدفوعة عنها

    # الحالة
    status = db.Column(db.String(50), default='مدفوع')  # مدفوع، ملغي، مرتجع

    # معلومات البنك (للشيكات والتحويلات)
    bank_name = db.Column(db.String(150))
    account_number = db.Column(db.String(100))

    # ملاحظات
    notes = db.Column(db.Text)
    receipt_number = db.Column(db.String(100))  # رقم الإيصال

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<LeasePayment {self.amount} - {self.payment_date}>'


class LeaseDocument(db.Model):
    """نموذج وثائق عقد الإيجار"""
    __tablename__ = 'lease_documents'

    id = db.Column(db.Integer, primary_key=True)
    lease_id = db.Column(db.Integer, db.ForeignKey('leases.id'), nullable=False, index=True)

    # تفاصيل الوثيقة
    document_name = db.Column(db.String(200), nullable=False)
    document_type = db.Column(db.String(100))  # عقد، هوية، راتب، ضمان، أخرى
    file_path = db.Column(db.String(500))
    file_size = db.Column(db.Integer)
    file_type = db.Column(db.String(50))  # pdf, jpg, png, doc

    # معلومات إضافية
    description = db.Column(db.Text)
    is_required = db.Column(db.Boolean, default=False)
    is_verified = db.Column(db.Boolean, default=False)
    verified_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    verified_date = db.Column(db.DateTime)

    # تواريخ النظام
    upload_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<LeaseDocument {self.document_name}>'


class LeaseRenewal(db.Model):
    """نموذج تجديد عقد الإيجار"""
    __tablename__ = 'lease_renewals'

    id = db.Column(db.Integer, primary_key=True)
    lease_id = db.Column(db.Integer, db.ForeignKey('leases.id'), nullable=False, index=True)

    # تفاصيل التجديد
    renewal_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    old_end_date = db.Column(db.DateTime, nullable=False)
    new_end_date = db.Column(db.DateTime, nullable=False)

    # التغييرات المالية
    old_rent = db.Column(db.Float, nullable=False)
    new_rent = db.Column(db.Float, nullable=False)
    rent_increase_amount = db.Column(db.Float)
    rent_increase_percentage = db.Column(db.Float)

    # نوع التجديد
    renewal_type = db.Column(db.String(50), default='يدوي')  # يدوي، تلقائي
    renewal_duration_months = db.Column(db.Integer)

    # الحالة
    status = db.Column(db.String(50), default='مقترح')  # مقترح، موافق عليه، مرفوض، مفعل

    # ملاحظات
    notes = db.Column(db.Text)
    terms_changes = db.Column(db.Text)  # التغييرات في الشروط

    # تواريخ النظام
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    approved_date = db.Column(db.DateTime)

    def __repr__(self):
        return f'<LeaseRenewal {self.lease_id} - {self.new_end_date}>'


class LeaseViolation(db.Model):
    """نموذج مخالفات عقد الإيجار"""
    __tablename__ = 'lease_violations'

    id = db.Column(db.Integer, primary_key=True)
    lease_id = db.Column(db.Integer, db.ForeignKey('leases.id'), nullable=False, index=True)

    # تفاصيل المخالفة
    violation_type = db.Column(db.String(100), nullable=False)  # تأخير دفع، إتلاف، ضوضاء، مخالفة شروط
    violation_date = db.Column(db.DateTime, nullable=False)
    description = db.Column(db.Text, nullable=False)
    severity = db.Column(db.String(50), default='متوسط')  # بسيط، متوسط، خطير

    # الإجراءات
    action_taken = db.Column(db.Text)  # الإجراء المتخذ
    fine_amount = db.Column(db.Float, default=0.0)  # مبلغ الغرامة
    warning_issued = db.Column(db.Boolean, default=False)  # إصدار إنذار

    # الحالة
    status = db.Column(db.String(50), default='نشط')  # نشط، محلول، مغلق
    resolution_date = db.Column(db.DateTime)  # تاريخ الحل
    resolution_notes = db.Column(db.Text)  # ملاحظات الحل

    # تواريخ النظام
    reported_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    reported_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    resolved_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<LeaseViolation {self.violation_type} - {self.violation_date}>'


class PropertyValuation(db.Model):
    """نموذج تقييم العقارات"""
    __tablename__ = 'property_valuations'

    id = db.Column(db.Integer, primary_key=True)
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'), nullable=False, index=True)

    # تفاصيل التقييم
    valuation_date = db.Column(db.DateTime, nullable=False)
    valuation_type = db.Column(db.String(100))  # تقييم سوقي، تقييم إيجاري، تقييم تأميني
    valuation_purpose = db.Column(db.String(200))  # بيع، شراء، تأمين، قرض

    # القيم
    market_value = db.Column(db.Float)  # القيمة السوقية
    rental_value_monthly = db.Column(db.Float)  # القيمة الإيجارية الشهرية
    rental_value_annual = db.Column(db.Float)  # القيمة الإيجارية السنوية
    insurance_value = db.Column(db.Float)  # القيمة التأمينية

    # معلومات المقيم
    appraiser_name = db.Column(db.String(200))
    appraiser_license = db.Column(db.String(100))
    appraiser_company = db.Column(db.String(200))

    # تفاصيل التقييم
    valuation_method = db.Column(db.String(100))  # مقارنة، دخل، تكلفة
    comparable_properties = db.Column(db.Text)  # العقارات المقارنة
    market_conditions = db.Column(db.Text)  # ظروف السوق

    # الوثائق
    report_file_path = db.Column(db.String(500))  # مسار تقرير التقييم

    # ملاحظات
    notes = db.Column(db.Text)

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<PropertyValuation {self.property_id} - {self.market_value}>'

    def calculate_rental_yield(self):
        """حساب العائد الإيجاري"""
        if self.market_value and self.rental_value_annual:
            return (self.rental_value_annual / self.market_value) * 100
        return 0


class PropertyMaintenance(db.Model):
    """نموذج صيانة العقارات"""
    __tablename__ = 'property_maintenance'

    id = db.Column(db.Integer, primary_key=True)
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'), nullable=False, index=True)

    # تفاصيل الصيانة
    maintenance_type = db.Column(db.String(100), nullable=False)  # وقائية، إصلاحية، طارئة
    category = db.Column(db.String(100))  # كهرباء، سباكة، تكييف، عامة
    description = db.Column(db.Text, nullable=False)
    priority = db.Column(db.String(50), default='متوسط')  # منخفض، متوسط، عالي، طارئ

    # التواريخ
    request_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    scheduled_date = db.Column(db.DateTime)
    start_date = db.Column(db.DateTime)
    completion_date = db.Column(db.DateTime)

    # التكلفة
    estimated_cost = db.Column(db.Float)
    actual_cost = db.Column(db.Float)

    # مقدم الخدمة
    service_provider = db.Column(db.String(200))
    technician_name = db.Column(db.String(150))
    technician_phone = db.Column(db.String(20))

    # الحالة
    status = db.Column(db.String(50), default='مطلوب')  # مطلوب، مجدول، قيد التنفيذ، مكتمل، ملغي

    # ملاحظات
    notes = db.Column(db.Text)
    completion_notes = db.Column(db.Text)

    # تواريخ النظام
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    assigned_to = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<PropertyMaintenance {self.maintenance_type} - {self.property_id}>'

    def is_overdue(self):
        """فحص تأخر الصيانة"""
        from datetime import datetime
        return (self.scheduled_date and
                self.scheduled_date < datetime.now() and
                self.status not in ['مكتمل', 'ملغي'])

    def days_overdue(self):
        """عدد أيام التأخير"""
        from datetime import datetime
        if self.is_overdue():
            return (datetime.now() - self.scheduled_date).days
        return 0

# ==================== النماذج المساعدة للعقارات ====================

class PropertyImage(db.Model):
    """نموذج صور العقارات"""
    __tablename__ = 'property_images'

    id = db.Column(db.Integer, primary_key=True)
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'), nullable=False, index=True)

    # تفاصيل الصورة
    image_name = db.Column(db.String(200), nullable=False)
    image_path = db.Column(db.String(500), nullable=False)
    image_type = db.Column(db.String(50))  # jpg, png, gif
    image_size = db.Column(db.Integer)  # بالبايت

    # معلومات إضافية
    description = db.Column(db.Text)
    is_main_image = db.Column(db.Boolean, default=False)  # الصورة الرئيسية
    display_order = db.Column(db.Integer, default=0)  # ترتيب العرض

    # تواريخ النظام
    upload_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<PropertyImage {self.image_name}>'


class PropertyDocument(db.Model):
    """نموذج وثائق العقارات"""
    __tablename__ = 'property_documents'

    id = db.Column(db.Integer, primary_key=True)
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'), nullable=False, index=True)

    # تفاصيل الوثيقة
    document_name = db.Column(db.String(200), nullable=False)
    document_type = db.Column(db.String(100))  # سند ملكية، رخصة بناء، مخطط، أخرى
    file_path = db.Column(db.String(500))
    file_size = db.Column(db.Integer)
    file_type = db.Column(db.String(50))  # pdf, jpg, png, doc

    # معلومات إضافية
    description = db.Column(db.Text)
    is_required = db.Column(db.Boolean, default=False)
    expiry_date = db.Column(db.DateTime)  # تاريخ انتهاء الوثيقة

    # تواريخ النظام
    upload_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<PropertyDocument {self.document_name}>'

    def is_expired(self):
        """فحص انتهاء صلاحية الوثيقة"""
        from datetime import datetime
        return self.expiry_date and self.expiry_date < datetime.now()


class TenantDocument(db.Model):
    """نموذج وثائق المستأجرين"""
    __tablename__ = 'tenant_documents'

    id = db.Column(db.Integer, primary_key=True)
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenants.id'), nullable=False, index=True)

    # تفاصيل الوثيقة
    document_name = db.Column(db.String(200), nullable=False)
    document_type = db.Column(db.String(100))  # هوية، جواز سفر، كشف راتب، ضمان، أخرى
    file_path = db.Column(db.String(500))
    file_size = db.Column(db.Integer)
    file_type = db.Column(db.String(50))  # pdf, jpg, png, doc

    # معلومات إضافية
    description = db.Column(db.Text)
    is_required = db.Column(db.Boolean, default=False)
    is_verified = db.Column(db.Boolean, default=False)
    verified_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    verified_date = db.Column(db.DateTime)
    expiry_date = db.Column(db.DateTime)  # تاريخ انتهاء الوثيقة

    # تواريخ النظام
    upload_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<TenantDocument {self.document_name}>'

    def is_expired(self):
        """فحص انتهاء صلاحية الوثيقة"""
        from datetime import datetime
        return self.expiry_date and self.expiry_date < datetime.now()


class PaymentRecord(db.Model):
    """نموذج سجل المدفوعات للمستأجرين"""
    __tablename__ = 'payment_records'

    id = db.Column(db.Integer, primary_key=True)
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenants.id'), nullable=False, index=True)
    lease_id = db.Column(db.Integer, db.ForeignKey('leases.id'), index=True)

    # تفاصيل الدفع
    payment_date = db.Column(db.DateTime, nullable=False, index=True)
    amount = db.Column(db.Float, nullable=False)
    payment_type = db.Column(db.String(50), default='إيجار')  # إيجار، تأمين، عمولة، غرامة
    payment_method = db.Column(db.String(50), default='نقداً')  # نقداً، شيك، تحويل بنكي
    payment_reference = db.Column(db.String(100))  # رقم الشيك أو التحويل

    # تفاصيل إضافية
    payment_for_month = db.Column(db.Integer)  # الشهر المدفوع عنه
    payment_for_year = db.Column(db.Integer)  # السنة المدفوعة عنها
    late_fee = db.Column(db.Float, default=0.0)  # غرامة التأخير
    discount = db.Column(db.Float, default=0.0)  # خصم
    net_amount = db.Column(db.Float)  # المبلغ الصافي

    # الحالة
    status = db.Column(db.String(50), default='مدفوع')  # مدفوع، ملغي، مرتجع

    # ملاحظات
    notes = db.Column(db.Text)
    receipt_number = db.Column(db.String(100))  # رقم الإيصال

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    lease = db.relationship('Lease', backref='payment_records')

    def __repr__(self):
        return f'<PaymentRecord {self.amount} - {self.payment_date}>'

    def calculate_net_amount(self):
        """حساب المبلغ الصافي"""
        self.net_amount = self.amount + (self.late_fee or 0) - (self.discount or 0)


class TenantCommunication(db.Model):
    """نموذج مراسلات المستأجرين"""
    __tablename__ = 'tenant_communications'

    id = db.Column(db.Integer, primary_key=True)
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenants.id'), nullable=False, index=True)

    # تفاصيل المراسلة
    communication_type = db.Column(db.String(50), nullable=False)  # مكالمة، رسالة، بريد إلكتروني، زيارة
    subject = db.Column(db.String(200))
    content = db.Column(db.Text, nullable=False)
    direction = db.Column(db.String(20), default='صادر')  # صادر، وارد

    # التواريخ
    communication_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    follow_up_date = db.Column(db.DateTime)  # تاريخ المتابعة

    # الحالة
    status = db.Column(db.String(50), default='مكتمل')  # مكتمل، يحتاج متابعة، ملغي
    priority = db.Column(db.String(50), default='متوسط')  # عالي، متوسط، منخفض

    # ملاحظات
    notes = db.Column(db.Text)

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<TenantCommunication {self.communication_type} - {self.subject}>'

    def needs_follow_up(self):
        """فحص الحاجة للمتابعة"""
        from datetime import datetime
        return (self.follow_up_date and
                self.follow_up_date <= datetime.now() and
                self.status == 'يحتاج متابعة')


# تم حذف النماذج القديمة - تم استبدالها بالنماذج الجديدة أعلاه

class PropertyInspection(db.Model):
    """نموذج معاينة العقارات"""
    __tablename__ = 'property_inspections'

    id = db.Column(db.Integer, primary_key=True)
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'), nullable=False, index=True)
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenants.id'), index=True)
    lease_id = db.Column(db.Integer, db.ForeignKey('leases.id'), index=True)

    # تفاصيل المعاينة
    inspection_type = db.Column(db.String(50), nullable=False)  # دخول، خروج، دورية، طارئة
    inspection_date = db.Column(db.DateTime, nullable=False)
    inspector_name = db.Column(db.String(150))

    # حالة العقار
    overall_condition = db.Column(db.String(50))  # ممتاز، جيد، متوسط، يحتاج إصلاح
    cleanliness_rating = db.Column(db.Integer, default=5)  # من 1-10

    # تفاصيل الفحص
    rooms_condition = db.Column(db.Text)  # حالة الغرف
    kitchen_condition = db.Column(db.Text)  # حالة المطبخ
    bathroom_condition = db.Column(db.Text)  # حالة الحمامات
    electrical_condition = db.Column(db.Text)  # حالة الكهرباء
    plumbing_condition = db.Column(db.Text)  # حالة السباكة

    # المشاكل والأضرار
    damages_found = db.Column(db.Text)  # الأضرار الموجودة
    repairs_needed = db.Column(db.Text)  # الإصلاحات المطلوبة
    estimated_repair_cost = db.Column(db.Float, default=0.0)  # تكلفة الإصلاح المقدرة

    # الملاحظات
    notes = db.Column(db.Text)
    recommendations = db.Column(db.Text)  # التوصيات

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    property = db.relationship('Property', backref='inspections')
    tenant = db.relationship('Tenant', backref='inspections')
    lease = db.relationship('Lease', backref='inspections')

    def __repr__(self):
        return f'<PropertyInspection {self.inspection_type} - {self.inspection_date}>'


class PropertyAlert(db.Model):
    """نموذج تنبيهات العقارات"""
    __tablename__ = 'property_alerts'

    id = db.Column(db.Integer, primary_key=True)
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'), index=True)
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenants.id'), index=True)
    lease_id = db.Column(db.Integer, db.ForeignKey('leases.id'), index=True)

    # تفاصيل التنبيه
    alert_type = db.Column(db.String(50), nullable=False)  # انتهاء عقد، استحقاق دفع، صيانة، أخرى
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    priority = db.Column(db.String(20), default='متوسط')  # عالي، متوسط، منخفض

    # التواريخ
    alert_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    due_date = db.Column(db.DateTime)  # تاريخ الاستحقاق
    reminder_date = db.Column(db.DateTime)  # تاريخ التذكير

    # الحالة
    status = db.Column(db.String(20), default='نشط')  # نشط، مقروء، مكتمل، ملغي
    is_read = db.Column(db.Boolean, default=False)
    is_completed = db.Column(db.Boolean, default=False)

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    completed_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    completed_date = db.Column(db.DateTime)

    # العلاقات
    property = db.relationship('Property', backref='alerts')
    tenant = db.relationship('Tenant', backref='alerts')
    lease = db.relationship('Lease', backref='alerts')

    def __repr__(self):
        return f'<PropertyAlert {self.alert_type} - {self.title}>'

    def is_overdue(self):
        """فحص تأخر التنبيه"""
        from datetime import datetime
        return self.due_date and self.due_date < datetime.now() and not self.is_completed

    def mark_as_read(self):
        """تسجيل قراءة التنبيه"""
        self.is_read = True
        if self.status == 'نشط':
            self.status = 'مقروء'

    def mark_as_completed(self, completed_by_user_id):
        """تسجيل إكمال التنبيه"""
        from datetime import datetime
        self.is_completed = True
        self.status = 'مكتمل'
        self.completed_by = completed_by_user_id
        self.completed_date = datetime.now()



class AuditLog(db.Model):
    """نموذج سجل المراجعة"""
    __tablename__ = 'audit_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), index=True)

    # تفاصيل العملية
    action = db.Column(db.String(50), nullable=False)  # create, update, delete, view
    table_name = db.Column(db.String(50), nullable=False)  # اسم الجدول
    record_id = db.Column(db.Integer)  # معرف السجل

    # البيانات
    old_values = db.Column(db.Text)  # القيم القديمة (JSON)
    new_values = db.Column(db.Text)  # القيم الجديدة (JSON)

    # معلومات إضافية
    ip_address = db.Column(db.String(45))  # عنوان IP
    user_agent = db.Column(db.Text)  # معلومات المتصفح
    description = db.Column(db.Text)  # وصف العملية

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp(), index=True)

    # العلاقات
    user = db.relationship('User', backref='audit_logs')

    def __repr__(self):
        return f'<AuditLog {self.action} - {self.table_name}>'

    @staticmethod
    def log_action(user_id, action, table_name, record_id=None, old_values=None, new_values=None, description=None):
        """تسجيل عملية في سجل المراجعة"""
        import json
        from flask import request

        log_entry = AuditLog(
            user_id=user_id,
            action=action,
            table_name=table_name,
            record_id=record_id,
            old_values=json.dumps(old_values) if old_values else None,
            new_values=json.dumps(new_values) if new_values else None,
            description=description,
            ip_address=request.remote_addr if request else None,
            user_agent=request.headers.get('User-Agent') if request else None
        )

        db.session.add(log_entry)
        return log_entry


class FinancialTransaction(db.Model):
    __tablename__ = 'financial_transactions'

    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Float, nullable=False)
    date = db.Column(db.DateTime, nullable=False)
    type = db.Column(db.String(50), nullable=False)  # قبض/صرف/رسوم/دفعة/أتعاب/تحصيل إيجار...
    description = db.Column(db.Text)
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'))
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenants.id'))
    currency = db.Column(db.String(20), nullable=False)  # العملة
    case = db.relationship('Case', back_populates='financial_transactions')
    client = db.relationship('Client', backref='financial_transactions', lazy=True)
    property = db.relationship('Property', backref='financial_transactions', lazy=True)
    tenant = db.relationship('Tenant', backref='financial_transactions', lazy=True)

# نموذج إدارة الأتعاب المحسن
class Fee(db.Model):
    __tablename__ = 'fees'

    id = db.Column(db.Integer, primary_key=True)
    fee_number = db.Column(db.String(50), unique=True, nullable=False)
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'), nullable=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)

    # تفاصيل الخدمة
    fee_type = db.Column(db.String(100), nullable=False)  # استشارة، ترافع، صياغة عقد، إلخ
    service_category = db.Column(db.String(100), nullable=False)  # قانون مدني، جنائي، تجاري، إلخ
    description = db.Column(db.Text, nullable=False)
    service_date = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())
    due_date = db.Column(db.DateTime, nullable=False)

    # المبالغ المالية
    base_amount = db.Column(db.Float, nullable=False)  # المبلغ الأساسي
    additional_fees = db.Column(db.Float, default=0.0)  # رسوم إضافية
    discount_amount = db.Column(db.Float, default=0.0)  # خصم
    tax_amount = db.Column(db.Float, default=0.0)  # ضريبة
    total_amount = db.Column(db.Float, nullable=False)  # المجموع الإجمالي

    # حالة الدفع
    payment_status = db.Column(db.String(50), default='غير مدفوع')  # غير مدفوع، مدفوع جزئياً، مدفوع كاملاً
    paid_amount = db.Column(db.Float, default=0.0)
    remaining_amount = db.Column(db.Float, nullable=False)

    # معلومات الدفع
    payment_method = db.Column(db.String(50))  # نقداً، شيك، تحويل بنكي
    payment_reference = db.Column(db.String(100))  # رقم الشيك أو التحويل
    payment_terms = db.Column(db.String(200))  # شروط الدفع

    # معلومات إضافية
    priority = db.Column(db.String(50), default='متوسطة')  # عالية، متوسطة، منخفضة
    currency = db.Column(db.String(20), default='شيكل')
    notes = db.Column(db.Text)
    is_recurring = db.Column(db.Boolean, default=False)  # أتعاب دورية
    recurring_period = db.Column(db.String(50))  # شهري، ربع سنوي، سنوي

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_date = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    # العلاقات
    case = db.relationship('Case', backref='enhanced_fees')
    client = db.relationship('Client', backref='enhanced_fees')
    payments = db.relationship('FeePayment', backref='fee', lazy='dynamic', cascade='all, delete-orphan')

    def __init__(self, **kwargs):
        super(Fee, self).__init__(**kwargs)
        if not self.fee_number:
            self.fee_number = self.generate_fee_number()
        self.calculate_totals()

    def generate_fee_number(self):
        """توليد رقم الأتعاب تلقائياً"""
        from datetime import datetime
        year = datetime.now().year
        count = Fee.query.filter(Fee.fee_number.like(f'FEE-{year}-%')).count() + 1
        return f'FEE-{year}-{count:04d}'

    def calculate_totals(self):
        """حساب المجاميع"""
        self.total_amount = (self.base_amount or 0) + (self.additional_fees or 0) - (self.discount_amount or 0) + (self.tax_amount or 0)
        self.remaining_amount = self.total_amount - (self.paid_amount or 0)

        # تحديث حالة الدفع
        if self.paid_amount == 0:
            self.payment_status = 'غير مدفوع'
        elif self.paid_amount >= self.total_amount:
            self.payment_status = 'مدفوع كاملاً'
        else:
            self.payment_status = 'مدفوع جزئياً'

    def add_payment(self, amount, payment_method='نقداً', reference=None, notes=None):
        """إضافة دفعة جديدة"""
        payment = FeePayment(
            fee_id=self.id,
            amount=amount,
            payment_method=payment_method,
            reference=reference,
            notes=notes
        )
        db.session.add(payment)

        # تحديث المبلغ المدفوع
        self.paid_amount = (self.paid_amount or 0) + amount
        self.calculate_totals()

        return payment

    def is_overdue(self):
        """التحقق من تأخر الدفع"""
        from datetime import datetime
        return self.due_date < datetime.now() and self.remaining_amount > 0

    def days_overdue(self):
        """عدد أيام التأخير"""
        if self.is_overdue():
            from datetime import datetime
            return (datetime.now() - self.due_date).days
        return 0

    def calculate_remaining(self):
        """حساب المبلغ المتبقي - للتوافق مع النموذج القديم"""
        self.calculate_totals()

    def __repr__(self):
        return f'<Fee {self.fee_number}>'

# نموذج دفعات الأتعاب
class FeePayment(db.Model):
    __tablename__ = 'fee_payments'

    id = db.Column(db.Integer, primary_key=True)
    fee_id = db.Column(db.Integer, db.ForeignKey('fees.id'), nullable=False)
    payment_date = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(50), nullable=False, default='نقداً')
    reference = db.Column(db.String(100))  # رقم الشيك أو التحويل
    notes = db.Column(db.Text)
    receipt_number = db.Column(db.String(50))

    # تواريخ النظام
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_date = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    def __init__(self, **kwargs):
        super(FeePayment, self).__init__(**kwargs)
        if not self.receipt_number:
            self.receipt_number = self.generate_receipt_number()

    def generate_receipt_number(self):
        """توليد رقم الإيصال تلقائياً"""
        from datetime import datetime
        year = datetime.now().year
        count = FeePayment.query.filter(FeePayment.receipt_number.like(f'REC-{year}-%')).count() + 1
        return f'REC-{year}-{count:04d}'

    def __repr__(self):
        return f'<FeePayment {self.receipt_number}>'

# نموذج إدارة الرسوم القضائية
class CourtFee(db.Model):
    __tablename__ = 'court_fees'

    id = db.Column(db.Integer, primary_key=True)
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'), nullable=False)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)  # مبلغ الرسوم
    paid_amount = db.Column(db.Float, default=0.0)  # المبلغ المدفوع
    remaining_amount = db.Column(db.Float, default=0.0)  # المبلغ المتبقي
    fee_type = db.Column(db.String(50), default='رسوم قضائية')  # نوع الرسوم
    payment_method = db.Column(db.String(50))  # طريقة الدفع
    due_date = db.Column(db.DateTime)  # تاريخ الاستحقاق
    status = db.Column(db.String(50), default='مستحقة')  # الحالة
    notes = db.Column(db.Text)  # ملاحظات
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())

    case = db.relationship('Case', backref='court_fees', lazy=True)
    client = db.relationship('Client', backref='court_fees', lazy=True)

    def calculate_remaining(self):
        """حساب المبلغ المتبقي"""
        self.remaining_amount = self.amount - self.paid_amount

# نموذج إدارة الديون المفصل
class Debt(db.Model):
    __tablename__ = 'debts'

    id = db.Column(db.Integer, primary_key=True)
    creditor_name = db.Column(db.String(150), nullable=False)  # اسم الدائن
    amount = db.Column(db.Float, nullable=False)  # مبلغ الدين
    paid_amount = db.Column(db.Float, default=0.0)  # المبلغ المدفوع
    remaining_amount = db.Column(db.Float, default=0.0)  # المبلغ المتبقي
    debt_type = db.Column(db.String(50), nullable=False)  # نوع الدين
    description = db.Column(db.Text)  # وصف الدين
    due_date = db.Column(db.DateTime)  # تاريخ الاستحقاق
    status = db.Column(db.String(50), default='مستحق')  # الحالة
    priority = db.Column(db.String(50), default='متوسطة')  # الأولوية
    currency = db.Column(db.String(20), default='شيكل')  # العملة
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())

    # ربط اختياري بالقضية أو العميل
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))

    case = db.relationship('Case', backref='debts', lazy=True)
    client = db.relationship('Client', backref='debts', lazy=True)
    payments = db.relationship('DebtPayment', backref='debt', lazy=True)

    def calculate_remaining(self):
        """حساب المبلغ المتبقي"""
        self.remaining_amount = self.amount - self.paid_amount

    def update_status(self):
        """تحديث حالة الدين"""
        if self.remaining_amount <= 0:
            self.status = 'مسدد'
        elif self.due_date and self.due_date < db.func.current_timestamp():
            self.status = 'متأخر'
        else:
            self.status = 'مستحق'

# نموذج دفعات الديون
class DebtPayment(db.Model):
    __tablename__ = 'debt_payments'

    id = db.Column(db.Integer, primary_key=True)
    debt_id = db.Column(db.Integer, db.ForeignKey('debts.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)  # مبلغ الدفعة
    payment_date = db.Column(db.DateTime, default=db.func.current_timestamp())  # تاريخ الدفع
    payment_method = db.Column(db.String(50))  # طريقة الدفع
    notes = db.Column(db.Text)  # ملاحظات
    currency = db.Column(db.String(20), default='شيكل')  # العملة

# نموذج إيرادات الإيجارات المفصل
class RentalIncome(db.Model):
    __tablename__ = 'rental_incomes'

    id = db.Column(db.Integer, primary_key=True)
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'), nullable=False)
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenants.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)  # مبلغ الإيجار
    month = db.Column(db.Integer, nullable=False)  # الشهر
    year = db.Column(db.Integer, nullable=False)  # السنة
    due_date = db.Column(db.DateTime, nullable=False)  # تاريخ الاستحقاق
    paid_date = db.Column(db.DateTime)  # تاريخ الدفع
    status = db.Column(db.String(50), default='مستحق')  # الحالة
    payment_method = db.Column(db.String(50))  # طريقة الدفع
    late_fee = db.Column(db.Float, default=0.0)  # غرامة التأخير
    discount = db.Column(db.Float, default=0.0)  # خصم
    net_amount = db.Column(db.Float)  # المبلغ الصافي
    currency = db.Column(db.String(20), default='شيكل')  # العملة
    notes = db.Column(db.Text)  # ملاحظات
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())

    property = db.relationship('Property', backref='rental_incomes', lazy=True)
    tenant = db.relationship('Tenant', backref='rental_incomes', lazy=True)

    def calculate_net_amount(self):
        """حساب المبلغ الصافي"""
        self.net_amount = self.amount + self.late_fee - self.discount

    def update_status(self):
        """تحديث حالة الإيجار"""
        if self.paid_date:
            self.status = 'مدفوع'
        elif self.due_date < db.func.current_timestamp():
            self.status = 'متأخر'
        else:
            self.status = 'مستحق'

# نموذج المصروفات المفصل
class Expense(db.Model):
    __tablename__ = 'expenses'

    id = db.Column(db.Integer, primary_key=True)
    category = db.Column(db.String(100), nullable=False)  # فئة المصروف
    subcategory = db.Column(db.String(100))  # فئة فرعية
    amount = db.Column(db.Float, nullable=False)  # المبلغ
    description = db.Column(db.Text, nullable=False)  # الوصف
    expense_date = db.Column(db.DateTime, default=db.func.current_timestamp())  # تاريخ المصروف
    payment_method = db.Column(db.String(50))  # طريقة الدفع
    vendor = db.Column(db.String(150))  # المورد
    receipt_number = db.Column(db.String(100))  # رقم الإيصال
    currency = db.Column(db.String(20), default='شيكل')  # العملة
    is_recurring = db.Column(db.Boolean, default=False)  # مصروف متكرر
    recurring_period = db.Column(db.String(50))  # فترة التكرار
    notes = db.Column(db.Text)  # ملاحظات
    created_date = db.Column(db.DateTime, default=db.func.current_timestamp())

    # ربط اختياري بالقضية أو العقار
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))
    property_id = db.Column(db.Integer, db.ForeignKey('properties.id'))

    case = db.relationship('Case', backref='expenses', lazy=True)
    property = db.relationship('Property', backref='expenses', lazy=True)

class ClientDocument(db.Model):
    __tablename__ = 'client_documents'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(200), nullable=False)
    upload_date = db.Column(db.DateTime)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))


# تم نقل نماذج الإعدادات إلى archive_models.py لتجنب التضارب


# تم نقل نماذج النسخ الاحتياطي إلى archive_models.py لتجنب التضارب
