#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import re
from bs4 import BeautifulSoup

BASE_URL = "http://localhost:5000"

def test_modal_csrf_integration():
    """اختبار تكامل CSRF في الـ modals"""
    print("🧪 اختبار تكامل CSRF في الـ modals...")
    
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {
        'username': 'office',
        'password': '66889088'
    }
    
    login_response = session.post(f"{BASE_URL}/lawyersameh", data=login_data)
    if login_response.status_code != 200:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # الحصول على صفحة العملاء
    clients_response = session.get(f"{BASE_URL}/clients")
    if clients_response.status_code != 200:
        print("❌ فشل في الوصول لصفحة العملاء")
        return False
    
    print("✅ تم الوصول لصفحة العملاء")
    
    # البحث عن CSRF token في الصفحة
    soup = BeautifulSoup(clients_response.text, 'html.parser')
    
    # البحث عن modal add_client
    modal_found = False
    if 'add_client.html' in clients_response.text or 'addClientModal' in clients_response.text:
        modal_found = True
        print("✅ تم العثور على modal إضافة العميل")
    
    # البحث عن CSRF token في الصفحة
    csrf_tokens = soup.find_all('input', {'name': 'csrf_token'})
    csrf_meta = soup.find('meta', {'name': 'csrf-token'})
    
    print(f"📊 عدد CSRF tokens الموجودة: {len(csrf_tokens)}")
    if csrf_meta:
        print("✅ تم العثور على CSRF meta tag")
    
    # اختبار إضافة عميل مع CSRF صحيح
    if csrf_tokens:
        csrf_token = csrf_tokens[0].get('value')
        print(f"✅ تم العثور على CSRF token: {csrf_token[:20]}...")
        
        client_data = {
            'name': 'عميل تجريبي من Modal',
            'phone': '0501234568',
            'email': '<EMAIL>',
            'address': 'عنوان تجريبي من Modal',
            'role': 'موكل',
            'csrf_token': csrf_token
        }
        
        print("🔄 محاولة إضافة عميل من خلال Modal...")
        add_response = session.post(f"{BASE_URL}/clients/add", data=client_data)
        
        print(f"📊 رمز الاستجابة: {add_response.status_code}")
        
        if add_response.status_code == 200:
            print("✅ تم إضافة العميل بنجاح من خلال Modal!")
            return True
        elif add_response.status_code == 302:
            print("✅ تم إضافة العميل بنجاح (تم التوجيه)!")
            return True
        else:
            print(f"❌ فشل في إضافة العميل: {add_response.status_code}")
            if "CSRF" in add_response.text:
                print("❌ مشكلة في CSRF token")
            return False
    else:
        print("❌ لم يتم العثور على CSRF token")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار تكامل Modal CSRF...")
    print("=" * 60)
    
    success = test_modal_csrf_integration()
    
    print("=" * 60)
    if success:
        print("🎉 الاختبار نجح! Modal CSRF يعمل بشكل صحيح")
    else:
        print("💥 الاختبار فشل! هناك مشكلة في Modal CSRF")
