// Calendar and Task Management JavaScript

let currentDate = new Date();
let currentView = 'month';
let events = [];
let tasks = [];

// Initialize calendar when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadCalendarData();
    renderCalendar();
    loadTodayTasks();
    loadUpcomingEvents();
    
    // Setup form handlers
    setupEventForm();
    setupTaskForm();
    
    // Setup filter handlers
    setupFilters();
});

// Load calendar data from server
function loadCalendarData() {
    fetch('/api/calendar/events')
        .then(response => response.json())
        .then(data => {
            events = data.events || [];
            renderCalendar();
        })
        .catch(error => console.error('Error loading events:', error));
        
    fetch('/api/calendar/tasks')
        .then(response => response.json())
        .then(data => {
            tasks = data.tasks || [];
            renderCalendar();
        })
        .catch(error => console.error('Error loading tasks:', error));
}

// Render calendar based on current view
function renderCalendar() {
    const container = document.getElementById('calendar-content');
    const title = document.getElementById('calendar-title');
    
    switch(currentView) {
        case 'month':
            renderMonthView(container, title);
            break;
        case 'week':
            renderWeekView(container, title);
            break;
        case 'day':
            renderDayView(container, title);
            break;
    }
}

// Render month view
function renderMonthView(container, title) {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // Set title
    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    title.textContent = `${monthNames[month]} ${year}`;
    
    // Create calendar grid
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    let html = '<table class="table table-bordered calendar-table">';
    html += '<thead><tr>';
    const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    dayNames.forEach(day => {
        html += `<th class="text-center">${day}</th>`;
    });
    html += '</tr></thead><tbody>';
    
    const currentDateObj = new Date(startDate);
    for (let week = 0; week < 6; week++) {
        html += '<tr>';
        for (let day = 0; day < 7; day++) {
            const isCurrentMonth = currentDateObj.getMonth() === month;
            const isToday = isDateToday(currentDateObj);
            const dayEvents = getEventsForDate(currentDateObj);
            const dayTasks = getTasksForDate(currentDateObj);
            
            html += `<td class="calendar-day ${isCurrentMonth ? '' : 'other-month'} ${isToday ? 'today' : ''}" 
                     data-date="${formatDate(currentDateObj)}">`;
            html += `<div class="day-number">${currentDateObj.getDate()}</div>`;
            
            // Add events
            dayEvents.forEach(event => {
                html += `<div class="calendar-event" style="background-color: ${event.color || '#007bff'}" 
                         onclick="showEventDetails(${event.id})">${event.title}</div>`;
            });
            
            // Add tasks
            dayTasks.forEach(task => {
                const priorityClass = getPriorityClass(task.priority);
                html += `<div class="calendar-task ${priorityClass}" 
                         onclick="showTaskDetails(${task.id})">${task.title}</div>`;
            });
            
            html += '</td>';
            currentDateObj.setDate(currentDateObj.getDate() + 1);
        }
        html += '</tr>';
        
        // Break if we've shown all days of the month and we're past it
        if (currentDateObj.getMonth() !== month && week >= 4) break;
    }
    
    html += '</tbody></table>';
    container.innerHTML = html;
}

// Render week view
function renderWeekView(container, title) {
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
    
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    
    title.textContent = `${formatDate(startOfWeek)} - ${formatDate(endOfWeek)}`;
    
    let html = '<div class="week-view">';
    html += '<div class="time-column"><div class="hour-slot"></div>';
    
    // Time slots
    for (let hour = 0; hour < 24; hour++) {
        html += `<div class="hour-slot">${hour.toString().padStart(2, '0')}:00</div>`;
    }
    html += '</div>';
    
    // Day columns
    const currentDateObj = new Date(startOfWeek);
    for (let day = 0; day < 7; day++) {
        const dayEvents = getEventsForDate(currentDateObj);
        const dayTasks = getTasksForDate(currentDateObj);
        
        html += `<div class="day-column">`;
        html += `<div class="day-header">${formatDate(currentDateObj)}</div>`;
        
        // Hour slots for this day
        for (let hour = 0; hour < 24; hour++) {
            html += `<div class="hour-slot" data-date="${formatDate(currentDateObj)}" data-hour="${hour}">`;
            
            // Add events for this hour
            dayEvents.forEach(event => {
                const eventHour = new Date(event.start_datetime).getHours();
                if (eventHour === hour) {
                    html += `<div class="week-event" style="background-color: ${event.color || '#007bff'}" 
                             onclick="showEventDetails(${event.id})">${event.title}</div>`;
                }
            });
            
            html += '</div>';
        }
        
        html += '</div>';
        currentDateObj.setDate(currentDateObj.getDate() + 1);
    }
    
    html += '</div>';
    container.innerHTML = html;
}

// Render day view
function renderDayView(container, title) {
    title.textContent = formatDate(currentDate);
    
    const dayEvents = getEventsForDate(currentDate);
    const dayTasks = getTasksForDate(currentDate);
    
    let html = '<div class="day-view">';
    html += '<div class="row">';
    
    // Events column
    html += '<div class="col-md-6">';
    html += '<h5><i class="fas fa-calendar-alt"></i> الأحداث</h5>';
    if (dayEvents.length > 0) {
        dayEvents.forEach(event => {
            html += `<div class="event-item mb-2 p-2 border rounded" style="border-left: 4px solid ${event.color || '#007bff'}">`;
            html += `<h6>${event.title}</h6>`;
            html += `<small class="text-muted">${formatTime(event.start_datetime)} - ${formatTime(event.end_datetime)}</small>`;
            if (event.location) html += `<br><small><i class="fas fa-map-marker-alt"></i> ${event.location}</small>`;
            html += `</div>`;
        });
    } else {
        html += '<p class="text-muted">لا توجد أحداث لهذا اليوم</p>';
    }
    html += '</div>';
    
    // Tasks column
    html += '<div class="col-md-6">';
    html += '<h5><i class="fas fa-tasks"></i> المهام</h5>';
    if (dayTasks.length > 0) {
        dayTasks.forEach(task => {
            const priorityClass = getPriorityClass(task.priority);
            html += `<div class="task-item mb-2 p-2 border rounded ${priorityClass}">`;
            html += `<h6>${task.title}</h6>`;
            if (task.due_date) html += `<small class="text-muted"><i class="fas fa-clock"></i> ${formatTime(task.due_date)}</small>`;
            html += `<div class="progress mt-1" style="height: 5px;">`;
            html += `<div class="progress-bar" style="width: ${task.completion_percentage || 0}%"></div>`;
            html += `</div>`;
            html += `</div>`;
        });
    } else {
        html += '<p class="text-muted">لا توجد مهام لهذا اليوم</p>';
    }
    html += '</div>';
    
    html += '</div></div>';
    container.innerHTML = html;
}

// Helper functions
function isDateToday(date) {
    const today = new Date();
    return date.toDateString() === today.toDateString();
}

function formatDate(date) {
    return date.toLocaleDateString('ar-SA');
}

function formatTime(dateTimeString) {
    return new Date(dateTimeString).toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getEventsForDate(date) {
    return events.filter(event => {
        const eventDate = new Date(event.start_datetime);
        return eventDate.toDateString() === date.toDateString();
    });
}

function getTasksForDate(date) {
    return tasks.filter(task => {
        if (!task.due_date) return false;
        const taskDate = new Date(task.due_date);
        return taskDate.toDateString() === date.toDateString();
    });
}

function getPriorityClass(priority) {
    switch(priority) {
        case 'urgent': return 'priority-urgent';
        case 'high': return 'priority-high';
        case 'medium': return 'priority-medium';
        case 'low': return 'priority-low';
        default: return '';
    }
}

// Navigation functions
function changeView(view) {
    currentView = view;
    document.querySelectorAll('.btn-outline-primary').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    renderCalendar();
}

function navigateCalendar(direction) {
    switch(direction) {
        case 'prev':
            if (currentView === 'month') {
                currentDate.setMonth(currentDate.getMonth() - 1);
            } else if (currentView === 'week') {
                currentDate.setDate(currentDate.getDate() - 7);
            } else {
                currentDate.setDate(currentDate.getDate() - 1);
            }
            break;
        case 'next':
            if (currentView === 'month') {
                currentDate.setMonth(currentDate.getMonth() + 1);
            } else if (currentView === 'week') {
                currentDate.setDate(currentDate.getDate() + 7);
            } else {
                currentDate.setDate(currentDate.getDate() + 1);
            }
            break;
        case 'today':
            currentDate = new Date();
            break;
    }
    renderCalendar();
}

// Form setup functions
function setupEventForm() {
    document.getElementById('addEventForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            title: document.getElementById('eventTitle').value,
            description: document.getElementById('eventDescription').value,
            start_datetime: document.getElementById('eventStartDate').value,
            end_datetime: document.getElementById('eventEndDate').value,
            location: document.getElementById('eventLocation').value,
            event_type: document.getElementById('eventType').value,
            priority: document.getElementById('eventPriority').value,
            client_id: document.getElementById('eventClient').value || null,
            case_id: document.getElementById('eventCase').value || null,
            color: document.getElementById('eventColor').value,
            is_all_day: document.getElementById('eventAllDay').checked,
            is_recurring: document.getElementById('eventRecurring').checked
        };
        
        fetch('/api/calendar/events', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                bootstrap.Modal.getInstance(document.getElementById('addEventModal')).hide();
                loadCalendarData();
                showAlert('تم إضافة الحدث بنجاح', 'success');
            } else {
                showAlert('حدث خطأ أثناء إضافة الحدث', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ أثناء إضافة الحدث', 'error');
        });
    });
}

function setupTaskForm() {
    document.getElementById('addTaskForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            title: document.getElementById('taskTitle').value,
            description: document.getElementById('taskDescription').value,
            due_date: document.getElementById('taskDueDate').value || null,
            priority: document.getElementById('taskPriority').value,
            category: document.getElementById('taskCategory').value,
            client_id: document.getElementById('taskClient').value || null,
            case_id: document.getElementById('taskCase').value || null,
            estimated_hours: parseFloat(document.getElementById('taskEstimatedHours').value) || null,
            assigned_to: document.getElementById('taskAssignedTo').value
        };
        
        fetch('/api/calendar/tasks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                bootstrap.Modal.getInstance(document.getElementById('addTaskModal')).hide();
                loadCalendarData();
                showAlert('تم إضافة المهمة بنجاح', 'success');
            } else {
                showAlert('حدث خطأ أثناء إضافة المهمة', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ أثناء إضافة المهمة', 'error');
        });
    });
}

function setupFilters() {
    ['showEvents', 'showTasks', 'showReminders'].forEach(filterId => {
        document.getElementById(filterId).addEventListener('change', function() {
            renderCalendar();
        });
    });
}

// Load sidebar data
function loadTodayTasks() {
    const today = new Date();
    const todayTasks = getTasksForDate(today);
    
    const container = document.getElementById('today-tasks');
    if (todayTasks.length > 0) {
        let html = '';
        todayTasks.forEach(task => {
            const priorityClass = getPriorityClass(task.priority);
            html += `<div class="task-item-small mb-2 p-2 border rounded ${priorityClass}">`;
            html += `<small class="fw-bold">${task.title}</small>`;
            if (task.due_date) html += `<br><small class="text-muted">${formatTime(task.due_date)}</small>`;
            html += `</div>`;
        });
        container.innerHTML = html;
    } else {
        container.innerHTML = '<small class="text-muted">لا توجد مهام لليوم</small>';
    }
}

function loadUpcomingEvents() {
    const today = new Date();
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 7);
    
    const upcomingEvents = events.filter(event => {
        const eventDate = new Date(event.start_datetime);
        return eventDate >= today && eventDate <= nextWeek;
    }).slice(0, 5);
    
    const container = document.getElementById('upcoming-events');
    if (upcomingEvents.length > 0) {
        let html = '';
        upcomingEvents.forEach(event => {
            html += `<div class="event-item-small mb-2 p-2 border rounded" style="border-left: 3px solid ${event.color || '#007bff'}">`;
            html += `<small class="fw-bold">${event.title}</small>`;
            html += `<br><small class="text-muted">${formatDate(new Date(event.start_datetime))} ${formatTime(event.start_datetime)}</small>`;
            html += `</div>`;
        });
        container.innerHTML = html;
    } else {
        container.innerHTML = '<small class="text-muted">لا توجد أحداث قادمة</small>';
    }
}

// Utility functions
function showAlert(message, type) {
    // Create and show alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
    
    // Auto dismiss after 3 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

function showEventDetails(eventId) {
    // Implementation for showing event details
    console.log('Show event details:', eventId);
}

function showTaskDetails(taskId) {
    // Implementation for showing task details
    console.log('Show task details:', taskId);
}
