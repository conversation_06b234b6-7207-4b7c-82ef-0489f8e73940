{% extends "base.html" %}

{% block title %}تعديل مهمة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-tasks"></i> تعديل مهمة</h1>
                    <p>تعديل بيانات المهمة المحددة</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit text-primary me-2"></i>
                        تعديل بيانات المهمة
                    </h5>
                </div>
                <div class="card-body">
        <form method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <div class="mb-3">
                <label class="form-label">العنوان</label>
                <input type="text" name="title" class="form-control" required value="{{ task.title }}">
            </div>
            <div class="mb-3">
                <label class="form-label">الوصف</label>
                <textarea name="description" class="form-control">{{ task.description }}</textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">تاريخ الاستحقاق</label>
                <input type="datetime-local" name="due_date" class="form-control" value="{{ task.due_date.strftime('%Y-%m-%dT%H:%M') if task.due_date else '' }}">
            </div>
            <div class="mb-3">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="معلقة" {% if task.status=='معلقة' %}selected{% endif %}>معلقة</option>
                    <option value="منجزة" {% if task.status=='منجزة' %}selected{% endif %}>منجزة</option>
                </select>
            </div>
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                            <a href="{{ url_for('tasks_list') }}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-arrow-right me-2"></i>
                                رجوع
                            </a>
                        </div>
                    </div>
                </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
