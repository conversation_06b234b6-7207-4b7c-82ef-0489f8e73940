"""Add financial fields to Case model and create Fee and CourtFee models

Revision ID: ab7c4cc60751
Revises: e8ef8d1130e9
Create Date: 2025-07-02 18:39:48.461525

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ab7c4cc60751'
down_revision = 'e8ef8d1130e9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('client_document',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('filename', sa.String(length=200), nullable=False),
    sa.Column('upload_date', sa.DateTime(), nullable=True),
    sa.Column('client_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['client.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('court_fee',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('case_id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.Integer(), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('paid_amount', sa.Float(), nullable=True),
    sa.Column('remaining_amount', sa.Float(), nullable=True),
    sa.Column('fee_type', sa.String(length=50), nullable=True),
    sa.Column('payment_method', sa.String(length=50), nullable=True),
    sa.Column('due_date', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_date', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['case_id'], ['case.id'], ),
    sa.ForeignKeyConstraint(['client_id'], ['client.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('fee',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('case_id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.Integer(), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('paid_amount', sa.Float(), nullable=True),
    sa.Column('remaining_amount', sa.Float(), nullable=True),
    sa.Column('fee_type', sa.String(length=50), nullable=True),
    sa.Column('payment_method', sa.String(length=50), nullable=True),
    sa.Column('due_date', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_date', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['case_id'], ['case.id'], ),
    sa.ForeignKeyConstraint(['client_id'], ['client.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('case', schema=None) as batch_op:
        batch_op.add_column(sa.Column('fees_total', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('fees_paid', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('fees_remaining', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('court_fees_total', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('court_fees_paid', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('court_fees_remaining', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('priority', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('next_session_date', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('case_value', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('currency', sa.String(length=20), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('case', schema=None) as batch_op:
        batch_op.drop_column('currency')
        batch_op.drop_column('case_value')
        batch_op.drop_column('next_session_date')
        batch_op.drop_column('priority')
        batch_op.drop_column('court_fees_remaining')
        batch_op.drop_column('court_fees_paid')
        batch_op.drop_column('court_fees_total')
        batch_op.drop_column('fees_remaining')
        batch_op.drop_column('fees_paid')
        batch_op.drop_column('fees_total')

    op.drop_table('fee')
    op.drop_table('court_fee')
    op.drop_table('client_document')
    # ### end Alembic commands ###
