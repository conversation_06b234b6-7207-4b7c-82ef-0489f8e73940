/* ملف CSS الرئيسي لنظام إدارة مكتب المحاماة */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --text-color: #495057;
    --bg-color: #ffffff;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* الخطوط العربية */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
    direction: rtl;
    text-align: right;
    background-color: var(--light-color);
    color: var(--text-color);
    line-height: 1.6;
}

/* تحسينات عامة */
.container-fluid {
    padding: 20px;
}

.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 20px;
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    padding: 15px 20px;
    font-weight: bold;
}

.card-body {
    padding: 20px;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    padding: 8px 16px;
    font-weight: 500;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1a252f, #2980b9);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #2ecc71);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #e67e22);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #c0392b);
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.875rem;
}

/* الجداول */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table thead th {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px;
    font-weight: 600;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table tbody td {
    padding: 12px;
    border-color: var(--border-color);
    vertical-align: middle;
}

/* النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    padding: 10px 12px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 5px;
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 12px 16px;
    margin-bottom: 20px;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* الشريط الجانبي */
.sidebar {
    background: linear-gradient(180deg, var(--primary-color), #34495e);
    min-height: 100vh;
    padding: 20px 0;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: #ecf0f1;
    padding: 12px 20px;
    margin: 2px 10px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar .nav-link:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    transform: translateX(-5px);
}

.sidebar .nav-link.active {
    background: var(--secondary-color);
    color: white;
}

/* الشريط العلوي */
.navbar {
    background: white !important;
    box-shadow: var(--shadow);
    padding: 10px 20px;
}

.navbar-brand {
    font-weight: bold;
    color: var(--primary-color) !important;
}

/* البطاقات الإحصائية */
.stats-card {
    background: linear-gradient(135deg, var(--secondary-color), #5dade2);
    color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.stats-card p {
    margin: 0;
    opacity: 0.9;
}

/* الأيقونات */
.icon {
    width: 20px;
    height: 20px;
    margin-left: 8px;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn {
        padding: 6px 12px;
        font-size: 0.875rem;
    }
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .table {
        font-size: 12px;
    }
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--secondary-color);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات إضافية */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }

/* تحسينات النصوص */
.fw-bold { font-weight: bold !important; }
.fw-normal { font-weight: normal !important; }
.fs-small { font-size: 0.875rem !important; }
.fs-large { font-size: 1.125rem !important; }

/* مسافات إضافية */
.mt-4 { margin-top: 1.5rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.p-4 { padding: 1.5rem !important; }
