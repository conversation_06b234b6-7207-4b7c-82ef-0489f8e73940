/**
 * نظام التصميم الاحترافي - مكتب المحاماة
 * Professional Design System - Law Office
 */

/* ===== استيراد الخطوط العربية ===== */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

/* ===== متغيرات الألوان الأساسية ===== */
:root {
  /* الألوان الأساسية */
  --primary-color: #1e3a8a;
  --primary-light: #3b82f6;
  --primary-dark: #1e40af;
  --primary-gradient: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  
  /* الألوان الثانوية */
  --secondary-color: #64748b;
  --secondary-light: #94a3b8;
  --secondary-dark: #475569;
  
  /* ألوان الحالة */
  --success-color: #059669;
  --success-light: #10b981;
  --success-dark: #047857;
  
  --warning-color: #d97706;
  --warning-light: #f59e0b;
  --warning-dark: #b45309;
  
  --danger-color: #dc2626;
  --danger-light: #ef4444;
  --danger-dark: #b91c1c;
  
  --info-color: #0891b2;
  --info-light: #06b6d4;
  --info-dark: #0e7490;
  
  /* ألوان الخلفية */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-dark: #0f172a;
  --bg-card: #ffffff;
  
  /* ألوان النص */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #64748b;
  --text-light: #94a3b8;
  --text-white: #ffffff;
  
  /* ألوان الحدود */
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --border-dark: #cbd5e1;
  
  /* الظلال */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* الخطوط */
  --font-primary: 'Cairo', 'Tajawal', 'Segoe UI', system-ui, sans-serif;
  --font-secondary: 'Tajawal', 'Cairo', 'Segoe UI', system-ui, sans-serif;
  --font-heading: 'Amiri', 'Cairo', serif;
  
  /* أحجام الخطوط */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* المسافات */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* نصف القطر */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* الانتقالات */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* ===== الوضع الليلي ===== */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-card: #1e293b;
  
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-light: #64748b;
  
  --border-color: #334155;
  --border-light: #475569;
  --border-dark: #64748b;
}

/* ===== إعدادات عامة ===== */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  margin: 0;
  padding: 0;
  direction: rtl;
  text-align: right;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== العناوين ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

/* ===== الروابط ===== */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--primary-light);
  text-decoration: underline;
}

/* ===== البطاقات ===== */
.card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  font-weight: 600;
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-md) var(--spacing-lg);
}

/* ===== الأزرار ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: var(--font-primary);
  font-size: var(--text-sm);
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition-fast);
  user-select: none;
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--primary-light);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* أنواع الأزرار */
.btn-primary {
  background: var(--primary-gradient);
  color: var(--text-white);
  border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--secondary-color);
  color: var(--text-white);
  border-color: var(--secondary-color);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--secondary-dark);
  transform: translateY(-1px);
}

.btn-success {
  background: var(--success-color);
  color: var(--text-white);
  border-color: var(--success-color);
}

.btn-success:hover:not(:disabled) {
  background: var(--success-dark);
  transform: translateY(-1px);
}

.btn-warning {
  background: var(--warning-color);
  color: var(--text-white);
  border-color: var(--warning-color);
}

.btn-warning:hover:not(:disabled) {
  background: var(--warning-dark);
  transform: translateY(-1px);
}

.btn-danger {
  background: var(--danger-color);
  color: var(--text-white);
  border-color: var(--danger-color);
}

.btn-danger:hover:not(:disabled) {
  background: var(--danger-dark);
  transform: translateY(-1px);
}

.btn-outline-primary {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover:not(:disabled) {
  background: var(--primary-color);
  color: var(--text-white);
}

/* أحجام الأزرار */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--text-lg);
}

/* ===== النماذج ===== */
.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: 1.5;
  color: var(--text-primary);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

/* ===== الجداول ===== */
.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th,
.table td {
  padding: var(--spacing-md);
  text-align: right;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background: var(--bg-tertiary);
  font-weight: 600;
  color: var(--text-primary);
}

.table tbody tr:hover {
  background: var(--bg-tertiary);
}

/* ===== التنبيهات ===== */
.alert {
  padding: var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
}

.alert-success {
  background: rgba(5, 150, 105, 0.1);
  border-color: var(--success-light);
  color: var(--success-dark);
}

.alert-warning {
  background: rgba(217, 119, 6, 0.1);
  border-color: var(--warning-light);
  color: var(--warning-dark);
}

.alert-danger {
  background: rgba(220, 38, 38, 0.1);
  border-color: var(--danger-light);
  color: var(--danger-dark);
}

.alert-info {
  background: rgba(8, 145, 178, 0.1);
  border-color: var(--info-light);
  color: var(--info-dark);
}

/* ===== الأدوات المساعدة ===== */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.d-flex { display: flex !important; }
.d-block { display: block !important; }
.d-none { display: none !important; }

.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.align-items-center { align-items: center !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }
