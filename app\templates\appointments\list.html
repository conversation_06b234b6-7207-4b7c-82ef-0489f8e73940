{% extends "base.html" %}

{% block title %}قائمة المواعيد{% endblock %}
{% block page_title %}قائمة المواعيد{% endblock %}

{% block content %}
<style>
    /* ===== تصميم صفحة المواعيد الاحترافية ===== */
    .page-header {
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-2xl);
        box-shadow: var(--shadow-md);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .page-header h1 {
        font-size: var(--text-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }

    .search-controls {
        display: flex;
        gap: var(--spacing-md);
        align-items: center;
        flex-wrap: wrap;
        margin-bottom: var(--spacing-xl);
    }

    .search-input {
        max-width: 300px;
        border-radius: var(--radius-lg);
        border: 2px solid var(--border-color);
        padding: var(--spacing-md);
        font-size: var(--font-size-base);
    }

    .search-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
    }

    .appointments-table {
        background: var(--bg-card);
        border-radius: var(--radius-xl);
        overflow: hidden;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
    }

    .table {
        margin: 0;
    }

    .table thead th {
        background: var(--primary-gradient);
        color: var(--text-white);
        font-weight: var(--font-weight-semibold);
        padding: var(--spacing-lg);
        border: none;
        text-align: center;
    }

    .table tbody td {
        padding: var(--spacing-lg);
        vertical-align: middle;
        border-bottom: 1px solid var(--border-color);
        text-align: center;
    }

    .table tbody tr:hover {
        background: var(--bg-hover);
    }

    .appointment-actions {
        display: flex;
        gap: var(--spacing-sm);
        justify-content: center;
    }

    .btn-action {
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-md);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
        transition: all var(--transition-normal);
        border: none;
        cursor: pointer;
    }

    .btn-edit {
        background: var(--warning-color);
        color: var(--text-white);
    }

    .btn-edit:hover {
        background: var(--warning-hover);
        transform: translateY(-1px);
        color: var(--text-white);
    }

    .btn-delete {
        background: var(--danger-color);
        color: var(--text-white);
    }

    .btn-delete:hover {
        background: var(--danger-hover);
        transform: translateY(-1px);
    }

    @media print {
        .no-print { display: none !important; }
        body { background: #fff; }
        .page-header, .search-controls { display: none; }
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: var(--spacing-md);
            text-align: center;
        }

        .search-controls {
            flex-direction: column;
            align-items: stretch;
        }

        .search-input {
            max-width: 100%;
        }

        .appointment-actions {
            flex-direction: column;
        }
    }
</style>

<div class="page-header">
    <h1>
        <i class="fas fa-calendar-days"></i>
        قائمة المواعيد
    </h1>
    <div class="header-actions">
        <a href="{{ url_for('add_appointment') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            إضافة موعد جديد
        </a>
    </div>
</div>

<div class="search-controls no-print">
    <input type="text" id="searchInput" class="form-control search-input" placeholder="ابحث في المواعيد...">
    <button onclick="window.print()" class="btn btn-outline-secondary">
        <i class="fas fa-print"></i> طباعة
    </button>
</div>

<div class="appointments-table">
    <h2 class="mb-4"><i class="fa fa-calendar-days text-secondary"></i> قائمة المواعيد</h2>
    <div class="mb-3 d-flex flex-wrap gap-2 align-items-center no-print">
        <input type="text" id="searchInput" class="form-control" placeholder="ابحث في المواعيد...">
        <button onclick="window.print()" class="btn btn-outline-dark"><i class="fa fa-print"></i> طباعة</button>
        <a href="{{ url_for('add_appointment') }}" class="btn btn-success"><i class="fa fa-plus"></i> إضافة موعد جديد</a>
        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary"><i class="fa fa-arrow-right"></i> رجوع إلى لوحة التحكم</a>
    </div>
    <div class="table-responsive">
    <table id="appointmentsTable" class="table table-bordered table-hover align-middle text-center bg-white shadow-sm">
        <thead class="table-secondary">
            <tr>
                <th>الموضوع</th>
                <th>التاريخ</th>
                <th>المكان</th>
                <th>العميل</th>
                <th>القضية</th>
                <th>ملاحظات</th>
                <th class="no-print">إجراءات</th>
            </tr>
        </thead>
        <tbody>
        {% for appointment in appointments %}
            <tr>
                <td>{{ appointment.subject }}</td>
                <td>{{ appointment.date.strftime('%Y-%m-%d %H:%M') if appointment.date else '' }}</td>
                <td>{{ appointment.location or '' }}</td>
                <td>{{ appointment.client.name if appointment.client else '' }}</td>
                <td>{{ appointment.case.title if appointment.case else '' }}</td>
                <td>{{ appointment.notes or '' }}</td>
                <td class="no-print">
                    <a href="{{ url_for('edit_appointment', appointment_id=appointment.id) }}" class="btn btn-primary btn-sm">تعديل</a>
                    <form action="{{ url_for('delete_appointment', appointment_id=appointment.id) }}" method="post" style="display:inline;">
                        {{ csrf_token() }}
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟');">حذف</button>
                    </form>
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
    </div>
</div>
<script>
// بحث فوري في الجدول
const searchInput = document.getElementById('searchInput');
searchInput.addEventListener('keyup', function() {
    const filter = this.value.trim();
    const rows = document.querySelectorAll('#appointmentsTable tbody tr');
    rows.forEach(row => {
        const text = row.innerText.replace(/\s+/g, ' ');
        row.style.display = text.includes(filter) ? '' : 'none';
    });
});
</script>
{% endblock %}
