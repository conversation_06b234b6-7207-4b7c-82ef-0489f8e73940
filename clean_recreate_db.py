#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime

# إضافة مسار التطبيق
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app import app, db

def clean_recreate_database():
    """إعادة إنشاء قاعدة البيانات بشكل نظيف"""
    
    print("🚀 بدء إعادة إنشاء قاعدة البيانات...")
    print("=" * 60)
    
    with app.app_context():
        try:
            # حذف جميع الجداول
            print("🗑️ حذف جميع الجداول...")
            db.drop_all()
            
            # إنشاء جميع الجداول
            print("🔄 إنشاء جميع الجداول...")
            db.create_all()
            
            print("✅ تم إنشاء هيكل قاعدة البيانات بنجاح")
            
            print("\n" + "=" * 60)
            print("🎉 تم إنشاء قاعدة البيانات بنجاح!")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False

if __name__ == "__main__":
    print("✅ تم تهيئة خدمات النظام بنجاح")
    success = clean_recreate_database()
    
    if success:
        print("\n🎉 تم إنشاء قاعدة البيانات بنجاح!")
    else:
        print("\n💥 فشل في إنشاء قاعدة البيانات")
