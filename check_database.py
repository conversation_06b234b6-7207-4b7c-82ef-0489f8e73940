import sqlite3

def check_database():
    try:
        conn = sqlite3.connect('lawoffice.db')
        cursor = conn.cursor()

        # فحص الجداول الموجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print('الجداول الموجودة:')
        for table in tables:
            print(f'- {table[0]}')

        # فحص بنية جدول القضايا
        print('\nبنية جدول cases:')
        cursor.execute('PRAGMA table_info(cases)')
        columns = cursor.fetchall()
        for col in columns:
            print(f'- {col[1]} ({col[2]})')

        # فحص عدد السجلات
        cursor.execute('SELECT COUNT(*) FROM cases')
        cases_count = cursor.fetchone()[0]
        print(f'\nعدد القضايا: {cases_count}')

        cursor.execute('SELECT COUNT(*) FROM clients')
        clients_count = cursor.fetchone()[0]
        print(f'عدد العملاء: {clients_count}')

        cursor.execute('SELECT COUNT(*) FROM users')
        users_count = cursor.fetchone()[0]
        print(f'عدد المستخدمين: {users_count}')

        # فحص الحقول الجديدة
        print('\nفحص الحقول الجديدة:')
        cursor.execute("SELECT client_role, fees_currency FROM cases LIMIT 5")
        sample_data = cursor.fetchall()
        for row in sample_data:
            print(f'- صفة الموكل: {row[0]}, عملة الأتعاب: {row[1]}')

        conn.close()
        print('\n✅ فحص قاعدة البيانات مكتمل بنجاح')
        return True
    except Exception as e:
        print(f'❌ خطأ في فحص قاعدة البيانات: {str(e)}')
        return False

if __name__ == '__main__':
    check_database()
