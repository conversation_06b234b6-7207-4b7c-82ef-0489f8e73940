{% extends "base.html" %}

{% block title %}إضافة سند مالي{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-money-bill-wave"></i> إضافة سند مالي جديد</h1>
                    <p>إضافة سند مالي جديد للنظام المالي</p>
                </div>
                <div class="page-actions">
                    <a href="{{ url_for('finance_list') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>رجوع
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <div class="row mb-3">
            <div class="col-md-4 mb-2">
                <label class="form-label">نوع السند</label>
                <select name="type" id="typeSelect" class="form-select" required>
                    <option value="">اختر النوع</option>
                    <option value="قبض">سند قبض (قضية)</option>
                    <option value="صرف">سند صرف (قضية)</option>
                    <option value="دفعة">دفعة (قضية)</option>
                    <option value="أتعاب">أتعاب (قضية)</option>
                    <option value="تحصيل إيجار">تحصيل إيجار (عقار)</option>
                </select>
            </div>
            <div class="col-md-4 mb-2">
                <label class="form-label">المبلغ</label>
                <input type="number" step="0.01" name="amount" class="form-control" required>
            </div>
            <div class="col-md-4 mb-2">
                <label class="form-label">التاريخ</label>
                <input type="date" name="date" class="form-control" required>
            </div>
        </div>
        <div class="row mb-3">
            <div class="col-md-6 mb-2" id="caseDiv">
                <label class="form-label">القضية</label>
                <select name="case_id" class="form-select">
                    <option value="">بدون</option>
                    {% for case in cases %}
                        <option value="{{ case.id }}">{{ case.title }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-6 mb-2" id="clientDiv">
                <label class="form-label">العميل</label>
                <select name="client_id" class="form-select">
                    <option value="">بدون</option>
                    {% for client in clients %}
                        <option value="{{ client.id }}">{{ client.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-6 mb-2 hidden" id="propertyDiv">
                <label class="form-label">العقار</label>
                <select name="property_id" class="form-select">
                    <option value="">بدون</option>
                    {% for property in properties %}
                        <option value="{{ property.id }}">{{ property.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-6 mb-2 hidden" id="tenantDiv">
                <label class="form-label">المستأجر</label>
                <select name="tenant_id" class="form-select">
                    <option value="">بدون</option>
                    {% for tenant in tenants %}
                        <option value="{{ tenant.id }}">{{ tenant.name }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
        <div class="row mb-3">
            <div class="col-md-6">
                <label class="form-label">العملة</label>
                <select name="currency" class="form-select">
                    <option value="شيكل">شيكل</option>
                    <option value="دولار">دولار</option>
                    <option value="دينار">دينار</option>
                    <option value="يورو">يورو</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">طريقة الدفع</label>
                <select name="payment_method" class="form-select">
                    <option value="نقدي">نقدي</option>
                    <option value="شيك">شيك</option>
                    <option value="تحويل بنكي">تحويل بنكي</option>
                    <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                </select>
            </div>
        </div>
        <div class="mb-3">
            <label class="form-label">ملاحظات</label>
            <textarea name="description" class="form-control" placeholder="تفاصيل إضافية عن السند المالي"></textarea>
        </div>
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ السند
                                </button>
                                <a href="{{ url_for('finance_list') }}" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
    .hidden { display: none; }
</style>

<script>
// إظهار/إخفاء الحقول حسب نوع السند
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('typeSelect');
    const caseDiv = document.getElementById('caseDiv');
    const clientDiv = document.getElementById('clientDiv');
    const propertyDiv = document.getElementById('propertyDiv');
    const tenantDiv = document.getElementById('tenantDiv');

    if (typeSelect) {
        typeSelect.addEventListener('change', function() {
            if (this.value === 'تحصيل إيجار') {
                if (propertyDiv) propertyDiv.classList.remove('hidden');
                if (tenantDiv) tenantDiv.classList.remove('hidden');
                if (caseDiv) caseDiv.classList.add('hidden');
                if (clientDiv) clientDiv.classList.add('hidden');
            } else {
                if (propertyDiv) propertyDiv.classList.add('hidden');
                if (tenantDiv) tenantDiv.classList.add('hidden');
                if (caseDiv) caseDiv.classList.remove('hidden');
                if (clientDiv) clientDiv.classList.remove('hidden');
            }
        });
    }
});
</script>
{% endblock %}
