/**
 * نظام الإشعارات والتنبيهات - التنسيقات
 * Notifications and Alerts System - Styles
 */

/* حاوية الإشعارات الرئيسية */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    width: 100%;
    pointer-events: none;
}

/* إشعار منبثق */
.notification-toast {
    background: var(--bs-white);
    border: 1px solid var(--bs-border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    pointer-events: auto;
    overflow: hidden;
    position: relative;
}

.notification-toast.show {
    opacity: 1;
    transform: translateX(0);
}

/* أنواع الإشعارات */
.notification-toast.success {
    border-left: 4px solid var(--bs-success);
}

.notification-toast.error {
    border-left: 4px solid var(--bs-danger);
}

.notification-toast.warning {
    border-left: 4px solid var(--bs-warning);
}

.notification-toast.info {
    border-left: 4px solid var(--bs-info);
}

.notification-toast.appointment {
    border-left: 4px solid var(--bs-primary);
}

.notification-toast.task {
    border-left: 4px solid var(--bs-secondary);
}

/* محتوى الإشعار */
.notification-content {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    gap: 12px;
}

.notification-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 14px;
}

.notification-toast.success .notification-icon {
    background: var(--bs-success);
    color: white;
}

.notification-toast.error .notification-icon {
    background: var(--bs-danger);
    color: white;
}

.notification-toast.warning .notification-icon {
    background: var(--bs-warning);
    color: white;
}

.notification-toast.info .notification-icon {
    background: var(--bs-info);
    color: white;
}

.notification-text {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: var(--bs-dark);
}

.notification-message {
    font-size: 13px;
    margin: 0;
    color: var(--bs-secondary);
    line-height: 1.4;
}

.notification-close {
    position: absolute;
    top: 8px;
    left: 8px;
    background: none;
    border: none;
    color: var(--bs-secondary);
    font-size: 12px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: var(--bs-light);
    color: var(--bs-dark);
}

/* أزرار الإجراءات */
.notification-actions {
    padding: 0 15px 15px 15px;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.notification-action {
    font-size: 12px;
    padding: 4px 12px;
    border-radius: 4px;
}

/* لوحة الإشعارات */
.notification-panel {
    position: fixed;
    top: 60px;
    right: 20px;
    width: 400px;
    max-height: 600px;
    background: var(--bs-white);
    border: 1px solid var(--bs-border-color);
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    z-index: 1050;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.notification-panel.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* رأس لوحة الإشعارات */
.notification-panel-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--bs-border-color);
    background: linear-gradient(135deg, var(--bs-primary), var(--bs-info));
    color: white;
    border-radius: 8px 8px 0 0;
}

.notification-panel-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.notification-panel-actions {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.notification-panel-actions .btn {
    font-size: 12px;
    padding: 4px 8px;
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.notification-panel-actions .btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* قائمة الإشعارات */
.notifications-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 15px 20px;
    border-bottom: 1px solid var(--bs-border-color);
    transition: background-color 0.2s ease;
    gap: 12px;
}

.notification-item:hover {
    background: var(--bs-light);
}

.notification-item.unread {
    background: rgba(var(--bs-primary-rgb), 0.05);
    border-right: 3px solid var(--bs-primary);
}

.notification-item .notification-icon {
    margin-top: 2px;
}

.notification-item .notification-content {
    flex: 1;
    padding: 0;
}

.notification-item .notification-title {
    font-size: 14px;
    margin-bottom: 4px;
}

.notification-item .notification-message {
    font-size: 13px;
    margin-bottom: 6px;
}

.notification-time {
    font-size: 11px;
    color: var(--bs-secondary);
}

.notification-item .notification-actions {
    padding: 0;
    flex-direction: column;
    gap: 4px;
}

.notification-item .notification-actions .btn {
    font-size: 11px;
    padding: 2px 6px;
    min-width: auto;
}

/* حالة عدم وجود إشعارات */
.no-notifications {
    text-align: center;
    padding: 40px 20px;
    color: var(--bs-secondary);
}

/* شارة الإشعارات */
.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--bs-danger);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

/* زر الإشعارات */
.notification-toggle {
    position: relative;
    background: none;
    border: none;
    color: var(--bs-secondary);
    font-size: 18px;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.notification-toggle:hover {
    background: var(--bs-light);
    color: var(--bs-primary);
}

/* نافذة إعدادات الإشعارات */
.notification-settings-modal .modal-header {
    background: linear-gradient(135deg, var(--bs-primary), var(--bs-info));
    color: white;
    border-bottom: none;
}

.notification-settings-modal .modal-title {
    font-weight: 600;
}

.notification-settings-form .form-group {
    margin-bottom: 20px;
}

.notification-settings-form .form-label {
    font-weight: 500;
    margin-bottom: 8px;
}

.notification-settings-form .form-check {
    margin-bottom: 10px;
}

.notification-settings-form .form-check-label {
    font-size: 14px;
}

/* إحصائيات الإشعارات */
.notification-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.notification-stat-card {
    background: var(--bs-light);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid var(--bs-border-color);
}

.notification-stat-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--bs-primary);
    margin-bottom: 5px;
}

.notification-stat-label {
    font-size: 12px;
    color: var(--bs-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* تأثيرات الحركة */
@keyframes notificationSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes notificationSlideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

.notification-toast.entering {
    animation: notificationSlideIn 0.3s ease;
}

.notification-toast.leaving {
    animation: notificationSlideOut 0.3s ease;
}

/* مؤشر التحميل للإشعارات */
.notification-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--bs-secondary);
}

.notification-loading .spinner-border {
    width: 20px;
    height: 20px;
    margin-left: 10px;
}

/* الوضع المظلم */
[data-theme="dark"] .notification-toast {
    background: var(--bs-dark);
    border-color: var(--bs-secondary);
    color: var(--bs-light);
}

[data-theme="dark"] .notification-title {
    color: var(--bs-light);
}

[data-theme="dark"] .notification-panel {
    background: var(--bs-dark);
    border-color: var(--bs-secondary);
}

[data-theme="dark"] .notification-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .notification-item.unread {
    background: rgba(var(--bs-primary-rgb), 0.1);
}

[data-theme="dark"] .notification-stat-card {
    background: rgba(255, 255, 255, 0.05);
    border-color: var(--bs-secondary);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .notification-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .notification-panel {
        right: 10px;
        left: 10px;
        width: auto;
        max-width: none;
    }
    
    .notification-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .notification-content {
        padding: 12px;
        gap: 10px;
    }
    
    .notification-title {
        font-size: 13px;
    }
    
    .notification-message {
        font-size: 12px;
    }
    
    .notification-stats {
        grid-template-columns: 1fr;
    }
}

/* تحسينات الطباعة */
@media print {
    .notification-container,
    .notification-panel,
    .notification-toggle {
        display: none !important;
    }
}
