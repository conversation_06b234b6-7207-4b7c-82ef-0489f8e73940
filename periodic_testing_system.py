#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الاختبار الدوري لجميع وظائف النظام
Periodic Testing System for All System Functions
"""

import sqlite3
import requests
import time
from datetime import datetime, timedelta
import json
import os
import schedule

class PeriodicTestingSystem:
    def __init__(self):
        self.db_path = 'instance/lawoffice.db'
        self.base_url = 'http://127.0.0.1:5000'
        self.test_results = []
        self.ensure_test_logs_table()
    
    def ensure_test_logs_table(self):
        """إنشاء جدول سجلات الاختبارات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS test_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    test_name TEXT NOT NULL,
                    test_type TEXT NOT NULL,
                    test_status TEXT NOT NULL,
                    test_duration REAL,
                    error_message TEXT,
                    test_details TEXT,
                    created_at TEXT NOT NULL
                )
            """)
            
            conn.commit()
            conn.close()
            print("✅ تم إعداد جدول سجلات الاختبارات")
            
        except Exception as e:
            print(f"⚠️ تحذير: خطأ في إنشاء جدول الاختبارات: {e}")
    
    def log_test_result(self, test_name, test_type, status, duration, error_msg=None, details=None):
        """تسجيل نتيجة الاختبار"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO test_logs (test_name, test_type, test_status, test_duration, 
                                     error_message, test_details, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (test_name, test_type, status, duration, error_msg, 
                  json.dumps(details, ensure_ascii=False) if details else None,
                  datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"⚠️ تحذير: خطأ في تسجيل نتيجة الاختبار: {e}")
    
    def test_database_connectivity(self):
        """اختبار الاتصال بقاعدة البيانات"""
        start_time = time.time()
        test_name = "Database Connectivity"
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # اختبار الاستعلام البسيط
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            
            # اختبار الجداول الرئيسية
            main_tables = ['users', 'clients', 'cases', 'properties', 'tenants', 'leases']
            existing_tables = []
            
            for table in main_tables:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    existing_tables.append(table)
            
            conn.close()
            
            duration = time.time() - start_time
            details = {
                'total_tables': table_count,
                'main_tables_found': len(existing_tables),
                'missing_tables': list(set(main_tables) - set(existing_tables))
            }
            
            if len(existing_tables) == len(main_tables):
                self.log_test_result(test_name, "Database", "PASS", duration, details=details)
                print(f"✅ {test_name}: نجح ({duration:.2f}s)")
                return True
            else:
                error_msg = f"Missing tables: {details['missing_tables']}"
                self.log_test_result(test_name, "Database", "FAIL", duration, error_msg, details)
                print(f"❌ {test_name}: فشل - {error_msg}")
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, "Database", "ERROR", duration, str(e))
            print(f"❌ {test_name}: خطأ - {e}")
            return False
    
    def test_web_server_connectivity(self):
        """اختبار الاتصال بخادم الويب"""
        start_time = time.time()
        test_name = "Web Server Connectivity"
        
        try:
            response = requests.get(f"{self.base_url}/", timeout=10)
            duration = time.time() - start_time
            
            details = {
                'status_code': response.status_code,
                'response_time': duration,
                'content_length': len(response.content)
            }
            
            if response.status_code == 200:
                self.log_test_result(test_name, "Web", "PASS", duration, details=details)
                print(f"✅ {test_name}: نجح ({duration:.2f}s)")
                return True
            else:
                error_msg = f"HTTP {response.status_code}"
                self.log_test_result(test_name, "Web", "FAIL", duration, error_msg, details)
                print(f"❌ {test_name}: فشل - {error_msg}")
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, "Web", "ERROR", duration, str(e))
            print(f"❌ {test_name}: خطأ - {e}")
            return False
    
    def test_login_functionality(self):
        """اختبار وظيفة تسجيل الدخول"""
        start_time = time.time()
        test_name = "Login Functionality"
        
        try:
            session = requests.Session()
            
            # الحصول على صفحة تسجيل الدخول
            login_page = session.get(f"{self.base_url}/login")
            if login_page.status_code != 200:
                raise Exception(f"Cannot access login page: {login_page.status_code}")
            
            # محاولة تسجيل الدخول
            login_data = {
                'username': 'office',
                'password': '66889088'
            }
            
            login_response = session.post(f"{self.base_url}/login", data=login_data)
            duration = time.time() - start_time
            
            details = {
                'login_status_code': login_response.status_code,
                'redirected': login_response.history != [],
                'final_url': login_response.url
            }
            
            # التحقق من نجاح تسجيل الدخول (إعادة توجيه إلى لوحة التحكم)
            if login_response.status_code == 200 and 'dashboard' in login_response.url:
                self.log_test_result(test_name, "Authentication", "PASS", duration, details=details)
                print(f"✅ {test_name}: نجح ({duration:.2f}s)")
                return True
            else:
                error_msg = f"Login failed - Status: {login_response.status_code}"
                self.log_test_result(test_name, "Authentication", "FAIL", duration, error_msg, details)
                print(f"❌ {test_name}: فشل - {error_msg}")
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, "Authentication", "ERROR", duration, str(e))
            print(f"❌ {test_name}: خطأ - {e}")
            return False
    
    def test_crud_operations(self):
        """اختبار عمليات CRUD الأساسية"""
        start_time = time.time()
        test_name = "CRUD Operations"
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # اختبار إدراج بيانات تجريبية
            test_client_data = {
                'name': 'عميل اختبار',
                'phone': '0501234567',
                'email': '<EMAIL>',
                'address': 'عنوان اختبار'
            }
            
            cursor.execute("""
                INSERT INTO clients (name, phone, email, address, created_date)
                VALUES (?, ?, ?, ?, ?)
            """, (test_client_data['name'], test_client_data['phone'], 
                  test_client_data['email'], test_client_data['address'],
                  datetime.now().isoformat()))
            
            client_id = cursor.lastrowid
            
            # اختبار قراءة البيانات
            cursor.execute("SELECT * FROM clients WHERE id = ?", (client_id,))
            client_data = cursor.fetchone()
            
            if not client_data:
                raise Exception("Failed to read inserted data")
            
            # اختبار تحديث البيانات
            cursor.execute("""
                UPDATE clients SET name = ? WHERE id = ?
            """, ('عميل اختبار محدث', client_id))
            
            # اختبار حذف البيانات
            cursor.execute("DELETE FROM clients WHERE id = ?", (client_id,))
            
            conn.commit()
            conn.close()
            
            duration = time.time() - start_time
            details = {
                'operations_tested': ['INSERT', 'SELECT', 'UPDATE', 'DELETE'],
                'test_client_id': client_id
            }
            
            self.log_test_result(test_name, "Database", "PASS", duration, details=details)
            print(f"✅ {test_name}: نجح ({duration:.2f}s)")
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, "Database", "ERROR", duration, str(e))
            print(f"❌ {test_name}: خطأ - {e}")
            return False
    
    def test_data_integrity(self):
        """اختبار سلامة البيانات"""
        start_time = time.time()
        test_name = "Data Integrity"
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # فحص العلاقات الخارجية
            integrity_issues = []
            
            # فحص القضايا المرتبطة بعملاء غير موجودين
            cursor.execute("""
                SELECT COUNT(*) FROM cases c 
                LEFT JOIN clients cl ON c.client_id = cl.id 
                WHERE c.client_id IS NOT NULL AND cl.id IS NULL
            """)
            orphaned_cases = cursor.fetchone()[0]
            if orphaned_cases > 0:
                integrity_issues.append(f"Orphaned cases: {orphaned_cases}")
            
            # فحص العقود المرتبطة بعقارات غير موجودة
            cursor.execute("""
                SELECT COUNT(*) FROM leases l 
                LEFT JOIN properties p ON l.property_id = p.id 
                WHERE l.property_id IS NOT NULL AND p.id IS NULL
            """)
            orphaned_leases = cursor.fetchone()[0]
            if orphaned_leases > 0:
                integrity_issues.append(f"Orphaned leases: {orphaned_leases}")
            
            # فحص المعاملات المالية المرتبطة بعملاء غير موجودين
            cursor.execute("""
                SELECT COUNT(*) FROM financial_transactions ft 
                LEFT JOIN clients c ON ft.client_id = c.id 
                WHERE ft.client_id IS NOT NULL AND c.id IS NULL
            """)
            orphaned_transactions = cursor.fetchone()[0]
            if orphaned_transactions > 0:
                integrity_issues.append(f"Orphaned transactions: {orphaned_transactions}")
            
            conn.close()
            
            duration = time.time() - start_time
            details = {
                'integrity_issues': integrity_issues,
                'orphaned_cases': orphaned_cases,
                'orphaned_leases': orphaned_leases,
                'orphaned_transactions': orphaned_transactions
            }
            
            if len(integrity_issues) == 0:
                self.log_test_result(test_name, "Database", "PASS", duration, details=details)
                print(f"✅ {test_name}: نجح ({duration:.2f}s)")
                return True
            else:
                error_msg = f"Integrity issues found: {len(integrity_issues)}"
                self.log_test_result(test_name, "Database", "FAIL", duration, error_msg, details)
                print(f"❌ {test_name}: فشل - {error_msg}")
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, "Database", "ERROR", duration, str(e))
            print(f"❌ {test_name}: خطأ - {e}")
            return False
    
    def run_comprehensive_test(self):
        """تشغيل اختبار شامل لجميع الوظائف"""
        print("🧪 بدء الاختبار الدوري الشامل للنظام")
        print("=" * 60)
        
        test_start_time = datetime.now()
        passed_tests = 0
        total_tests = 0
        
        # قائمة الاختبارات
        tests = [
            ("اختبار الاتصال بقاعدة البيانات", self.test_database_connectivity),
            ("اختبار الاتصال بخادم الويب", self.test_web_server_connectivity),
            ("اختبار وظيفة تسجيل الدخول", self.test_login_functionality),
            ("اختبار عمليات CRUD", self.test_crud_operations),
            ("اختبار سلامة البيانات", self.test_data_integrity)
        ]
        
        for test_description, test_function in tests:
            total_tests += 1
            print(f"\n{total_tests}️⃣ {test_description}...")
            
            if test_function():
                passed_tests += 1
        
        # تقرير النتائج
        test_end_time = datetime.now()
        total_duration = (test_end_time - test_start_time).total_seconds()
        
        print("\n" + "=" * 60)
        print("📊 تقرير نتائج الاختبار الشامل")
        print("=" * 60)
        print(f"✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
        print(f"❌ الاختبارات الفاشلة: {total_tests - passed_tests}/{total_tests}")
        print(f"⏱️ إجمالي الوقت: {total_duration:.2f} ثانية")
        print(f"📅 تاريخ الاختبار: {test_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        success_rate = (passed_tests / total_tests) * 100
        print(f"📈 معدل النجاح: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 النظام يعمل بشكل ممتاز!")
        elif success_rate >= 60:
            print("⚠️ النظام يعمل بشكل جيد مع بعض المشاكل")
        else:
            print("🚨 النظام يحتاج إلى صيانة فورية!")
        
        return success_rate
    
    def schedule_periodic_tests(self):
        """جدولة الاختبارات الدورية"""
        # اختبار كل ساعة
        schedule.every().hour.do(self.run_comprehensive_test)
        
        # اختبار شامل يومياً في الساعة 1:00 صباحاً
        schedule.every().day.at("01:00").do(self.run_comprehensive_test)
        
        print("✅ تم جدولة الاختبارات الدورية:")
        print("   🔄 اختبار سريع كل ساعة")
        print("   📅 اختبار شامل يومياً في الساعة 1:00 صباحاً")
    
    def run_scheduler(self):
        """تشغيل جدولة الاختبارات"""
        print("🔄 بدء تشغيل نظام الاختبار الدوري...")
        self.schedule_periodic_tests()
        
        while True:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة

def main():
    """الدالة الرئيسية"""
    testing_system = PeriodicTestingSystem()
    
    print("🧪 نظام الاختبار الدوري للنظام")
    print("=" * 50)
    
    # تشغيل اختبار شامل فوري
    success_rate = testing_system.run_comprehensive_test()
    
    print(f"\n✅ تم إعداد نظام الاختبار الدوري بنجاح!")
    print(f"📊 معدل نجاح الاختبار الأولي: {success_rate:.1f}%")
    print("📝 ملاحظة: لتشغيل الجدولة الدورية، استخدم: testing_system.run_scheduler()")

if __name__ == "__main__":
    main()
