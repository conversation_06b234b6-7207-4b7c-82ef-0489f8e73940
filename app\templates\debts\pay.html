{% extends "base.html" %}

{% block title %}سداد دين{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="fa fa-money-bill"></i> سداد دين</h4>
                </div>
                <div class="card-body">
                    <!-- معلومات الدين -->
                    <div class="alert alert-info">
                        <h6><strong>معلومات الدين:</strong></h6>
                        <p><strong>الدائن:</strong> {{ debt.creditor_name }}</p>
                        <p><strong>نوع الدين:</strong> {{ debt.debt_type }}</p>
                        <p><strong>إجمالي المبلغ:</strong> {{ "%.2f"|format(debt.amount) }} {{ debt.currency }}</p>
                        <p><strong>المدفوع سابقاً:</strong> {{ "%.2f"|format(debt.paid_amount) }} {{ debt.currency }}</p>
                        <p><strong>المتبقي:</strong> <span class="text-danger">{{ "%.2f"|format(debt.remaining_amount) }} {{ debt.currency }}</span></p>
                    </div>

                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">مبلغ الدفعة <span class="text-danger">*</span></label>
                            <input type="number" name="amount" class="form-control" 
                                   step="0.01" min="0.01" max="{{ debt.remaining_amount }}" 
                                   value="{{ debt.remaining_amount }}" required>
                            <div class="form-text">الحد الأقصى: {{ "%.2f"|format(debt.remaining_amount) }} {{ debt.currency }}</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">طريقة الدفع</label>
                            <select name="payment_method" class="form-control">
                                <option value="">اختر طريقة الدفع</option>
                                <option value="نقداً">نقداً</option>
                                <option value="شيك">شيك</option>
                                <option value="تحويل بنكي">تحويل بنكي</option>
                                <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea name="notes" class="form-control" rows="3" 
                                      placeholder="ملاحظات حول الدفعة..."></textarea>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-success px-5 py-2">
                                <i class="fa fa-check"></i> تأكيد الدفع
                            </button>
                            <a href="{{ url_for('debts_list') }}" class="btn btn-secondary px-4 py-2">
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- سجل الدفعات السابقة -->
            {% if debt.payments %}
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fa fa-history"></i> سجل الدفعات السابقة</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in debt.payments %}
                                <tr>
                                    <td>{{ payment.payment_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ "%.2f"|format(payment.amount) }} {{ payment.currency }}</td>
                                    <td>{{ payment.payment_method or 'غير محدد' }}</td>
                                    <td>{{ payment.notes or '-' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.alert {
    border-radius: 10px;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.text-danger {
    color: #dc3545 !important;
}
</style>
{% endblock %}
