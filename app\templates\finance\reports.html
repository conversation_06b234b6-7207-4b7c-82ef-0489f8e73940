{% extends 'base.html' %}
{% block title %}التقارير المالية{% endblock %}
{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .report-card { border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); transition: transform 0.3s; }
    .report-card:hover { transform: translateY(-5px); }
    .stat-icon { font-size: 2.5rem; opacity: 0.8; }
    .chart-container { position: relative; height: 400px; margin: 20px 0; }
    .filter-section { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; color: white; }
    .btn-export { background: linear-gradient(45deg, #28a745, #20c997); border: none; border-radius: 25px; }
    .btn-export:hover { background: linear-gradient(45deg, #20c997, #28a745); }
    @media print {
        .no-print { display: none !important; }
        .chart-container { height: 300px; }
    }
</style>
{% endblock %}
{% block content %}
<div class="container-fluid mt-4" dir="rtl">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-primary"><i class="fa fa-chart-line"></i> التقارير المالية المتقدمة</h2>
        <div class="no-print">
            <button onclick="window.print()" class="btn btn-outline-dark me-2">
                <i class="fa fa-print"></i> طباعة التقرير
            </button>
            <button onclick="exportToExcel()" class="btn btn-export">
                <i class="fa fa-file-excel"></i> تصدير Excel
            </button>
        </div>
    </div>

    <!-- الملخص المالي المحسن -->
    {% if summary %}
    <div class="row mb-4">
        {% for currency, s in summary.items() %}
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card report-card border-0 h-100">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title text-primary mb-0">{{ currency }}</h5>
                        <i class="fa fa-coins stat-icon text-warning"></i>
                    </div>
                    <div class="row text-center">
                        <div class="col-12 mb-2">
                            <small class="text-muted">إجمالي الدخل</small>
                            <h4 class="text-success mb-0">{{ '{:,.2f}'.format(s.income) }}</h4>
                        </div>
                        <div class="col-12 mb-2">
                            <small class="text-muted">إجمالي المصروفات</small>
                            <h4 class="text-danger mb-0">{{ '{:,.2f}'.format(s.expense) }}</h4>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">صافي الربح</small>
                            <h4 class="{% if s.balance >= 0 %}text-success{% else %}text-danger{% endif %} mb-0">
                                {{ '{:,.2f}'.format(s.balance) }}
                            </h4>
                        </div>
                    </div>
                    <div class="progress mt-3" style="height: 8px;">
                        {% set profit_percentage = ((s.balance / s.income * 100) if s.income > 0 else 0) %}
                        <div class="progress-bar {% if profit_percentage >= 0 %}bg-success{% else %}bg-danger{% endif %}"
                             style="width: {{ profit_percentage|abs }}%"></div>
                    </div>
                    <small class="text-muted">نسبة الربح: {{ '{:.1f}'.format(profit_percentage) }}%</small>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    <!-- قسم الفلاتر المحسن -->
    <div class="card filter-section p-4 mb-4 no-print">
        <h5 class="text-white mb-3"><i class="fa fa-filter"></i> فلاتر التقرير</h5>
        <form method="get" class="row g-3">
            <div class="col-lg-2 col-md-4">
                <label class="form-label text-white">العميل</label>
                <select name="client_id" class="form-select">
                    <option value="">كل العملاء</option>
                    {% for client in clients %}
                    <option value="{{ client.id }}" {% if request.args.get('client_id') == client.id|string %}selected{% endif %}>
                        {{ client.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-lg-2 col-md-4">
                <label class="form-label text-white">القضية</label>
                <select name="case_id" class="form-select">
                    <option value="">كل القضايا</option>
                    {% for case in cases %}
                    <option value="{{ case.id }}" {% if request.args.get('case_id') == case.id|string %}selected{% endif %}>
                        {{ case.title }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-lg-2 col-md-4">
                <label class="form-label text-white">نوع المعاملة</label>
                <select name="transaction_type" class="form-select">
                    <option value="">كل الأنواع</option>
                    <option value="أتعاب" {% if request.args.get('transaction_type') == 'أتعاب' %}selected{% endif %}>أتعاب</option>
                    <option value="رسوم" {% if request.args.get('transaction_type') == 'رسوم' %}selected{% endif %}>رسوم</option>
                    <option value="مصاريف" {% if request.args.get('transaction_type') == 'مصاريف' %}selected{% endif %}>مصاريف</option>
                    <option value="إيجار" {% if request.args.get('transaction_type') == 'إيجار' %}selected{% endif %}>إيجار</option>
                </select>
            </div>
            <div class="col-lg-2 col-md-4">
                <label class="form-label text-white">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ request.args.get('date_from', '') }}">
            </div>
            <div class="col-lg-2 col-md-4">
                <label class="form-label text-white">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ request.args.get('date_to', '') }}">
            </div>
            <div class="col-lg-2 col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-light w-100">
                    <i class="fa fa-search"></i> تطبيق الفلتر
                </button>
            </div>
        </form>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="card report-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fa fa-pie-chart"></i> توزيع الإيرادات والمصروفات</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="incomeExpenseChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="card report-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fa fa-line-chart"></i> الاتجاه الشهري</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="monthlyTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تقارير تفصيلية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card report-card">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fa fa-table"></i> تفاصيل المعاملات المالية</h5>
                    <div class="no-print">
                        <input type="text" id="searchInput" class="form-control form-control-sm"
                               placeholder="بحث في المعاملات..." style="width: 200px;">
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="transactionsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>العملة</th>
                                    <th>العميل</th>
                                    <th>القضية</th>
                                    <th>الوصف</th>
                                    <th class="no-print">الإجراءات</th>
                                </tr>
                            </thead>
                                {% for t in transactions %}
                                <tr>
                                    <td>{{ t.date.strftime('%Y-%m-%d') if t.date else '-' }}</td>
                                    <td>
                                        <span class="badge {% if t.type in ['أتعاب', 'قبض', 'تحصيل إيجار'] %}bg-success{% else %}bg-danger{% endif %}">
                                            {{ t.type }}
                                        </span>
                                    </td>
                                    <td class="fw-bold {% if t.type in ['أتعاب', 'قبض', 'تحصيل إيجار'] %}text-success{% else %}text-danger{% endif %}">
                                        {{ '{:,.2f}'.format(t.amount) }}
                                    </td>
                                    <td>{{ t.currency or 'شيكل' }}</td>
                                    <td>{{ t.client.name if t.client else '-' }}</td>
                                    <td>{{ t.case.title if t.case else '-' }}</td>
                                    <td>{{ t.description or '-' }}</td>
                                    <td class="no-print">
                                        <a href="{{ url_for('edit_financial_transaction', transaction_id=t.id) }}"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fa fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// بيانات الرسوم البيانية
const chartData = {
    {% if summary %}
    income: [{% for currency, s in summary.items() %}{{ s.income }}{% if not loop.last %},{% endif %}{% endfor %}],
    expense: [{% for currency, s in summary.items() %}{{ s.expense }}{% if not loop.last %},{% endif %}{% endfor %}],
    currencies: [{% for currency, s in summary.items %}'{{ currency }}'{% if not loop.last %},{% endif %}{% endfor %}],
    {% endif %}
    transactions: [
        {% for t in transactions %}
        {
            date: '{{ t.date.strftime('%Y-%m') if t.date else '' }}',
            amount: {{ t.amount }},
            type: '{{ t.type }}',
            currency: '{{ t.currency or 'شيكل' }}'
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ]
};

// رسم بياني دائري للإيرادات والمصروفات
const incomeExpenseCtx = document.getElementById('incomeExpenseChart').getContext('2d');
new Chart(incomeExpenseCtx, {
    type: 'doughnut',
    data: {
        labels: ['الإيرادات', 'المصروفات'],
        datasets: [{
            data: [
                chartData.income.reduce((a, b) => a + b, 0),
                chartData.expense.reduce((a, b) => a + b, 0)
            ],
            backgroundColor: ['#28a745', '#dc3545'],
            borderWidth: 3,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    font: { size: 14 },
                    padding: 20
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                        return context.label + ': ' + context.parsed.toLocaleString() + ' (' + percentage + '%)';
                    }
                }
            }
        }
    }
});

// رسم بياني خطي للاتجاه الشهري
const monthlyData = {};
chartData.transactions.forEach(t => {
    if (t.date) {
        if (!monthlyData[t.date]) {
            monthlyData[t.date] = { income: 0, expense: 0 };
        }
        if (['أتعاب', 'قبض', 'تحصيل إيجار'].includes(t.type)) {
            monthlyData[t.date].income += t.amount;
        } else {
            monthlyData[t.date].expense += t.amount;
        }
    }
});

const sortedMonths = Object.keys(monthlyData).sort();
const monthlyIncomes = sortedMonths.map(month => monthlyData[month].income);
const monthlyExpenses = sortedMonths.map(month => monthlyData[month].expense);

const monthlyTrendCtx = document.getElementById('monthlyTrendChart').getContext('2d');
new Chart(monthlyTrendCtx, {
    type: 'line',
    data: {
        labels: sortedMonths,
        datasets: [{
            label: 'الإيرادات',
            data: monthlyIncomes,
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'المصروفات',
            data: monthlyExpenses,
            borderColor: '#dc3545',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString();
                    }
                }
            }
        }
    }
});

// وظيفة البحث في الجدول
document.getElementById('searchInput').addEventListener('keyup', function() {
    const searchValue = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('#transactionsTable tbody tr');

    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchValue) ? '' : 'none';
    });
});

// وظيفة تصدير Excel
function exportToExcel() {
    const table = document.getElementById('transactionsTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "التقرير المالي"});
    XLSX.writeFile(wb, 'التقرير_المالي_' + new Date().toISOString().split('T')[0] + '.xlsx');
}
</script>

<!-- مكتبة تصدير Excel -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

{% endblock %}
