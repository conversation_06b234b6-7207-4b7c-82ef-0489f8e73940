#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص مشكلة تسجيل الدخول
Check Login Issue
"""

import sqlite3
import hashlib
from datetime import datetime

def check_login_credentials():
    """فحص بيانات تسجيل الدخول"""
    print("🔍 فحص مشكلة تسجيل الدخول...")
    print("=" * 60)
    
    db_path = 'instance/lawoffice.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص جدول المستخدمين
        print("👥 فحص جدول المستخدمين:")
        print("-" * 40)
        
        cursor.execute("SELECT id, username, password, role FROM users")
        users = cursor.fetchall()
        
        if not users:
            print("❌ جدول المستخدمين فارغ!")
            return False
        
        print(f"📊 عدد المستخدمين: {len(users)}")
        print("\n📋 قائمة المستخدمين:")
        
        office_found = False
        admin_found = False
        
        for user_id, username, password, role in users:
            print(f"   - ID: {user_id}")
            print(f"     اسم المستخدم: {username}")
            print(f"     كلمة المرور (مشفرة): {password[:20]}...")
            print(f"     الدور: {role}")
            print()
            
            if username == 'office':
                office_found = True
                # فحص كلمة المرور
                expected_password = hashlib.sha256('66889088'.encode()).hexdigest()
                if password == expected_password:
                    print("   ✅ حساب office: كلمة المرور صحيحة")
                else:
                    print("   ❌ حساب office: كلمة المرور خاطئة")
                    print(f"      المتوقع: {expected_password}")
                    print(f"      الموجود: {password}")
            
            elif username == 'admin':
                admin_found = True
                # فحص كلمة المرور
                expected_password = hashlib.sha256('admin123'.encode()).hexdigest()
                if password == expected_password:
                    print("   ✅ حساب admin: كلمة المرور صحيحة")
                else:
                    print("   ❌ حساب admin: كلمة المرور خاطئة")
                    print(f"      المتوقع: {expected_password}")
                    print(f"      الموجود: {password}")
        
        # التحقق من وجود الحسابات المطلوبة
        print("\n🔐 فحص الحسابات المطلوبة:")
        print("-" * 40)
        
        if not office_found:
            print("❌ حساب office غير موجود!")
        else:
            print("✅ حساب office موجود")
        
        if not admin_found:
            print("❌ حساب admin غير موجود!")
        else:
            print("✅ حساب admin موجود")
        
        # فحص هيكل جدول المستخدمين
        print("\n🗄️ فحص هيكل جدول المستخدمين:")
        print("-" * 40)
        
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        
        required_columns = ['id', 'username', 'password', 'role']
        existing_columns = [col[1] for col in columns]
        
        print("📋 الأعمدة الموجودة:")
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")
        
        missing_columns = [col for col in required_columns if col not in existing_columns]
        if missing_columns:
            print(f"\n❌ أعمدة مفقودة: {missing_columns}")
        else:
            print("\n✅ جميع الأعمدة المطلوبة موجودة")
        
        conn.close()
        
        return office_found and admin_found
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def fix_login_credentials():
    """إصلاح بيانات تسجيل الدخول"""
    print("\n🔧 إصلاح بيانات تسجيل الدخول...")
    print("=" * 60)
    
    db_path = 'instance/lawoffice.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # حذف المستخدمين الموجودين
        print("🗑️ حذف المستخدمين الموجودين...")
        cursor.execute("DELETE FROM users")
        
        # إنشاء كلمات المرور المشفرة
        office_password = hashlib.sha256('66889088'.encode()).hexdigest()
        admin_password = hashlib.sha256('admin123'.encode()).hexdigest()
        
        # إضافة المستخدمين الجدد
        print("👤 إضافة حساب office...")
        cursor.execute("""
            INSERT INTO users (username, password, role, full_name, email, phone, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            'office',
            office_password,
            'مدير',
            'مكتب المحاماة',
            '<EMAIL>',
            '66889088',
            datetime.now().isoformat()
        ))
        
        print("👤 إضافة حساب admin...")
        cursor.execute("""
            INSERT INTO users (username, password, role, full_name, email, phone, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            'admin',
            admin_password,
            'مدير',
            'مدير النظام',
            '<EMAIL>',
            'admin123',
            datetime.now().isoformat()
        ))
        
        conn.commit()
        
        # التحقق من الإضافة
        cursor.execute("SELECT username, role FROM users")
        users = cursor.fetchall()
        
        print("\n✅ تم إنشاء المستخدمين بنجاح:")
        for username, role in users:
            print(f"   - {username} ({role})")
        
        conn.close()
        
        print("\n🎉 تم إصلاح بيانات تسجيل الدخول بنجاح!")
        print("📋 بيانات الدخول:")
        print("   - office / 66889088")
        print("   - admin / admin123")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح بيانات الدخول: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_functionality():
    """اختبار وظيفة تسجيل الدخول"""
    print("\n🧪 اختبار وظيفة تسجيل الدخول...")
    print("=" * 60)
    
    try:
        import requests
        import time
        
        # انتظار تشغيل الخادم
        time.sleep(2)
        
        # اختبار صفحة تسجيل الدخول
        print("🌐 اختبار صفحة تسجيل الدخول...")
        response = requests.get('http://127.0.0.1:5000/lawyersameh', timeout=10)
        
        if response.status_code == 200:
            print("✅ صفحة تسجيل الدخول متاحة")
            
            # فحص وجود نموذج تسجيل الدخول
            if 'form' in response.text and 'username' in response.text:
                print("✅ نموذج تسجيل الدخول موجود")
            else:
                print("❌ نموذج تسجيل الدخول غير موجود")
                
        else:
            print(f"❌ صفحة تسجيل الدخول غير متاحة: {response.status_code}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

if __name__ == "__main__":
    print("🔍 بدء فحص وإصلاح مشكلة تسجيل الدخول...")
    print()
    
    # فحص المشكلة
    login_ok = check_login_credentials()
    
    if not login_ok:
        print("\n🔧 إصلاح المشكلة...")
        fix_success = fix_login_credentials()
        
        if fix_success:
            print("\n🧪 اختبار الإصلاح...")
            test_login_functionality()
        else:
            print("\n❌ فشل في إصلاح المشكلة")
    else:
        print("\n✅ بيانات تسجيل الدخول صحيحة")
        test_login_functionality()
    
    print("\n" + "=" * 60)
    print("✅ انتهى فحص مشكلة تسجيل الدخول")
