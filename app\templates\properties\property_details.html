{% extends "base.html" %}

{% block title %}تفاصيل العقار - {{ property.name }}{% endblock %}

{% block extra_css %}
<style>
    .property-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        border-radius: 15px;
        margin-bottom: 30px;
    }

    .property-image-gallery {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .main-image {
        height: 400px;
        object-fit: cover;
        width: 100%;
    }

    .thumbnail-images {
        display: flex;
        gap: 10px;
        margin-top: 15px;
        overflow-x: auto;
    }

    .thumbnail {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .thumbnail:hover, .thumbnail.active {
        border-color: #007bff;
        transform: scale(1.05);
    }

    .info-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        border: 1px solid #e9ecef;
    }

    .info-card h5 {
        color: #495057;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

    .feature-badge {
        background: #e3f2fd;
        color: #1976d2;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        margin: 3px;
        display: inline-block;
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 25px;
        font-weight: bold;
        font-size: 0.9rem;
    }

    .status-available { background: #d4edda; color: #155724; }
    .status-rented { background: #f8d7da; color: #721c24; }
    .status-maintenance { background: #fff3cd; color: #856404; }

    .price-display {
        font-size: 2rem;
        font-weight: bold;
        color: #28a745;
        text-align: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
        margin: 20px 0;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        margin-top: 20px;
    }

    .btn-action {
        flex: 1;
        min-width: 150px;
        padding: 12px;
        border-radius: 8px;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .map-container {
        height: 300px;
        border-radius: 10px;
        overflow: hidden;
        border: 1px solid #ddd;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #f1f1f1;
    }

    .detail-row:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: #495057;
    }

    .detail-value {
        color: #6c757d;
    }

    @media print {
        .action-buttons, .btn, button {
            display: none !important;
        }
        .property-header {
            background: #f8f9fa !important;
            color: #333 !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Property Header -->
    <div class="property-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">{{ property.name }}</h1>
                    <p class="mb-2">
                        <i class="fas fa-map-marker-alt"></i>
                        {{ property.city }}, {{ property.district }}
                        {% if property.street %}, {{ property.street }}{% endif %}
                    </p>
                    <p class="mb-0">
                        <span class="status-badge status-{{ property.status|replace('متاح', 'available')|replace('مؤجر', 'rented')|replace('صيانة', 'maintenance') }}">
                            {{ property.status }}
                        </span>
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="price-display">
                        {{ property.monthly_rent or 0 }} {{ property.currency or 'JOD' }}/شهر
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column - Images and Map -->
        <div class="col-lg-8">
            <!-- Image Gallery -->
            <div class="info-card">
                <div class="property-image-gallery">
                    {% if property.images %}
                        <img src="{{ property.images[0] }}" class="main-image" id="mainImage" alt="{{ property.name }}">
                        {% if property.images|length > 1 %}
                        <div class="thumbnail-images">
                            {% for image in property.images %}
                            <img src="{{ image }}" class="thumbnail {{ 'active' if loop.first }}" 
                                 onclick="changeMainImage('{{ image }}')" alt="صورة {{ loop.index }}">
                            {% endfor %}
                        </div>
                        {% endif %}
                    {% else %}
                        <img src="{{ url_for('static', filename='images/default-property.jpg') }}" 
                             class="main-image" alt="صورة افتراضية">
                    {% endif %}
                </div>
            </div>

            <!-- Property Details -->
            <div class="info-card">
                <h5><i class="fas fa-info-circle"></i> تفاصيل العقار</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="detail-row">
                            <span class="detail-label">رمز العقار:</span>
                            <span class="detail-value">{{ property.property_code }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">نوع العقار:</span>
                            <span class="detail-value">{{ property.property_type }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">الفئة:</span>
                            <span class="detail-value">{{ property.category or 'غير محدد' }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">المساحة:</span>
                            <span class="detail-value">{{ property.area or 0 }} م²</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-row">
                            <span class="detail-label">عدد الغرف:</span>
                            <span class="detail-value">{{ property.bedrooms or 0 }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">عدد الحمامات:</span>
                            <span class="detail-value">{{ property.bathrooms or 0 }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">عدد الصالات:</span>
                            <span class="detail-value">{{ property.living_rooms or 0 }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">مواقف السيارات:</span>
                            <span class="detail-value">{{ property.parking_spaces or 0 }}</span>
                        </div>
                    </div>
                </div>

                {% if property.description %}
                <div class="mt-3">
                    <h6>الوصف:</h6>
                    <p class="text-muted">{{ property.description }}</p>
                </div>
                {% endif %}

                <!-- Features -->
                <div class="mt-3">
                    <h6>المرافق والخدمات:</h6>
                    <div>
                        {% if property.has_elevator %}<span class="feature-badge">مصعد</span>{% endif %}
                        {% if property.has_balcony %}<span class="feature-badge">شرفة</span>{% endif %}
                        {% if property.has_garden %}<span class="feature-badge">حديقة</span>{% endif %}
                        {% if property.has_pool %}<span class="feature-badge">مسبح</span>{% endif %}
                        {% if property.has_ac %}<span class="feature-badge">تكييف</span>{% endif %}
                        {% if property.has_heating %}<span class="feature-badge">تدفئة</span>{% endif %}
                        {% if property.furnished %}<span class="feature-badge">مفروش</span>{% endif %}
                        {% if property.has_security %}<span class="feature-badge">حراسة</span>{% endif %}
                    </div>
                </div>
            </div>

            <!-- Map -->
            {% if property.latitude and property.longitude %}
            <div class="info-card">
                <h5><i class="fas fa-map"></i> الموقع على الخريطة</h5>
                <div class="map-container" id="map"></div>
            </div>
            {% endif %}
        </div>

        <!-- Right Column - Financial Info and Actions -->
        <div class="col-lg-4">
            <!-- Financial Information -->
            <div class="info-card">
                <h5><i class="fas fa-dollar-sign"></i> المعلومات المالية</h5>
                <div class="detail-row">
                    <span class="detail-label">الإيجار الشهري:</span>
                    <span class="detail-value">{{ property.monthly_rent or 0 }} {{ property.currency or 'JOD' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">الإيجار السنوي:</span>
                    <span class="detail-value">{{ property.annual_rent or 0 }} {{ property.currency or 'JOD' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">مبلغ التأمين:</span>
                    <span class="detail-value">{{ property.security_deposit or 0 }} {{ property.currency or 'JOD' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">نسبة العمولة:</span>
                    <span class="detail-value">{{ property.commission_rate or 0 }}%</span>
                </div>
                {% if property.property_value %}
                <div class="detail-row">
                    <span class="detail-label">قيمة العقار:</span>
                    <span class="detail-value">{{ property.property_value }} {{ property.currency or 'JOD' }}</span>
                </div>
                {% endif %}
            </div>

            <!-- Owner Information -->
            {% if property.owner_name %}
            <div class="info-card">
                <h5><i class="fas fa-user"></i> معلومات المالك</h5>
                <div class="detail-row">
                    <span class="detail-label">الاسم:</span>
                    <span class="detail-value">{{ property.owner_name }}</span>
                </div>
                {% if property.owner_phone %}
                <div class="detail-row">
                    <span class="detail-label">الهاتف:</span>
                    <span class="detail-value">
                        <a href="tel:{{ property.owner_phone }}">{{ property.owner_phone }}</a>
                    </span>
                </div>
                {% endif %}
                {% if property.owner_email %}
                <div class="detail-row">
                    <span class="detail-label">البريد الإلكتروني:</span>
                    <span class="detail-value">
                        <a href="mailto:{{ property.owner_email }}">{{ property.owner_email }}</a>
                    </span>
                </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="info-card">
                <h5><i class="fas fa-cogs"></i> الإجراءات</h5>
                <div class="action-buttons">
                    <a href="{{ url_for('edit_property', id=property.id) }}" 
                       class="btn btn-warning btn-action">
                        <i class="fas fa-edit"></i> تعديل العقار
                    </a>
                    
                    {% if property.status == 'متاح' %}
                    <button class="btn btn-success btn-action" data-bs-toggle="modal" data-bs-target="#addLeaseModal">
                        <i class="fas fa-file-contract"></i> إنشاء عقد إيجار
                    </button>
                    {% endif %}
                    
                    <button class="btn btn-info btn-action" onclick="printProperty()">
                        <i class="fas fa-print"></i> طباعة التفاصيل
                    </button>
                    
                    <button class="btn btn-secondary btn-action" onclick="shareProperty()">
                        <i class="fas fa-share"></i> مشاركة العقار
                    </button>
                    
                    <button class="btn btn-danger btn-action" onclick="deleteProperty({{ property.id }})">
                        <i class="fas fa-trash"></i> حذف العقار
                    </button>
                </div>
            </div>

            <!-- Current Lease Info -->
            {% if property.current_lease %}
            <div class="info-card">
                <h5><i class="fas fa-file-contract"></i> العقد الحالي</h5>
                <div class="detail-row">
                    <span class="detail-label">المستأجر:</span>
                    <span class="detail-value">
                        <a href="{{ url_for('tenant_details', id=property.current_lease.tenant_id) }}">
                            {{ property.current_lease.tenant.full_name }}
                        </a>
                    </span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">تاريخ البداية:</span>
                    <span class="detail-value">{{ property.current_lease.start_date.strftime('%Y-%m-%d') }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">تاريخ الانتهاء:</span>
                    <span class="detail-value">{{ property.current_lease.end_date.strftime('%Y-%m-%d') }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">الأيام المتبقية:</span>
                    <span class="detail-value">{{ property.current_lease.days_remaining() }} يوم</span>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Add Lease Modal -->
{% if property.status == 'متاح' %}
{% include 'leases/modals/add_lease_modal.html' %}
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
function changeMainImage(imageSrc) {
    document.getElementById('mainImage').src = imageSrc;
    
    // Update active thumbnail
    document.querySelectorAll('.thumbnail').forEach(thumb => {
        thumb.classList.remove('active');
    });
    event.target.classList.add('active');
}

function deleteProperty(propertyId) {
    if (confirm('هل أنت متأكد من حذف هذا العقار؟ سيتم حذف جميع البيانات المرتبطة به.')) {
        fetch(`/properties/delete/${propertyId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '{{ url_for("properties") }}';
            } else {
                alert('حدث خطأ أثناء الحذف');
            }
        });
    }
}

function printProperty() {
    window.print();
}

function shareProperty() {
    if (navigator.share) {
        navigator.share({
            title: '{{ property.name }}',
            text: 'عقار للإيجار - {{ property.name }}',
            url: window.location.href
        });
    } else {
        // Fallback - copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('تم نسخ رابط العقار');
        });
    }
}

{% if property.latitude and property.longitude %}
// Initialize map
function initMap() {
    const propertyLocation = { lat: {{ property.latitude }}, lng: {{ property.longitude }} };
    const map = new google.maps.Map(document.getElementById("map"), {
        zoom: 15,
        center: propertyLocation,
    });
    const marker = new google.maps.Marker({
        position: propertyLocation,
        map: map,
        title: "{{ property.name }}"
    });
}
{% endif %}
</script>

{% if property.latitude and property.longitude %}
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap">
</script>
{% endif %}
{% endblock %}
