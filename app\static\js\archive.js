// Archive and Settings Management JavaScript

document.addEventListener('DOMContentLoaded', function() {
    loadArchiveData();
    loadSystemInfo();
    setupEventListeners();
});

function setupEventListeners() {
    // Tab switching
    document.querySelectorAll('#archiveTabs button').forEach(tab => {
        tab.addEventListener('click', function() {
            const target = this.getAttribute('data-bs-target');
            if (target === '#cases-archive') {
                loadArchivedCases();
            } else if (target === '#clients-archive') {
                loadArchivedClients();
            } else if (target === '#documents-archive') {
                loadArchivedDocuments();
            } else if (target === '#financial-archive') {
                loadArchivedFinancials();
            }
        });
    });

    // Settings forms
    document.getElementById('systemSettingsForm').addEventListener('submit', saveSystemSettings);
    document.getElementById('archiveSettingsForm').addEventListener('submit', saveArchiveSettings);
}

function loadArchiveData() {
    loadArchivedCases();
    loadArchiveStatistics();
}

function loadArchivedCases() {
    fetch('/api/archive/cases')
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#archivedCasesTable tbody');
            tbody.innerHTML = '';
            
            data.cases.forEach(case_ => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${case_.case_number}</td>
                    <td>${case_.title}</td>
                    <td>${case_.client_name}</td>
                    <td>${formatDate(case_.archived_date)}</td>
                    <td><span class="badge bg-secondary">${getArchiveReasonText(case_.archive_reason)}</span></td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewArchivedCase(${case_.id})" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="restoreCase(${case_.id})" title="استعادة">
                                <i class="fas fa-undo"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="permanentDeleteCase(${case_.id})" title="حذف نهائي">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('Error loading archived cases:', error);
            showAlert('خطأ في تحميل القضايا المؤرشفة', 'danger');
        });
}

function loadArchivedClients() {
    fetch('/api/archive/clients')
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#archivedClientsTable tbody');
            tbody.innerHTML = '';
            
            data.clients.forEach(client => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${client.name}</td>
                    <td>${client.id_number}</td>
                    <td>${client.phone}</td>
                    <td>${formatDate(client.archived_date)}</td>
                    <td>${client.cases_count}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewArchivedClient(${client.id})" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="restoreClient(${client.id})" title="استعادة">
                                <i class="fas fa-undo"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('Error loading archived clients:', error);
        });
}

function loadArchivedDocuments() {
    fetch('/api/archive/documents')
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#archivedDocumentsTable tbody');
            tbody.innerHTML = '';
            
            data.documents.forEach(doc => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${doc.name}</td>
                    <td>${doc.type}</td>
                    <td>${doc.case_title}</td>
                    <td>${formatDate(doc.archived_date)}</td>
                    <td>${formatFileSize(doc.size)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="downloadArchivedDocument(${doc.id})" title="تحميل">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="restoreDocument(${doc.id})" title="استعادة">
                                <i class="fas fa-undo"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('Error loading archived documents:', error);
        });
}

function loadArchivedFinancials() {
    // Load archived transactions
    fetch('/api/archive/transactions')
        .then(response => response.json())
        .then(data => {
            const tbody = document.getElementById('archivedTransactions');
            tbody.innerHTML = '';
            
            data.transactions.forEach(transaction => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${formatDate(transaction.date)}</td>
                    <td>${transaction.type}</td>
                    <td>${transaction.amount}</td>
                    <td>${transaction.description}</td>
                `;
                tbody.appendChild(row);
            });
        });

    // Load archived invoices
    fetch('/api/archive/invoices')
        .then(response => response.json())
        .then(data => {
            const tbody = document.getElementById('archivedInvoices');
            tbody.innerHTML = '';
            
            data.invoices.forEach(invoice => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${invoice.invoice_number}</td>
                    <td>${invoice.client_name}</td>
                    <td>${invoice.amount}</td>
                    <td>${formatDate(invoice.date)}</td>
                `;
                tbody.appendChild(row);
            });
        });
}

function loadArchiveStatistics() {
    fetch('/api/archive/statistics')
        .then(response => response.json())
        .then(data => {
            document.getElementById('totalArchivedCases').textContent = data.archived_cases;
            document.getElementById('totalArchivedClients').textContent = data.archived_clients;
            document.getElementById('totalArchivedDocuments').textContent = data.archived_documents;
            document.getElementById('archiveSize').textContent = formatFileSize(data.archive_size);
        })
        .catch(error => {
            console.error('Error loading archive statistics:', error);
        });
}

function loadSystemInfo() {
    fetch('/api/system/info')
        .then(response => response.json())
        .then(data => {
            document.getElementById('dbSize').textContent = formatFileSize(data.database_size);
            document.getElementById('lastBackup').textContent = data.last_backup ? formatDate(data.last_backup) : 'لم يتم إنشاء نسخة احتياطية';
        })
        .catch(error => {
            console.error('Error loading system info:', error);
        });
}

function filterArchive() {
    const dateFrom = document.getElementById('archiveDateFrom').value;
    const dateTo = document.getElementById('archiveDateTo').value;
    const reason = document.getElementById('archiveReason').value;

    const params = new URLSearchParams();
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    if (reason) params.append('reason', reason);

    fetch(`/api/archive/cases?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            // Update the cases table with filtered results
            const tbody = document.querySelector('#archivedCasesTable tbody');
            tbody.innerHTML = '';
            
            data.cases.forEach(case_ => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${case_.case_number}</td>
                    <td>${case_.title}</td>
                    <td>${case_.client_name}</td>
                    <td>${formatDate(case_.archived_date)}</td>
                    <td><span class="badge bg-secondary">${getArchiveReasonText(case_.archive_reason)}</span></td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewArchivedCase(${case_.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="restoreCase(${case_.id})">
                                <i class="fas fa-undo"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="permanentDeleteCase(${case_.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        });
}

function saveSystemSettings(event) {
    event.preventDefault();
    
    const settings = {
        office_name: document.getElementById('officeName').value,
        office_address: document.getElementById('officeAddress').value,
        office_phone: document.getElementById('officePhone').value,
        office_email: document.getElementById('officeEmail').value,
        currency: document.getElementById('currency').value
    };

    fetch('/api/settings/system', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حفظ الإعدادات بنجاح', 'success');
        } else {
            showAlert('خطأ في حفظ الإعدادات', 'danger');
        }
    })
    .catch(error => {
        console.error('Error saving settings:', error);
        showAlert('خطأ في حفظ الإعدادات', 'danger');
    });
}

function saveArchiveSettings(event) {
    event.preventDefault();
    
    const settings = {
        auto_archive_days: document.getElementById('autoArchiveDays').value,
        enable_auto_archive: document.getElementById('enableAutoArchive').checked,
        archive_notifications: document.getElementById('archiveNotifications').checked,
        backup_frequency: document.getElementById('backupFrequency').value
    };

    fetch('/api/settings/archive', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حفظ إعدادات الأرشفة بنجاح', 'success');
        } else {
            showAlert('خطأ في حفظ إعدادات الأرشفة', 'danger');
        }
    });
}

function saveArchiveRules() {
    const rules = {
        completed_cases_archive_days: document.getElementById('completedCasesArchiveDays').value,
        cancelled_cases_archive_days: document.getElementById('cancelledCasesArchiveDays').value,
        documents_archive_days: document.getElementById('documentsArchiveDays').value,
        archive_empty_folders: document.getElementById('archiveEmptyFolders').checked
    };

    fetch('/api/archive/rules', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(rules)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حفظ قواعد الأرشفة بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('archiveRulesModal')).hide();
        } else {
            showAlert('خطأ في حفظ قواعد الأرشفة', 'danger');
        }
    });
}

function createBackup() {
    showAlert('جاري إنشاء النسخة الاحتياطية...', 'info');
    
    fetch('/api/backup/create', {
        method: 'POST'
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `backup_${new Date().toISOString().split('T')[0]}.sql`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        bootstrap.Modal.getInstance(document.getElementById('backupModal')).hide();
    })
    .catch(error => {
        console.error('Error creating backup:', error);
        showAlert('خطأ في إنشاء النسخة الاحتياطية', 'danger');
    });
}

function restoreBackup() {
    const fileInput = document.getElementById('backupFile');
    if (!fileInput.files[0]) {
        showAlert('يرجى اختيار ملف النسخة الاحتياطية', 'warning');
        return;
    }

    const formData = new FormData();
    formData.append('backup_file', fileInput.files[0]);

    fetch('/api/backup/restore', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم استعادة النسخة الاحتياطية بنجاح', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showAlert('خطأ في استعادة النسخة الاحتياطية', 'danger');
        }
    })
    .catch(error => {
        console.error('Error restoring backup:', error);
        showAlert('خطأ في استعادة النسخة الاحتياطية', 'danger');
    });
}

// Archive action functions
function viewArchivedCase(caseId) {
    window.open(`/case/${caseId}?archived=true`, '_blank');
}

function restoreCase(caseId) {
    if (confirm('هل أنت متأكد من استعادة هذه القضية؟')) {
        fetch(`/api/archive/cases/${caseId}/restore`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم استعادة القضية بنجاح', 'success');
                loadArchivedCases();
            } else {
                showAlert('خطأ في استعادة القضية', 'danger');
            }
        });
    }
}

function permanentDeleteCase(caseId) {
    if (confirm('تحذير: سيتم حذف القضية نهائياً ولا يمكن استعادتها. هل أنت متأكد؟')) {
        fetch(`/api/archive/cases/${caseId}/delete`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم حذف القضية نهائياً', 'success');
                loadArchivedCases();
            } else {
                showAlert('خطأ في حذف القضية', 'danger');
            }
        });
    }
}

function restoreClient(clientId) {
    if (confirm('هل أنت متأكد من استعادة هذا العميل؟')) {
        fetch(`/api/archive/clients/${clientId}/restore`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم استعادة العميل بنجاح', 'success');
                loadArchivedClients();
            } else {
                showAlert('خطأ في استعادة العميل', 'danger');
            }
        });
    }
}

function restoreDocument(docId) {
    if (confirm('هل أنت متأكد من استعادة هذا المستند؟')) {
        fetch(`/api/archive/documents/${docId}/restore`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم استعادة المستند بنجاح', 'success');
                loadArchivedDocuments();
            } else {
                showAlert('خطأ في استعادة المستند', 'danger');
            }
        });
    }
}

// Utility functions
function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getArchiveReasonText(reason) {
    const reasons = {
        'completed': 'مكتملة',
        'cancelled': 'ملغية',
        'transferred': 'محولة',
        'inactive': 'غير نشطة'
    };
    return reasons[reason] || reason;
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
