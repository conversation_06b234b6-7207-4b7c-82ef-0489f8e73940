{% extends "base.html" %}

{% block title %}إدارة العقارات المحسنة{% endblock %}

{% block extra_css %}
<style>
    /* تصميم البطاقات الاحترافي */
    .property-card {
        border: none;
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        overflow: hidden;
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        position: relative;
        margin-bottom: 30px;
    }

    .property-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }

    .property-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .property-image {
        height: 220px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .property-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.4s ease;
    }

    .property-card:hover .property-image img {
        transform: scale(1.1);
    }

    .property-image .placeholder-icon {
        font-size: 4rem;
        color: rgba(255,255,255,0.7);
    }

    .property-status {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.75rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .status-available {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
    }

    .status-rented {
        background: linear-gradient(135deg, #dc3545, #fd7e14);
        color: white;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
    }

    .status-maintenance {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: white;
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
    }

    .property-info {
        padding: 25px;
    }

    .property-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 12px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.3;
    }

    .property-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 15px 0;
        border-bottom: 1px solid #eee;
    }

    .property-price {
        font-size: 1.6rem;
        font-weight: 800;
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .property-area {
        color: #7f8c8d;
        font-size: 0.95rem;
        font-weight: 500;
    }

    .property-features {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }

    .feature-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #34495e;
        font-size: 0.9rem;
        font-weight: 500;
        background: rgba(52, 73, 94, 0.05);
        padding: 6px 12px;
        border-radius: 15px;
        transition: all 0.3s ease;
    }

    .feature-item:hover {
        background: rgba(52, 73, 94, 0.1);
        transform: translateY(-1px);
    }

    .feature-item i {
        color: #667eea;
        font-size: 1rem;
    }

    .property-actions {
        display: flex;
        gap: 12px;
        justify-content: center;
        padding-top: 20px;
        border-top: 2px solid #f8f9fa;
    }

    .btn-action {
        flex: 1;
        padding: 12px 16px;
        border: none;
        border-radius: 12px;
        font-size: 0.9rem;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        text-decoration: none;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        position: relative;
        overflow: hidden;
    }

    .btn-action::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-action:hover::before {
        left: 100%;
    }

    .btn-view {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    .btn-view:hover {
        background: linear-gradient(135deg, #2980b9, #1f4e79);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
    }

    .btn-edit {
        background: linear-gradient(135deg, #f39c12, #e67e22);
        color: white;
        box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
    }

    .btn-edit:hover {
        background: linear-gradient(135deg, #e67e22, #d35400);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
    }

    .btn-delete {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
        box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
    }

    .btn-delete:hover {
        background: linear-gradient(135deg, #c0392b, #a93226);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
    }

    /* تصميم قسم الفلاتر المحسن */
    .search-filters {
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 40px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .filters-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 25px;
        color: #2c3e50;
        text-align: center;
    }

    .filter-group {
        margin-bottom: 20px;
    }

    .filter-group label {
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
        color: #34495e;
    }

    .filter-group .form-control,
    .filter-group .form-select {
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 12px 16px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
    }

    .filter-group .form-control:focus,
    .filter-group .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
    }

    /* تصميم قسم الإحصائيات المحسن */
    .stats-cards {
        margin-bottom: 40px;
    }

    .stat-card {
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        border: none;
        border-radius: 20px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        height: 100%;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .stat-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stat-icon {
        font-size: 3.5rem;
        margin-bottom: 20px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-number {
        font-size: 2.8rem;
        font-weight: 800;
        color: #2c3e50;
        margin-bottom: 8px;
    }

    .stat-label {
        color: #7f8c8d;
        font-size: 1.1rem;
        font-weight: 600;
    }

    /* تصميم رأس الصفحة المحسن */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 50px 0;
        margin-bottom: 50px;
        border-radius: 0 0 40px 40px;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    }

    .page-title {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 15px;
        position: relative;
        z-index: 2;
    }

    .page-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }

    .add-property-btn {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        border-radius: 50px;
        padding: 18px 35px;
        font-size: 1.1rem;
        font-weight: 700;
        color: white;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        position: relative;
        overflow: hidden;
        z-index: 2;
    }

    .add-property-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .add-property-btn:hover::before {
        left: 100%;
    }

    .add-property-btn:hover {
        background: linear-gradient(135deg, #20c997, #17a2b8);
        transform: translateY(-4px);
        box-shadow: 0 15px 35px rgba(40, 167, 69, 0.6);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 80px 20px;
        color: #7f8c8d;
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        margin: 40px 0;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }

    .empty-state i {
        font-size: 6rem;
        margin-bottom: 25px;
        background: linear-gradient(135deg, #bdc3c7, #95a5a6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .empty-state h3 {
        font-size: 1.8rem;
        margin-bottom: 15px;
        color: #34495e;
        font-weight: 700;
    }

    .empty-state p {
        font-size: 1.1rem;
        margin-bottom: 35px;
        color: #7f8c8d;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title mb-0">
                    <i class="fas fa-building me-3"></i>
                    إدارة العقارات المحسنة
                </h1>
                <p class="page-subtitle mb-0 mt-2">إدارة شاملة ومتطورة لجميع العقارات</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="add-property-btn" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة عقار جديد
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- إحصائيات سريعة -->
    <div class="row stats-cards">
        <div class="col-md-3 mb-4">
            <div class="card stat-card">
                <i class="stat-icon fas fa-building"></i>
                <div class="stat-number">{{ total_properties or 0 }}</div>
                <div class="stat-label">إجمالي العقارات</div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card stat-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                <i class="stat-icon fas fa-check-circle" style="color: white;"></i>
                <div class="stat-number" style="color: white;">{{ available_properties or 0 }}</div>
                <div class="stat-label" style="color: rgba(255,255,255,0.9);">عقارات متاحة</div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card stat-card" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); color: white;">
                <i class="stat-icon fas fa-home" style="color: white;"></i>
                <div class="stat-number" style="color: white;">{{ rented_properties or 0 }}</div>
                <div class="stat-label" style="color: rgba(255,255,255,0.9);">عقارات مؤجرة</div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card stat-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white;">
                <i class="stat-icon fas fa-tools" style="color: white;"></i>
                <div class="stat-number" style="color: white;">{{ maintenance_properties or 0 }}</div>
                <div class="stat-label" style="color: rgba(255,255,255,0.9);">تحت الصيانة</div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="search-filters">
        <h4 class="filters-title">
            <i class="fas fa-filter me-2"></i>
            فلاتر البحث والتصفية
        </h4>
        <form method="GET" id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <div class="filter-group">
                        <label for="search">البحث</label>
                        <input type="text" class="form-control" id="search" name="search"
                               placeholder="ابحث عن عقار..." value="{{ request.args.get('search', '') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="filter-group">
                        <label for="property_type">نوع العقار</label>
                        <select class="form-select" id="property_type" name="property_type">
                            <option value="">جميع الأنواع</option>
                            <option value="سكني" {{ 'selected' if request.args.get('property_type') == 'سكني' }}>سكني</option>
                            <option value="تجاري" {{ 'selected' if request.args.get('property_type') == 'تجاري' }}>تجاري</option>
                            <option value="إداري" {{ 'selected' if request.args.get('property_type') == 'إداري' }}>إداري</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="filter-group">
                        <label for="status">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="متاح" {{ 'selected' if request.args.get('status') == 'متاح' }}>متاح</option>
                            <option value="مؤجر" {{ 'selected' if request.args.get('status') == 'مؤجر' }}>مؤجر</option>
                            <option value="صيانة" {{ 'selected' if request.args.get('status') == 'صيانة' }}>صيانة</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="filter-group">
                        <label for="city">المدينة</label>
                        <select class="form-select" id="city" name="city">
                            <option value="">جميع المدن</option>
                            {% for city in cities %}
                            <option value="{{ city }}" {{ 'selected' if request.args.get('city') == city }}>{{ city }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12 text-center">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{{ url_for('properties_enhanced') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- قائمة العقارات -->
    <div class="row">
        {% if properties %}
            {% for property in properties %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card property-card">
                    <div class="property-image">
                        {% if property.images and property.images.first() %}
                            <img src="{{ property.images.first().image_path }}" alt="{{ property.name }}">
                        {% else %}
                            <i class="placeholder-icon fas fa-building"></i>
                        {% endif %}

                        <div class="property-status
                            {% if property.status == 'متاح' %}status-available
                            {% elif property.status == 'مؤجر' %}status-rented
                            {% else %}status-maintenance{% endif %}">
                            {{ property.status }}
                        </div>
                    </div>
                    
                    <div class="property-info">
                        <h5 class="property-title">{{ property.name }}</h5>
                        
                        <div class="property-details">
                            <div class="property-price">
                                {{ property.monthly_rent|number_format }} {{ property.currency }}
                                <small class="text-muted">/شهر</small>
                            </div>
                            <div class="property-area">
                                <i class="fas fa-expand-arrows-alt me-1"></i>
                                {{ property.area }} م²
                            </div>
                        </div>
                        
                        <div class="property-features">
                            <div class="feature-item">
                                <i class="fas fa-bed"></i>
                                <span>{{ property.rooms_count }}</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-bath"></i>
                                <span>{{ property.bathrooms_count or 0 }}</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ property.district }}</span>
                            </div>
                        </div>
                        
                        <div class="property-rating">
                            {% for i in range(1, 6) %}
                                <i class="fas fa-star star {% if i > (property.rating or 0) %}empty{% endif %}"></i>
                            {% endfor %}
                            <small class="text-muted ms-2">({{ property.rating or 0 }}/5)</small>
                        </div>
                        
                        <div class="property-actions mt-3">
                            <a href="{{ url_for('property_details', id=property.id) }}" class="btn btn-action btn-view">
                                <i class="fas fa-eye me-1"></i>
                                عرض
                            </a>
                            <a href="{{ url_for('edit_property', id=property.id) }}" class="btn btn-action btn-edit">
                                <i class="fas fa-edit me-1"></i>
                                تعديل
                            </a>
                            <button class="btn btn-action btn-delete" onclick="confirmDelete({{ property.id }}, '{{ property.name }}')">
                                <i class="fas fa-trash me-1"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="empty-state">
                    <i class="fas fa-building"></i>
                    <h4>لا توجد عقارات</h4>
                    <p>لم يتم العثور على عقارات تطابق معايير البحث</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة عقار جديد
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- زر الإضافة العائم -->
<button class="floating-add-btn" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
    <i class="fas fa-plus"></i>
</button>

<!-- تضمين مودال إضافة عقار -->
{% include 'properties/modals/add_property_modal.html' %}
{% endblock %}

{% block extra_js %}
<script>
// تأكيد الحذف
function confirmDelete(propertyId, propertyName) {
    if (confirm(`هل أنت متأكد من حذف العقار "${propertyName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // إرسال طلب الحذف
        fetch(`/properties/${propertyId}/delete`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrf_token]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف العقار');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف العقار');
        });
    }
}

// تحديث البحث التلقائي
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        filterForm.addEventListener('change', function() {
            this.submit();
        });
    }

    // تحسين تجربة المستخدم للفلاتر
    document.querySelectorAll('.filter-chip').forEach(chip => {
        chip.addEventListener('click', function() {
            this.classList.toggle('active');
        });
    });

    // إضافة تأثيرات التحميل للبطاقات
    const propertyCards = document.querySelectorAll('.property-card');
    propertyCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate__animated', 'animate__fadeInUp');
    });

    // تحسين تجربة المستخدم للمودال
    const addPropertyModal = document.getElementById('addPropertyModal');
    if (addPropertyModal) {
        addPropertyModal.addEventListener('show.bs.modal', function() {
            // تركيز على أول حقل في المودال
            setTimeout(() => {
                const firstInput = this.querySelector('input[type="text"]');
                if (firstInput) firstInput.focus();
            }, 500);
        });

        // تحديد action للفورم عند فتح المودال
        const form = addPropertyModal.querySelector('form');
        if (form && !form.action) {
            form.action = "{{ url_for('add_property') }}";
        }
    }
});

// تحديث إحصائيات الوقت الفعلي (اختياري)
function updateStats() {
    fetch('/api/properties/stats')
        .then(response => response.json())
        .then(data => {
            // تحديث الإحصائيات
            const statCards = document.querySelectorAll('.stat-number');
            if (statCards.length >= 4) {
                statCards[0].textContent = data.total || 0;
                statCards[1].textContent = data.available || 0;
                statCards[2].textContent = data.rented || 0;
                statCards[3].textContent = data.maintenance || 0;
            }
        })
        .catch(error => console.error('Error updating stats:', error));
}

// تحديث الإحصائيات كل دقيقة
setInterval(updateStats, 60000);

// إضافة تأثيرات بصرية للأزرار
document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('.btn-action');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
{% endblock %}
