{% extends "base.html" %}

{% block title %}إدارة العقارات المحسنة{% endblock %}

{% block extra_css %}
<style>
    .property-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    }
    
    .property-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    
    .property-image {
        height: 200px;
        background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
    }
    
    .property-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .property-card:hover .property-image img {
        transform: scale(1.05);
    }
    
    .property-status {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .status-available {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }
    
    .status-rented {
        background: linear-gradient(45deg, #dc3545, #fd7e14);
        color: white;
    }
    
    .status-maintenance {
        background: linear-gradient(45deg, #ffc107, #fd7e14);
        color: white;
    }
    
    .property-info {
        padding: 20px;
    }
    
    .property-title {
        font-size: 1.3rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .property-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .property-price {
        font-size: 1.4rem;
        font-weight: bold;
        color: #e74c3c;
    }
    
    .property-area {
        color: #7f8c8d;
        font-size: 0.9rem;
    }
    
    .property-features {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .feature-item {
        display: flex;
        align-items: center;
        gap: 5px;
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .property-actions {
        display: flex;
        gap: 10px;
        justify-content: space-between;
    }
    
    .btn-action {
        flex: 1;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .btn-view {
        background: linear-gradient(45deg, #3498db, #2980b9);
        border: none;
        color: white;
    }
    
    .btn-edit {
        background: linear-gradient(45deg, #f39c12, #e67e22);
        border: none;
        color: white;
    }
    
    .btn-delete {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        border: none;
        color: white;
    }
    
    .search-filters {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    }
    
    .stats-cards {
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 0 0 20px 20px;
    }
    
    .floating-add-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 20px rgba(40, 167, 69, 0.4);
        transition: all 0.3s ease;
        z-index: 1000;
    }
    
    .floating-add-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(40, 167, 69, 0.6);
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
    
    .filter-chip {
        display: inline-block;
        background: #e9ecef;
        color: #495057;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        margin: 2px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .filter-chip.active {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
    }
    
    .property-rating {
        display: flex;
        gap: 2px;
        margin-top: 10px;
    }
    
    .star {
        color: #ffc107;
        font-size: 0.9rem;
    }
    
    .star.empty {
        color: #e9ecef;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-0">
                    <i class="fas fa-building me-3"></i>
                    إدارة العقارات المحسنة
                </h1>
                <p class="mb-0 mt-2 opacity-75">إدارة شاملة ومتطورة لجميع العقارات</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة عقار جديد
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- إحصائيات سريعة -->
    <div class="row stats-cards">
        <div class="col-md-3">
            <div class="card stat-card">
                <div class="stat-number">{{ total_properties or 0 }}</div>
                <div class="stat-label">إجمالي العقارات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="stat-number">{{ available_properties or 0 }}</div>
                <div class="stat-label">عقارات متاحة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);">
                <div class="stat-number">{{ rented_properties or 0 }}</div>
                <div class="stat-label">عقارات مؤجرة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <div class="stat-number">{{ maintenance_properties or 0 }}</div>
                <div class="stat-label">تحت الصيانة</div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="search-filters">
        <form method="GET" id="searchForm">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" class="form-control" name="search" value="{{ request.args.get('search', '') }}" 
                           placeholder="اسم العقار، العنوان، أو الرمز">
                </div>
                <div class="col-md-2">
                    <label class="form-label">نوع العقار</label>
                    <select class="form-select" name="type">
                        <option value="">جميع الأنواع</option>
                        <option value="شقة" {{ 'selected' if request.args.get('type') == 'شقة' }}>شقة</option>
                        <option value="فيلا" {{ 'selected' if request.args.get('type') == 'فيلا' }}>فيلا</option>
                        <option value="مكتب" {{ 'selected' if request.args.get('type') == 'مكتب' }}>مكتب</option>
                        <option value="محل" {{ 'selected' if request.args.get('type') == 'محل' }}>محل</option>
                        <option value="مستودع" {{ 'selected' if request.args.get('type') == 'مستودع' }}>مستودع</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="متاح" {{ 'selected' if request.args.get('status') == 'متاح' }}>متاح</option>
                        <option value="مؤجر" {{ 'selected' if request.args.get('status') == 'مؤجر' }}>مؤجر</option>
                        <option value="صيانة" {{ 'selected' if request.args.get('status') == 'صيانة' }}>صيانة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">المدينة</label>
                    <select class="form-select" name="city">
                        <option value="">جميع المدن</option>
                        {% for city in cities %}
                        <option value="{{ city }}" {{ 'selected' if request.args.get('city') == city }}>{{ city }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">نطاق السعر</label>
                    <div class="row">
                        <div class="col-6">
                            <input type="number" class="form-control" name="min_price" 
                                   value="{{ request.args.get('min_price', '') }}" placeholder="من">
                        </div>
                        <div class="col-6">
                            <input type="number" class="form-control" name="max_price" 
                                   value="{{ request.args.get('max_price', '') }}" placeholder="إلى">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{{ url_for('properties_enhanced') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        مسح الفلاتر
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- قائمة العقارات -->
    <div class="row">
        {% if properties %}
            {% for property in properties %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card property-card">
                    <div class="property-image">
                        {% if property.images and property.images.first() %}
                            <img src="{{ property.images.first().image_path }}" alt="{{ property.name }}">
                        {% else %}
                            <div class="d-flex align-items-center justify-content-center h-100">
                                <i class="fas fa-building text-white" style="font-size: 3rem; opacity: 0.7;"></i>
                            </div>
                        {% endif %}
                        
                        <div class="property-status 
                            {% if property.status == 'متاح' %}status-available
                            {% elif property.status == 'مؤجر' %}status-rented
                            {% else %}status-maintenance{% endif %}">
                            {{ property.status }}
                        </div>
                    </div>
                    
                    <div class="property-info">
                        <h5 class="property-title">{{ property.name }}</h5>
                        
                        <div class="property-details">
                            <div class="property-price">
                                {{ property.monthly_rent|number_format }} {{ property.currency }}
                                <small class="text-muted">/شهر</small>
                            </div>
                            <div class="property-area">
                                <i class="fas fa-expand-arrows-alt me-1"></i>
                                {{ property.area }} م²
                            </div>
                        </div>
                        
                        <div class="property-features">
                            <div class="feature-item">
                                <i class="fas fa-bed"></i>
                                <span>{{ property.rooms_count }}</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-bath"></i>
                                <span>{{ property.bathrooms_count or 0 }}</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ property.district }}</span>
                            </div>
                        </div>
                        
                        <div class="property-rating">
                            {% for i in range(1, 6) %}
                                <i class="fas fa-star star {% if i > (property.rating or 0) %}empty{% endif %}"></i>
                            {% endfor %}
                            <small class="text-muted ms-2">({{ property.rating or 0 }}/5)</small>
                        </div>
                        
                        <div class="property-actions mt-3">
                            <a href="{{ url_for('property_details', id=property.id) }}" class="btn btn-action btn-view">
                                <i class="fas fa-eye me-1"></i>
                                عرض
                            </a>
                            <a href="{{ url_for('edit_property', id=property.id) }}" class="btn btn-action btn-edit">
                                <i class="fas fa-edit me-1"></i>
                                تعديل
                            </a>
                            <button class="btn btn-action btn-delete" onclick="confirmDelete({{ property.id }}, '{{ property.name }}')">
                                <i class="fas fa-trash me-1"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="empty-state">
                    <i class="fas fa-building"></i>
                    <h4>لا توجد عقارات</h4>
                    <p>لم يتم العثور على عقارات تطابق معايير البحث</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة عقار جديد
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- زر الإضافة العائم -->
<button class="floating-add-btn" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
    <i class="fas fa-plus"></i>
</button>

<!-- مودال إضافة عقار جديد -->
<div class="modal fade" id="addPropertyModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle me-2"></i>
                    إضافة عقار جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPropertyForm" method="POST" enctype="multipart/form-data">
                    {{ csrf_token() }}

                    <!-- تبويبات -->
                    <ul class="nav nav-tabs mb-4" id="propertyTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button">
                                <i class="fas fa-info-circle me-1"></i>
                                المعلومات الأساسية
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button">
                                <i class="fas fa-list-ul me-1"></i>
                                التفاصيل
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="financial-tab" data-bs-toggle="tab" data-bs-target="#financial" type="button">
                                <i class="fas fa-dollar-sign me-1"></i>
                                المعلومات المالية
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="media-tab" data-bs-toggle="tab" data-bs-target="#media" type="button">
                                <i class="fas fa-images me-1"></i>
                                الصور والمستندات
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="propertyTabsContent">
                        <!-- تبويب المعلومات الأساسية -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">اسم العقار *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رمز العقار</label>
                                        <input type="text" class="form-control" name="property_code"
                                               placeholder="سيتم إنشاؤه تلقائياً إذا ترك فارغاً">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">نوع العقار *</label>
                                        <select class="form-select" name="property_type" required>
                                            <option value="">اختر النوع</option>
                                            <option value="شقة">شقة</option>
                                            <option value="فيلا">فيلا</option>
                                            <option value="مكتب">مكتب</option>
                                            <option value="محل">محل تجاري</option>
                                            <option value="مستودع">مستودع</option>
                                            <option value="أرض">أرض</option>
                                            <option value="عمارة">عمارة</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">حالة العقار *</label>
                                        <select class="form-select" name="status" required>
                                            <option value="متاح">متاح للإيجار</option>
                                            <option value="مؤجر">مؤجر</option>
                                            <option value="صيانة">تحت الصيانة</option>
                                            <option value="غير متاح">غير متاح</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">المساحة (م²) *</label>
                                        <input type="number" class="form-control" name="area" step="0.01" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">العنوان *</label>
                                        <input type="text" class="form-control" name="address" required>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">المدينة *</label>
                                        <input type="text" class="form-control" name="city" required>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">الحي</label>
                                        <input type="text" class="form-control" name="district">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">وصف العقار</label>
                                        <textarea class="form-control" name="description" rows="3"
                                                  placeholder="وصف تفصيلي للعقار وميزاته"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب التفاصيل -->
                        <div class="tab-pane fade" id="details" role="tabpanel">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">عدد الغرف</label>
                                        <input type="number" class="form-control" name="rooms_count" min="0">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">عدد دورات المياه</label>
                                        <input type="number" class="form-control" name="bathrooms_count" min="0">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">عدد المواقف</label>
                                        <input type="number" class="form-control" name="parking_spaces" min="0">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">الطابق</label>
                                        <input type="number" class="form-control" name="floor_number">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">سنة البناء</label>
                                        <input type="number" class="form-control" name="construction_year"
                                               min="1900" max="2030">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">حالة العقار</label>
                                        <select class="form-select" name="condition">
                                            <option value="">اختر الحالة</option>
                                            <option value="ممتاز">ممتاز</option>
                                            <option value="جيد جداً">جيد جداً</option>
                                            <option value="جيد">جيد</option>
                                            <option value="مقبول">مقبول</option>
                                            <option value="يحتاج صيانة">يحتاج صيانة</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">نوع التشطيب</label>
                                        <select class="form-select" name="finishing_type">
                                            <option value="">اختر نوع التشطيب</option>
                                            <option value="سوبر لوكس">سوبر لوكس</option>
                                            <option value="لوكس">لوكس</option>
                                            <option value="عادي">عادي</option>
                                            <option value="بدون تشطيب">بدون تشطيب</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">المرافق والخدمات</label>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="amenities" value="مصعد">
                                                    <label class="form-check-label">مصعد</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="amenities" value="حارس">
                                                    <label class="form-check-label">حارس</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="amenities" value="حديقة">
                                                    <label class="form-check-label">حديقة</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="amenities" value="مسبح">
                                                    <label class="form-check-label">مسبح</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="amenities" value="جيم">
                                                    <label class="form-check-label">صالة رياضية</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="amenities" value="مولد كهرباء">
                                                    <label class="form-check-label">مولد كهرباء</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="amenities" value="تكييف مركزي">
                                                    <label class="form-check-label">تكييف مركزي</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="amenities" value="انترنت">
                                                    <label class="form-check-label">إنترنت</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب المعلومات المالية -->
                        <div class="tab-pane fade" id="financial" role="tabpanel">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">الإيجار الشهري *</label>
                                        <input type="number" class="form-control" name="monthly_rent" step="0.01" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">العملة</label>
                                        <select class="form-select" name="currency">
                                            <option value="ريال">ريال سعودي</option>
                                            <option value="دولار">دولار أمريكي</option>
                                            <option value="يورو">يورو</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">مبلغ التأمين</label>
                                        <input type="number" class="form-control" name="security_deposit" step="0.01">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">عمولة الوسيط</label>
                                        <input type="number" class="form-control" name="broker_commission" step="0.01">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رسوم الخدمات الشهرية</label>
                                        <input type="number" class="form-control" name="service_charges" step="0.01">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">ملاحظات مالية</label>
                                        <textarea class="form-control" name="financial_notes" rows="3"
                                                  placeholder="أي ملاحظات إضافية حول الجوانب المالية"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب الصور والمستندات -->
                        <div class="tab-pane fade" id="media" role="tabpanel">
                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-4">
                                        <label class="form-label">صور العقار</label>
                                        <div class="border rounded p-3" style="border-style: dashed !important;">
                                            <input type="file" class="form-control" name="property_images"
                                                   multiple accept="image/*" id="propertyImages">
                                            <div class="text-center mt-2">
                                                <i class="fas fa-cloud-upload-alt fa-2x text-muted"></i>
                                                <p class="text-muted mt-2">اسحب الصور هنا أو انقر للاختيار</p>
                                                <small class="text-muted">يمكن رفع عدة صور (JPG, PNG, GIF)</small>
                                            </div>
                                        </div>
                                        <div id="imagePreview" class="mt-3"></div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">مستندات العقار</label>
                                        <div class="border rounded p-3" style="border-style: dashed !important;">
                                            <input type="file" class="form-control" name="property_documents"
                                                   multiple accept=".pdf,.doc,.docx,.jpg,.png" id="propertyDocuments">
                                            <div class="text-center mt-2">
                                                <i class="fas fa-file-upload fa-2x text-muted"></i>
                                                <p class="text-muted mt-2">اسحب المستندات هنا أو انقر للاختيار</p>
                                                <small class="text-muted">PDF, Word, أو صور</small>
                                            </div>
                                        </div>
                                        <div id="documentPreview" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="submit" form="addPropertyForm" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    حفظ العقار
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تأكيد الحذف
function confirmDelete(propertyId, propertyName) {
    if (confirm(`هل أنت متأكد من حذف العقار "${propertyName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // إرسال طلب الحذف
        fetch(`/properties/${propertyId}/delete`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrf_token]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف العقار');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف العقار');
        });
    }
}

// معاينة الصور
document.getElementById('propertyImages').addEventListener('change', function(e) {
    const preview = document.getElementById('imagePreview');
    preview.innerHTML = '';

    Array.from(e.target.files).forEach(file => {
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const div = document.createElement('div');
                div.className = 'd-inline-block me-2 mb-2';
                div.innerHTML = `
                    <img src="${e.target.result}" class="img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;">
                    <div class="text-center mt-1">
                        <small class="text-muted">${file.name}</small>
                    </div>
                `;
                preview.appendChild(div);
            };
            reader.readAsDataURL(file);
        }
    });
});

// معاينة المستندات
document.getElementById('propertyDocuments').addEventListener('change', function(e) {
    const preview = document.getElementById('documentPreview');
    preview.innerHTML = '';

    Array.from(e.target.files).forEach(file => {
        const div = document.createElement('div');
        div.className = 'alert alert-info d-inline-block me-2 mb-2';
        div.innerHTML = `
            <i class="fas fa-file me-2"></i>
            <small>${file.name}</small>
        `;
        preview.appendChild(div);
    });
});

// تحديث البحث التلقائي
document.getElementById('searchForm').addEventListener('change', function() {
    this.submit();
});

// تحسين تجربة المستخدم للفلاتر
document.querySelectorAll('.filter-chip').forEach(chip => {
    chip.addEventListener('click', function() {
        this.classList.toggle('active');
        // يمكن إضافة منطق إضافي هنا للفلترة
    });
});

// تحديث إحصائيات الوقت الفعلي (اختياري)
function updateStats() {
    fetch('/api/properties/stats')
        .then(response => response.json())
        .then(data => {
            // تحديث الإحصائيات
            document.querySelector('.stat-card:nth-child(1) .stat-number').textContent = data.total || 0;
            document.querySelector('.stat-card:nth-child(2) .stat-number').textContent = data.available || 0;
            document.querySelector('.stat-card:nth-child(3) .stat-number').textContent = data.rented || 0;
            document.querySelector('.stat-card:nth-child(4) .stat-number').textContent = data.maintenance || 0;
        })
        .catch(error => console.error('Error updating stats:', error));
}

// تحديث الإحصائيات كل 30 ثانية
setInterval(updateStats, 30000);
</script>
{% endblock %}
