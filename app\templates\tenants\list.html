{% extends 'base.html' %}
{% block title %}قائمة المستأجرين{% endblock %}
{% block head %}
<style>
    .avatar-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: linear-gradient(135deg, #ffc107 60%, #6c757d 100%);
        color: #fff;
        font-weight: bold;
        font-size: 1.1rem;
        margin-left: 8px;
        box-shadow: 0 2px 6px #eee;
    }
    .table thead th { vertical-align: middle; }
</style>
{% endblock %}
{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-users"></i> إدارة المستأجرين</h1>
                    <p>عرض وإدارة جميع المستأجرين المسجلين في النظام</p>
                </div>
                <div class="page-actions">
                    <a href="{{ url_for('add_tenant') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مستأجر جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list text-primary me-2"></i>
                            قائمة المستأجرين
                        </h5>
                        <span class="badge bg-primary">{{ tenants|length }} مستأجر</span>
                    </div>
                </div>
                <div class="card-body">
    <div class="btn-group mb-3">
        <a href="{{ url_for('add_tenant') }}" class="btn btn-success"><i class="fa fa-plus"></i> إضافة مستأجر جديد</a>
        <button type="button" class="btn btn-primary"
                onclick="openFormModal('{{ url_for('modal_add_tenant') }}', 'إضافة مستأجر جديد', {
                    size: 'xl',
                    onSuccess: function() { window.location.reload(); }
                })"
                >
            <i class="fa fa-plus-circle"></i> إضافة سريعة
        </button>
    </div>
    <div class="d-flex justify-content-between align-items-center mb-3">
        <form method="GET" class="d-flex" style="max-width:350px;">
            <input type="text" name="q" class="form-control me-2" placeholder="بحث..." value="{{ request.args.get('q','') }}">
            <button type="submit" class="btn btn-outline-primary"><i class="fa fa-search"></i></button>
        </form>
        <button onclick="window.print()" class="btn btn-outline-dark"><i class="fa fa-print"></i> طباعة</button>
    </div>
    <div class="table-responsive">
    <table class="table table-bordered table-hover align-middle text-center bg-white shadow-sm">
        <thead class="table-warning">
            <tr>
                <th>الاسم</th>
                <th>الهاتف</th>
                <th>البريد الإلكتروني</th>
                <th>العنوان</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
        {% for tenant in tenants %}
            <tr>
                <td>
                    <span class="avatar-badge" title="{{ tenant.name }}">{{ tenant.name[:2] }}</span>
                    <span data-bs-toggle="tooltip" title="{{ tenant.name }}">{{ tenant.name }}</span>
                </td>
                <td>{{ tenant.phone }}</td>
                <td>{{ tenant.email }}</td>
                <td>{{ tenant.address }}</td>
                <td>
                    <a href="{{ url_for('edit_tenant', tenant_id=tenant.id) }}" class="btn btn-primary btn-sm">تعديل</a>
                    <form action="{{ url_for('delete_tenant', tenant_id=tenant.id) }}" method="post" style="display:inline;">
                        {% if csrf_token is defined and csrf_token %}<input type="hidden" name="csrf_token" value="{{ csrf_token()|safe }}">{% endif %}
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟');">حذف</button>
                    </form>
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script>
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl)
    });
</script>
{% endblock %}
