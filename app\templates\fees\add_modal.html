<!-- نموذج إضافة أتعاب جديدة -->
<form id="addFeeForm" method="POST" action="{{ url_for('modal_add_fee') }}">
    <div class="modal-body" dir="rtl">
        <div class="row">
            <!-- معلومات أساسية -->
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fa fa-info-circle"></i> المعلومات الأساسية</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">العميل <span class="text-danger">*</span></label>
                            <select name="client_id" class="form-select" required>
                                <option value="">اختر العميل</option>
                                {% for client in clients %}
                                <option value="{{ client.id }}">{{ client.name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">القضية (اختياري)</label>
                            <select name="case_id" class="form-select">
                                <option value="">اختر القضية</option>
                                {% for case in cases %}
                                <option value="{{ case.id }}">{{ case.case_number }} - {{ case.title }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع الأتعاب <span class="text-danger">*</span></label>
                                    <select name="fee_type" class="form-select" required>
                                        <option value="">اختر النوع</option>
                                        <option value="استشارة قانونية">استشارة قانونية</option>
                                        <option value="ترافع أمام المحاكم">ترافع أمام المحاكم</option>
                                        <option value="صياغة عقود">صياغة عقود</option>
                                        <option value="تحصيل ديون">تحصيل ديون</option>
                                        <option value="إجراءات إدارية">إجراءات إدارية</option>
                                        <option value="تمثيل قانوني">تمثيل قانوني</option>
                                        <option value="مراجعة قانونية">مراجعة قانونية</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">فئة الخدمة <span class="text-danger">*</span></label>
                                    <select name="service_category" class="form-select" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="قانون مدني">قانون مدني</option>
                                        <option value="قانون جنائي">قانون جنائي</option>
                                        <option value="قانون تجاري">قانون تجاري</option>
                                        <option value="قانون عمل">قانون عمل</option>
                                        <option value="قانون أسرة">قانون أسرة</option>
                                        <option value="قانون عقاري">قانون عقاري</option>
                                        <option value="قانون إداري">قانون إداري</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وصف الخدمة <span class="text-danger">*</span></label>
                            <textarea name="description" class="form-control" rows="3" required 
                                      placeholder="وصف تفصيلي للخدمة المقدمة..."></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المعلومات المالية -->
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fa fa-money-bill-wave"></i> المعلومات المالية</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المبلغ الأساسي <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" name="base_amount" class="form-control" 
                                               step="0.01" min="0" required id="baseAmount">
                                        <span class="input-group-text">₪</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رسوم إضافية</label>
                                    <div class="input-group">
                                        <input type="number" name="additional_fees" class="form-control" 
                                               step="0.01" min="0" value="0" id="additionalFees">
                                        <span class="input-group-text">₪</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">خصم</label>
                                    <div class="input-group">
                                        <input type="number" name="discount_amount" class="form-control" 
                                               step="0.01" min="0" value="0" id="discountAmount">
                                        <span class="input-group-text">₪</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ضريبة</label>
                                    <div class="input-group">
                                        <input type="number" name="tax_amount" class="form-control" 
                                               step="0.01" min="0" value="0" id="taxAmount">
                                        <span class="input-group-text">₪</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">المجموع الإجمالي</label>
                            <div class="input-group">
                                <input type="number" name="total_amount" class="form-control bg-light" 
                                       readonly id="totalAmount">
                                <span class="input-group-text">₪</span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الأولوية</label>
                                    <select name="priority" class="form-select">
                                        <option value="منخفضة">منخفضة</option>
                                        <option value="متوسطة" selected>متوسطة</option>
                                        <option value="عالية">عالية</option>
                                        <option value="عاجلة">عاجلة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">العملة</label>
                                    <select name="currency" class="form-select">
                                        <option value="شيكل" selected>شيكل إسرائيلي</option>
                                        <option value="دولار">دولار أمريكي</option>
                                        <option value="يورو">يورو</option>
                                        <option value="دينار">دينار أردني</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- التواريخ وشروط الدفع -->
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fa fa-calendar"></i> التواريخ</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الخدمة <span class="text-danger">*</span></label>
                                    <input type="date" name="service_date" class="form-control" 
                                           value="{{ today }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الاستحقاق <span class="text-danger">*</span></label>
                                    <input type="date" name="due_date" class="form-control" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">شروط الدفع</label>
                            <select name="payment_terms" class="form-select">
                                <option value="فوري">دفع فوري</option>
                                <option value="خلال 7 أيام">خلال 7 أيام</option>
                                <option value="خلال 15 يوم">خلال 15 يوم</option>
                                <option value="خلال 30 يوم" selected>خلال 30 يوم</option>
                                <option value="خلال 60 يوم">خلال 60 يوم</option>
                                <option value="خلال 90 يوم">خلال 90 يوم</option>
                                <option value="حسب الاتفاق">حسب الاتفاق</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0"><i class="fa fa-cog"></i> إعدادات إضافية</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="is_recurring" class="form-check-input" id="isRecurring">
                                <label class="form-check-label" for="isRecurring">
                                    أتعاب دورية
                                </label>
                            </div>
                        </div>

                        <div class="mb-3" id="recurringPeriodDiv" style="display: none;">
                            <label class="form-label">فترة التكرار</label>
                            <select name="recurring_period" class="form-select">
                                <option value="شهري">شهري</option>
                                <option value="ربع سنوي">ربع سنوي</option>
                                <option value="نصف سنوي">نصف سنوي</option>
                                <option value="سنوي">سنوي</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea name="notes" class="form-control" rows="3" 
                                      placeholder="أي ملاحظات إضافية..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="fa fa-times"></i> إلغاء
        </button>
        <button type="submit" class="btn btn-success">
            <i class="fa fa-save"></i> حفظ الأتعاب
        </button>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // حساب المجموع الإجمالي تلقائياً
    function calculateTotal() {
        const baseAmount = parseFloat(document.getElementById('baseAmount').value) || 0;
        const additionalFees = parseFloat(document.getElementById('additionalFees').value) || 0;
        const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
        const taxAmount = parseFloat(document.getElementById('taxAmount').value) || 0;
        
        const total = baseAmount + additionalFees - discountAmount + taxAmount;
        document.getElementById('totalAmount').value = total.toFixed(2);
    }

    // ربط الأحداث
    document.getElementById('baseAmount').addEventListener('input', calculateTotal);
    document.getElementById('additionalFees').addEventListener('input', calculateTotal);
    document.getElementById('discountAmount').addEventListener('input', calculateTotal);
    document.getElementById('taxAmount').addEventListener('input', calculateTotal);

    // إظهار/إخفاء فترة التكرار
    document.getElementById('isRecurring').addEventListener('change', function() {
        const recurringDiv = document.getElementById('recurringPeriodDiv');
        recurringDiv.style.display = this.checked ? 'block' : 'none';
    });

    // تحديد تاريخ الاستحقاق بناءً على شروط الدفع
    document.querySelector('select[name="payment_terms"]').addEventListener('change', function() {
        const serviceDate = document.querySelector('input[name="service_date"]').value;
        if (serviceDate) {
            const date = new Date(serviceDate);
            const terms = this.value;
            
            if (terms.includes('7 أيام')) {
                date.setDate(date.getDate() + 7);
            } else if (terms.includes('15 يوم')) {
                date.setDate(date.getDate() + 15);
            } else if (terms.includes('30 يوم')) {
                date.setDate(date.getDate() + 30);
            } else if (terms.includes('60 يوم')) {
                date.setDate(date.getDate() + 60);
            } else if (terms.includes('90 يوم')) {
                date.setDate(date.getDate() + 90);
            }
            
            if (terms !== 'حسب الاتفاق') {
                document.querySelector('input[name="due_date"]').value = date.toISOString().split('T')[0];
            }
        }
    });

    // تحديث القضايا عند تغيير العميل
    document.querySelector('select[name="client_id"]').addEventListener('change', function() {
        const clientId = this.value;
        const caseSelect = document.querySelector('select[name="case_id"]');
        
        // مسح القضايا الحالية
        caseSelect.innerHTML = '<option value="">اختر القضية</option>';
        
        if (clientId) {
            // جلب قضايا العميل (يمكن تحسينها بـ AJAX)
            fetch(`/api/client/${clientId}/cases`)
                .then(response => response.json())
                .then(cases => {
                    cases.forEach(case => {
                        const option = document.createElement('option');
                        option.value = case.id;
                        option.textContent = `${case.case_number} - ${case.title}`;
                        caseSelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Error fetching cases:', error));
        }
    });
});
</script>
