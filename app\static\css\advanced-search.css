/**
 * Advanced Search and Filtering System Styles
 * أنماط نظام البحث والتصفية المتقدم
 */

/* الحاوي الرئيسي للبحث */
.advanced-search-container {
    background: var(--theme-bg-primary);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    padding: 25px;
    border: 1px solid var(--theme-border-color);
    transition: all 0.3s ease;
}

/* شريط البحث الرئيسي */
.main-search-bar {
    position: relative;
    margin-bottom: 20px;
}

.search-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
    background: var(--theme-bg-secondary);
    border-radius: 50px;
    padding: 8px;
    border: 2px solid var(--theme-border-color);
    transition: all 0.3s ease;
}

.search-input-group:focus-within {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 12px 20px;
    font-size: 16px;
    color: var(--theme-text-primary);
    outline: none;
}

.search-input::placeholder {
    color: var(--theme-text-secondary);
}

.search-buttons {
    display: flex;
    gap: 8px;
}

.search-buttons .btn {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.search-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* اقتراحات البحث */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--theme-bg-primary);
    border: 1px solid var(--theme-border-color);
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 5px;
}

.suggestions-list {
    padding: 10px 0;
}

.suggestion-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--theme-border-color);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover {
    background: var(--theme-bg-tertiary);
    padding-right: 25px;
}

.suggestion-text {
    flex: 1;
    font-weight: 500;
    color: var(--theme-text-primary);
}

.suggestion-type {
    font-size: 0.85em;
    color: var(--theme-text-secondary);
    background: var(--theme-bg-tertiary);
    padding: 4px 8px;
    border-radius: 12px;
}

/* الفلاتر المتقدمة */
.advanced-filters {
    background: var(--theme-bg-secondary);
    border-radius: 12px;
    padding: 20px;
    margin-top: 20px;
    border: 1px solid var(--theme-border-color);
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--theme-border-color);
}

.filters-header h5 {
    margin: 0;
    color: var(--theme-text-primary);
}

.filters-content .form-label {
    font-weight: 600;
    color: var(--theme-text-primary);
    margin-bottom: 8px;
}

.filters-content .form-select,
.filters-content .form-control {
    background: var(--theme-bg-primary);
    border: 1px solid var(--theme-border-color);
    color: var(--theme-text-primary);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.filters-content .form-select:focus,
.filters-content .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.filters-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid var(--theme-border-color);
}

/* نتائج البحث */
.search-results {
    margin-top: 30px;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: var(--theme-bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--theme-border-color);
}

.search-results-header h4 {
    margin: 0;
    color: var(--theme-text-primary);
}

.results-actions {
    display: flex;
    gap: 10px;
}

.search-results-tabs .nav-tabs {
    border-bottom: 2px solid var(--theme-border-color);
    margin-bottom: 20px;
}

.search-results-tabs .nav-link {
    color: var(--theme-text-secondary);
    border: none;
    border-bottom: 3px solid transparent;
    padding: 12px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.search-results-tabs .nav-link:hover {
    color: var(--theme-text-primary);
    background: var(--theme-bg-tertiary);
}

.search-results-tabs .nav-link.active {
    color: #0d6efd;
    background: transparent;
    border-bottom-color: #0d6efd;
}

.section-results {
    background: var(--theme-bg-primary);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--theme-border-color);
}

.section-results .table {
    margin: 0;
    color: var(--theme-text-primary);
}

.section-results .table th {
    background: var(--theme-bg-tertiary);
    border-color: var(--theme-border-color);
    color: var(--theme-text-primary);
    font-weight: 600;
}

.section-results .table td {
    border-color: var(--theme-border-color);
    vertical-align: middle;
}

.section-results .table tbody tr:hover {
    background: var(--theme-bg-tertiary);
}

/* حالة عدم وجود نتائج */
.no-results {
    text-align: center;
    padding: 60px 20px;
    color: var(--theme-text-secondary);
}

.no-results i {
    opacity: 0.5;
}

/* مؤشر التحميل */
.loading-indicator {
    color: var(--theme-text-secondary);
}

.loading-indicator .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* تاريخ البحث */
.search-history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid var(--theme-border-color);
    border-radius: 8px;
    margin-bottom: 10px;
    background: var(--theme-bg-secondary);
    transition: all 0.3s ease;
}

.history-item:hover {
    background: var(--theme-bg-tertiary);
    transform: translateX(-5px);
}

.history-content {
    flex: 1;
}

.history-term {
    font-weight: 600;
    color: var(--theme-text-primary);
    margin-bottom: 5px;
}

.history-date {
    font-size: 0.85em;
    color: var(--theme-text-secondary);
}

.history-actions {
    display: flex;
    gap: 8px;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .advanced-search-container {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .search-input-group {
        flex-direction: column;
        border-radius: 12px;
        padding: 15px;
    }
    
    .search-input {
        padding: 15px;
        text-align: center;
    }
    
    .search-buttons {
        justify-content: center;
        width: 100%;
    }
    
    .filters-content .row {
        margin: 0;
    }
    
    .filters-content .col-md-3,
    .filters-content .col-md-4,
    .filters-content .col-md-6 {
        padding: 0;
        margin-bottom: 15px;
    }
    
    .search-results-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .results-actions {
        justify-content: center;
    }
    
    .history-item {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .history-actions {
        justify-content: center;
    }
}

/* طباعة */
@media print {
    .advanced-search-container,
    .search-suggestions,
    .advanced-filters,
    .search-results-header .results-actions,
    .no-print {
        display: none !important;
    }
    
    .search-results {
        margin-top: 0;
    }
    
    .section-results {
        border: none;
        box-shadow: none;
    }
}

/* الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .advanced-search-container {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    
    .search-suggestions {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    }
}
