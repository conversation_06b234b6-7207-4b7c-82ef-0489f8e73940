#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime

def test_comprehensive_fixes():
    """اختبار شامل لجميع الإصلاحات المطبقة"""
    
    print("🚀 بدء الاختبار الشامل للإصلاحات...")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    session = requests.Session()
    
    # تسجيل الدخول
    print("🔐 تسجيل الدخول...")

    # الحصول على صفحة تسجيل الدخول أولاً
    login_page = session.get(f"{base_url}/login")
    if login_page.status_code != 200:
        print("❌ فشل في الوصول لصفحة تسجيل الدخول")
        return False

    login_data = {
        'username': 'office',
        'password': '66889088'
    }

    login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
    if login_response.status_code not in [200, 302]:
        print(f"❌ فشل في تسجيل الدخول - كود الحالة: {login_response.status_code}")
        return False
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # الحصول على CSRF token
    dashboard_response = session.get(f"{base_url}/dashboard")
    csrf_token = None
    if 'csrf_token' in dashboard_response.text:
        # استخراج CSRF token من الصفحة
        import re
        csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', dashboard_response.text)
        if csrf_match:
            csrf_token = csrf_match.group(1)
    
    if not csrf_token:
        print("⚠️ لم يتم العثور على CSRF token")
        return False
    
    print(f"🔑 تم الحصول على CSRF token: {csrf_token[:20]}...")
    
    # اختبار إضافة العقار (تم إصلاحه)
    print("\n🏢 اختبار إضافة عقار...")
    property_data = {
        'csrf_token': csrf_token,
        'name': f'عقار اختبار {datetime.now().strftime("%H%M%S")}',
        'type': 'شقة',
        'address': 'عنوان اختبار',
        'description': 'وصف اختبار',
        'area': '100',
        'rooms_count': '3',
        'bathrooms_count': '2',
        'monthly_rent': '800',
        'currency': 'شيكل',
        'status': 'متاح',
        'owner_name': 'مالك اختبار',
        'owner_phone': '0501234567',
        'notes': 'ملاحظات اختبار'
    }
    
    property_response = session.post(
        f"{base_url}/properties/add",
        data=property_data,
        headers={'X-CSRFToken': csrf_token}
    )
    
    if property_response.status_code == 200:
        try:
            property_result = property_response.json()
            if property_result.get('success'):
                print("✅ تم إضافة العقار بنجاح")
            else:
                print(f"❌ فشل في إضافة العقار: {property_result.get('message', 'خطأ غير معروف')}")
        except:
            print("❌ خطأ في تحليل استجابة إضافة العقار")
    else:
        print(f"❌ فشل في إضافة العقار - كود الحالة: {property_response.status_code}")
    
    # اختبار إضافة المستأجر (تم إصلاحه)
    print("\n👤 اختبار إضافة مستأجر...")
    tenant_data = {
        'csrf_token': csrf_token,
        'name': f'مستأجر اختبار {datetime.now().strftime("%H%M%S")}',
        'phone': '0501234567',
        'email': '<EMAIL>',
        'address': 'عنوان مستأجر اختبار',
        'national_id': '123456789',
        'occupation': 'مهندس',
        'emergency_contact': 'جهة طوارئ',
        'notes': 'ملاحظات مستأجر'
    }
    
    tenant_response = session.post(
        f"{base_url}/tenants/add",
        data=tenant_data,
        headers={'X-CSRFToken': csrf_token}
    )
    
    if tenant_response.status_code == 200:
        try:
            tenant_result = tenant_response.json()
            if tenant_result.get('success'):
                print("✅ تم إضافة المستأجر بنجاح")
            else:
                print(f"❌ فشل في إضافة المستأجر: {tenant_result.get('message', 'خطأ غير معروف')}")
        except:
            print("❌ خطأ في تحليل استجابة إضافة المستأجر")
    else:
        print(f"❌ فشل في إضافة المستأجر - كود الحالة: {tenant_response.status_code}")
    
    # اختبار إضافة عقد إيجار محسن
    print("\n📄 اختبار إضافة عقد إيجار محسن...")
    lease_data = {
        'csrf_token': csrf_token,
        'property_id': '1',
        'tenant_id': '1',
        'start_date': '2024-01-01',
        'end_date': '2024-12-31',
        'monthly_rent': '800',
        'annual_rent': '9600',
        'payment_frequency': 'شهري',
        'currency': 'شيكل',
        'security_deposit': '800',
        'payment_due_day': '1',
        'auto_renewal': 'on',
        'status': 'نشط',
        'terms': 'شروط العقد',
        'notes': 'ملاحظات العقد'
    }
    
    lease_response = session.post(
        f"{base_url}/leases/add",
        data=lease_data,
        headers={'X-CSRFToken': csrf_token}
    )
    
    if lease_response.status_code == 200:
        try:
            lease_result = lease_response.json()
            if lease_result.get('success'):
                print("✅ تم إضافة عقد الإيجار المحسن بنجاح")
            else:
                print(f"❌ فشل في إضافة عقد الإيجار: {lease_result.get('message', 'خطأ غير معروف')}")
        except:
            print("❌ خطأ في تحليل استجابة إضافة عقد الإيجار")
    else:
        print(f"❌ فشل في إضافة عقد الإيجار - كود الحالة: {lease_response.status_code}")
    
    print("\n" + "=" * 60)
    print("🎉 انتهى الاختبار الشامل للإصلاحات")
    
    return True

if __name__ == "__main__":
    test_comprehensive_fixes()
