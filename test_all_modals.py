#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import re
from bs4 import BeautifulSoup

BASE_URL = "http://localhost:5000"

def test_all_modals():
    """اختبار جميع الـ modals"""
    print("🧪 اختبار جميع الـ modals...")
    
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {
        'username': 'office',
        'password': '66889088'
    }
    
    login_response = session.post(f"{BASE_URL}/lawyersameh", data=login_data)
    if login_response.status_code != 200:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # قائمة الـ modals للاختبار
    modals_to_test = [
        {'name': 'إضافة عميل', 'url': '/modal/add_client'},
        {'name': 'إضافة قضية', 'url': '/modal/add_case'},
        {'name': 'إضافة عقار', 'url': '/modal/add_property'},
        {'name': 'إضافة مستأجر', 'url': '/modal/add_tenant'},
        {'name': 'إضافة دين', 'url': '/modal/add_debt'},
        {'name': 'إضافة مصروف', 'url': '/modal/add_expense'},
        {'name': 'إضافة سند مالي', 'url': '/modal/add_financial_transaction'},
        {'name': 'إضافة عقد إيجار', 'url': '/modal/add_lease'},
        {'name': 'إضافة إيراد إيجار', 'url': '/modal/add_rental_income'}
    ]
    
    successful_modals = 0
    total_modals = len(modals_to_test)
    
    for modal in modals_to_test:
        print(f"\n🔍 اختبار {modal['name']}...")
        
        # الحصول على الـ modal
        modal_response = session.get(f"{BASE_URL}{modal['url']}")
        
        if modal_response.status_code != 200:
            print(f"  ❌ فشل في الوصول للـ modal: {modal_response.status_code}")
            continue
        
        # البحث عن CSRF token
        soup = BeautifulSoup(modal_response.text, 'html.parser')
        csrf_input = soup.find('input', {'name': 'csrf_token'})
        
        if csrf_input and csrf_input.get('value'):
            print(f"  ✅ CSRF token موجود: {csrf_input.get('value')[:15]}...")
            successful_modals += 1
        else:
            print(f"  ❌ CSRF token مفقود")
            # طباعة جزء من محتوى الـ modal للتشخيص
            print(f"  📄 محتوى الـ modal: {modal_response.text[:200]}...")
    
    print(f"\n📊 النتائج النهائية:")
    print(f"  ✅ Modals ناجحة: {successful_modals}/{total_modals}")
    print(f"  📈 معدل النجاح: {(successful_modals/total_modals)*100:.1f}%")
    
    if successful_modals == total_modals:
        print("🎉 جميع الـ modals تعمل بشكل صحيح!")
        return True
    else:
        print("⚠️ بعض الـ modals تحتاج إصلاح")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار جميع الـ modals...")
    print("=" * 60)
    
    success = test_all_modals()
    
    print("=" * 60)
    if success:
        print("🎉 جميع الـ modals تعمل بشكل ممتاز!")
    else:
        print("💥 بعض الـ modals تحتاج إصلاح")
