{% extends "base.html" %}

{% block title %}إضافة عقار{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-building"></i> إضافة عقار جديد</h1>
                    <p>إضافة عقار جديد إلى قاعدة البيانات</p>
                </div>
                <div class="page-actions">
                    <a href="{{ url_for('properties_list') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>رجوع
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-home text-primary me-2"></i>
                        بيانات العقار
                    </h5>
                </div>
                <div class="card-body">
        <form method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <div class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">اسم العقار</label>
                    <input type="text" name="name" class="form-control" required placeholder="مثال: مكتب رقم 5، محل تجاري...">
                </div>
                <div class="col-md-6">
                    <label class="form-label">نوع العقار</label>
                    <select name="property_type" class="form-control">
                        <option value="شقة">شقة</option>
                        <option value="فيلا">فيلا</option>
                        <option value="مكتب">مكتب</option>
                        <option value="محل تجاري">محل تجاري</option>
                        <option value="مستودع">مستودع</option>
                        <option value="أرض">أرض</option>
                        <option value="أخرى">أخرى</option>
                    </select>
                </div>
                <div class="col-12">
                    <label class="form-label">العنوان</label>
                    <textarea name="address" class="form-control" rows="2" placeholder="العنوان الكامل للعقار"></textarea>
                </div>
                <div class="col-md-4">
                    <label class="form-label">المساحة (م²)</label>
                    <input type="number" name="area" class="form-control" step="0.01" min="0" placeholder="المساحة">
                </div>
                <div class="col-md-4">
                    <label class="form-label">عدد الغرف</label>
                    <input type="number" name="rooms_count" class="form-control" min="0" placeholder="عدد الغرف">
                </div>
                <div class="col-md-4">
                    <label class="form-label">عدد الحمامات</label>
                    <input type="number" name="bathrooms_count" class="form-control" min="0" placeholder="عدد الحمامات">
                </div>
                <div class="col-12">
                    <label class="form-label">الوصف</label>
                    <textarea name="description" class="form-control" rows="2" placeholder="تفاصيل إضافية عن العقار"></textarea>
                </div>
            </div>
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ العقار
                            </button>
                            <a href="{{ url_for('properties_list') }}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-arrow-right me-2"></i>
                                رجوع
                            </a>
                        </div>
                    </div>
                </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
