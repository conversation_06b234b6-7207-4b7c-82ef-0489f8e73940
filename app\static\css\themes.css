/* Theme System CSS - Dark/Light Mode Support */

/* Base Theme Variables */
:root {
    /* Light Theme (Default) */
    --theme-bg-primary: #ffffff;
    --theme-bg-secondary: #f8f9fa;
    --theme-bg-tertiary: #e9ecef;
    --theme-text-primary: #212529;
    --theme-text-secondary: #6c757d;
    --theme-text-muted: #adb5bd;
    --theme-border-color: #dee2e6;
    --theme-shadow: rgba(0, 0, 0, 0.1);
    --theme-shadow-lg: rgba(0, 0, 0, 0.15);
    
    /* Sidebar Colors */
    --sidebar-bg: linear-gradient(135deg, #23272b 90%, #444 100%);
    --sidebar-text: #ffffff;
    --sidebar-hover: rgba(255, 255, 255, 0.1);
    --sidebar-active: rgba(13, 110, 253, 0.8);
    
    /* Form Colors */
    --form-bg: #ffffff;
    --form-border: #ced4da;
    --form-focus-border: #86b7fe;
    --form-focus-shadow: rgba(13, 110, 253, 0.25);
    
    /* Card Colors */
    --card-bg: #ffffff;
    --card-border: #dee2e6;
    --card-header-bg: #f8f9fa;
    
    /* Table Colors */
    --table-bg: transparent;
    --table-striped-bg: rgba(0, 0, 0, 0.05);
    --table-hover-bg: rgba(0, 0, 0, 0.075);
    --table-border-color: #dee2e6;
    
    /* Button Colors */
    --btn-outline-border: #6c757d;
    --btn-outline-color: #6c757d;
    --btn-outline-hover-bg: #6c757d;
    --btn-outline-hover-color: #ffffff;
    
    /* Modal Colors */
    --modal-bg: #ffffff;
    --modal-border: #dee2e6;
    --modal-header-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    /* Alert Colors */
    --alert-primary-bg: rgba(13, 110, 253, 0.1);
    --alert-primary-color: #084298;
    --alert-success-bg: rgba(25, 135, 84, 0.1);
    --alert-success-color: #0f5132;
    --alert-warning-bg: rgba(255, 193, 7, 0.1);
    --alert-warning-color: #664d03;
    --alert-danger-bg: rgba(220, 53, 69, 0.1);
    --alert-danger-color: #842029;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --theme-bg-primary: #1a1a1a;
    --theme-bg-secondary: #2d3748;
    --theme-bg-tertiary: #4a5568;
    --theme-text-primary: #e2e8f0;
    --theme-text-secondary: #a0aec0;
    --theme-text-muted: #718096;
    --theme-border-color: #4a5568;
    --theme-shadow: rgba(0, 0, 0, 0.3);
    --theme-shadow-lg: rgba(0, 0, 0, 0.5);
    
    /* Sidebar Colors */
    --sidebar-bg: linear-gradient(135deg, #2d3748 90%, #4a5568 100%);
    --sidebar-text: #e2e8f0;
    --sidebar-hover: rgba(255, 255, 255, 0.1);
    --sidebar-active: rgba(13, 110, 253, 0.8);
    
    /* Form Colors */
    --form-bg: #374151;
    --form-border: #4a5568;
    --form-focus-border: #0d6efd;
    --form-focus-shadow: rgba(13, 110, 253, 0.25);
    
    /* Card Colors */
    --card-bg: #2d3748;
    --card-border: #4a5568;
    --card-header-bg: #4a5568;
    
    /* Table Colors */
    --table-bg: #2d3748;
    --table-striped-bg: #374151;
    --table-hover-bg: #374151;
    --table-border-color: #4a5568;
    
    /* Button Colors */
    --btn-outline-border: #6c757d;
    --btn-outline-color: #6c757d;
    --btn-outline-hover-bg: #6c757d;
    --btn-outline-hover-color: #ffffff;
    
    /* Modal Colors */
    --modal-bg: #2d3748;
    --modal-border: #4a5568;
    --modal-header-bg: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    
    /* Alert Colors */
    --alert-primary-bg: rgba(13, 110, 253, 0.1);
    --alert-primary-color: #6ea8fe;
    --alert-success-bg: rgba(25, 135, 84, 0.1);
    --alert-success-color: #75b798;
    --alert-warning-bg: rgba(255, 193, 7, 0.1);
    --alert-warning-color: #ffda6a;
    --alert-danger-bg: rgba(220, 53, 69, 0.1);
    --alert-danger-color: #ea868f;
}

/* Theme Toggle Button Styles */
#theme-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1050;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px var(--theme-shadow);
    background-color: var(--theme-bg-primary);
    border: 1px solid var(--theme-border-color);
    color: var(--theme-text-primary);
}

#theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px var(--theme-shadow-lg);
}

#theme-toggle:active {
    transform: scale(0.95);
}

/* Smooth Transitions for Theme Changes */
body,
.card,
.form-control,
.form-select,
.table,
.modal-content,
.sidebar,
.btn {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Apply Theme Variables to Components */

/* Body */
body {
    background-color: var(--theme-bg-secondary) !important;
    color: var(--theme-text-primary) !important;
}

/* Sidebar */
.sidebar {
    background: var(--sidebar-bg) !important;
    color: var(--sidebar-text) !important;
}

.sidebar .nav-link {
    color: var(--sidebar-text) !important;
}

.sidebar .nav-link:hover {
    background-color: var(--sidebar-hover) !important;
    color: #ffffff !important;
}

.sidebar .nav-link.active {
    background-color: var(--sidebar-active) !important;
    color: #ffffff !important;
}

/* Cards */
.card {
    background-color: var(--card-bg) !important;
    border-color: var(--card-border) !important;
    color: var(--theme-text-primary) !important;
}

.card-header {
    background-color: var(--card-header-bg) !important;
    border-bottom-color: var(--theme-border-color) !important;
    color: var(--theme-text-primary) !important;
}

/* Forms */
.form-control {
    background-color: var(--form-bg) !important;
    border-color: var(--form-border) !important;
    color: var(--theme-text-primary) !important;
}

.form-control:focus {
    background-color: var(--form-bg) !important;
    border-color: var(--form-focus-border) !important;
    color: var(--theme-text-primary) !important;
    box-shadow: 0 0 0 0.25rem var(--form-focus-shadow) !important;
}

.form-select {
    background-color: var(--form-bg) !important;
    border-color: var(--form-border) !important;
    color: var(--theme-text-primary) !important;
}

.form-select:focus {
    background-color: var(--form-bg) !important;
    border-color: var(--form-focus-border) !important;
    color: var(--theme-text-primary) !important;
}

.form-label {
    color: var(--theme-text-primary) !important;
}

/* Tables */
.table {
    --bs-table-bg: var(--table-bg);
    --bs-table-color: var(--theme-text-primary);
    --bs-table-border-color: var(--table-border-color);
    --bs-table-striped-bg: var(--table-striped-bg);
    --bs-table-hover-bg: var(--table-hover-bg);
}

.table th {
    background-color: var(--card-header-bg) !important;
    color: var(--theme-text-primary) !important;
    border-color: var(--theme-border-color) !important;
}

/* Modals */
.modal-content {
    background-color: var(--modal-bg) !important;
    color: var(--theme-text-primary) !important;
    border-color: var(--modal-border) !important;
}

.modal-header {
    background: var(--modal-header-bg) !important;
    border-bottom-color: var(--theme-border-color) !important;
}

.modal-footer {
    border-top-color: var(--theme-border-color) !important;
}

/* Alerts */
.alert-primary {
    background-color: var(--alert-primary-bg) !important;
    color: var(--alert-primary-color) !important;
    border-color: var(--theme-border-color) !important;
}

.alert-success {
    background-color: var(--alert-success-bg) !important;
    color: var(--alert-success-color) !important;
    border-color: var(--theme-border-color) !important;
}

.alert-warning {
    background-color: var(--alert-warning-bg) !important;
    color: var(--alert-warning-color) !important;
    border-color: var(--theme-border-color) !important;
}

.alert-danger {
    background-color: var(--alert-danger-bg) !important;
    color: var(--alert-danger-color) !important;
    border-color: var(--theme-border-color) !important;
}

/* Dropdowns */
.dropdown-menu {
    background-color: var(--card-bg) !important;
    border-color: var(--theme-border-color) !important;
}

.dropdown-item {
    color: var(--theme-text-primary) !important;
}

.dropdown-item:hover {
    background-color: var(--table-hover-bg) !important;
    color: var(--theme-text-primary) !important;
}

/* Navigation Tabs */
.nav-tabs .nav-link {
    color: var(--theme-text-primary) !important;
    border-color: var(--theme-border-color) !important;
}

.nav-tabs .nav-link.active {
    background-color: var(--card-bg) !important;
    color: var(--theme-text-primary) !important;
    border-color: var(--theme-border-color) var(--theme-border-color) var(--card-bg) !important;
}

/* Pagination */
.pagination .page-link {
    background-color: var(--card-bg) !important;
    border-color: var(--theme-border-color) !important;
    color: var(--theme-text-primary) !important;
}

.pagination .page-link:hover {
    background-color: var(--table-hover-bg) !important;
    color: var(--theme-text-primary) !important;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
}

/* Custom Components */
.stat-item {
    background: linear-gradient(135deg, var(--card-bg) 0%, var(--card-header-bg) 100%) !important;
    color: var(--theme-text-primary) !important;
}

.filter-panel {
    background: var(--card-header-bg) !important;
    color: var(--theme-text-primary) !important;
}

.settings-form {
    background: var(--card-bg) !important;
    color: var(--theme-text-primary) !important;
}

/* Print Styles - Always Light */
@media print {
    body {
        background-color: white !important;
        color: black !important;
    }
    
    .card {
        background-color: white !important;
        color: black !important;
        border-color: #dee2e6 !important;
    }
    
    .table {
        --bs-table-bg: white;
        --bs-table-color: black;
        --bs-table-border-color: #dee2e6;
    }
    
    #theme-toggle {
        display: none !important;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    #theme-toggle,
    body,
    .card,
    .form-control,
    .form-select,
    .table,
    .modal-content,
    .sidebar,
    .btn {
        transition: none !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --theme-border-color: #000000;
        --form-border: #000000;
    }
    
    [data-theme="dark"] {
        --theme-border-color: #ffffff;
        --form-border: #ffffff;
    }
}
