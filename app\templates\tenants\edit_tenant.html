{% extends "base.html" %}

{% block title %}تعديل المستأجر - {{ tenant.full_name }}{% endblock %}

{% block extra_css %}
<style>
    .edit-header {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 0 0 20px 20px;
    }
    
    .form-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
    }
    
    .form-section h5 {
        color: #495057;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
        margin-bottom: 20px;
        font-weight: bold;
    }
    
    .form-control, .form-select {
        border-radius: 8px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #ffc107;
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        color: white;
    }
    
    .btn-cancel {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-cancel:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
        color: white;
    }
    
    .required-field {
        color: #dc3545;
    }
    
    .auto-calculate {
        background-color: #f8f9fa;
        border-style: dashed;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="edit-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-2">
                        <i class="fas fa-user-edit"></i>
                        تعديل المستأجر
                    </h2>
                    <p class="mb-0">تعديل بيانات: {{ tenant.full_name }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('tenant_details', id=tenant.id) }}" class="btn btn-light">
                        <i class="fas fa-arrow-right"></i>
                        العودة للتفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form method="POST" id="editTenantForm">
        {{ csrf_token() }}
        
        <!-- المعلومات الشخصية -->
        <div class="form-section">
            <h5><i class="fas fa-user text-primary"></i> المعلومات الشخصية</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="full_name" class="form-label">الاسم الكامل <span class="required-field">*</span></label>
                        <input type="text" class="form-control" id="full_name" name="full_name" 
                               value="{{ tenant.full_name or '' }}" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="id_number" class="form-label">رقم الهوية <span class="required-field">*</span></label>
                        <input type="text" class="form-control" id="id_number" name="id_number" 
                               value="{{ tenant.id_number or '' }}" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="nationality" class="form-label">الجنسية <span class="required-field">*</span></label>
                        <select class="form-select" id="nationality" name="nationality" required>
                            <option value="">اختر الجنسية</option>
                            <option value="أردني" {{ 'selected' if tenant.nationality == 'أردني' }}>أردني</option>
                            <option value="فلسطيني" {{ 'selected' if tenant.nationality == 'فلسطيني' }}>فلسطيني</option>
                            <option value="سوري" {{ 'selected' if tenant.nationality == 'سوري' }}>سوري</option>
                            <option value="لبناني" {{ 'selected' if tenant.nationality == 'لبناني' }}>لبناني</option>
                            <option value="مصري" {{ 'selected' if tenant.nationality == 'مصري' }}>مصري</option>
                            <option value="عراقي" {{ 'selected' if tenant.nationality == 'عراقي' }}>عراقي</option>
                            <option value="سعودي" {{ 'selected' if tenant.nationality == 'سعودي' }}>سعودي</option>
                            <option value="أخرى" {{ 'selected' if tenant.nationality == 'أخرى' }}>أخرى</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                        <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                               value="{{ tenant.date_of_birth.strftime('%Y-%m-%d') if tenant.date_of_birth }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="gender" class="form-label">الجنس</label>
                        <select class="form-select" id="gender" name="gender">
                            <option value="">اختر الجنس</option>
                            <option value="ذكر" {{ 'selected' if tenant.gender == 'ذكر' }}>ذكر</option>
                            <option value="أنثى" {{ 'selected' if tenant.gender == 'أنثى' }}>أنثى</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="marital_status" class="form-label">الحالة الاجتماعية</label>
                        <select class="form-select" id="marital_status" name="marital_status">
                            <option value="">اختر الحالة</option>
                            <option value="أعزب" {{ 'selected' if tenant.marital_status == 'أعزب' }}>أعزب</option>
                            <option value="متزوج" {{ 'selected' if tenant.marital_status == 'متزوج' }}>متزوج</option>
                            <option value="مطلق" {{ 'selected' if tenant.marital_status == 'مطلق' }}>مطلق</option>
                            <option value="أرمل" {{ 'selected' if tenant.marital_status == 'أرمل' }}>أرمل</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="family_members" class="form-label">عدد أفراد الأسرة</label>
                        <input type="number" class="form-control" id="family_members" name="family_members" 
                               value="{{ tenant.family_members or '' }}" min="1">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="children_count" class="form-label">عدد الأطفال</label>
                        <input type="number" class="form-control" id="children_count" name="children_count" 
                               value="{{ tenant.children_count or '' }}" min="0">
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الاتصال -->
        <div class="form-section">
            <h5><i class="fas fa-phone text-success"></i> معلومات الاتصال</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف <span class="required-field">*</span></label>
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               value="{{ tenant.phone or '' }}" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="mobile" class="form-label">رقم الجوال</label>
                        <input type="tel" class="form-control" id="mobile" name="mobile" 
                               value="{{ tenant.mobile or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="{{ tenant.email or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="whatsapp" class="form-label">رقم الواتساب</label>
                        <input type="tel" class="form-control" id="whatsapp" name="whatsapp" 
                               value="{{ tenant.whatsapp or '' }}">
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="current_address" class="form-label">العنوان الحالي</label>
                        <textarea class="form-control" id="current_address" name="current_address" rows="2">{{ tenant.current_address or '' }}</textarea>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="permanent_address" class="form-label">العنوان الدائم</label>
                        <textarea class="form-control" id="permanent_address" name="permanent_address" rows="2">{{ tenant.permanent_address or '' }}</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات العمل -->
        <div class="form-section">
            <h5><i class="fas fa-briefcase text-info"></i> معلومات العمل</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="employment_status" class="form-label">حالة العمل</label>
                        <select class="form-select" id="employment_status" name="employment_status">
                            <option value="">اختر حالة العمل</option>
                            <option value="موظف" {{ 'selected' if tenant.employment_status == 'موظف' }}>موظف</option>
                            <option value="أعمال حرة" {{ 'selected' if tenant.employment_status == 'أعمال حرة' }}>أعمال حرة</option>
                            <option value="متقاعد" {{ 'selected' if tenant.employment_status == 'متقاعد' }}>متقاعد</option>
                            <option value="عاطل" {{ 'selected' if tenant.employment_status == 'عاطل' }}>عاطل</option>
                            <option value="طالب" {{ 'selected' if tenant.employment_status == 'طالب' }}>طالب</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="job_title" class="form-label">المسمى الوظيفي</label>
                        <input type="text" class="form-control" id="job_title" name="job_title" 
                               value="{{ tenant.job_title or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="employer_name" class="form-label">اسم جهة العمل</label>
                        <input type="text" class="form-control" id="employer_name" name="employer_name" 
                               value="{{ tenant.employer_name or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="work_phone" class="form-label">هاتف العمل</label>
                        <input type="tel" class="form-control" id="work_phone" name="work_phone" 
                               value="{{ tenant.work_phone or '' }}">
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="work_address" class="form-label">عنوان العمل</label>
                        <textarea class="form-control" id="work_address" name="work_address" rows="2">{{ tenant.work_address or '' }}</textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="employment_start_date" class="form-label">تاريخ بداية العمل</label>
                        <input type="date" class="form-control" id="employment_start_date" name="employment_start_date" 
                               value="{{ tenant.employment_start_date.strftime('%Y-%m-%d') if tenant.employment_start_date }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="work_experience_years" class="form-label">سنوات الخبرة</label>
                        <input type="number" class="form-control" id="work_experience_years" name="work_experience_years" 
                               value="{{ tenant.work_experience_years or '' }}" min="0">
                    </div>
                </div>
            </div>
        </div>

        <!-- المعلومات المالية -->
        <div class="form-section">
            <h5><i class="fas fa-dollar-sign text-warning"></i> المعلومات المالية</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="monthly_salary" class="form-label">الراتب الشهري</label>
                        <input type="number" step="0.01" class="form-control" id="monthly_salary" name="monthly_salary" 
                               value="{{ tenant.monthly_salary or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="salary_currency" class="form-label">عملة الراتب</label>
                        <select class="form-select" id="salary_currency" name="salary_currency">
                            <option value="JOD" {{ 'selected' if tenant.salary_currency == 'JOD' }}>دينار أردني</option>
                            <option value="USD" {{ 'selected' if tenant.salary_currency == 'USD' }}>دولار أمريكي</option>
                            <option value="EUR" {{ 'selected' if tenant.salary_currency == 'EUR' }}>يورو</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="additional_income" class="form-label">دخل إضافي</label>
                        <input type="number" step="0.01" class="form-control" id="additional_income" name="additional_income" 
                               value="{{ tenant.additional_income or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="total_monthly_income" class="form-label">إجمالي الدخل الشهري</label>
                        <input type="number" step="0.01" class="form-control auto-calculate" id="total_monthly_income" 
                               name="total_monthly_income" value="{{ tenant.total_monthly_income or '' }}" readonly>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="bank_name" class="form-label">اسم البنك</label>
                        <input type="text" class="form-control" id="bank_name" name="bank_name" 
                               value="{{ tenant.bank_name or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="bank_account_number" class="form-label">رقم الحساب البنكي</label>
                        <input type="text" class="form-control" id="bank_account_number" name="bank_account_number" 
                               value="{{ tenant.bank_account_number or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="credit_score" class="form-label">التقييم الائتماني</label>
                        <input type="number" class="form-control" id="credit_score" name="credit_score" 
                               value="{{ tenant.credit_score or '' }}" min="300" max="850">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="debt_to_income_ratio" class="form-label">نسبة الدين إلى الدخل (%)</label>
                        <input type="number" step="0.01" class="form-control" id="debt_to_income_ratio" name="debt_to_income_ratio" 
                               value="{{ tenant.debt_to_income_ratio or '' }}">
                    </div>
                </div>
            </div>
        </div>

        <!-- المراجع والطوارئ -->
        <div class="form-section">
            <h5><i class="fas fa-users text-danger"></i> المراجع وجهات الاتصال الطارئة</h5>
            <div class="row">
                <div class="col-12">
                    <h6 class="mb-3">جهة الاتصال في حالات الطوارئ</h6>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="emergency_contact_name" class="form-label">اسم جهة الاتصال</label>
                        <input type="text" class="form-control" id="emergency_contact_name" name="emergency_contact_name" 
                               value="{{ tenant.emergency_contact_name or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="emergency_contact_relationship" class="form-label">صلة القرابة</label>
                        <input type="text" class="form-control" id="emergency_contact_relationship" name="emergency_contact_relationship" 
                               value="{{ tenant.emergency_contact_relationship or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="emergency_contact_phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="emergency_contact_phone" name="emergency_contact_phone" 
                               value="{{ tenant.emergency_contact_phone or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="emergency_contact_address" class="form-label">العنوان</label>
                        <input type="text" class="form-control" id="emergency_contact_address" name="emergency_contact_address" 
                               value="{{ tenant.emergency_contact_address or '' }}">
                    </div>
                </div>
                
                <div class="col-12">
                    <hr>
                    <h6 class="mb-3">المراجع</h6>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="reference1_name" class="form-label">اسم المرجع الأول</label>
                        <input type="text" class="form-control" id="reference1_name" name="reference1_name" 
                               value="{{ tenant.reference1_name or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="reference1_phone" class="form-label">هاتف المرجع الأول</label>
                        <input type="tel" class="form-control" id="reference1_phone" name="reference1_phone" 
                               value="{{ tenant.reference1_phone or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="reference2_name" class="form-label">اسم المرجع الثاني</label>
                        <input type="text" class="form-control" id="reference2_name" name="reference2_name" 
                               value="{{ tenant.reference2_name or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="reference2_phone" class="form-label">هاتف المرجع الثاني</label>
                        <input type="tel" class="form-control" id="reference2_phone" name="reference2_phone" 
                               value="{{ tenant.reference2_phone or '' }}">
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ tenant.notes or '' }}</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mb-4">
            <button type="submit" class="btn btn-save me-3">
                <i class="fas fa-save"></i>
                حفظ التغييرات
            </button>
            <a href="{{ url_for('tenant_details', id=tenant.id) }}" class="btn btn-cancel">
                <i class="fas fa-times"></i>
                إلغاء
            </a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-calculate total monthly income
function calculateTotalIncome() {
    const salary = parseFloat(document.getElementById('monthly_salary').value) || 0;
    const additional = parseFloat(document.getElementById('additional_income').value) || 0;
    const total = salary + additional;
    document.getElementById('total_monthly_income').value = total;
}

document.getElementById('monthly_salary').addEventListener('input', calculateTotalIncome);
document.getElementById('additional_income').addEventListener('input', calculateTotalIncome);

// Form validation
document.getElementById('editTenantForm').addEventListener('submit', function(e) {
    const requiredFields = ['full_name', 'id_number', 'nationality', 'phone'];
    let isValid = true;
    
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
    }
});

// Remove validation styling on input
document.querySelectorAll('.form-control, .form-select').forEach(field => {
    field.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });
});
</script>
{% endblock %}
