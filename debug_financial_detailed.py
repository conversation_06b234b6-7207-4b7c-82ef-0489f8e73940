#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مفصل لمشكلة المعاملات المالية
"""

import requests
import re
from datetime import datetime

BASE_URL = "http://localhost:5000"
LOGIN_DATA = {
    'username': 'office',
    'password': '66889088'
}

def debug_financial():
    session = requests.Session()
    
    # تسجيل الدخول
    print("🔐 تسجيل الدخول...")
    login_page = session.get(f"{BASE_URL}/lawyersameh")
    csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', login_page.text)
    csrf_token = csrf_match.group(1) if csrf_match else None
    
    if not csrf_token:
        print("❌ لم يتم العثور على CSRF token في صفحة تسجيل الدخول")
        return
    
    login_data = LOGIN_DATA.copy()
    login_data['csrf_token'] = csrf_token
    
    response = session.post(f"{BASE_URL}/lawyersameh", data=login_data)
    if response.status_code != 200 or "dashboard" not in response.url:
        print("❌ فشل في تسجيل الدخول")
        return
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # اختبار المعاملة المالية
    print("💰 اختبار حفظ معاملة مالية جديدة...")
    
    # الحصول على CSRF token من صفحة إضافة المعاملة المالية
    add_page = session.get(f"{BASE_URL}/finance/add")
    if add_page.status_code != 200:
        print(f"❌ فشل في الوصول لصفحة إضافة المعاملة المالية: {add_page.status_code}")
        return
    
    csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', add_page.text)
    csrf_token = csrf_match.group(1) if csrf_match else None
    
    if not csrf_token:
        print("❌ لم يتم العثور على CSRF token في صفحة إضافة المعاملة المالية")
        return
    
    print(f"✅ تم الحصول على CSRF token: {csrf_token[:20]}...")
    
    transaction_data = {
        'csrf_token': csrf_token,
        'type': 'قبض',
        'amount': '500',
        'currency': 'شيكل',
        'description': f'معاملة اختبار {datetime.now().strftime("%H:%M:%S")}',
        'date': datetime.now().strftime('%Y-%m-%d'),
        'client_id': '1',
        'case_id': '1',
        'payment_method': 'نقدي',
        'reference_number': '',
        'notes': ''
    }
    
    print("📝 بيانات المعاملة:")
    for key, value in transaction_data.items():
        if key != 'csrf_token':
            print(f"  {key}: {value}")
    
    response = session.post(f"{BASE_URL}/finance/add", data=transaction_data)
    
    print(f"📊 نتيجة الطلب:")
    print(f"  Status Code: {response.status_code}")
    print(f"  URL: {response.url}")
    
    # فحص المحتوى للبحث عن رسائل النجاح أو الخطأ
    if "تمت إضافة" in response.text or "تم حفظ" in response.text or "نجح" in response.text:
        print("✅ تم حفظ المعاملة المالية بنجاح")
    elif "alert-danger" in response.text:
        error_match = re.search(r'<div class="alert alert-danger[^>]*>([^<]+)', response.text)
        if error_match:
            print(f"❌ رسالة خطأ: {error_match.group(1).strip()}")
        else:
            print("❌ يوجد خطأ ولكن لم يتم العثور على رسالة الخطأ")
    elif response.url.endswith("/finance/add"):
        print("❌ تم إرجاع المستخدم لنفس الصفحة - يوجد خطأ في التحقق من صحة البيانات")
    elif "finance" in response.url:
        print("✅ تم إعادة التوجيه لصفحة المالية - نجح الحفظ")
    else:
        print("❓ حالة غير متوقعة")
    
    # عرض جزء من المحتوى للتشخيص
    print("\n📄 جزء من محتوى الاستجابة:")
    content_lines = response.text.split('\n')
    for i, line in enumerate(content_lines[:50]):
        if 'alert' in line.lower() or 'error' in line.lower() or 'success' in line.lower():
            print(f"  Line {i+1}: {line.strip()}")

if __name__ == "__main__":
    debug_financial()
