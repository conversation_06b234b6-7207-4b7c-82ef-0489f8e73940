"""Add client_role and fees_currency to Case model

Revision ID: add_client_role_fees_currency
Revises: 
Create Date: 2024-12-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_client_role_fees_currency'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    try:
        with op.batch_alter_table('cases', schema=None) as batch_op:
            batch_op.add_column(sa.Column('client_role', sa.String(length=50), nullable=True))
            batch_op.add_column(sa.Column('fees_currency', sa.String(length=20), nullable=True))
        
        # تحديث القيم الافتراضية للحقول الجديدة
        op.execute("UPDATE cases SET client_role = 'مدعي' WHERE client_role IS NULL")
        op.execute("UPDATE cases SET fees_currency = 'شيكل' WHERE fees_currency IS NULL")
        
    except Exception as e:
        print(f"Error in upgrade: {e}")
        pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    try:
        with op.batch_alter_table('cases', schema=None) as batch_op:
            batch_op.drop_column('fees_currency')
            batch_op.drop_column('client_role')
    except Exception as e:
        print(f"Error in downgrade: {e}")
        pass
    # ### end Alembic commands ###
