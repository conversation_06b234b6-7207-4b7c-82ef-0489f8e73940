{% extends "base.html" %}

{% block title %}إنهاء عقد الإيجار{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0"><i class="fa fa-stop"></i> إنهاء عقد الإيجار</h4>
                </div>
                <div class="card-body">
                    <!-- معلومات العقد -->
                    <div class="alert alert-info">
                        <h6><strong>معلومات العقد:</strong></h6>
                        <p><strong>العقار:</strong> {{ lease.property.name }}</p>
                        <p><strong>المستأجر:</strong> {{ lease.tenant.name }}</p>
                        <p><strong>تاريخ البداية:</strong> {{ lease.start_date.strftime('%Y-%m-%d') }}</p>
                        <p><strong>تاريخ النهاية المقرر:</strong> {{ lease.end_date.strftime('%Y-%m-%d') }}</p>
                        <p><strong>الإيجار الشهري:</strong> {{ "%.2f"|format(lease.rent_amount) }} {{ lease.currency }}</p>
                        <p><strong>الأيام المتبقية:</strong> 
                            {% if lease.days_remaining() > 0 %}
                                <span class="text-warning">{{ lease.days_remaining() }} يوم</span>
                            {% else %}
                                <span class="text-danger">منتهي</span>
                            {% endif %}
                        </p>
                    </div>

                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">تاريخ الإنهاء <span class="text-danger">*</span></label>
                            <input type="date" name="termination_date" class="form-control" 
                                   value="{{ today }}" required>
                            <div class="form-text">تاريخ إنهاء العقد الفعلي</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">سبب الإنهاء <span class="text-danger">*</span></label>
                            <select name="termination_reason" class="form-control" required id="terminationReason">
                                <option value="">اختر سبب الإنهاء</option>
                                <option value="انتهاء مدة العقد">انتهاء مدة العقد</option>
                                <option value="طلب المستأجر">طلب المستأجر</option>
                                <option value="طلب المالك">طلب المالك</option>
                                <option value="عدم دفع الإيجار">عدم دفع الإيجار</option>
                                <option value="مخالفة شروط العقد">مخالفة شروط العقد</option>
                                <option value="بيع العقار">بيع العقار</option>
                                <option value="تجديد العقار">تجديد العقار</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>

                        <div class="mb-3" id="customReasonDiv" style="display: none;">
                            <label class="form-label">تفاصيل السبب</label>
                            <textarea name="custom_reason" class="form-control" rows="3" 
                                      placeholder="اكتب تفاصيل سبب الإنهاء..."></textarea>
                        </div>

                        <!-- تحذيرات -->
                        <div class="alert alert-warning">
                            <h6><i class="fa fa-exclamation-triangle"></i> تنبيه مهم:</h6>
                            <ul class="mb-0">
                                <li>سيتم تغيير حالة العقد إلى "منتهي"</li>
                                <li>سيتم تحديث حالة العقار إلى "متاح"</li>
                                <li>لن يمكن التراجع عن هذا الإجراء</li>
                                <li>تأكد من تسوية جميع المستحقات المالية</li>
                            </ul>
                        </div>

                        <!-- معلومات مالية -->
                        <div class="alert alert-info">
                            <h6><strong>معلومات مالية:</strong></h6>
                            <p><strong>مبلغ التأمين:</strong> {{ "%.2f"|format(lease.deposit) }} {{ lease.currency }}</p>
                            <p><strong>ملاحظة:</strong> تأكد من إرجاع مبلغ التأمين للمستأجر بعد فحص العقار</p>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-warning px-5 py-2" 
                                    onclick="return confirm('هل أنت متأكد من إنهاء هذا العقد؟')">
                                <i class="fa fa-stop"></i> إنهاء العقد
                            </button>
                            <a href="{{ url_for('leases_list') }}" class="btn btn-secondary px-4 py-2">
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.alert {
    border-radius: 10px;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.text-danger {
    color: #dc3545 !important;
}
</style>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // إظهار حقل السبب المخصص عند اختيار "أخرى"
    $('#terminationReason').change(function() {
        if ($(this).val() === 'أخرى') {
            $('#customReasonDiv').show();
        } else {
            $('#customReasonDiv').hide();
        }
    });
    
    // تعيين تاريخ اليوم كافتراضي
    var today = new Date().toISOString().split('T')[0];
    $('input[name="termination_date"]').val(today);
});
</script>
{% endblock %}
