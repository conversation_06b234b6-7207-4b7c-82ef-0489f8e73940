<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عقد إيجار رقم {{ lease.lease_number or lease.id }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        body {
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: white;
        }
        
        .contract-header {
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .contract-title {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .contract-number {
            font-size: 18px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .contract-date {
            font-size: 14px;
            color: #6c757d;
        }
        
        .section-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            margin: 25px 0 15px 0;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
        }
        
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .info-table td {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            vertical-align: top;
        }
        
        .info-table .label {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 30%;
            color: #495057;
        }
        
        .info-table .value {
            background-color: white;
            width: 70%;
        }
        
        .terms-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }
        
        .terms-list {
            list-style: none;
            padding: 0;
        }
        
        .terms-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            position: relative;
            padding-right: 25px;
        }
        
        .terms-list li:before {
            content: "✓";
            position: absolute;
            right: 0;
            color: #28a745;
            font-weight: bold;
        }
        
        .terms-list li:last-child {
            border-bottom: none;
        }
        
        .signature-section {
            margin-top: 50px;
            page-break-inside: avoid;
        }
        
        .signature-box {
            border: 2px solid #dee2e6;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .signature-line {
            border-bottom: 2px solid #333;
            width: 200px;
            margin: 20px auto;
            height: 40px;
        }
        
        .footer-note {
            text-align: center;
            font-size: 12px;
            color: #6c757d;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }
        
        @media print {
            body {
                font-size: 12px;
            }
            
            .no-print {
                display: none !important;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .signature-section {
                page-break-inside: avoid;
            }
            
            .section-title {
                background: #667eea !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .print-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .amount-highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            color: #856404;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-expired { background: #f8d7da; color: #721c24; }
        .status-terminated { background: #e2e3e5; color: #383d41; }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button class="print-button no-print" onclick="window.print()">
        <i class="fas fa-print"></i> طباعة العقد
    </button>

    <div class="container">
        <!-- Contract Header -->
        <div class="contract-header">
            <div class="contract-title">عقد إيجار عقار</div>
            <div class="contract-number">رقم العقد: {{ lease.lease_number or lease.id }}</div>
            <div class="contract-date">تاريخ الإصدار: {{ lease.created_at.strftime('%Y/%m/%d') if lease.created_at else 'غير محدد' }}</div>
            <div class="mt-2">
                <span class="status-badge status-{{ 'active' if lease.status == 'نشط' else 'pending' if lease.status == 'معلق' else 'expired' if lease.status == 'منتهي' else 'terminated' }}">
                    {{ lease.status }}
                </span>
            </div>
        </div>

        <!-- Basic Information -->
        <div class="section-title">
            <i class="fas fa-info-circle"></i> المعلومات الأساسية
        </div>
        <table class="info-table">
            <tr>
                <td class="label">رقم العقد:</td>
                <td class="value">{{ lease.lease_number or lease.id }}</td>
            </tr>
            <tr>
                <td class="label">نوع العقد:</td>
                <td class="value">{{ lease.lease_type or 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label">تاريخ بداية العقد:</td>
                <td class="value">{{ lease.start_date.strftime('%Y/%m/%d') if lease.start_date else 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label">تاريخ انتهاء العقد:</td>
                <td class="value">{{ lease.end_date.strftime('%Y/%m/%d') if lease.end_date else 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label">مدة العقد:</td>
                <td class="value">{{ lease.lease_duration_months or 0 }} شهر</td>
            </tr>
            <tr>
                <td class="label">حالة العقد:</td>
                <td class="value">{{ lease.status }}</td>
            </tr>
        </table>

        <!-- Property Information -->
        <div class="section-title">
            <i class="fas fa-home"></i> معلومات العقار
        </div>
        <table class="info-table">
            {% if lease.property %}
            <tr>
                <td class="label">اسم العقار:</td>
                <td class="value">{{ lease.property.name }}</td>
            </tr>
            <tr>
                <td class="label">العنوان:</td>
                <td class="value">{{ lease.property.address or 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label">نوع العقار:</td>
                <td class="value">{{ lease.property.property_type or 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label">المساحة:</td>
                <td class="value">{{ lease.property.area or 0 }} متر مربع</td>
            </tr>
            <tr>
                <td class="label">عدد الغرف:</td>
                <td class="value">{{ lease.property.bedrooms or 0 }} غرفة</td>
            </tr>
            <tr>
                <td class="label">عدد الحمامات:</td>
                <td class="value">{{ lease.property.bathrooms or 0 }} حمام</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="2" class="value text-center text-muted">العقار غير متوفر أو محذوف</td>
            </tr>
            {% endif %}
        </table>

        <!-- Tenant Information -->
        <div class="section-title">
            <i class="fas fa-user"></i> معلومات المستأجر
        </div>
        <table class="info-table">
            {% if lease.tenant %}
            <tr>
                <td class="label">الاسم الكامل:</td>
                <td class="value">{{ lease.tenant.full_name }}</td>
            </tr>
            <tr>
                <td class="label">رقم الهوية:</td>
                <td class="value">{{ lease.tenant.id_number or 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label">الهاتف:</td>
                <td class="value">{{ lease.tenant.phone or 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label">البريد الإلكتروني:</td>
                <td class="value">{{ lease.tenant.email or 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label">الجنسية:</td>
                <td class="value">{{ lease.tenant.nationality or 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label">العنوان:</td>
                <td class="value">{{ lease.tenant.address or 'غير محدد' }}</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="2" class="value text-center text-muted">المستأجر غير متوفر أو محذوف</td>
            </tr>
            {% endif %}
        </table>

        <!-- Financial Information -->
        <div class="section-title">
            <i class="fas fa-dollar-sign"></i> المعلومات المالية
        </div>
        <table class="info-table">
            <tr>
                <td class="label">الإيجار الشهري:</td>
                <td class="value">
                    <span class="amount-highlight">{{ lease.monthly_rent or 0 }} {{ lease.currency or 'JOD' }}</span>
                </td>
            </tr>
            <tr>
                <td class="label">مبلغ التأمين:</td>
                <td class="value">{{ lease.security_deposit or 0 }} {{ lease.currency or 'JOD' }}</td>
            </tr>
            <tr>
                <td class="label">العمولة:</td>
                <td class="value">{{ lease.commission_amount or 0 }} {{ lease.currency or 'JOD' }}</td>
            </tr>
            <tr>
                <td class="label">إجمالي قيمة العقد:</td>
                <td class="value">
                    <span class="amount-highlight">{{ lease.total_lease_value or 0 }} {{ lease.currency or 'JOD' }}</span>
                </td>
            </tr>
            <tr>
                <td class="label">دورية الدفع:</td>
                <td class="value">{{ lease.payment_frequency or 'شهري' }}</td>
            </tr>
            <tr>
                <td class="label">يوم استحقاق الدفع:</td>
                <td class="value">{{ lease.payment_due_day or 1 }} من كل شهر</td>
            </tr>
            <tr>
                <td class="label">رسوم التأخير:</td>
                <td class="value">{{ lease.late_fee_amount or 0 }} {{ lease.currency or 'JOD' }}</td>
            </tr>
            <tr>
                <td class="label">فترة السماح:</td>
                <td class="value">{{ lease.grace_period_days or 0 }} يوم</td>
            </tr>
        </table>

        <!-- Terms and Conditions -->
        {% if lease.terms_and_conditions or lease.special_conditions %}
        <div class="section-title">
            <i class="fas fa-file-alt"></i> الشروط والأحكام
        </div>
        <div class="terms-section">
            {% if lease.terms_and_conditions %}
            <h6><strong>الشروط العامة:</strong></h6>
            <p>{{ lease.terms_and_conditions }}</p>
            {% endif %}
            
            {% if lease.special_conditions %}
            <h6><strong>الشروط الخاصة:</strong></h6>
            <p>{{ lease.special_conditions }}</p>
            {% endif %}
            
            <h6><strong>الأحكام الإضافية:</strong></h6>
            <ul class="terms-list">
                <li>{{ 'يُسمح' if lease.pets_allowed else 'لا يُسمح' }} بالحيوانات الأليفة في العقار</li>
                <li>{{ 'يُسمح' if lease.smoking_allowed else 'لا يُسمح' }} بالتدخين في العقار</li>
                <li>{{ 'يُسمح' if lease.subletting_allowed else 'لا يُسمح' }} بالإيجار من الباطن</li>
                <li>{{ 'يتم' if lease.auto_renewal else 'لا يتم' }} تجديد العقد تلقائياً</li>
            </ul>
        </div>
        {% endif %}

        <!-- Additional Information -->
        {% if lease.utilities_included or lease.maintenance_responsibility or lease.parking_spaces or lease.furnished_status %}
        <div class="section-title">
            <i class="fas fa-plus-circle"></i> معلومات إضافية
        </div>
        <table class="info-table">
            {% if lease.utilities_included %}
            <tr>
                <td class="label">الخدمات المشمولة:</td>
                <td class="value">{{ lease.utilities_included }}</td>
            </tr>
            {% endif %}
            {% if lease.maintenance_responsibility %}
            <tr>
                <td class="label">مسؤولية الصيانة:</td>
                <td class="value">{{ lease.maintenance_responsibility }}</td>
            </tr>
            {% endif %}
            {% if lease.parking_spaces %}
            <tr>
                <td class="label">مواقف السيارات:</td>
                <td class="value">{{ lease.parking_spaces }} موقف</td>
            </tr>
            {% endif %}
            {% if lease.furnished_status %}
            <tr>
                <td class="label">حالة الأثاث:</td>
                <td class="value">{{ lease.furnished_status }}</td>
            </tr>
            {% endif %}
        </table>
        {% endif %}

        <!-- Notes -->
        {% if lease.notes %}
        <div class="section-title">
            <i class="fas fa-sticky-note"></i> ملاحظات
        </div>
        <div class="terms-section">
            <p>{{ lease.notes }}</p>
        </div>
        {% endif %}

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="section-title">
                <i class="fas fa-signature"></i> التوقيعات
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="signature-box">
                        <h6><strong>المؤجر</strong></h6>
                        <div class="signature-line"></div>
                        <p class="mb-0">الاسم: ___________________</p>
                        <p class="mb-0">التوقيع: ___________________</p>
                        <p class="mb-0">التاريخ: ___________________</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="signature-box">
                        <h6><strong>المستأجر</strong></h6>
                        <div class="signature-line"></div>
                        <p class="mb-0">الاسم: {{ lease.tenant.full_name if lease.tenant else '___________________' }}</p>
                        <p class="mb-0">التوقيع: ___________________</p>
                        <p class="mb-0">التاريخ: ___________________</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer-note">
            <p>تم إنشاء هذا العقد بواسطة نظام إدارة العقارات</p>
            <p>تاريخ الطباعة: {{ moment().format('YYYY/MM/DD HH:mm') }}</p>
        </div>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
        
        // Format current date for footer
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const dateStr = now.getFullYear() + '/' + 
                           String(now.getMonth() + 1).padStart(2, '0') + '/' + 
                           String(now.getDate()).padStart(2, '0') + ' ' +
                           String(now.getHours()).padStart(2, '0') + ':' + 
                           String(now.getMinutes()).padStart(2, '0');
            
            const footerDate = document.querySelector('.footer-note p:last-child');
            if (footerDate) {
                footerDate.textContent = 'تاريخ الطباعة: ' + dateStr;
            }
        });
    </script>
</body>
</html>
