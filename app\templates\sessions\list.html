{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0 text-primary"><i class="fa fa-gavel text-info"></i> قائمة الجلسات</h2>
        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary"><i class="fa fa-arrow-right"></i> رجوع إلى لوحة التحكم</a>
    </div>
    <div class="mb-3">
        <span class="badge bg-info text-dark fs-6"><i class="fa fa-gavel"></i> عدد الجلسات: {{ sessions|length }}</span>
    </div>
    <a href="{{ url_for('add_session') }}" class="btn btn-success mb-3"><i class="fa fa-plus"></i> إضافة جلسة جديدة</a>
    <div class="d-flex justify-content-between align-items-center mb-3">
        <form method="GET" class="d-flex" style="max-width:350px;">
            <input type="text" id="searchInput" class="form-control me-2" placeholder="ابحث...">
            <button type="submit" class="btn btn-outline-primary"><i class="fa fa-search"></i></button>
        </form>
        <button onclick="window.print()" class="btn btn-outline-dark"><i class="fa fa-print"></i> طباعة</button>
    </div>
    <div class="table-responsive">
        <table id="sessionsTable" class="table table-bordered table-hover align-middle text-center bg-white shadow-sm" style="white-space:nowrap;">
            <thead class="table-light">
                <tr>
                    <th onclick="sortTable(0)" style="vertical-align:middle;">القضية <i class="fa fa-sort"></i></th>
                    <th onclick="sortTable(1)" style="vertical-align:middle;">العميل <i class="fa fa-sort"></i></th>
                    <th onclick="sortTable(2)" style="vertical-align:middle;">التاريخ <i class="fa fa-sort"></i></th>
                    <th onclick="sortTable(3)" style="vertical-align:middle;">المحكمة <i class="fa fa-sort"></i></th>
                    <th onclick="sortTable(4)" style="vertical-align:middle;">ملاحظات <i class="fa fa-sort"></i></th>
                    <th style="vertical-align:middle;">إجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for session in sessions %}
                <tr style="vertical-align:middle;">
                    <td>{{ session.case.title }}</td>
                    <td>{{ session.case.client.name }}</td>
                    <td>{{ session.date.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td>{{ session.case.court or '-' }}</td>
                    <td>{{ session.notes or '-' }}</td>
                    <td>
                        <a href="{{ url_for('edit_session', session_id=session.id) }}" class="btn btn-primary btn-sm"><i class="fa fa-edit"></i> تعديل</a>
                        <form action="{{ url_for('delete_session', session_id=session.id) }}" method="post" style="display:inline-block;">
                            {{ form.csrf_token if form and form.csrf_token else '' }}
                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف الجلسة؟');"><i class="fa fa-trash"></i> حذف</button>
                        </form>
                    </td>
                </tr>
                {% else %}
                <tr><td colspan="6">لا توجد جلسات حالياً</td></tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
<script>
// بحث فوري
const searchInput = document.getElementById('searchInput');
searchInput.addEventListener('keyup', function() {
    let filter = this.value.toLowerCase();
    let rows = document.querySelectorAll('#sessionsTable tbody tr');
    rows.forEach(row => {
        let text = row.textContent.toLowerCase();
        row.style.display = text.includes(filter) ? '' : 'none';
    });
});
// فرز الأعمدة
function sortTable(n) {
    var table = document.getElementById("sessionsTable");
    var tbody = table.tBodies[0];
    var rows = Array.from(tbody.rows);
    var dir = table.getAttribute('data-sort-dir'+n) || "asc";
    rows.sort(function(a, b) {
        var x = a.cells[n].textContent.trim();
        var y = b.cells[n].textContent.trim();
        if (!isNaN(Date.parse(x)) && !isNaN(Date.parse(y))) {
            // فرز التاريخ
            return dir === "asc" ? new Date(x) - new Date(y) : new Date(y) - new Date(x);
        }
        return dir === "asc" ? x.localeCompare(y, 'ar', {numeric:true}) : y.localeCompare(x, 'ar', {numeric:true});
    });
    // إعادة ترتيب الصفوف
    rows.forEach(row => tbody.appendChild(row));
    // تبديل اتجاه الفرز
    table.setAttribute('data-sort-dir'+n, dir === "asc" ? "desc" : "asc");
}
</script>
{% endblock %}
