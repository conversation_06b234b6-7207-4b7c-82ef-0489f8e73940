{% extends "base.html" %}

{% block title %}التقارير والإحصائيات{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-primary mb-1">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير والإحصائيات
                    </h2>
                    <p class="text-muted">تقارير شاملة وإحصائيات مفصلة لإدارة العقارات</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-file-pdf me-2"></i>
                        إنشاء تقرير
                    </button>
                    <button class="btn btn-success" onclick="exportToExcel()">
                        <i class="fas fa-file-excel me-2"></i>
                        تصدير Excel
                    </button>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="totalProperties">0</h4>
                                    <p class="mb-0">إجمالي العقارات</p>
                                </div>
                                <i class="fas fa-building fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="totalTenants">0</h4>
                                    <p class="mb-0">إجمالي المستأجرين</p>
                                </div>
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="activeLeases">0</h4>
                                    <p class="mb-0">العقود النشطة</p>
                                </div>
                                <i class="fas fa-file-contract fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="monthlyRevenue">0 ر.س</h4>
                                    <p class="mb-0">الإيرادات الشهرية</p>
                                </div>
                                <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقارير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">نوع التقرير</label>
                            <select class="form-select" id="reportType">
                                <option value="financial">التقرير المالي</option>
                                <option value="properties">تقرير العقارات</option>
                                <option value="tenants">تقرير المستأجرين</option>
                                <option value="leases">تقرير العقود</option>
                                <option value="maintenance">تقرير الصيانة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">المدينة</label>
                            <select class="form-select" id="cityFilter">
                                <option value="">جميع المدن</option>
                                <option value="الرياض">الرياض</option>
                                <option value="جدة">جدة</option>
                                <option value="الدمام">الدمام</option>
                                <option value="مكة">مكة</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-primary w-100" onclick="loadReportData()">
                                <i class="fas fa-search me-2"></i>
                                تطبيق الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                توزيع العقارات حسب النوع
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="propertyTypeChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                الإيرادات الشهرية
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="revenueChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Occupancy Chart -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                معدل الإشغال
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="occupancyChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                توزيع العقارات حسب المدينة
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="cityDistributionChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Reports Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        تفاصيل التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="reportTable">
                            <thead class="table-light" id="reportTableHead">
                                <!-- سيتم ملء العناوين بواسطة JavaScript -->
                            </thead>
                            <tbody id="reportTableBody">
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Empty State -->
                    <div id="emptyReportState" class="text-center py-5" style="display: none;">
                        <i class="fas fa-chart-bar fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد بيانات</h5>
                        <p class="text-muted">لم يتم العثور على بيانات للفترة المحددة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.opacity-75 {
    opacity: 0.75;
}

.chart-container {
    position: relative;
    height: 300px;
}

#reportTable th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group .btn {
    margin-left: 0.25rem;
}

.btn-group .btn:first-child {
    margin-left: 0;
}
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let charts = {};

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تعيين التواريخ الافتراضية
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);

    document.getElementById('startDate').value = firstDay.toISOString().split('T')[0];
    document.getElementById('endDate').value = today.toISOString().split('T')[0];

    // تحميل البيانات الأولية
    loadDashboardStats();
    loadReportData();
});

// تحميل إحصائيات لوحة التحكم
function loadDashboardStats() {
    fetch('/api/reports/dashboard-stats')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('totalProperties').textContent = data.stats.total_properties || 0;
            document.getElementById('totalTenants').textContent = data.stats.total_tenants || 0;
            document.getElementById('activeLeases').textContent = data.stats.active_leases || 0;
            document.getElementById('monthlyRevenue').textContent = (data.stats.monthly_revenue || 0) + ' ر.س';
        }
    })
    .catch(error => {
        console.error('Error loading dashboard stats:', error);
    });
}

// تحميل بيانات التقرير
function loadReportData() {
    const reportType = document.getElementById('reportType').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const city = document.getElementById('cityFilter').value;

    const filters = {
        report_type: reportType,
        start_date: startDate,
        end_date: endDate,
        city: city
    };

    // تحميل بيانات الرسوم البيانية
    loadChartData(filters);

    // تحميل بيانات الجدول
    loadTableData(filters);
}

// تحميل بيانات الرسوم البيانية
function loadChartData(filters) {
    // رسم توزيع العقارات حسب النوع
    fetch('/api/reports/property-type-distribution', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            createPropertyTypeChart(data.data);
        }
    });

    // رسم الإيرادات الشهرية
    fetch('/api/reports/monthly-revenue', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            createRevenueChart(data.data);
        }
    });

    // رسم معدل الإشغال
    fetch('/api/reports/occupancy-rate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            createOccupancyChart(data.data);
        }
    });

    // رسم توزيع العقارات حسب المدينة
    fetch('/api/reports/city-distribution', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            createCityDistributionChart(data.data);
        }
    });
}

// إنشاء رسم توزيع العقارات حسب النوع
function createPropertyTypeChart(data) {
    const ctx = document.getElementById('propertyTypeChart').getContext('2d');

    if (charts.propertyType) {
        charts.propertyType.destroy();
    }

    charts.propertyType = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: data.labels || [],
            datasets: [{
                data: data.values || [],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// إنشاء رسم الإيرادات الشهرية
function createRevenueChart(data) {
    const ctx = document.getElementById('revenueChart').getContext('2d');

    if (charts.revenue) {
        charts.revenue.destroy();
    }

    charts.revenue = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels || [],
            datasets: [{
                label: 'الإيرادات (ر.س)',
                data: data.values || [],
                borderColor: '#36A2EB',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// إنشاء رسم معدل الإشغال
function createOccupancyChart(data) {
    const ctx = document.getElementById('occupancyChart').getContext('2d');

    if (charts.occupancy) {
        charts.occupancy.destroy();
    }

    charts.occupancy = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['مؤجرة', 'شاغرة', 'قيد الصيانة'],
            datasets: [{
                data: data.values || [0, 0, 0],
                backgroundColor: ['#28a745', '#dc3545', '#ffc107']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// إنشاء رسم توزيع العقارات حسب المدينة
function createCityDistributionChart(data) {
    const ctx = document.getElementById('cityDistributionChart').getContext('2d');

    if (charts.cityDistribution) {
        charts.cityDistribution.destroy();
    }

    charts.cityDistribution = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.labels || [],
            datasets: [{
                data: data.values || [],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// تحميل بيانات الجدول
function loadTableData(filters) {
    fetch('/api/reports/detailed-report', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayTableData(data.data, filters.report_type);
        }
    })
    .catch(error => {
        console.error('Error loading table data:', error);
    });
}

// عرض بيانات الجدول
function displayTableData(data, reportType) {
    const thead = document.getElementById('reportTableHead');
    const tbody = document.getElementById('reportTableBody');
    const emptyState = document.getElementById('emptyReportState');

    if (!data || data.length === 0) {
        tbody.innerHTML = '';
        emptyState.style.display = 'block';
        return;
    }

    emptyState.style.display = 'none';

    // تحديد العناوين حسب نوع التقرير
    let headers = [];
    switch(reportType) {
        case 'financial':
            headers = ['العقار', 'المستأجر', 'الإيجار الشهري', 'المدفوع', 'المتبقي', 'الحالة'];
            break;
        case 'properties':
            headers = ['اسم العقار', 'النوع', 'المدينة', 'الحالة', 'الإيجار الشهري', 'المستأجر'];
            break;
        case 'tenants':
            headers = ['اسم المستأجر', 'رقم الهاتف', 'العقار', 'تاريخ البداية', 'الحالة'];
            break;
        case 'leases':
            headers = ['رقم العقد', 'العقار', 'المستأجر', 'تاريخ البداية', 'تاريخ النهاية', 'الحالة'];
            break;
        default:
            headers = ['البيان', 'القيمة'];
    }

    // إنشاء العناوين
    thead.innerHTML = '<tr>' + headers.map(header => `<th>${header}</th>`).join('') + '</tr>';

    // إنشاء الصفوف
    tbody.innerHTML = data.map(row => {
        return '<tr>' + Object.values(row).map(value => `<td>${value || '-'}</td>`).join('') + '</tr>';
    }).join('');
}

// إنشاء تقرير PDF
function generateReport() {
    const reportType = document.getElementById('reportType').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const city = document.getElementById('cityFilter').value;

    const params = new URLSearchParams({
        report_type: reportType,
        start_date: startDate,
        end_date: endDate,
        city: city
    });

    window.open(`/api/reports/generate-pdf?${params.toString()}`, '_blank');
}

// تصدير إلى Excel
function exportToExcel() {
    const reportType = document.getElementById('reportType').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const city = document.getElementById('cityFilter').value;

    const filters = {
        report_type: reportType,
        start_date: startDate,
        end_date: endDate,
        city: city
    };

    fetch('/api/reports/export-excel', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters)
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('فشل في تصدير التقرير');
    })
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `report_${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تصدير التقرير');
    });
}
</script>
{% endblock %}