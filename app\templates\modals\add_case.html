<form method="POST" action="{{ url_for('add_case') }}" class="modal-form" id="addCaseForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-gavel"></i> معلومات القضية الأساسية
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">عنوان القضية <span class="text-danger">*</span></label>
                <input type="text" name="title" class="form-control" required
                       placeholder="عنوان القضية">
            </div>
            <div class="col-md-6">
                <label class="form-label">رقم القضية <span class="text-danger">*</span></label>
                <input type="text" name="case_number" class="form-control" required
                       placeholder="رقم القضية">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-4">
                <label class="form-label">العميل <span class="text-danger">*</span></label>
                <select name="client_id" class="form-control" required>
                    <option value="">اختر العميل</option>
                    {% for client in clients %}
                    <option value="{{ client.id }}">{{ client.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">صفة الموكل <span class="text-danger">*</span></label>
                <select name="client_role" class="form-control" required>
                    <option value="مدعي">مدعي</option>
                    <option value="مدعى عليه">مدعى عليه</option>
                    <option value="محكوم له">محكوم له</option>
                    <option value="محكوم عليه">محكوم عليه</option>
                    <option value="مشتكي">مشتكي</option>
                    <option value="متهم">متهم</option>
                    <option value="مستأنف">مستأنف</option>
                    <option value="مستأنف ضده">مستأنف ضده</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">نوع القضية <span class="text-danger">*</span></label>
                <select name="type" class="form-control" required>
                    <option value="">اختر نوع القضية</option>
                    <option value="مدني">مدني</option>
                    <option value="جنائي">جنائي</option>
                    <option value="تجاري">تجاري</option>
                    <option value="عمالي">عمالي</option>
                    <option value="أحوال شخصية">أحوال شخصية</option>
                    <option value="إداري">إداري</option>
                    <option value="عقاري">عقاري</option>
                </select>
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-4">
                <label class="form-label">حالة القضية <span class="text-danger">*</span></label>
                <select name="status" class="form-control" required>
                    <option value="جديدة">جديدة</option>
                    <option value="قيد المراجعة">قيد المراجعة</option>
                    <option value="في المحكمة">في المحكمة</option>
                    <option value="مؤجلة">مؤجلة</option>
                    <option value="مغلقة">مغلقة</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">المحكمة <span class="text-danger">*</span></label>
                <input type="text" name="court" class="form-control" required
                       placeholder="اسم المحكمة">
            </div>
            <div class="col-md-4">
                <label class="form-label">الخصم <span class="text-danger">*</span></label>
                <input type="text" name="opponent" class="form-control" required
                       placeholder="اسم الخصم">
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-calendar"></i> التواريخ المهمة
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">تاريخ بداية القضية</label>
                <input type="date" name="start_date" class="form-control">
            </div>
            <div class="col-md-6">
                <label class="form-label">الجلسة القادمة</label>
                <input type="datetime-local" name="next_hearing" class="form-control">
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-money-bill"></i> المعلومات المالية
        </div>
        <div class="row g-3">
            <div class="col-md-3">
                <label class="form-label">أتعاب المحاماة</label>
                <input type="number" name="lawyer_fees" class="form-control"
                       step="0.01" min="0" value="0">
            </div>
            <div class="col-md-3">
                <label class="form-label">عملة الأتعاب</label>
                <select name="fees_currency" class="form-control">
                    <option value="شيكل">شيكل</option>
                    <option value="دولار">دولار أمريكي</option>
                    <option value="دينار">دينار أردني</option>
                    <option value="يورو">يورو</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">رسوم المحكمة</label>
                <input type="number" name="court_fees" class="form-control"
                       step="0.01" min="0" value="0">
            </div>
            <div class="col-md-3">
                <label class="form-label">العملة</label>
                <select name="currency" class="form-control">
                    <option value="شيكل">شيكل</option>
                    <option value="دولار">دولار أمريكي</option>
                    <option value="دينار">دينار أردني</option>
                    <option value="يورو">يورو</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-file-alt"></i> تفاصيل القضية
        </div>
        <div class="mb-3">
            <label class="form-label">وصف القضية <span class="text-danger">*</span></label>
            <textarea name="description" class="form-control" rows="3" required
                      placeholder="وصف مفصل للقضية..."></textarea>
        </div>
        <div class="mb-3">
            <label class="form-label">ملاحظات</label>
            <textarea name="notes" class="form-control" rows="2" 
                      placeholder="ملاحظات إضافية..."></textarea>
        </div>
    </div>

    <div class="text-center mt-4">
        <button type="submit" class="btn btn-success px-4">
            <i class="fa fa-save"></i> حفظ القضية
        </button>
        <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
            إلغاء
        </button>
    </div>
</form>

<script>
document.getElementById('addCaseForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            if (modal) modal.hide();

            // إظهار رسالة نجاح
            showAlert('تم إضافة القضية بنجاح', 'success');

            // إعادة تحميل الصفحة أو تحديث القائمة
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert(data.message || 'حدث خطأ أثناء إضافة القضية', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
});

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>

<style>
.modal-form .form-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.modal-form .form-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
}

.modal-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.modal-form .text-danger {
    color: #dc3545 !important;
}

.modal-form .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.modal-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
</style>
