# نظام إدارة مكتب المحاماة

مشروع متكامل لإدارة مكاتب المحاماة باستخدام Python وFlask مع واجهة مستخدم عربية ودعم RTL.

## المميزات
- إدارة القضايا
- إدارة العملاء
- إدارة الجلسات والمواعيد
- إدارة الفواتير والمدفوعات
- إدارة المستندات
- إدارة المستخدمين والصلاحيات
- لوحة تحكم وتقارير
- واجهة مستخدم عربية بالكامل

## المتطلبات
- Python 3.10 أو أحدث
- Flask
- Flask-SQLAlchemy
- Flask-Login
- Flask-Babel
- Bootstrap RTL

## بدء التشغيل
1. تثبيت المتطلبات:
   ```bash
   pip install -r requirements.txt
   ```
2. تشغيل التطبيق:
   ```bash
   flask run
   ```

## ملاحظات
- جميع الواجهات باللغة العربية مع دعم الاتجاه من اليمين إلى اليسار.
- يمكن تخصيص النظام حسب احتياجات المكتب.
