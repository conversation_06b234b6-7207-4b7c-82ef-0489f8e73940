# خدمة النسخ الاحتياطي المتقدمة
import os
import json
import zipfile
import shutil
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
import threading
import schedule
import time
from flask import current_app
from app import db
from .archive_models import BackupSchedule, BackupLog, SystemSettings
from .models import User, Case, Client, Task, Event, Property, Tenant, Lease
from .models import FinancialTransaction, Document, Debt, Fee


class BackupService:
    """خدمة النسخ الاحتياطي المتقدمة"""
    
    def __init__(self, app=None):
        self.app = app
        self.backup_thread = None
        self.is_running = False
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة الخدمة مع التطبيق"""
        self.app = app
        
        # إنشاء مجلد النسخ الاحتياطية
        backup_dir = os.path.join(app.instance_path, 'backups')
        os.makedirs(backup_dir, exist_ok=True)
        
        # بدء خدمة النسخ الاحتياطي التلقائي
        self.start_scheduler()
    
    def start_scheduler(self):
        """بدء جدولة النسخ الاحتياطي التلقائي"""
        if not self.is_running:
            self.is_running = True
            self.backup_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.backup_thread.start()
    
    def stop_scheduler(self):
        """إيقاف جدولة النسخ الاحتياطي"""
        self.is_running = False
        if self.backup_thread:
            self.backup_thread.join()
    
    def _run_scheduler(self):
        """تشغيل جدولة النسخ الاحتياطي"""
        while self.is_running:
            try:
                with self.app.app_context():
                    self._check_scheduled_backups()
                time.sleep(60)  # فحص كل دقيقة
            except Exception as e:
                print(f"خطأ في جدولة النسخ الاحتياطي: {e}")
                time.sleep(300)  # انتظار 5 دقائق عند حدوث خطأ
    
    def _check_scheduled_backups(self):
        """فحص النسخ الاحتياطية المجدولة"""
        now = datetime.now()
        schedules = BackupSchedule.query.filter(
            BackupSchedule.is_active == True,
            BackupSchedule.next_backup <= now
        ).all()
        
        for schedule in schedules:
            try:
                self.create_scheduled_backup(schedule)
            except Exception as e:
                print(f"خطأ في إنشاء نسخة احتياطية مجدولة: {e}")
    
    def create_manual_backup(self, backup_types=None, user_id=None):
        """إنشاء نسخة احتياطية يدوية"""
        if backup_types is None:
            backup_types = ['database', 'files', 'documents']
        
        # إنشاء سجل النسخة الاحتياطية
        backup_log = BackupLog(
            backup_type='manual',
            status='in_progress',
            created_by=user_id
        )
        db.session.add(backup_log)
        db.session.commit()
        
        try:
            # إنشاء النسخة الاحتياطية
            backup_path = self._create_backup(backup_types, backup_log.id)
            
            # تحديث سجل النسخة الاحتياطية
            backup_log.status = 'success'
            backup_log.end_time = datetime.now()
            backup_log.file_path = backup_path
            backup_log.file_size = os.path.getsize(backup_path) if os.path.exists(backup_path) else 0
            
            # معلومات إضافية
            backup_data = {
                'backup_types': backup_types,
                'total_files': self._count_backup_files(backup_path),
                'compression': True
            }
            backup_log.set_backup_data(backup_data)
            
            db.session.commit()
            return backup_log
            
        except Exception as e:
            backup_log.status = 'failed'
            backup_log.end_time = datetime.now()
            backup_log.error_message = str(e)
            db.session.commit()
            raise e
    
    def create_scheduled_backup(self, schedule):
        """إنشاء نسخة احتياطية مجدولة"""
        backup_log = BackupLog(
            schedule_id=schedule.id,
            backup_type='scheduled',
            status='in_progress',
            created_by=schedule.created_by
        )
        db.session.add(backup_log)
        db.session.commit()
        
        try:
            # إنشاء النسخة الاحتياطية
            backup_types = schedule.get_backup_types()
            backup_path = self._create_backup(backup_types, backup_log.id)
            
            # تحديث سجل النسخة الاحتياطية
            backup_log.status = 'success'
            backup_log.end_time = datetime.now()
            backup_log.file_path = backup_path
            backup_log.file_size = os.path.getsize(backup_path) if os.path.exists(backup_path) else 0
            
            # معلومات إضافية
            backup_data = {
                'backup_types': backup_types,
                'schedule_name': schedule.name,
                'total_files': self._count_backup_files(backup_path),
                'compression': schedule.compression_enabled
            }
            backup_log.set_backup_data(backup_data)
            
            # تحديث الجدولة
            schedule.last_backup = datetime.now()
            schedule.calculate_next_backup()
            
            db.session.commit()
            
            # تنظيف النسخ القديمة
            self._cleanup_old_backups(schedule)
            
            return backup_log
            
        except Exception as e:
            backup_log.status = 'failed'
            backup_log.end_time = datetime.now()
            backup_log.error_message = str(e)
            db.session.commit()
            raise e
    
    def _create_backup(self, backup_types, backup_id):
        """إنشاء ملف النسخة الاحتياطية"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'backup_{backup_id}_{timestamp}.zip'
        backup_dir = os.path.join(self.app.instance_path, 'backups')
        backup_path = os.path.join(backup_dir, backup_filename)
        
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # نسخ قاعدة البيانات
            if 'database' in backup_types:
                self._backup_database(zipf)
            
            # نسخ الملفات
            if 'files' in backup_types:
                self._backup_files(zipf)
            
            # نسخ المستندات
            if 'documents' in backup_types:
                self._backup_documents(zipf)
            
            # إضافة معلومات النسخة الاحتياطية
            self._add_backup_info(zipf, backup_types)
        
        return backup_path
    
    def _backup_database(self, zipf):
        """نسخ قاعدة البيانات"""
        db_path = os.path.join(self.app.instance_path, 'law_office.db')
        if os.path.exists(db_path):
            zipf.write(db_path, 'database/law_office.db')
            
            # إنشاء نسخة SQL
            sql_backup_path = os.path.join(self.app.instance_path, 'temp_backup.sql')
            self._export_database_to_sql(db_path, sql_backup_path)
            zipf.write(sql_backup_path, 'database/law_office.sql')
            os.remove(sql_backup_path)
    
    def _backup_files(self, zipf):
        """نسخ ملفات النظام"""
        # نسخ ملفات التطبيق الأساسية
        app_files = ['app.py', 'config.py', 'requirements.txt']
        for file in app_files:
            file_path = os.path.join(os.path.dirname(self.app.instance_path), file)
            if os.path.exists(file_path):
                zipf.write(file_path, f'app_files/{file}')
        
        # نسخ مجلد app
        app_dir = os.path.join(os.path.dirname(self.app.instance_path), 'app')
        if os.path.exists(app_dir):
            for root, dirs, files in os.walk(app_dir):
                for file in files:
                    if file.endswith(('.py', '.html', '.css', '.js')):
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, os.path.dirname(self.app.instance_path))
                        zipf.write(file_path, arc_path)
    
    def _backup_documents(self, zipf):
        """نسخ المستندات المرفوعة"""
        uploads_dir = os.path.join(self.app.instance_path, 'uploads')
        if os.path.exists(uploads_dir):
            for root, dirs, files in os.walk(uploads_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, self.app.instance_path)
                    zipf.write(file_path, f'documents/{arc_path}')
    
    def _add_backup_info(self, zipf, backup_types):
        """إضافة معلومات النسخة الاحتياطية"""
        backup_info = {
            'created_at': datetime.now().isoformat(),
            'backup_types': backup_types,
            'app_version': '1.0.0',
            'database_tables': self._get_database_info()
        }
        
        info_json = json.dumps(backup_info, ensure_ascii=False, indent=2)
        zipf.writestr('backup_info.json', info_json)
    
    def _get_database_info(self):
        """الحصول على معلومات قاعدة البيانات"""
        tables_info = {}
        
        # عدد السجلات في كل جدول
        tables = [
            ('users', User),
            ('cases', Case),
            ('clients', Client),
            ('tasks', Task),
            ('events', Event),
            ('properties', Property),
            ('tenants', Tenant),
            ('leases', Lease),
            ('financial_transactions', FinancialTransaction),
            ('documents', Document),
            ('debts', Debt),
            ('fees', Fee)
        ]
        
        for table_name, model in tables:
            try:
                count = model.query.count()
                tables_info[table_name] = count
            except:
                tables_info[table_name] = 0
        
        return tables_info
    
    def _export_database_to_sql(self, db_path, sql_path):
        """تصدير قاعدة البيانات إلى ملف SQL"""
        conn = sqlite3.connect(db_path)
        with open(sql_path, 'w', encoding='utf-8') as f:
            for line in conn.iterdump():
                f.write('%s\n' % line)
        conn.close()
    
    def _count_backup_files(self, backup_path):
        """عد الملفات في النسخة الاحتياطية"""
        try:
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                return len(zipf.namelist())
        except:
            return 0
    
    def _cleanup_old_backups(self, schedule):
        """تنظيف النسخ الاحتياطية القديمة"""
        if schedule.retention_days > 0:
            cutoff_date = datetime.now() - timedelta(days=schedule.retention_days)
            old_logs = BackupLog.query.filter(
                BackupLog.schedule_id == schedule.id,
                BackupLog.start_time < cutoff_date,
                BackupLog.status == 'success'
            ).all()
            
            for log in old_logs:
                # حذف الملف
                if log.file_path and os.path.exists(log.file_path):
                    try:
                        os.remove(log.file_path)
                    except:
                        pass
                
                # حذف السجل
                db.session.delete(log)
            
            db.session.commit()
    
    def restore_backup(self, backup_path, restore_types=None):
        """استعادة نسخة احتياطية"""
        if restore_types is None:
            restore_types = ['database', 'files', 'documents']
        
        if not os.path.exists(backup_path):
            raise FileNotFoundError("ملف النسخة الاحتياطية غير موجود")
        
        with zipfile.ZipFile(backup_path, 'r') as zipf:
            # استعادة قاعدة البيانات
            if 'database' in restore_types:
                self._restore_database(zipf)
            
            # استعادة الملفات
            if 'files' in restore_types:
                self._restore_files(zipf)
            
            # استعادة المستندات
            if 'documents' in restore_types:
                self._restore_documents(zipf)
    
    def _restore_database(self, zipf):
        """استعادة قاعدة البيانات"""
        db_backup_path = 'database/law_office.db'
        if db_backup_path in zipf.namelist():
            db_path = os.path.join(self.app.instance_path, 'law_office.db')
            
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
            if os.path.exists(db_path):
                backup_current = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(db_path, backup_current)
            
            # استعادة قاعدة البيانات
            with zipf.open(db_backup_path) as source, open(db_path, 'wb') as target:
                shutil.copyfileobj(source, target)
    
    def _restore_files(self, zipf):
        """استعادة ملفات النظام"""
        app_dir = os.path.dirname(self.app.instance_path)
        
        for file_info in zipf.filelist:
            if file_info.filename.startswith('app_files/'):
                target_path = os.path.join(app_dir, file_info.filename[10:])  # إزالة 'app_files/'
                os.makedirs(os.path.dirname(target_path), exist_ok=True)
                
                with zipf.open(file_info) as source, open(target_path, 'wb') as target:
                    shutil.copyfileobj(source, target)
    
    def _restore_documents(self, zipf):
        """استعادة المستندات"""
        for file_info in zipf.filelist:
            if file_info.filename.startswith('documents/'):
                target_path = os.path.join(self.app.instance_path, file_info.filename[10:])  # إزالة 'documents/'
                os.makedirs(os.path.dirname(target_path), exist_ok=True)
                
                with zipf.open(file_info) as source, open(target_path, 'wb') as target:
                    shutil.copyfileobj(source, target)
    
    def get_backup_statistics(self):
        """الحصول على إحصائيات النسخ الاحتياطية"""
        total_backups = BackupLog.query.count()
        successful_backups = BackupLog.query.filter_by(status='success').count()
        failed_backups = BackupLog.query.filter_by(status='failed').count()
        
        # حجم النسخ الاحتياطية
        total_size = db.session.query(db.func.sum(BackupLog.file_size)).scalar() or 0
        
        # آخر نسخة احتياطية
        last_backup = BackupLog.query.filter_by(status='success').order_by(BackupLog.start_time.desc()).first()
        
        return {
            'total_backups': total_backups,
            'successful_backups': successful_backups,
            'failed_backups': failed_backups,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'last_backup': last_backup.start_time.isoformat() if last_backup else None,
            'success_rate': round((successful_backups / total_backups * 100), 2) if total_backups > 0 else 0
        }


# إنشاء مثيل الخدمة
backup_service = BackupService()
