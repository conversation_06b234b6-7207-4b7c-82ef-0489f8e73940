{% extends "base.html" %}

{% block title %}تعديل مستأجر{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-user-edit"></i> تعديل بيانات المستأجر</h1>
                    <p>تعديل بيانات المستأجر المحدد</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit text-primary me-2"></i>
                        تعديل بيانات المستأجر
                    </h5>
                </div>
                <div class="card-body">
    <form method="POST">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <div class="mb-3">
            <label class="form-label">اسم المستأجر</label>
            <input type="text" name="name" class="form-control" value="{{ tenant.name }}" required>
        </div>
        <div class="mb-3">
            <label class="form-label">الهاتف</label>
            <input type="text" name="phone" class="form-control" value="{{ tenant.phone }}">
        </div>
        <div class="mb-3">
            <label class="form-label">البريد الإلكتروني</label>
            <input type="email" name="email" class="form-control" value="{{ tenant.email }}">
        </div>
        <div class="mb-3">
            <label class="form-label">العنوان</label>
            <input type="text" name="address" class="form-control" value="{{ tenant.address }}">
        </div>
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                تحديث البيانات
                            </button>
                            <a href="{{ url_for('tenants_list') }}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-arrow-right me-2"></i>
                                رجوع
                            </a>
                        </div>
                    </div>
                </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
