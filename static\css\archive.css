/* Archive and Settings Styles */

.archive-container {
    padding: 20px;
}

/* Archive Statistics */
.stat-item {
    padding: 15px;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    margin-bottom: 10px;
    transition: transform 0.2s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat-item h4 {
    margin: 0;
    font-weight: bold;
}

.stat-item small {
    color: #6c757d;
    font-size: 0.85em;
}

/* Archive Tables */
.archive-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.archive-table .table {
    margin-bottom: 0;
}

.archive-table .table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.archive-table .table td {
    vertical-align: middle;
    padding: 12px;
}

/* Archive Status Badges */
.archive-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75em;
    font-weight: 500;
}

.archive-status.completed {
    background-color: #d4edda;
    color: #155724;
}

.archive-status.cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

.archive-status.transferred {
    background-color: #d1ecf1;
    color: #0c5460;
}

.archive-status.inactive {
    background-color: #e2e3e5;
    color: #383d41;
}

/* Filter Panel */
.filter-panel {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.filter-panel .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.filter-panel .form-control,
.filter-panel .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-panel .form-control:focus,
.filter-panel .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Settings Forms */
.settings-form {
    background: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.settings-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.settings-form .form-control,
.settings-form .form-select,
.settings-form .form-check-input {
    border-radius: 6px;
}

.settings-form .form-text {
    font-size: 0.875em;
    color: #6c757d;
}

/* System Information */
.system-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 20px;
}

.system-info .table {
    color: white;
    margin-bottom: 0;
}

.system-info .table td {
    border-color: rgba(255,255,255,0.2);
    padding: 8px 12px;
}

.system-info .table td:first-child {
    font-weight: 600;
}

/* Archive Action Buttons */
.archive-actions {
    display: flex;
    gap: 5px;
}

.archive-actions .btn {
    padding: 4px 8px;
    font-size: 0.875em;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.archive-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Modal Enhancements */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 15px 25px;
}

/* Archive Rules Form */
.archive-rules-form {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.archive-rules-form h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #dee2e6;
}

/* Backup Section */
.backup-section {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.backup-section h6 {
    color: #8b4513;
    font-weight: 600;
    margin-bottom: 10px;
}

.backup-section p {
    color: #8b4513;
    margin-bottom: 15px;
}

.backup-section .btn {
    border-radius: 6px;
    font-weight: 500;
}

/* Progress Indicators */
.progress-indicator {
    display: none;
    text-align: center;
    padding: 20px;
}

.progress-indicator .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .archive-container {
        padding: 10px;
    }
    
    .stat-item {
        margin-bottom: 15px;
    }
    
    .archive-actions {
        flex-direction: column;
        gap: 2px;
    }
    
    .archive-actions .btn {
        width: 100%;
        margin-bottom: 2px;
    }
    
    .filter-panel {
        padding: 15px;
    }
    
    .settings-form {
        padding: 15px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .stat-item {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        color: #e2e8f0;
    }
    
    .archive-table {
        background: #2d3748;
    }
    
    .archive-table .table th {
        background-color: #4a5568;
        color: #e2e8f0;
        border-bottom-color: #718096;
    }
    
    .archive-table .table td {
        background-color: #2d3748;
        color: #e2e8f0;
        border-color: #718096;
    }
    
    .filter-panel {
        background: #4a5568;
        color: #e2e8f0;
    }
    
    .settings-form {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .archive-rules-form {
        background: #4a5568;
        color: #e2e8f0;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
}

.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #007bff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Print Styles */
@media print {
    .archive-actions,
    .filter-panel,
    .btn,
    .modal {
        display: none !important;
    }
    
    .archive-table {
        box-shadow: none;
    }
    
    .table {
        font-size: 12px;
    }
    
    .stat-item {
        break-inside: avoid;
    }
}
