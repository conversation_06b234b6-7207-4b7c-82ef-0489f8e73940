#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الوظائف الفعلية للأزرار
"""

import requests
import re
from datetime import datetime

BASE_URL = "http://localhost:5000"
LOGIN_DATA = {
    'username': 'office',
    'password': '66889088'
}

class ActualFunctionalityTester:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = []
        
    def login(self):
        """تسجيل الدخول"""
        print("🔐 تسجيل الدخول...")
        login_page = self.session.get(f"{BASE_URL}/lawyersameh")
        csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', login_page.text)
        csrf_token = csrf_match.group(1) if csrf_match else None
        
        if not csrf_token:
            print("❌ لم يتم العثور على CSRF token في صفحة تسجيل الدخول")
            return False
        
        login_data = LOGIN_DATA.copy()
        login_data['csrf_token'] = csrf_token
        
        response = self.session.post(f"{BASE_URL}/lawyersameh", data=login_data)
        if response.status_code != 200 or "dashboard" not in response.url:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        return True
    
    def test_save_functionality(self):
        """اختبار وظائف الحفظ الفعلية"""
        print("\n💾 اختبار وظائف الحفظ الفعلية...")
        
        # اختبار حفظ عميل جديد
        print("  👤 اختبار حفظ عميل جديد...")
        client_data = {
            'name': 'عميل اختبار',
            'phone': '0123456789',
            'email': '<EMAIL>',
            'address': 'عنوان اختبار',
            'role': 'موكل'
        }
        
        # الحصول على CSRF token من صفحة الإضافة
        add_page = self.session.get(f"{BASE_URL}/clients/add")
        csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', add_page.text)
        if csrf_match:
            client_data['csrf_token'] = csrf_match.group(1)
            
            response = self.session.post(f"{BASE_URL}/clients/add", data=client_data)
            if response.status_code == 200 and ("تم" in response.text or "clients" in response.url):
                print("    ✅ حفظ العميل يعمل بنجاح")
            else:
                print(f"    ❌ فشل في حفظ العميل: {response.status_code}")
        else:
            print("    ❌ لم يتم العثور على CSRF token")
    
    def test_edit_functionality(self):
        """اختبار وظائف التعديل الفعلية"""
        print("\n✏️ اختبار وظائف التعديل الفعلية...")
        
        # الحصول على قائمة العملاء للعثور على عميل للتعديل
        clients_page = self.session.get(f"{BASE_URL}/clients")
        client_id_match = re.search(r'/clients/(\d+)/edit', clients_page.text)
        
        if client_id_match:
            client_id = client_id_match.group(1)
            print(f"  👤 اختبار تعديل العميل رقم {client_id}...")
            
            # الوصول لصفحة التعديل
            edit_page = self.session.get(f"{BASE_URL}/clients/{client_id}/edit")
            if edit_page.status_code == 200:
                print("    ✅ صفحة التعديل تعمل بنجاح")
                
                # اختبار حفظ التعديل
                csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', edit_page.text)
                if csrf_match:
                    edit_data = {
                        'name': 'عميل محدث',
                        'phone': '0123456789',
                        'email': '<EMAIL>',
                        'address': 'عنوان محدث',
                        'role': 'موكل',
                        'csrf_token': csrf_match.group(1)
                    }
                    
                    response = self.session.post(f"{BASE_URL}/clients/{client_id}/edit", data=edit_data)
                    if response.status_code == 200 and ("تم" in response.text or "clients" in response.url):
                        print("    ✅ حفظ التعديل يعمل بنجاح")
                    else:
                        print(f"    ❌ فشل في حفظ التعديل: {response.status_code}")
                else:
                    print("    ❌ لم يتم العثور على CSRF token في صفحة التعديل")
            else:
                print(f"    ❌ فشل في الوصول لصفحة التعديل: {edit_page.status_code}")
        else:
            print("    ❌ لم يتم العثور على عميل للتعديل")
    
    def test_delete_functionality(self):
        """اختبار وظائف الحذف الفعلية"""
        print("\n🗑️ اختبار وظائف الحذف الفعلية...")
        
        # اختبار حذف عميل (JavaScript)
        print("  👤 اختبار حذف عميل (JavaScript)...")
        clients_page = self.session.get(f"{BASE_URL}/clients")
        client_id_match = re.search(r'confirmDelete\((\d+)', clients_page.text)
        
        if client_id_match:
            client_id = client_id_match.group(1)

            # الحصول على CSRF token
            csrf_match = re.search(r'name="csrf-token" content="([^"]+)"', clients_page.text)
            headers = {}
            if csrf_match:
                headers['X-CSRFToken'] = csrf_match.group(1)

            # محاولة حذف العميل
            response = self.session.post(f"{BASE_URL}/clients/{client_id}/delete", headers=headers)
            if response.status_code == 200:
                print("    ✅ حذف العميل يعمل بنجاح")
            else:
                print(f"    ❌ فشل في حذف العميل: {response.status_code}")
        else:
            print("    ❌ لم يتم العثور على عميل للحذف")
        
        # اختبار حذف عقار (نموذج HTML)
        print("  🏢 اختبار حذف عقار (نموذج HTML)...")
        properties_page = self.session.get(f"{BASE_URL}/properties")
        property_id_match = re.search(r'delete_property.*?(\d+)', properties_page.text)
        
        if property_id_match:
            property_id = property_id_match.group(1)
            
            # الحصول على CSRF token
            csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', properties_page.text)
            if csrf_match:
                delete_data = {'csrf_token': csrf_match.group(1)}
                response = self.session.post(f"{BASE_URL}/properties/{property_id}/delete", data=delete_data)
                if response.status_code == 200:
                    print("    ✅ حذف العقار يعمل بنجاح")
                else:
                    print(f"    ❌ فشل في حذف العقار: {response.status_code}")
            else:
                print("    ❌ لم يتم العثور على CSRF token")
        else:
            print("    ❌ لم يتم العثور على عقار للحذف")
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار الوظائف الفعلية...")
        print("=" * 50)
        
        if not self.login():
            return
        
        self.test_save_functionality()
        self.test_edit_functionality()
        self.test_delete_functionality()
        
        print("\n" + "=" * 50)
        print("📊 انتهى اختبار الوظائف الفعلية")

if __name__ == "__main__":
    tester = ActualFunctionalityTester()
    tester.run_all_tests()
