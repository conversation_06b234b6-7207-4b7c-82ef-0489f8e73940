"""add open_date to case

Revision ID: add_open_date_001
Revises: 72d1f599167b
Create Date: 2024-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_open_date_001'
down_revision = '72d1f599167b'
branch_labels = None
depends_on = None

def upgrade():
    op.add_column('case', sa.Column('open_date', sa.Date(), nullable=True))

def downgrade():
    op.drop_column('case', 'open_date')
