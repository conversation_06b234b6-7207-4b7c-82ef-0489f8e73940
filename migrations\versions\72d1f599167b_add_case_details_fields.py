"""add case details fields

Revision ID: 72d1f599167b
Revises: 1d9a28e86155
Create Date: 2025-07-01 17:49:19.419575

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '72d1f599167b'
down_revision = '1d9a28e86155'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('case', schema=None) as batch_op:
        batch_op.add_column(sa.Column('case_number', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('opponent', sa.String(length=150), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('case', schema=None) as batch_op:
        batch_op.drop_column('opponent')
        batch_op.drop_column('case_number')

    # ### end Alembic commands ###
