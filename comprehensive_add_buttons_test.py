#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لأزرار الإضافة مع تسجيل الدخول
يتحقق من أن جميع أزرار الإضافة تعمل بشكل صحيح بعد تسجيل الدخول
"""

import requests
import sys
from urllib.parse import urljoin

# إعدادات الاختبار
BASE_URL = 'http://localhost:5000'
LOGIN_URL = urljoin(BASE_URL, '/lawyersameh')

# بيانات تسجيل الدخول
LOGIN_CREDENTIALS = {
    'username': 'office',
    'password': '66889088'
}

def login_to_system():
    """تسجيل الدخول إلى النظام"""
    session = requests.Session()
    
    try:
        # الحصول على صفحة تسجيل الدخول أولاً
        login_page = session.get(LOGIN_URL, timeout=10)
        if login_page.status_code != 200:
            print(f"❌ خطأ في الوصول لصفحة تسجيل الدخول: {login_page.status_code}")
            return None
        
        # تسجيل الدخول
        login_response = session.post(LOGIN_URL, data=LOGIN_CREDENTIALS, timeout=10)
        
        # التحقق من نجاح تسجيل الدخول
        if login_response.status_code == 200 and 'لوحة التحكم' in login_response.text:
            print("✅ تم تسجيل الدخول بنجاح")
            return session
        elif login_response.status_code == 302:
            # إعادة توجيه - قد يكون تسجيل دخول ناجح
            dashboard_response = session.get(urljoin(BASE_URL, '/dashboard'), timeout=10)
            if dashboard_response.status_code == 200:
                print("✅ تم تسجيل الدخول بنجاح (مع إعادة توجيه)")
                return session
        
        print("❌ فشل في تسجيل الدخول")
        return None
        
    except Exception as e:
        print(f"❌ خطأ في تسجيل الدخول: {e}")
        return None

def test_add_pages_with_login(session):
    """اختبار صفحات الإضافة مع تسجيل الدخول"""
    add_pages = [
        ('/clients/add', 'إضافة عميل/وكيل جديد'),
        ('/cases/add', 'إضافة قضية جديدة'),
        ('/properties/add', 'إضافة عقار جديد'),
        ('/tenants/add', 'إضافة مستأجر جديد'),
        ('/finance/add', 'إضافة سند مالي جديد'),
        ('/tasks/add', 'إضافة مهمة جديدة'),
        ('/appointments/add', 'إضافة موعد جديد'),
        ('/debts/add', 'إضافة دين جديد')
    ]
    
    successful_pages = 0
    total_pages = len(add_pages)
    
    print("\n🔍 اختبار صفحات الإضافة مع تسجيل الدخول:")
    print("-" * 60)
    
    for page_path, expected_title in add_pages:
        try:
            page_url = urljoin(BASE_URL, page_path)
            response = session.get(page_url, timeout=10)
            
            if response.status_code == 200:
                # التحقق من وجود العنوان المتوقع
                if expected_title in response.text:
                    print(f"✅ {page_path} - يعمل بشكل صحيح")
                    successful_pages += 1
                else:
                    print(f"⚠️ {page_path} - متاح ولكن المحتوى غير متوقع")
                    successful_pages += 1
            else:
                print(f"❌ {page_path} - كود خطأ: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {page_path} - خطأ: {e}")
    
    print(f"\n📊 صفحات الإضافة: {successful_pages}/{total_pages} تعمل بشكل صحيح")
    return successful_pages == total_pages

def test_back_buttons(session):
    """اختبار أزرار الرجوع في صفحات الإضافة"""
    pages_with_back_buttons = [
        ('/clients/add', 'رجوع'),
        ('/cases/add', 'رجوع'),
        ('/properties/add', 'رجوع'),
        ('/tenants/add', 'رجوع'),
        ('/finance/add', 'رجوع'),
        ('/tasks/add', 'رجوع'),
        ('/appointments/add', 'رجوع'),
        ('/debts/add', 'رجوع')
    ]
    
    successful_buttons = 0
    total_buttons = len(pages_with_back_buttons)
    
    print("\n🔙 اختبار أزرار الرجوع:")
    print("-" * 40)
    
    for page_path, button_text in pages_with_back_buttons:
        try:
            page_url = urljoin(BASE_URL, page_path)
            response = session.get(page_url, timeout=10)
            
            if response.status_code == 200:
                if button_text in response.text and 'btn-outline-secondary' in response.text:
                    print(f"✅ {page_path} - زر الرجوع موجود")
                    successful_buttons += 1
                else:
                    print(f"❌ {page_path} - زر الرجوع مفقود")
            else:
                print(f"❌ {page_path} - لا يمكن الوصول للصفحة")
                
        except Exception as e:
            print(f"❌ {page_path} - خطأ: {e}")
    
    print(f"\n📊 أزرار الرجوع: {successful_buttons}/{total_buttons} موجودة")
    return successful_buttons == total_buttons

def test_dashboard_quick_actions(session):
    """اختبار الإجراءات السريعة في لوحة التحكم"""
    try:
        dashboard_url = urljoin(BASE_URL, '/dashboard')
        response = session.get(dashboard_url, timeout=10)
        
        if response.status_code != 200:
            print("❌ لا يمكن الوصول للوحة التحكم")
            return False
        
        quick_actions = [
            'إضافة عميل جديد',
            'إضافة قضية جديدة', 
            'إضافة عقار',
            'إضافة سند مالي',
            'إضافة مهمة'
        ]
        
        found_actions = 0
        total_actions = len(quick_actions)
        
        print("\n⚡ اختبار الإجراءات السريعة في لوحة التحكم:")
        print("-" * 50)
        
        for action in quick_actions:
            if action in response.text:
                print(f"✅ {action} - موجود")
                found_actions += 1
            else:
                print(f"❌ {action} - مفقود")
        
        print(f"\n📊 الإجراءات السريعة: {found_actions}/{total_actions} موجودة")
        return found_actions == total_actions
        
    except Exception as e:
        print(f"❌ خطأ في اختبار لوحة التحكم: {e}")
        return False

def test_list_pages_add_buttons(session):
    """اختبار أزرار الإضافة في صفحات القوائم"""
    list_pages = [
        ('/clients', 'إضافة عميل'),
        ('/cases', 'إضافة قضية'),
        ('/properties', 'إضافة عقار'),
        ('/tenants', 'إضافة مستأجر'),
        ('/finance', 'إضافة'),
        ('/tasks', 'إضافة مهمة'),
        ('/appointments', 'إضافة موعد'),
        ('/debts', 'إضافة دين'),
        ('/fees', 'إضافة أتعاب')
    ]
    
    successful_buttons = 0
    total_buttons = len(list_pages)
    
    print("\n📋 اختبار أزرار الإضافة في صفحات القوائم:")
    print("-" * 50)
    
    for page_path, button_text in list_pages:
        try:
            page_url = urljoin(BASE_URL, page_path)
            response = session.get(page_url, timeout=10)
            
            if response.status_code == 200:
                if button_text in response.text:
                    print(f"✅ {page_path} - زر الإضافة موجود")
                    successful_buttons += 1
                else:
                    print(f"❌ {page_path} - زر الإضافة مفقود")
            else:
                print(f"❌ {page_path} - لا يمكن الوصول للصفحة")
                
        except Exception as e:
            print(f"❌ {page_path} - خطأ: {e}")
    
    print(f"\n📊 أزرار الإضافة في القوائم: {successful_buttons}/{total_buttons} موجودة")
    return successful_buttons >= total_buttons * 0.8  # 80% نجاح مقبول

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء الاختبار الشامل لأزرار الإضافة")
    print("=" * 60)
    
    # تسجيل الدخول
    print("\n1️⃣ تسجيل الدخول:")
    session = login_to_system()
    if not session:
        print("❌ فشل في تسجيل الدخول. لا يمكن متابعة الاختبارات.")
        return False
    
    tests_passed = 0
    total_tests = 4
    
    # اختبار صفحات الإضافة
    print("\n2️⃣ اختبار صفحات الإضافة:")
    if test_add_pages_with_login(session):
        tests_passed += 1
    
    # اختبار أزرار الرجوع
    print("\n3️⃣ اختبار أزرار الرجوع:")
    if test_back_buttons(session):
        tests_passed += 1
    
    # اختبار الإجراءات السريعة
    print("\n4️⃣ اختبار الإجراءات السريعة:")
    if test_dashboard_quick_actions(session):
        tests_passed += 1
    
    # اختبار أزرار الإضافة في القوائم
    print("\n5️⃣ اختبار أزرار الإضافة في القوائم:")
    if test_list_pages_add_buttons(session):
        tests_passed += 1
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار الشامل:")
    print(f"✅ اختبارات نجحت: {tests_passed}")
    print(f"❌ اختبارات فشلت: {total_tests - tests_passed}")
    print(f"🎯 معدل النجاح: {(tests_passed/total_tests)*100:.1f}%")
    
    if tests_passed == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! أزرار الإضافة تعمل بشكل مثالي.")
        return True
    elif tests_passed >= total_tests * 0.75:
        print("\n⚠️ معظم الاختبارات نجحت. أزرار الإضافة تعمل بشكل جيد.")
        return True
    else:
        print("\n❌ عدة اختبارات فشلت. تحتاج أزرار الإضافة إلى مراجعة.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
