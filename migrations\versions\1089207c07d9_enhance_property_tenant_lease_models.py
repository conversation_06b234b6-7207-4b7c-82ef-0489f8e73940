"""enhance_property_tenant_lease_models

Revision ID: 1089207c07d9
Revises: 586ce628dfbd
Create Date: 2025-07-02 18:58:30.351651

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1089207c07d9'
down_revision = '586ce628dfbd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('lease', schema=None) as batch_op:
        batch_op.add_column(sa.Column('contract_date', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('deposit', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('commission', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('currency', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('contract_duration', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('payment_day', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('late_fee_rate', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('renewal_option', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('status', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('termination_date', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('termination_reason', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('terms_conditions', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('special_terms', sa.Text(), nullable=True))

    with op.batch_alter_table('property', schema=None) as batch_op:
        batch_op.add_column(sa.Column('area', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('rooms_count', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('bathrooms_count', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('floor_number', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('building_age', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('furnished', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('parking', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('elevator', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('monthly_rent', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('currency', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('deposit_amount', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('commission_rate', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('status', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('availability_date', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('owner_name', sa.String(length=150), nullable=True))
        batch_op.add_column(sa.Column('owner_phone', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('owner_email', sa.String(length=120), nullable=True))
        batch_op.add_column(sa.Column('created_date', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('updated_date', sa.DateTime(), nullable=True))

    with op.batch_alter_table('tenant', schema=None) as batch_op:
        batch_op.add_column(sa.Column('national_id', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('work_place', sa.String(length=200), nullable=True))
        batch_op.add_column(sa.Column('monthly_income', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('emergency_contact', sa.String(length=150), nullable=True))
        batch_op.add_column(sa.Column('emergency_phone', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('credit_score', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('payment_history', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('notes', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('status', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('registration_date', sa.DateTime(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tenant', schema=None) as batch_op:
        batch_op.drop_column('registration_date')
        batch_op.drop_column('status')
        batch_op.drop_column('notes')
        batch_op.drop_column('payment_history')
        batch_op.drop_column('credit_score')
        batch_op.drop_column('emergency_phone')
        batch_op.drop_column('emergency_contact')
        batch_op.drop_column('monthly_income')
        batch_op.drop_column('work_place')
        batch_op.drop_column('national_id')

    with op.batch_alter_table('property', schema=None) as batch_op:
        batch_op.drop_column('updated_date')
        batch_op.drop_column('created_date')
        batch_op.drop_column('owner_email')
        batch_op.drop_column('owner_phone')
        batch_op.drop_column('owner_name')
        batch_op.drop_column('availability_date')
        batch_op.drop_column('status')
        batch_op.drop_column('commission_rate')
        batch_op.drop_column('deposit_amount')
        batch_op.drop_column('currency')
        batch_op.drop_column('monthly_rent')
        batch_op.drop_column('elevator')
        batch_op.drop_column('parking')
        batch_op.drop_column('furnished')
        batch_op.drop_column('building_age')
        batch_op.drop_column('floor_number')
        batch_op.drop_column('bathrooms_count')
        batch_op.drop_column('rooms_count')
        batch_op.drop_column('area')

    with op.batch_alter_table('lease', schema=None) as batch_op:
        batch_op.drop_column('special_terms')
        batch_op.drop_column('terms_conditions')
        batch_op.drop_column('termination_reason')
        batch_op.drop_column('termination_date')
        batch_op.drop_column('status')
        batch_op.drop_column('renewal_option')
        batch_op.drop_column('late_fee_rate')
        batch_op.drop_column('payment_day')
        batch_op.drop_column('contract_duration')
        batch_op.drop_column('currency')
        batch_op.drop_column('commission')
        batch_op.drop_column('deposit')
        batch_op.drop_column('contract_date')

    # ### end Alembic commands ###
