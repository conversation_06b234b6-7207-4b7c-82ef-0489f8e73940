"""
خدمة الإشعارات والتنبيهات
Notifications and Alerts Service
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timezone, timedelta
from flask import current_app, url_for
from .notification_models import Notification, Alert, NotificationSettings, create_notification, create_alert
from . import db


class NotificationService:
    """خدمة إدارة الإشعارات"""
    
    @staticmethod
    def create_notification(user_id, title, message, **kwargs):
        """إنشاء إشعار جديد"""
        # فحص إعدادات المستخدم
        settings = NotificationSettings.get_or_create_for_user(user_id)
        
        if not settings.enabled:
            return None
        
        # فحص نوع الإشعار
        notification_type = kwargs.get('type', 'info')
        if not NotificationService._is_type_enabled(settings, notification_type):
            return None
        
        # فحص ساعات الهدوء
        if settings.is_quiet_time():
            kwargs['delayed'] = True
        
        # إنشاء الإشعار
        notification = create_notification(user_id, title, message, **kwargs)
        
        # إرسال الإشعار
        NotificationService._send_notification(notification, settings)
        
        return notification
    
    @staticmethod
    def _is_type_enabled(settings, notification_type):
        """فحص ما إذا كان نوع الإشعار مفعل"""
        type_mapping = {
            'case': settings.case_notifications,
            'client': settings.client_notifications,
            'appointment': settings.appointment_notifications,
            'task': settings.task_notifications,
            'payment': settings.payment_notifications,
            'document': settings.document_notifications
        }
        return type_mapping.get(notification_type, True)
    
    @staticmethod
    def _send_notification(notification, settings):
        """إرسال الإشعار بالطرق المختلفة"""
        # إشعار سطح المكتب (يتم التعامل معه في JavaScript)
        if settings.desktop_enabled:
            notification.desktop_sent = True
        
        # إرسال بريد إلكتروني
        if settings.email_enabled:
            NotificationService._send_email_notification(notification)
        
        db.session.commit()
    
    @staticmethod
    def _send_email_notification(notification):
        """إرسال إشعار بالبريد الإلكتروني"""
        try:
            # إعدادات البريد الإلكتروني
            smtp_server = current_app.config.get('MAIL_SERVER', 'localhost')
            smtp_port = current_app.config.get('MAIL_PORT', 587)
            smtp_username = current_app.config.get('MAIL_USERNAME')
            smtp_password = current_app.config.get('MAIL_PASSWORD')
            
            if not smtp_username or not smtp_password:
                return False
            
            # إنشاء الرسالة
            msg = MIMEMultipart()
            msg['From'] = smtp_username
            msg['To'] = notification.user.email
            msg['Subject'] = f"إشعار: {notification.title}"
            
            # محتوى الرسالة
            body = f"""
            <html>
            <body dir="rtl">
                <h2>{notification.title}</h2>
                <p>{notification.message}</p>
                <hr>
                <p><small>تم إرسال هذا الإشعار من نظام إدارة مكتب المحاماة</small></p>
                {f'<p><a href="{notification.url}">انقر هنا للمتابعة</a></p>' if notification.url else ''}
            </body>
            </html>
            """
            
            msg.attach(MIMEText(body, 'html', 'utf-8'))
            
            # إرسال الرسالة
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.send_message(msg)
            server.quit()
            
            notification.email_sent = True
            return True
            
        except Exception as e:
            print(f"خطأ في إرسال البريد الإلكتروني: {e}")
            return False
    
    @staticmethod
    def create_case_notification(user_id, case, action):
        """إنشاء إشعار متعلق بقضية"""
        actions = {
            'created': 'تم إنشاء قضية جديدة',
            'updated': 'تم تحديث القضية',
            'closed': 'تم إغلاق القضية',
            'reopened': 'تم إعادة فتح القضية'
        }
        
        title = actions.get(action, 'تحديث في القضية')
        message = f"القضية: {case.title} - العميل: {case.client.name if case.client else 'غير محدد'}"
        
        return NotificationService.create_notification(
            user_id=user_id,
            title=title,
            message=message,
            type='case',
            url=url_for('case_details', case_id=case.id),
            action='open_case',
            data={'case_id': case.id}
        )
    
    @staticmethod
    def create_client_notification(user_id, client, action):
        """إنشاء إشعار متعلق بعميل"""
        actions = {
            'created': 'تم إضافة عميل جديد',
            'updated': 'تم تحديث بيانات العميل',
            'deleted': 'تم حذف العميل'
        }
        
        title = actions.get(action, 'تحديث في العميل')
        message = f"العميل: {client.name}"
        
        return NotificationService.create_notification(
            user_id=user_id,
            title=title,
            message=message,
            type='client',
            url=url_for('client_details', client_id=client.id),
            action='open_client',
            data={'client_id': client.id}
        )
    
    @staticmethod
    def create_appointment_notification(user_id, appointment, minutes_before=30):
        """إنشاء إشعار موعد قادم"""
        title = "موعد قادم"
        message = f"موعد مع {appointment.client_name} في {appointment.time.strftime('%H:%M')}"
        
        # جدولة الإشعار قبل الموعد
        schedule_time = appointment.datetime - timedelta(minutes=minutes_before)
        
        return NotificationService.create_scheduled_alert(
            user_id=user_id,
            title=title,
            message=message,
            schedule_time=schedule_time,
            type='appointment',
            priority='high',
            url=url_for('calendar'),
            action='open_appointment',
            data={'event_id': appointment.id}
        )
    
    @staticmethod
    def create_task_notification(user_id, task, action):
        """إنشاء إشعار متعلق بمهمة"""
        actions = {
            'created': 'تم إنشاء مهمة جديدة',
            'updated': 'تم تحديث المهمة',
            'completed': 'تم إكمال المهمة',
            'overdue': 'مهمة متأخرة'
        }
        
        title = actions.get(action, 'تحديث في المهمة')
        message = f"المهمة: {task.title}"
        
        priority = 'high' if action == 'overdue' else 'normal'
        
        return NotificationService.create_notification(
            user_id=user_id,
            title=title,
            message=message,
            type='task',
            priority=priority,
            url=url_for('tasks'),
            action='open_task',
            data={'task_id': task.id}
        )
    
    @staticmethod
    def create_payment_notification(user_id, payment, action):
        """إنشاء إشعار متعلق بدفعة"""
        actions = {
            'received': 'تم استلام دفعة جديدة',
            'overdue': 'دفعة متأخرة',
            'reminder': 'تذكير بدفعة مستحقة'
        }
        
        title = actions.get(action, 'تحديث في الدفعة')
        message = f"المبلغ: {payment.amount} - العميل: {payment.client.name if hasattr(payment, 'client') else 'غير محدد'}"
        
        priority = 'high' if action in ['overdue', 'reminder'] else 'normal'
        
        return NotificationService.create_notification(
            user_id=user_id,
            title=title,
            message=message,
            type='payment',
            priority=priority
        )
    
    @staticmethod
    def create_document_notification(user_id, document, action):
        """إنشاء إشعار متعلق بوثيقة"""
        actions = {
            'uploaded': 'تم رفع وثيقة جديدة',
            'updated': 'تم تحديث الوثيقة',
            'deleted': 'تم حذف الوثيقة'
        }
        
        title = actions.get(action, 'تحديث في الوثيقة')
        message = f"الوثيقة: {document.name}"
        
        return NotificationService.create_notification(
            user_id=user_id,
            title=title,
            message=message,
            type='document'
        )
    
    @staticmethod
    def create_scheduled_alert(user_id, title, message, schedule_time, **kwargs):
        """إنشاء تنبيه مجدول"""
        return create_alert(
            user_id=user_id,
            title=title,
            message=message,
            schedule_time=schedule_time,
            **kwargs
        )
    
    @staticmethod
    def process_pending_alerts():
        """معالجة التنبيهات المعلقة"""
        return Alert.process_pending_alerts()
    
    @staticmethod
    def cleanup_old_notifications(days_old=30):
        """تنظيف الإشعارات القديمة"""
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)
        
        old_notifications = Notification.query.filter(
            Notification.created_at < cutoff_date,
            Notification.read == True
        ).all()
        
        count = len(old_notifications)
        for notification in old_notifications:
            db.session.delete(notification)
        
        db.session.commit()
        return count
    
    @staticmethod
    def get_user_notifications(user_id, limit=50, unread_only=False):
        """الحصول على إشعارات المستخدم"""
        query = Notification.query.filter_by(user_id=user_id)
        
        if unread_only:
            query = query.filter_by(read=False)
        
        return query.order_by(Notification.created_at.desc()).limit(limit).all()
    
    @staticmethod
    def mark_notification_as_read(notification_id, user_id):
        """تمييز إشعار كمقروء"""
        notification = Notification.query.filter_by(
            id=notification_id,
            user_id=user_id
        ).first()
        
        if notification:
            notification.mark_as_read()
            return True
        return False
    
    @staticmethod
    def mark_all_as_read(user_id):
        """تمييز جميع الإشعارات كمقروءة"""
        notifications = Notification.query.filter_by(
            user_id=user_id,
            read=False
        ).all()
        
        for notification in notifications:
            notification.read = True
            notification.read_at = datetime.now(timezone.utc)
        
        db.session.commit()
        return len(notifications)
    
    @staticmethod
    def delete_notification(notification_id, user_id):
        """حذف إشعار"""
        notification = Notification.query.filter_by(
            id=notification_id,
            user_id=user_id
        ).first()
        
        if notification:
            db.session.delete(notification)
            db.session.commit()
            return True
        return False
    
    @staticmethod
    def clear_all_notifications(user_id):
        """حذف جميع الإشعارات"""
        notifications = Notification.query.filter_by(user_id=user_id).all()
        
        for notification in notifications:
            db.session.delete(notification)
        
        db.session.commit()
        return len(notifications)
    
    @staticmethod
    def get_notification_settings(user_id):
        """الحصول على إعدادات الإشعارات"""
        return NotificationSettings.get_or_create_for_user(user_id)
    
    @staticmethod
    def update_notification_settings(user_id, settings_data):
        """تحديث إعدادات الإشعارات"""
        settings = NotificationSettings.get_or_create_for_user(user_id)
        
        for key, value in settings_data.items():
            if hasattr(settings, key):
                setattr(settings, key, value)
        
        db.session.commit()
        return settings


class NotificationScheduler:
    """مجدول الإشعارات"""
    
    @staticmethod
    def schedule_daily_reminders():
        """جدولة التذكيرات اليومية"""
        # تذكيرات المواعيد
        NotificationScheduler._schedule_appointment_reminders()
        
        # تذكيرات المهام المتأخرة
        NotificationScheduler._schedule_overdue_task_reminders()
        
        # تذكيرات الدفعات المستحقة
        NotificationScheduler._schedule_payment_reminders()
    
    @staticmethod
    def _schedule_appointment_reminders():
        """جدولة تذكيرات المواعيد"""
        from .calendar_models import Event
        
        tomorrow = datetime.now(timezone.utc) + timedelta(days=1)
        tomorrow_start = tomorrow.replace(hour=0, minute=0, second=0, microsecond=0)
        tomorrow_end = tomorrow.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        appointments = Event.query.filter(
            Event.start_time >= tomorrow_start,
            Event.start_time <= tomorrow_end,
            Event.type == 'appointment'
        ).all()
        
        for appointment in appointments:
            NotificationService.create_appointment_notification(
                user_id=1,  # يجب تحديد المستخدم المناسب
                appointment=appointment,
                minutes_before=30
            )
    
    @staticmethod
    def _schedule_overdue_task_reminders():
        """جدولة تذكيرات المهام المتأخرة"""
        from .calendar_models import TaskItem
        
        now = datetime.now(timezone.utc)
        overdue_tasks = TaskItem.query.filter(
            TaskItem.due_date < now,
            TaskItem.completed == False
        ).all()
        
        for task in overdue_tasks:
            NotificationService.create_task_notification(
                user_id=1,  # يجب تحديد المستخدم المناسب
                task=task,
                action='overdue'
            )
    
    @staticmethod
    def _schedule_payment_reminders():
        """جدولة تذكيرات الدفعات"""
        from .models import Debt
        
        now = datetime.now(timezone.utc)
        overdue_debts = Debt.query.filter(
            Debt.due_date < now,
            Debt.paid == False
        ).all()
        
        for debt in overdue_debts:
            NotificationService.create_payment_notification(
                user_id=1,  # يجب تحديد المستخدم المناسب
                payment=debt,
                action='overdue'
            )
