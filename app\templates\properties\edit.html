{% extends "base.html" %}

{% block title %}تعديل عقار{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-building-edit"></i> تعديل بيانات العقار</h1>
                    <p>تعديل بيانات العقار المحدد</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit text-primary me-2"></i>
                        تعديل بيانات العقار
                    </h5>
                </div>
                <div class="card-body">
    <form method="POST">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <div class="mb-3">
            <label class="form-label">اسم العقار</label>
            <input type="text" name="name" class="form-control" value="{{ property.name }}" required>
        </div>
        <div class="mb-3">
            <label class="form-label">النوع</label>
            <input type="text" name="type" class="form-control" value="{{ property.type }}">
        </div>
        <div class="mb-3">
            <label class="form-label">العنوان</label>
            <input type="text" name="address" class="form-control" value="{{ property.address }}">
        </div>
        <div class="mb-3">
            <label class="form-label">الوصف</label>
            <textarea name="description" class="form-control">{{ property.description }}</textarea>
        </div>
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                تحديث البيانات
                            </button>
                            <a href="{{ url_for('properties_list') }}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-arrow-right me-2"></i>
                                رجوع
                            </a>
                        </div>
                    </div>
                </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
