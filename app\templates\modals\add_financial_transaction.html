<form method="POST" action="{{ url_for('add_financial_transaction') }}" class="modal-form" id="addFinancialTransactionForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    
    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-money-bill"></i> معلومات السند المالي الأساسية
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">نوع المعاملة <span class="text-danger">*</span></label>
                <select name="type" class="form-control" required>
                    <option value="">اختر نوع المعاملة</option>
                    <option value="إيراد">إيراد</option>
                    <option value="مصروف">مصروف</option>
                    <option value="أتعاب">أتعاب</option>
                    <option value="رسوم">رسوم</option>
                    <option value="إيجار">إيجار</option>
                    <option value="دين">دين</option>
                    <option value="استثمار">استثمار</option>
                    <option value="أخرى">أخرى</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                <input type="number" name="amount" class="form-control" step="0.01" required 
                       placeholder="المبلغ">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">العملة</label>
                <select name="currency" class="form-control">
                    <option value="شيكل">شيكل</option>
                    <option value="دولار">دولار أمريكي</option>
                    <option value="دينار">دينار أردني</option>
                    <option value="يورو">يورو</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">تاريخ المعاملة</label>
                <input type="date" name="date" class="form-control" value="{{ today }}">
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-link"></i> ربط المعاملة
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">العميل</label>
                <select name="client_id" class="form-control">
                    <option value="">اختر العميل (اختياري)</option>
                    {% for client in clients %}
                    <option value="{{ client.id }}">{{ client.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">القضية</label>
                <select name="case_id" class="form-control">
                    <option value="">اختر القضية (اختياري)</option>
                    {% for case in cases %}
                    <option value="{{ case.id }}">{{ case.title }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-info-circle"></i> تفاصيل إضافية
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">طريقة الدفع</label>
                <select name="payment_method" class="form-control">
                    <option value="نقدي">نقدي</option>
                    <option value="شيك">شيك</option>
                    <option value="تحويل بنكي">تحويل بنكي</option>
                    <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                    <option value="أخرى">أخرى</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">رقم المرجع</label>
                <input type="text" name="reference_number" class="form-control" 
                       placeholder="رقم الشيك أو التحويل">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-12">
                <label class="form-label">الوصف <span class="text-danger">*</span></label>
                <textarea name="description" class="form-control" rows="3" required
                          placeholder="وصف المعاملة المالية..."></textarea>
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-12">
                <label class="form-label">ملاحظات</label>
                <textarea name="notes" class="form-control" rows="2" 
                          placeholder="ملاحظات إضافية..."></textarea>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <button type="submit" class="btn btn-success px-4">
            <i class="fa fa-save"></i> حفظ السند المالي
        </button>
        <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
            إلغاء
        </button>
    </div>
</form>

<script>
document.getElementById('addFinancialTransactionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            if (modal) modal.hide();
            showAlert('تم إضافة السند المالي بنجاح', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(data.message || 'حدث خطأ أثناء إضافة السند المالي', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
});

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
    document.body.appendChild(alertDiv);
    setTimeout(() => alertDiv.remove(), 5000);
}
</script>

<style>
.modal-form .form-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.modal-form .form-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
}

.modal-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.modal-form .text-danger {
    color: #dc3545 !important;
}

.modal-form .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.modal-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
</style>
