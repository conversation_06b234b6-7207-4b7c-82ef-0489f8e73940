#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة الأداء عند زيادة البيانات
Performance Monitoring System for Data Growth
"""

import sqlite3
import time
import psutil
import os
from datetime import datetime, timedelta
import json
import schedule

class PerformanceMonitoringSystem:
    def __init__(self):
        self.db_path = 'instance/lawoffice.db'
        self.ensure_performance_logs_table()
    
    def ensure_performance_logs_table(self):
        """إنشاء جدول سجلات الأداء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS performance_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    metric_unit TEXT,
                    database_size INTEGER,
                    total_records INTEGER,
                    memory_usage REAL,
                    cpu_usage REAL,
                    disk_usage REAL,
                    query_time REAL,
                    created_at TEXT NOT NULL
                )
            """)
            
            conn.commit()
            conn.close()
            print("✅ تم إعداد جدول سجلات الأداء")
            
        except Exception as e:
            print(f"⚠️ تحذير: خطأ في إنشاء جدول الأداء: {e}")
    
    def get_database_stats(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # حجم قاعدة البيانات
            db_size = os.path.getsize(self.db_path)
            
            # عدد الجداول
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            
            # إجمالي السجلات في الجداول الرئيسية
            main_tables = ['clients', 'cases', 'properties', 'tenants', 'leases', 
                          'financial_transactions', 'fees', 'debts', 'tasks', 'appointments']
            
            total_records = 0
            table_stats = {}
            
            for table in main_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    table_stats[table] = count
                    total_records += count
                except:
                    table_stats[table] = 0
            
            conn.close()
            
            return {
                'db_size': db_size,
                'table_count': table_count,
                'total_records': total_records,
                'table_stats': table_stats
            }
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على إحصائيات قاعدة البيانات: {e}")
            return None
    
    def get_system_performance(self):
        """الحصول على إحصائيات أداء النظام"""
        try:
            # استخدام الذاكرة
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            
            # استخدام المعالج
            cpu_usage = psutil.cpu_percent(interval=1)
            
            # استخدام القرص
            disk = psutil.disk_usage('.')
            disk_usage = disk.percent
            
            # معلومات العملية الحالية
            process = psutil.Process()
            process_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            return {
                'memory_usage': memory_usage,
                'cpu_usage': cpu_usage,
                'disk_usage': disk_usage,
                'process_memory': process_memory,
                'available_memory': memory.available / 1024 / 1024 / 1024,  # GB
                'total_memory': memory.total / 1024 / 1024 / 1024  # GB
            }
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على إحصائيات الأداء: {e}")
            return None
    
    def measure_query_performance(self):
        """قياس أداء الاستعلامات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query_times = {}
            
            # اختبار استعلامات مختلفة
            test_queries = {
                'simple_count': "SELECT COUNT(*) FROM clients",
                'join_query': """
                    SELECT c.name, COUNT(ca.id) as case_count 
                    FROM clients c 
                    LEFT JOIN cases ca ON c.id = ca.client_id 
                    GROUP BY c.id 
                    LIMIT 10
                """,
                'complex_query': """
                    SELECT 
                        c.name,
                        COUNT(DISTINCT ca.id) as cases,
                        COUNT(DISTINCT ft.id) as transactions,
                        SUM(ft.amount) as total_amount
                    FROM clients c
                    LEFT JOIN cases ca ON c.id = ca.client_id
                    LEFT JOIN financial_transactions ft ON c.id = ft.client_id
                    GROUP BY c.id
                    LIMIT 5
                """
            }
            
            for query_name, query in test_queries.items():
                start_time = time.time()
                try:
                    cursor.execute(query)
                    cursor.fetchall()
                    query_time = time.time() - start_time
                    query_times[query_name] = query_time
                except Exception as e:
                    query_times[query_name] = -1  # خطأ في الاستعلام
            
            conn.close()
            return query_times
            
        except Exception as e:
            print(f"❌ خطأ في قياس أداء الاستعلامات: {e}")
            return {}
    
    def log_performance_metric(self, metric_name, metric_value, metric_unit, 
                             db_stats, sys_stats, query_time=None):
        """تسجيل مقياس الأداء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO performance_logs 
                (metric_name, metric_value, metric_unit, database_size, total_records,
                 memory_usage, cpu_usage, disk_usage, query_time, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                metric_name, metric_value, metric_unit,
                db_stats['db_size'] if db_stats else 0,
                db_stats['total_records'] if db_stats else 0,
                sys_stats['memory_usage'] if sys_stats else 0,
                sys_stats['cpu_usage'] if sys_stats else 0,
                sys_stats['disk_usage'] if sys_stats else 0,
                query_time,
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"⚠️ تحذير: خطأ في تسجيل مقياس الأداء: {e}")
    
    def run_performance_monitoring(self):
        """تشغيل مراقبة الأداء الشاملة"""
        print("📊 بدء مراقبة الأداء الشاملة")
        print("=" * 50)
        
        # الحصول على إحصائيات قاعدة البيانات
        print("1️⃣ جمع إحصائيات قاعدة البيانات...")
        db_stats = self.get_database_stats()
        
        if db_stats:
            print(f"   📁 حجم قاعدة البيانات: {db_stats['db_size'] / 1024 / 1024:.2f} MB")
            print(f"   📊 إجمالي السجلات: {db_stats['total_records']:,}")
            print(f"   🗂️ عدد الجداول: {db_stats['table_count']}")
            
            # عرض إحصائيات الجداول الرئيسية
            print("   📋 إحصائيات الجداول:")
            for table, count in db_stats['table_stats'].items():
                if count > 0:
                    print(f"      - {table}: {count:,} سجل")
        
        # الحصول على إحصائيات أداء النظام
        print("\n2️⃣ جمع إحصائيات أداء النظام...")
        sys_stats = self.get_system_performance()
        
        if sys_stats:
            print(f"   🧠 استخدام الذاكرة: {sys_stats['memory_usage']:.1f}%")
            print(f"   ⚡ استخدام المعالج: {sys_stats['cpu_usage']:.1f}%")
            print(f"   💾 استخدام القرص: {sys_stats['disk_usage']:.1f}%")
            print(f"   📱 ذاكرة العملية: {sys_stats['process_memory']:.1f} MB")
            print(f"   💽 الذاكرة المتاحة: {sys_stats['available_memory']:.1f} GB")
        
        # قياس أداء الاستعلامات
        print("\n3️⃣ قياس أداء الاستعلامات...")
        query_times = self.measure_query_performance()
        
        if query_times:
            print("   ⏱️ أوقات الاستعلامات:")
            for query_name, query_time in query_times.items():
                if query_time >= 0:
                    print(f"      - {query_name}: {query_time:.3f} ثانية")
                    # تسجيل وقت الاستعلام
                    self.log_performance_metric(
                        f"query_time_{query_name}", query_time, "seconds",
                        db_stats, sys_stats, query_time
                    )
                else:
                    print(f"      - {query_name}: خطأ في الاستعلام")
        
        # تسجيل المقاييس الرئيسية
        if db_stats and sys_stats:
            # تسجيل حجم قاعدة البيانات
            self.log_performance_metric(
                "database_size", db_stats['db_size'], "bytes",
                db_stats, sys_stats
            )
            
            # تسجيل إجمالي السجلات
            self.log_performance_metric(
                "total_records", db_stats['total_records'], "count",
                db_stats, sys_stats
            )
            
            # تسجيل استخدام الذاكرة
            self.log_performance_metric(
                "memory_usage", sys_stats['memory_usage'], "percent",
                db_stats, sys_stats
            )
            
            # تسجيل استخدام المعالج
            self.log_performance_metric(
                "cpu_usage", sys_stats['cpu_usage'], "percent",
                db_stats, sys_stats
            )
        
        # تحليل الأداء وإعطاء توصيات
        print("\n4️⃣ تحليل الأداء والتوصيات...")
        self.analyze_performance(db_stats, sys_stats, query_times)
        
        print("\n✅ تم إكمال مراقبة الأداء بنجاح!")
    
    def analyze_performance(self, db_stats, sys_stats, query_times):
        """تحليل الأداء وإعطاء التوصيات"""
        recommendations = []
        
        if db_stats:
            # تحليل حجم قاعدة البيانات
            db_size_mb = db_stats['db_size'] / 1024 / 1024
            if db_size_mb > 100:
                recommendations.append("🔍 قاعدة البيانات كبيرة الحجم - فكر في الأرشفة")
            
            # تحليل عدد السجلات
            if db_stats['total_records'] > 10000:
                recommendations.append("📈 عدد كبير من السجلات - فكر في فهرسة الجداول")
        
        if sys_stats:
            # تحليل استخدام الذاكرة
            if sys_stats['memory_usage'] > 80:
                recommendations.append("🧠 استخدام عالي للذاكرة - قم بإغلاق التطبيقات غير الضرورية")
            
            # تحليل استخدام المعالج
            if sys_stats['cpu_usage'] > 70:
                recommendations.append("⚡ استخدام عالي للمعالج - تحقق من العمليات الجارية")
            
            # تحليل استخدام القرص
            if sys_stats['disk_usage'] > 85:
                recommendations.append("💾 مساحة القرص منخفضة - قم بتنظيف الملفات غير الضرورية")
        
        if query_times:
            # تحليل أوقات الاستعلامات
            slow_queries = [name for name, time in query_times.items() if time > 1.0]
            if slow_queries:
                recommendations.append(f"⏱️ استعلامات بطيئة: {', '.join(slow_queries)}")
        
        # عرض التوصيات
        if recommendations:
            print("   ⚠️ توصيات تحسين الأداء:")
            for rec in recommendations:
                print(f"      {rec}")
        else:
            print("   ✅ الأداء ممتاز - لا توجد توصيات")
    
    def schedule_performance_monitoring(self):
        """جدولة مراقبة الأداء الدورية"""
        # مراقبة كل 30 دقيقة
        schedule.every(30).minutes.do(self.run_performance_monitoring)
        
        # مراقبة شاملة يومياً في الساعة 4:00 صباحاً
        schedule.every().day.at("04:00").do(self.run_performance_monitoring)
        
        print("✅ تم جدولة مراقبة الأداء الدورية:")
        print("   🔄 مراقبة سريعة كل 30 دقيقة")
        print("   📅 مراقبة شاملة يومياً في الساعة 4:00 صباحاً")
    
    def run_scheduler(self):
        """تشغيل جدولة مراقبة الأداء"""
        print("🔄 بدء تشغيل نظام مراقبة الأداء الدوري...")
        self.schedule_performance_monitoring()
        
        while True:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة

def main():
    """الدالة الرئيسية"""
    monitoring_system = PerformanceMonitoringSystem()
    
    print("📊 نظام مراقبة الأداء عند زيادة البيانات")
    print("=" * 60)
    
    # تشغيل مراقبة أداء فورية
    monitoring_system.run_performance_monitoring()
    
    print(f"\n✅ تم إعداد نظام مراقبة الأداء بنجاح!")
    print("📝 ملاحظة: لتشغيل الجدولة الدورية، استخدم: monitoring_system.run_scheduler()")

if __name__ == "__main__":
    main()
