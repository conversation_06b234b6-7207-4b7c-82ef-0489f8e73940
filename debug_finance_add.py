#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تشخيص مشكلة صفحة إضافة المعاملات المالية
"""

import requests
from urllib.parse import urljoin

BASE_URL = 'http://localhost:5000'
LOGIN_URL = urljoin(BASE_URL, '/lawyersameh')

def test_finance_add():
    """اختبار صفحة إضافة المعاملات المالية"""
    session = requests.Session()
    
    try:
        # تسجيل الدخول
        login_data = {
            'username': 'office',
            'password': '66889088'
        }
        
        print("🔐 محاولة تسجيل الدخول...")
        login_response = session.post(LOGIN_URL, data=login_data, timeout=10)
        
        if login_response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {login_response.status_code}")
            return
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة إضافة المعاملات المالية
        print("\n🧪 اختبار صفحة إضافة المعاملات المالية...")
        finance_add_url = urljoin(BASE_URL, '/finance/add')
        response = session.get(finance_add_url, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Content Length: {len(response.text)}")
        
        if response.status_code == 200:
            print("✅ صفحة إضافة المعاملات المالية تعمل بشكل صحيح")
        elif response.status_code == 500:
            print("❌ خطأ خادم 500 - مشكلة في الكود")
            print("أول 1000 حرف من الاستجابة:")
            print(response.text[:1000])
            print("\n" + "="*50)
            
            # البحث عن رسالة الخطأ
            if 'werkzeug.routing.exceptions.BuildError' in response.text:
                print("🔍 مشكلة في بناء URL")
            elif 'jinja2.exceptions.UndefinedError' in response.text:
                print("🔍 متغير غير معرف في القالب")
            elif 'NameError' in response.text:
                print("🔍 متغير غير معرف في Python")
            elif 'ImportError' in response.text:
                print("🔍 مشكلة في الاستيراد")
            else:
                print("🔍 نوع خطأ غير محدد")
                
        else:
            print(f"❌ خطأ غير متوقع: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    test_finance_add()
