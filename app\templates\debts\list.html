{% extends "base.html" %}

{% block title %}إدارة الديون{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-credit-card"></i> إدارة الديون</h1>
                    <p>عرض وإدارة جميع الديون والالتزامات المالية</p>
                </div>
                <div class="page-actions">
                    <a href="{{ url_for('add_debt') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة دين جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h5><i class="fa fa-exclamation-triangle"></i> إجمالي الديون</h5>
                    <h3>{{ "%.2f"|format(total_debt) }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h5><i class="fa fa-check-circle"></i> المدفوع</h5>
                    <h3>{{ "%.2f"|format(total_paid) }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h5><i class="fa fa-clock"></i> المتبقي</h5>
                    <h3>{{ "%.2f"|format(total_remaining) }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h5><i class="fa fa-list"></i> عدد الديون</h5>
                    <h3>{{ debts|length }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات حسب الحالة -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <h6 class="text-warning">الديون المستحقة</h6>
                    <h4>{{ pending_count }}</h4>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-danger">
                <div class="card-body text-center">
                    <h6 class="text-danger">الديون المتأخرة</h6>
                    <h4>{{ overdue_count }}</h4>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-success">
                <div class="card-body text-center">
                    <h6 class="text-success">الديون المسددة</h6>
                    <h4>{{ paid_count }}</h4>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الديون -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fa fa-table"></i> قائمة الديون</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>الدائن</th>
                            <th>نوع الدين</th>
                            <th>المبلغ</th>
                            <th>المدفوع</th>
                            <th>المتبقي</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>الحالة</th>
                            <th>الأولوية</th>
                            <th>العملة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for debt in debts %}
                        <tr class="{% if debt.status == 'متأخر' %}table-danger{% elif debt.status == 'مسدد' %}table-success{% endif %}">
                            <td>{{ debt.creditor_name }}</td>
                            <td>{{ debt.debt_type }}</td>
                            <td>{{ "%.2f"|format(debt.amount) }}</td>
                            <td>{{ "%.2f"|format(debt.paid_amount) }}</td>
                            <td>{{ "%.2f"|format(debt.remaining_amount) }}</td>
                            <td>
                                {% if debt.due_date %}
                                    {{ debt.due_date.strftime('%Y-%m-%d') }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge 
                                    {% if debt.status == 'مسدد' %}bg-success
                                    {% elif debt.status == 'متأخر' %}bg-danger
                                    {% else %}bg-warning{% endif %}">
                                    {{ debt.status }}
                                </span>
                            </td>
                            <td>
                                <span class="badge 
                                    {% if debt.priority == 'عاجلة' %}bg-danger
                                    {% elif debt.priority == 'عالية' %}bg-warning
                                    {% else %}bg-secondary{% endif %}">
                                    {{ debt.priority }}
                                </span>
                            </td>
                            <td>{{ debt.currency }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    {% if debt.remaining_amount > 0 %}
                                    <a href="{{ url_for('pay_debt', debt_id=debt.id) }}" 
                                       class="btn btn-success btn-sm" title="دفع">
                                        <i class="fa fa-money-bill"></i>
                                    </a>
                                    {% endif %}
                                    <a href="{{ url_for('edit_debt', debt_id=debt.id) }}" 
                                       class="btn btn-primary btn-sm" title="تعديل">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                    <form method="POST" action="{{ url_for('delete_debt', debt_id=debt.id) }}" 
                                          style="display: inline;" 
                                          onsubmit="return confirm('هل أنت متأكد من حذف هذا الدين؟')">
                                        <button type="submit" class="btn btn-danger btn-sm" title="حذف">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="10" class="text-center text-muted">لا توجد ديون مسجلة</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تحديث الصفحة كل 5 دقائق لتحديث حالات الديون
setTimeout(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
