#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('.')

from app import app, db
from app.models import User
from werkzeug.security import generate_password_hash
from datetime import datetime

def create_admin_user():
    """إنشاء مستخدم مدير افتراضي"""
    with app.app_context():
        try:
            # التحقق من وجود مستخدمين
            users_count = User.query.count()
            print(f'عدد المستخدمين الحاليين: {users_count}')
            
            if users_count == 0:
                # إنشاء مستخدم افتراضي
                admin_user = User(
                    username='admin',
                    password=generate_password_hash('admin123'),
                    email='<EMAIL>',
                    full_name='مدير النظام',
                    role='admin',
                    is_active=True,
                    created_at=datetime.now(),
                    last_login=None
                )
                
                db.session.add(admin_user)
                db.session.commit()
                print('✅ تم إنشاء المستخدم الافتراضي بنجاح!')
                print('📋 بيانات تسجيل الدخول:')
                print('   اسم المستخدم: admin')
                print('   كلمة المرور: admin123')
                print('🔗 رابط تسجيل الدخول: http://localhost:5000/lawyersameh')
            else:
                print('المستخدمون الموجودون:')
                users = User.query.all()
                for user in users:
                    print(f'- {user.username} ({user.full_name}) - نشط: {user.is_active}')
                    
        except Exception as e:
            print(f'❌ خطأ في إنشاء المستخدم: {str(e)}')
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    create_admin_user()
