{% extends "base.html" %}

{% block title %}إضافة دين جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-credit-card"></i> إضافة دين جديد</h1>
                    <p>إضافة دين جديد إلى النظام المالي</p>
                </div>
                <div class="page-actions">
                    <a href="{{ url_for('debts_list') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>رجوع
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-money-bill text-primary me-2"></i>
                        بيانات الدين
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <!-- معلومات الدائن -->
                        <div class="section-divider">
                            <h5 class="text-primary"><i class="fa fa-user"></i> معلومات الدائن</h5>
                        </div>
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <label class="form-label">اسم الدائن <span class="text-danger">*</span></label>
                                <input type="text" name="creditor_name" class="form-control" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">نوع الدين <span class="text-danger">*</span></label>
                                <select name="debt_type" class="form-control" required>
                                    <option value="">اختر نوع الدين</option>
                                    <option value="قرض شخصي">قرض شخصي</option>
                                    <option value="قرض بنكي">قرض بنكي</option>
                                    <option value="مستحقات موردين">مستحقات موردين</option>
                                    <option value="رواتب ومكافآت">رواتب ومكافآت</option>
                                    <option value="إيجارات">إيجارات</option>
                                    <option value="ضرائب ورسوم">ضرائب ورسوم</option>
                                    <option value="خدمات مهنية">خدمات مهنية</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                        </div>

                        <!-- تفاصيل الدين -->
                        <div class="section-divider">
                            <h5 class="text-info"><i class="fa fa-info-circle"></i> تفاصيل الدين</h5>
                        </div>
                        <div class="row g-3 mb-3">
                            <div class="col-md-4">
                                <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                                <input type="number" name="amount" class="form-control" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">العملة</label>
                                <select name="currency" class="form-control">
                                    <option value="شيكل">شيكل</option>
                                    <option value="دولار">دولار أمريكي</option>
                                    <option value="دينار">دينار أردني</option>
                                    <option value="يورو">يورو</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">الأولوية</label>
                                <select name="priority" class="form-control">
                                    <option value="منخفضة">منخفضة</option>
                                    <option value="متوسطة" selected>متوسطة</option>
                                    <option value="عالية">عالية</option>
                                    <option value="عاجلة">عاجلة</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">تاريخ الاستحقاق</label>
                            <input type="date" name="due_date" class="form-control">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وصف الدين</label>
                            <textarea name="description" class="form-control" rows="3" 
                                      placeholder="تفاصيل إضافية عن الدين..."></textarea>
                        </div>

                        <!-- ربط اختياري -->
                        <div class="section-divider">
                            <h5 class="text-warning"><i class="fa fa-link"></i> ربط اختياري</h5>
                        </div>
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <label class="form-label">ربط بقضية (اختياري)</label>
                                <select name="case_id" class="form-control">
                                    <option value="">لا يوجد ربط</option>
                                    {% for case in cases %}
                                    <option value="{{ case.id }}">{{ case.case_number }} - {{ case.title }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">ربط بعميل (اختياري)</label>
                                <select name="client_id" class="form-control">
                                    <option value="">لا يوجد ربط</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}">{{ client.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-success px-5 py-2">
                                <i class="fa fa-save"></i> حفظ الدين
                            </button>
                            <a href="{{ url_for('debts_list') }}" class="btn btn-secondary px-4 py-2">
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.section-divider {
    border-bottom: 2px solid #e9ecef;
    margin: 20px 0 15px 0;
    padding-bottom: 10px;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.text-danger {
    color: #dc3545 !important;
}

.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}
</style>
{% endblock %}
