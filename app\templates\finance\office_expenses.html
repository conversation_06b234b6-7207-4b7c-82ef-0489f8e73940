{% extends 'base.html' %}
{% block title %}مصاريف المكتب{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-building"></i> مصاريف المكتب</h1>
                    <p>إدارة وتتبع جميع مصاريف المكتب والنفقات التشغيلية</p>
                </div>
                <div class="page-actions">
                    <a href="{{ url_for('financial_transactions') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>رجوع للسندات المالية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة مصروف جديد -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus-circle text-primary me-2"></i>
                        إضافة مصروف جديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-money-bill"></i>
                                </span>
                                <input type="number" step="0.01" name="amount" class="form-control" required placeholder="0.00">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">العملة <span class="text-danger">*</span></label>
                            <select name="currency" class="form-select" required>
                                <option value="">اختر العملة</option>
                                <option value="شيقل">شيقل</option>
                                <option value="دينار">دينار</option>
                                <option value="دولار">دولار</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">الوصف <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-file-text"></i>
                                </span>
                                <input type="text" name="description" class="form-control" placeholder="وصف المصروف (مثال: إيجار، كهرباء...)" required>
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>إضافة مصروف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- جدول المصاريف -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list text-primary me-2"></i>
                        قائمة المصاريف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th><i class="fas fa-calendar me-1"></i>التاريخ</th>
                                    <th><i class="fas fa-money-bill me-1"></i>المبلغ</th>
                                    <th><i class="fas fa-coins me-1"></i>العملة</th>
                                    <th><i class="fas fa-file-text me-1"></i>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                            {% for e in expenses %}
                                <tr>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            {{ e.date.strftime('%Y-%m-%d') if e.date else '-' }}
                                        </span>
                                    </td>
                                    <td>
                                        <strong class="text-primary">{{ e.amount }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ e.currency }}</span>
                                    </td>
                                    <td>{{ e.description }}</td>
                                </tr>
                            {% else %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-2x mb-2"></i>
                                        <br>لا توجد مصاريف مكتبية حالياً
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
