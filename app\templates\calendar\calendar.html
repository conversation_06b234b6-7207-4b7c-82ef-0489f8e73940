{% extends "base.html" %}

{% block title %}إدارة المواعيد والتقويم المتقدم{% endblock %}

{% block head %}
<link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css' rel='stylesheet' />
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/locales/ar.global.min.js'></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<style>
    /* تحسينات التصميم الأساسي */
    .calendar-container {
        background: var(--theme-bg-primary);
        border-radius: 20px;
        box-shadow: var(--theme-shadow-xl);
        border: 1px solid var(--theme-border-color);
        overflow: hidden;
    }

    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 20px;
        text-align: center;
        margin-bottom: 25px;
        transition: all var(--theme-transition-normal);
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .stats-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: var(--theme-shadow-2xl);
    }

    .stats-card h3 {
        font-size: 3rem;
        margin-bottom: 15px;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .stats-card.bg-warning {
        background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
    }
    .stats-card.bg-success {
        background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    }
    .stats-card.bg-danger {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    }
    .stats-card.bg-info {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    }

    /* تحسينات شريط أدوات التقويم */
    .fc-toolbar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        margin-bottom: 25px;
        box-shadow: var(--theme-shadow-lg);
        border: none;
    }

    .fc-toolbar-title {
        color: white !important;
        font-weight: 700;
        font-size: 1.8rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .fc-button {
        background: rgba(255,255,255,0.15) !important;
        border: 1px solid rgba(255,255,255,0.3) !important;
        border-radius: 10px !important;
        padding: 8px 16px !important;
        font-weight: 600 !important;
        transition: all var(--theme-transition-fast) !important;
    }

    .fc-button:hover {
        background: rgba(255,255,255,0.25) !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .fc-button-active {
        background: rgba(255,255,255,0.3) !important;
        box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
    }

    /* تحسينات عناصر المهام */
    .task-item {
        background: var(--theme-bg-primary);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: var(--theme-shadow-md);
        border-left: 5px solid;
        transition: all var(--theme-transition-normal);
        position: relative;
        overflow: hidden;
    }

    .task-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.05) 0%, transparent 100%);
        pointer-events: none;
    }

    .task-item:hover {
        transform: translateX(8px) translateY(-3px);
        box-shadow: var(--theme-shadow-xl);
    }

    .task-item.priority-high {
        border-left-color: #ff6b6b;
        background: linear-gradient(135deg, rgba(255,107,107,0.05) 0%, transparent 100%);
    }
    .task-item.priority-medium {
        border-left-color: #ffa726;
        background: linear-gradient(135deg, rgba(255,167,38,0.05) 0%, transparent 100%);
    }
    .task-item.priority-low {
        border-left-color: #66bb6a;
        background: linear-gradient(135deg, rgba(102,187,106,0.05) 0%, transparent 100%);
    }

    /* دائرة التقدم المحسنة */
    .progress-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: conic-gradient(
            var(--theme-primary) 0deg,
            var(--theme-primary) var(--progress),
            var(--theme-bg-secondary) var(--progress),
            var(--theme-bg-secondary) 360deg
        );
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--theme-shadow-md);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333;
        font-weight: bold;
        font-size: 12px;
    }

    .event-modal .modal-content { border-radius: 15px; }
    .btn-gradient { background: linear-gradient(45deg, #667eea, #764ba2); border: none; }
    .btn-gradient:hover { background: linear-gradient(45deg, #764ba2, #667eea); }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-primary"><i class="fas fa-calendar-alt"></i> التقويم والمهام المتقدم</h2>
        <div class="btn-group">
            <button type="button" class="btn btn-gradient text-white" data-bs-toggle="modal" data-bs-target="#addEventModal">
                <i class="fas fa-plus"></i> إضافة حدث
            </button>
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addTaskModal">
                <i class="fas fa-tasks"></i> إضافة مهمة
            </button>
            <button type="button" class="btn btn-info" onclick="refreshCalendar()">
                <i class="fas fa-sync"></i> تحديث
            </button>
        </div>
    </div>

    <!-- إحصائيات محسنة -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card bg-primary">
                <h3 id="total-events">{{ stats.total_events or 0 }}</h3>
                <p><i class="fas fa-calendar-check"></i> إجمالي الأحداث</p>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card bg-warning">
                <h3 id="pending-tasks">{{ stats.pending_tasks or 0 }}</h3>
                <p><i class="fas fa-clock"></i> المهام المعلقة</p>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card bg-success">
                <h3 id="completed-tasks">{{ stats.completed_tasks or 0 }}</h3>
                <p><i class="fas fa-check-circle"></i> المهام المكتملة</p>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card bg-danger">
                <h3 id="overdue-tasks">{{ stats.overdue_tasks or 0 }}</h3>
                <p><i class="fas fa-exclamation-triangle"></i> المهام المتأخرة</p>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card bg-info">
                <h3 id="today-events">{{ stats.today_events or 0 }}</h3>
                <p><i class="fas fa-calendar-day"></i> أحداث اليوم</p>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="stats-card" style="background: linear-gradient(135deg, #6f42c1, #e83e8c);">
                <h3 id="this-week-tasks">{{ stats.this_week_tasks or 0 }}</h3>
                <p><i class="fas fa-calendar-week"></i> مهام الأسبوع</p>
            </div>
        </div>
    </div>

    <!-- التقويم والمهام -->
    <div class="row">
        <!-- التقويم -->
        <div class="col-lg-8">
            <div class="card calendar-container">
                <div class="card-header bg-gradient text-white">
                    <h5 class="mb-0"><i class="fas fa-calendar"></i> التقويم الميلادي</h5>
                </div>
                <div class="card-body">
                    <div id="calendar"></div>
                </div>
            </div>
        </div>

        <!-- قائمة المهام -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list-check"></i> المهام القادمة</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            فلترة
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="filterTasks('all')">كل المهام</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterTasks('pending')">المعلقة</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterTasks('in-progress')">قيد التنفيذ</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterTasks('high')">عالية الأولوية</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterTasks('overdue')">متأخرة</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                    <div id="tasks-container">
                        {% for task in tasks %}
                        <div class="task-item priority-{{ task.priority|lower }}" data-task-id="{{ task.id }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ task.title }}</h6>
                                    <p class="text-muted small mb-2">{{ task.description[:50] }}{% if task.description|length > 50 %}...{% endif %}</p>
                                    <div class="d-flex align-items-center gap-2 mb-2">
                                        <span class="badge bg-{{ 'danger' if task.priority == 'عالية' else 'warning' if task.priority == 'متوسطة' else 'success' }}">
                                            {{ task.priority }}
                                        </span>
                                        <span class="badge bg-secondary">{{ task.category or 'عامة' }}</span>
                                        {% if task.due_date %}
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i> {{ task.due_date.strftime('%Y-%m-%d') }}
                                        </small>
                                        {% endif %}
                                    </div>
                                    {% if task.client %}
                                    <small class="text-info"><i class="fas fa-user"></i> {{ task.client.name }}</small>
                                    {% endif %}
                                </div>
                                <div class="d-flex align-items-center gap-2">
                                    <div class="progress-circle" style="--progress: {{ task.progress * 3.6 }}deg;">
                                        {{ task.progress }}%
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="editTask({{ task.id }})">
                                                <i class="fas fa-edit"></i> تعديل
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="updateTaskProgress({{ task.id }})">
                                                <i class="fas fa-percentage"></i> تحديث التقدم
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="completeTask({{ task.id }})">
                                                <i class="fas fa-check"></i> إكمال
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteTask({{ task.id }})">
                                                <i class="fas fa-trash"></i> حذف
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- الأحداث القادمة -->
            <div class="card mt-3">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-calendar-check"></i> الأحداث القادمة</h5>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    {% for event in upcoming_events %}
                    <div class="d-flex align-items-center mb-3 p-2 border-start border-3" style="border-color: {{ event.color }}!important;">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ event.title }}</h6>
                            <small class="text-muted">
                                <i class="fas fa-calendar"></i> {{ event.start_date.strftime('%Y-%m-%d %H:%M') }}
                                {% if event.location %}
                                <br><i class="fas fa-map-marker-alt"></i> {{ event.location }}
                                {% endif %}
                            </small>
                        </div>
                        <span class="badge bg-primary">{{ event.event_type }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج إضافة حدث -->
<div class="modal fade event-modal" id="addEventModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-calendar-plus"></i> إضافة حدث جديد</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="addEventForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">عنوان الحدث *</label>
                                <input type="text" class="form-control" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع الحدث</label>
                                <select class="form-select" name="event_type">
                                    <option value="موعد">موعد</option>
                                    <option value="جلسة">جلسة محكمة</option>
                                    <option value="اجتماع">اجتماع</option>
                                    <option value="مهمة">مهمة</option>
                                    <option value="تذكير">تذكير</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ البداية *</label>
                                <input type="datetime-local" class="form-control" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ النهاية</label>
                                <input type="datetime-local" class="form-control" name="end_date">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">العميل</label>
                                <select class="form-select" name="client_id">
                                    <option value="">اختر العميل</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}">{{ client.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">القضية</label>
                                <select class="form-select" name="case_id">
                                    <option value="">اختر القضية</option>
                                    {% for case in cases %}
                                    <option value="{{ case.id }}">{{ case.title }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الموقع</label>
                                <input type="text" class="form-control" name="location" placeholder="مكان الحدث">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">لون الحدث</label>
                                <input type="color" class="form-control form-control-color" name="color" value="#007bff">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تذكير قبل (بالدقائق)</label>
                                <select class="form-select" name="reminder_minutes">
                                    <option value="15">15 دقيقة</option>
                                    <option value="30" selected>30 دقيقة</option>
                                    <option value="60">ساعة</option>
                                    <option value="1440">يوم</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="all_day" id="allDay">
                                    <label class="form-check-label" for="allDay">حدث يوم كامل</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" name="description" rows="3" placeholder="تفاصيل الحدث"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ الحدث</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج إضافة مهمة -->
<div class="modal fade event-modal" id="addTaskModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-tasks"></i> إضافة مهمة جديدة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="addTaskForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">عنوان المهمة *</label>
                                <input type="text" class="form-control" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الأولوية</label>
                                <select class="form-select" name="priority">
                                    <option value="منخفضة">منخفضة</option>
                                    <option value="متوسطة" selected>متوسطة</option>
                                    <option value="عالية">عالية</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الفئة</label>
                                <select class="form-select" name="category">
                                    <option value="قانونية">قانونية</option>
                                    <option value="إدارية">إدارية</option>
                                    <option value="مالية">مالية</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ الاستحقاق</label>
                                <input type="datetime-local" class="form-control" name="due_date">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">العميل</label>
                                <select class="form-select" name="client_id">
                                    <option value="">اختر العميل</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}">{{ client.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">القضية</label>
                                <select class="form-select" name="case_id">
                                    <option value="">اختر القضية</option>
                                    {% for case in cases %}
                                    <option value="{{ case.id }}">{{ case.title }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الساعات المقدرة</label>
                                <input type="number" class="form-control" name="estimated_hours" step="0.5" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نسبة الإنجاز</label>
                                <div class="input-group">
                                    <input type="range" class="form-range" name="progress" min="0" max="100" value="0" oninput="updateProgressValue(this.value)">
                                    <span class="input-group-text" id="progressValue">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">وصف المهمة</label>
                        <textarea class="form-control" name="description" rows="4" placeholder="تفاصيل المهمة والخطوات المطلوبة"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ المهمة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تحديث تقدم المهمة -->
<div class="modal fade" id="updateProgressModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title"><i class="fas fa-percentage"></i> تحديث تقدم المهمة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="updateProgressForm">
                <div class="modal-body">
                    <input type="hidden" id="taskIdForProgress" name="task_id">
                    <div class="mb-3">
                        <label class="form-label">نسبة الإنجاز الجديدة</label>
                        <div class="progress mb-2" style="height: 25px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" id="progressBarPreview" style="width: 0%">0%</div>
                        </div>
                        <input type="range" class="form-range" id="progressSlider" name="progress" min="0" max="100" value="0" oninput="updateProgressPreview(this.value)">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الساعات الفعلية المنجزة</label>
                        <input type="number" class="form-control" name="actual_hours" step="0.5" min="0">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات التقدم</label>
                        <textarea class="form-control" name="progress_notes" rows="3" placeholder="ملاحظات حول التقدم المحرز"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">تحديث التقدم</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تهيئة التقويم
document.addEventListener('DOMContentLoaded', function() {
    var calendarEl = document.getElementById('calendar');
    var calendar = new FullCalendar.Calendar(calendarEl, {
        locale: 'ar',
        direction: 'rtl',
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
        },
        buttonText: {
            today: 'اليوم',
            month: 'شهر',
            week: 'أسبوع',
            day: 'يوم',
            list: 'قائمة'
        },
        height: 'auto',
        events: [
            {% for event in events %}
            {
                id: '{{ event.id }}',
                title: '{{ event.title }}',
                start: '{{ event.start_date.isoformat() }}',
                {% if event.end_date %}end: '{{ event.end_date.isoformat() }}',{% endif %}
                color: '{{ event.color }}',
                allDay: {{ 'true' if event.all_day else 'false' }},
                extendedProps: {
                    type: '{{ event.event_type }}',
                    location: '{{ event.location or '' }}',
                    description: '{{ event.description or '' }}',
                    client: '{{ event.client.name if event.client else '' }}',
                    case: '{{ event.case.title if event.case else '' }}'
                }
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        eventClick: function(info) {
            showEventDetails(info.event);
        },
        dateClick: function(info) {
            openAddEventModal(info.dateStr);
        },
        eventDidMount: function(info) {
            // إضافة تأثيرات بصرية
            info.el.style.borderRadius = '8px';
            info.el.style.border = 'none';
            info.el.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';

            // إضافة tooltip
            info.el.setAttribute('title',
                info.event.title +
                (info.event.extendedProps.location ? '\nالموقع: ' + info.event.extendedProps.location : '') +
                (info.event.extendedProps.client ? '\nالعميل: ' + info.event.extendedProps.client : '')
            );
        }
    });

    calendar.render();

    // تحديث الإحصائيات
    updateStats();
});

// وظائف التفاعل
function openAddEventModal(dateStr) {
    document.querySelector('#addEventModal input[name="start_date"]').value = dateStr + 'T09:00';
    new bootstrap.Modal(document.getElementById('addEventModal')).show();
}

function showEventDetails(event) {
    const details = `
        <div class="event-details">
            <h5>${event.title}</h5>
            <p><strong>النوع:</strong> ${event.extendedProps.type}</p>
            <p><strong>التاريخ:</strong> ${event.start.toLocaleString('ar-SA')}</p>
            ${event.extendedProps.location ? `<p><strong>الموقع:</strong> ${event.extendedProps.location}</p>` : ''}
            ${event.extendedProps.client ? `<p><strong>العميل:</strong> ${event.extendedProps.client}</p>` : ''}
            ${event.extendedProps.case ? `<p><strong>القضية:</strong> ${event.extendedProps.case}</p>` : ''}
            ${event.extendedProps.description ? `<p><strong>الوصف:</strong> ${event.extendedProps.description}</p>` : ''}
        </div>
    `;

    Swal.fire({
        title: 'تفاصيل الحدث',
        html: details,
        icon: 'info',
        showCancelButton: true,
        confirmButtonText: 'تعديل',
        cancelButtonText: 'إغلاق',
        confirmButtonColor: '#007bff'
    }).then((result) => {
        if (result.isConfirmed) {
            editEvent(event.id);
        }
    });
}

function updateProgressValue(value) {
    document.getElementById('progressValue').textContent = value + '%';
}

function updateProgressPreview(value) {
    const progressBar = document.getElementById('progressBarPreview');
    progressBar.style.width = value + '%';
    progressBar.textContent = value + '%';

    // تغيير لون شريط التقدم حسب النسبة
    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated ';
    if (value < 30) {
        progressBar.className += 'bg-danger';
    } else if (value < 70) {
        progressBar.className += 'bg-warning';
    } else {
        progressBar.className += 'bg-success';
    }
}

function filterTasks(filter) {
    const tasks = document.querySelectorAll('.task-item');
    tasks.forEach(task => {
        const priority = task.classList.contains('priority-high') ? 'high' :
                        task.classList.contains('priority-medium') ? 'medium' : 'low';
        const status = task.dataset.status || 'pending';

        let show = false;
        switch(filter) {
            case 'all': show = true; break;
            case 'pending': show = status === 'pending'; break;
            case 'in-progress': show = status === 'in-progress'; break;
            case 'high': show = priority === 'high'; break;
            case 'overdue': show = task.classList.contains('overdue'); break;
        }

        task.style.display = show ? 'block' : 'none';
    });
}

function updateTaskProgress(taskId) {
    document.getElementById('taskIdForProgress').value = taskId;
    new bootstrap.Modal(document.getElementById('updateProgressModal')).show();
}

function completeTask(taskId) {
    Swal.fire({
        title: 'إكمال المهمة',
        text: 'هل أنت متأكد من إكمال هذه المهمة؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم، إكمال',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#28a745'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/tasks/${taskId}/complete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', 'تم إكمال المهمة بنجاح', 'success');
                    location.reload();
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            });
        }
    });
}

function deleteTask(taskId) {
    Swal.fire({
        title: 'حذف المهمة',
        text: 'هل أنت متأكد من حذف هذه المهمة؟ لا يمكن التراجع عن هذا الإجراء.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#dc3545'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/tasks/${taskId}/delete`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', 'تم حذف المهمة بنجاح', 'success');
                    location.reload();
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            });
        }
    });
}

function refreshCalendar() {
    location.reload();
}

function updateStats() {
    // تحديث الإحصائيات بشكل ديناميكي
    const tasks = document.querySelectorAll('.task-item');
    let pending = 0, completed = 0, overdue = 0;

    tasks.forEach(task => {
        const status = task.dataset.status || 'pending';
        if (status === 'pending') pending++;
        if (status === 'completed') completed++;
        if (task.classList.contains('overdue')) overdue++;
    });

    document.getElementById('pending-tasks').textContent = pending;
    document.getElementById('completed-tasks').textContent = completed;
    document.getElementById('overdue-tasks').textContent = overdue;
}

// معالجة النماذج
document.getElementById('addEventForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);

    fetch('/events/add', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire('تم!', 'تم إضافة الحدث بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addEventModal')).hide();
            location.reload();
        } else {
            Swal.fire('خطأ!', data.message, 'error');
        }
    });
});

document.getElementById('addTaskForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);

    fetch('/tasks/add', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire('تم!', 'تم إضافة المهمة بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addTaskModal')).hide();
            location.reload();
        } else {
            Swal.fire('خطأ!', data.message, 'error');
        }
    });
});

document.getElementById('updateProgressForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    const taskId = formData.get('task_id');

    fetch(`/tasks/${taskId}/progress`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire('تم!', 'تم تحديث تقدم المهمة بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('updateProgressModal')).hide();
            location.reload();
        } else {
            Swal.fire('خطأ!', data.message, 'error');
        }
    });
});

// تأثيرات بصرية إضافية
document.querySelectorAll('.task-item').forEach(item => {
    item.addEventListener('mouseenter', function() {
        this.style.transform = 'translateX(10px)';
        this.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';
    });

    item.addEventListener('mouseleave', function() {
        this.style.transform = 'translateX(5px)';
        this.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
    });
});

// تحديث الوقت كل دقيقة
setInterval(function() {
    const now = new Date();
    document.querySelectorAll('.task-item').forEach(task => {
        const dueDate = task.dataset.dueDate;
        if (dueDate && new Date(dueDate) < now) {
            task.classList.add('overdue');
        }
    });
    updateStats();
}, 60000);
</script>

<!-- مكتبة SweetAlert2 للتنبيهات الجميلة -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// دوال إضافة الأحداث والمهام المحسنة

// حفظ حدث جديد
function saveEvent() {
    const form = document.getElementById('add-event-form');
    const formData = new FormData(form);

    // تحويل البيانات إلى JSON
    const eventData = {
        title: formData.get('title'),
        description: formData.get('description'),
        start_date: formData.get('start_date'),
        end_date: formData.get('end_date'),
        location: formData.get('location'),
        event_type: formData.get('event_type'),
        priority: formData.get('priority'),
        color: formData.get('color'),
        all_day: formData.get('all_day') === 'on',
        recurring: formData.get('recurring'),
        reminder_minutes: parseInt(formData.get('reminder_minutes')),
        client_id: formData.get('client_id') || null,
        case_id: formData.get('case_id') || null
    };

    fetch('/api/calendar/events/enhanced', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(eventData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'تم بنجاح!',
                text: data.message,
                timer: 2000,
                showConfirmButton: false
            });

            // إغلاق النافذة المنبثقة وإعادة تحميل البيانات
            const modal = bootstrap.Modal.getInstance(document.getElementById('addEventModal'));
            modal.hide();
            form.reset();
            loadCalendarData();
            updateStats();
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: data.message || 'فشل في إضافة الحدث'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ!',
            text: 'حدث خطأ في الاتصال بالخادم'
        });
    });
}

// حفظ مهمة جديدة
function saveTask() {
    const form = document.getElementById('add-task-form');
    const formData = new FormData(form);

    // تحويل البيانات إلى JSON
    const taskData = {
        title: formData.get('title'),
        description: formData.get('description'),
        due_date: formData.get('due_date'),
        priority: formData.get('priority'),
        category: formData.get('category'),
        estimated_hours: formData.get('estimated_hours'),
        client_id: formData.get('client_id') || null,
        case_id: formData.get('case_id') || null
    };

    fetch('/api/calendar/tasks/enhanced', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(taskData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'تم بنجاح!',
                text: data.message,
                timer: 2000,
                showConfirmButton: false
            });

            // إغلاق النافذة المنبثقة وإعادة تحميل البيانات
            const modal = bootstrap.Modal.getInstance(document.getElementById('addTaskModal'));
            modal.hide();
            form.reset();
            loadCalendarData();
            updateStats();
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: data.message || 'فشل في إضافة المهمة'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ!',
            text: 'حدث خطأ في الاتصال بالخادم'
        });
    });
}

// تحديث تقدم المهمة
function updateTaskProgress(taskId) {
    const progress = document.getElementById('task-progress').value;
    const actualHours = document.getElementById('actual-hours').value;

    const progressData = {
        progress: parseInt(progress),
        actual_hours: actualHours ? parseFloat(actualHours) : null
    };

    fetch(`/api/calendar/tasks/${taskId}/progress`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(progressData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'تم بنجاح!',
                text: data.message,
                timer: 2000,
                showConfirmButton: false
            });

            // إغلاق النافذة المنبثقة وإعادة تحميل البيانات
            const modal = bootstrap.Modal.getInstance(document.getElementById('updateProgressModal'));
            modal.hide();
            loadCalendarData();
            updateStats();
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: data.message || 'فشل في تحديث المهمة'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ!',
            text: 'حدث خطأ في الاتصال بالخادم'
        });
    });
}

// تحديث الإحصائيات
function updateStats() {
    fetch('/api/calendar/stats')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const stats = data.stats;
            document.getElementById('total-events').textContent = stats.total_events;
            document.getElementById('pending-tasks').textContent = stats.pending_tasks;
            document.getElementById('completed-tasks').textContent = stats.completed_tasks;
            document.getElementById('overdue-tasks').textContent = stats.overdue_tasks;
            document.getElementById('today-events').textContent = stats.today_events;
            document.getElementById('this-week-tasks').textContent = stats.this_week_tasks;
        }
    })
    .catch(error => {
        console.error('Error loading stats:', error);
    });
}

// دالة للحصول على CSRF Token
function getCsrfToken() {
    const token = document.querySelector('meta[name=csrf-token]');
    return token ? token.getAttribute('content') : '';
}

// تحديث التقويم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateStats();

    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateStats, 30000);
});
</script>
{% endblock %}
