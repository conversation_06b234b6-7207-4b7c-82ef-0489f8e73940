#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء جدول سجلات النسخ الاحتياطية
Create Backup Logs Table
"""

import sqlite3
from datetime import datetime

def create_backup_logs_table():
    """إنشاء جدول سجلات النسخ الاحتياطية"""
    try:
        print("🔧 إنشاء جدول سجلات النسخ الاحتياطية...")
        
        db_path = 'instance/lawoffice.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول backup_logs
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS backup_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                backup_name TEXT NOT NULL,
                backup_path TEXT NOT NULL,
                backup_size INTEGER NOT NULL,
                backup_type TEXT NOT NULL,
                created_at TEXT NOT NULL,
                restored_at TEXT,
                notes TEXT
            )
        """)
        
        print("   ✅ تم إنشاء جدول backup_logs")
        
        # التحقق من وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='backup_logs'")
        result = cursor.fetchone()
        
        if result:
            print("   ✅ تم التحقق من وجود الجدول")
            
            # عرض هيكل الجدول
            cursor.execute("PRAGMA table_info(backup_logs)")
            columns = cursor.fetchall()
            
            print("   📋 هيكل جدول backup_logs:")
            for column in columns:
                print(f"      - {column[1]} ({column[2]})")
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء جدول سجلات النسخ الاحتياطية بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول النسخ الاحتياطية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إعداد جدول سجلات النسخ الاحتياطية")
    print("=" * 50)
    
    success = create_backup_logs_table()
    
    if success:
        print("\n🎉 تم إعداد جدول النسخ الاحتياطية بنجاح!")
    else:
        print("\n❌ فشل في إعداد جدول النسخ الاحتياطية")

if __name__ == "__main__":
    main()
