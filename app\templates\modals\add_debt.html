<form method="POST" action="{{ url_for('add_debt') }}" class="modal-form" id="addDebtForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-credit-card"></i> معلومات الدين الأساسية
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">اسم الدائن <span class="text-danger">*</span></label>
                <input type="text" name="creditor_name" class="form-control" required 
                       placeholder="اسم الشخص أو الجهة الدائنة">
            </div>
            <div class="col-md-6">
                <label class="form-label">مبلغ الدين <span class="text-danger">*</span></label>
                <input type="number" name="amount" class="form-control" required 
                       step="0.01" min="0" placeholder="مبلغ الدين">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">العملة</label>
                <select name="currency" class="form-control">
                    <option value="شيكل">شيكل</option>
                    <option value="دولار">دولار أمريكي</option>
                    <option value="دينار">دينار أردني</option>
                    <option value="يورو">يورو</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">حالة الدين</label>
                <select name="status" class="form-control">
                    <option value="مستحق">مستحق</option>
                    <option value="مدفوع جزئياً">مدفوع جزئياً</option>
                    <option value="مدفوع بالكامل">مدفوع بالكامل</option>
                    <option value="متأخر">متأخر</option>
                    <option value="ملغي">ملغي</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-calendar"></i> التواريخ المهمة
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">تاريخ الدين</label>
                <input type="date" name="debt_date" class="form-control" 
                       value="{{ today }}">
            </div>
            <div class="col-md-6">
                <label class="form-label">تاريخ الاستحقاق</label>
                <input type="date" name="due_date" class="form-control">
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-info-circle"></i> معلومات الدائن
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">هاتف الدائن</label>
                <input type="tel" name="creditor_phone" class="form-control" 
                       placeholder="رقم هاتف الدائن">
            </div>
            <div class="col-md-6">
                <label class="form-label">بريد الدائن الإلكتروني</label>
                <input type="email" name="creditor_email" class="form-control" 
                       placeholder="البريد الإلكتروني للدائن">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-12">
                <label class="form-label">عنوان الدائن</label>
                <textarea name="creditor_address" class="form-control" rows="2" 
                          placeholder="عنوان الدائن"></textarea>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-money-bill"></i> تفاصيل الدفع
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">المبلغ المدفوع</label>
                <input type="number" name="paid_amount" class="form-control" 
                       step="0.01" min="0" value="0" placeholder="المبلغ المدفوع">
            </div>
            <div class="col-md-6">
                <label class="form-label">نوع الدين</label>
                <select name="debt_type" class="form-control">
                    <option value="شخصي">شخصي</option>
                    <option value="تجاري">تجاري</option>
                    <option value="قرض">قرض</option>
                    <option value="فاتورة">فاتورة</option>
                    <option value="أتعاب">أتعاب</option>
                    <option value="أخرى">أخرى</option>
                </select>
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">أولوية الدفع</label>
                <select name="priority" class="form-control">
                    <option value="عادية">عادية</option>
                    <option value="متوسطة">متوسطة</option>
                    <option value="عالية">عالية</option>
                    <option value="عاجلة">عاجلة</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">طريقة الدفع المفضلة</label>
                <select name="payment_method" class="form-control">
                    <option value="نقداً">نقداً</option>
                    <option value="شيك">شيك</option>
                    <option value="تحويل بنكي">تحويل بنكي</option>
                    <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                    <option value="أخرى">أخرى</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-file-alt"></i> تفاصيل إضافية
        </div>
        <div class="mb-3">
            <label class="form-label">وصف الدين</label>
            <textarea name="description" class="form-control" rows="3" 
                      placeholder="وصف تفصيلي للدين وسبب نشوئه..."></textarea>
        </div>
        <div class="mb-3">
            <label class="form-label">ملاحظات</label>
            <textarea name="notes" class="form-control" rows="2" 
                      placeholder="ملاحظات إضافية..."></textarea>
        </div>
    </div>

    <!-- ملخص مالي -->
    <div class="alert alert-info">
        <h6><strong>ملخص مالي:</strong></h6>
        <p><strong>إجمالي الدين:</strong> <span id="totalDebt">0.00</span> شيكل</p>
        <p><strong>المبلغ المدفوع:</strong> <span id="paidAmount">0.00</span> شيكل</p>
        <p><strong>المبلغ المتبقي:</strong> <span id="remainingAmount">0.00</span> شيكل</p>
    </div>

    <div class="text-center mt-4">
        <button type="submit" class="btn btn-success px-4">
            <i class="fa fa-save"></i> حفظ الدين
        </button>
        <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
            إلغاء
        </button>
    </div>
</form>

<script>
document.getElementById('addDebtForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            if (modal) modal.hide();
            showAlert('تم إضافة الدين بنجاح', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(data.message || 'حدث خطأ أثناء إضافة الدين', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
});

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
    document.body.appendChild(alertDiv);
    setTimeout(() => alertDiv.remove(), 5000);
}
</script>

<style>
.modal-form .form-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.modal-form .form-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
}

.modal-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.modal-form .text-danger {
    color: #dc3545 !important;
}

.modal-form .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.modal-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.alert {
    border-radius: 10px;
}
</style>

<script>
// حساب المبلغ المتبقي تلقائياً
function calculateRemaining() {
    var total = parseFloat(document.querySelector('input[name="amount"]').value) || 0;
    var paid = parseFloat(document.querySelector('input[name="paid_amount"]').value) || 0;
    var remaining = total - paid;
    
    document.getElementById('totalDebt').textContent = total.toFixed(2);
    document.getElementById('paidAmount').textContent = paid.toFixed(2);
    document.getElementById('remainingAmount').textContent = remaining.toFixed(2);
}

// ربط الأحداث
document.querySelector('input[name="amount"]').addEventListener('input', calculateRemaining);
document.querySelector('input[name="paid_amount"]').addEventListener('input', calculateRemaining);

// حساب أولي
calculateRemaining();
</script>
