<form method="POST" action="{{ url_for('add_rental_income') }}" class="modal-form" id="addRentalIncomeForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-home"></i> معلومات الإيجار الأساسية
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">العقار <span class="text-danger">*</span></label>
                <select name="property_id" class="form-control" required>
                    <option value="">اختر العقار</option>
                    {% for property in properties %}
                    <option value="{{ property.id }}">{{ property.name }} - {{ property.address[:50] }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">المستأجر <span class="text-danger">*</span></label>
                <select name="tenant_id" class="form-control" required>
                    <option value="">اختر المستأجر</option>
                    {% for tenant in tenants %}
                    <option value="{{ tenant.id }}">{{ tenant.name }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">مبلغ الإيجار <span class="text-danger">*</span></label>
                <input type="number" name="amount" class="form-control" required 
                       step="0.01" min="0" placeholder="مبلغ الإيجار">
            </div>
            <div class="col-md-6">
                <label class="form-label">العملة</label>
                <select name="currency" class="form-control">
                    <option value="شيكل">شيكل</option>
                    <option value="دولار">دولار أمريكي</option>
                    <option value="دينار">دينار أردني</option>
                    <option value="يورو">يورو</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-calendar"></i> فترة الإيجار
        </div>
        <div class="row g-3">
            <div class="col-md-4">
                <label class="form-label">الشهر</label>
                <select name="month" class="form-control">
                    <option value="1">يناير</option>
                    <option value="2">فبراير</option>
                    <option value="3">مارس</option>
                    <option value="4">أبريل</option>
                    <option value="5">مايو</option>
                    <option value="6">يونيو</option>
                    <option value="7">يوليو</option>
                    <option value="8">أغسطس</option>
                    <option value="9">سبتمبر</option>
                    <option value="10">أكتوبر</option>
                    <option value="11">نوفمبر</option>
                    <option value="12">ديسمبر</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">السنة</label>
                <input type="number" name="year" class="form-control" 
                       min="2020" max="2030" value="{{ current_year }}" placeholder="السنة">
            </div>
            <div class="col-md-4">
                <label class="form-label">تاريخ الاستحقاق</label>
                <input type="date" name="due_date" class="form-control">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">تاريخ الدفع الفعلي</label>
                <input type="date" name="payment_date" class="form-control" 
                       value="{{ today }}">
            </div>
            <div class="col-md-6">
                <label class="form-label">حالة الدفع</label>
                <select name="payment_status" class="form-control">
                    <option value="مدفوع">مدفوع</option>
                    <option value="غير مدفوع">غير مدفوع</option>
                    <option value="مدفوع جزئياً">مدفوع جزئياً</option>
                    <option value="متأخر">متأخر</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-money-bill"></i> تفاصيل الدفع
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">المبلغ المدفوع</label>
                <input type="number" name="paid_amount" class="form-control" 
                       step="0.01" min="0" value="0" placeholder="المبلغ المدفوع">
            </div>
            <div class="col-md-6">
                <label class="form-label">طريقة الدفع</label>
                <select name="payment_method" class="form-control">
                    <option value="نقداً">نقداً</option>
                    <option value="شيك">شيك</option>
                    <option value="تحويل بنكي">تحويل بنكي</option>
                    <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                    <option value="أخرى">أخرى</option>
                </select>
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">رقم المرجع/الإيصال</label>
                <input type="text" name="reference_number" class="form-control" 
                       placeholder="رقم الإيصال أو المرجع">
            </div>
            <div class="col-md-6">
                <label class="form-label">عدد أيام التأخير</label>
                <input type="number" name="late_days" class="form-control" 
                       min="0" value="0" placeholder="عدد أيام التأخير">
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-calculator"></i> الرسوم الإضافية
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">رسوم التأخير</label>
                <input type="number" name="late_fee" class="form-control" 
                       step="0.01" min="0" value="0" placeholder="رسوم التأخير">
            </div>
            <div class="col-md-6">
                <label class="form-label">رسوم إضافية</label>
                <input type="number" name="additional_fees" class="form-control" 
                       step="0.01" min="0" value="0" placeholder="رسوم إضافية">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">خصومات</label>
                <input type="number" name="discount" class="form-control" 
                       step="0.01" min="0" value="0" placeholder="مبلغ الخصم">
            </div>
            <div class="col-md-6">
                <label class="form-label">نسبة العمولة (%)</label>
                <input type="number" name="commission_rate" class="form-control" 
                       step="0.01" min="0" max="100" value="0" placeholder="نسبة العمولة">
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-receipt"></i> معلومات الفاتورة
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">رقم الفاتورة</label>
                <input type="text" name="invoice_number" class="form-control" 
                       placeholder="رقم الفاتورة">
            </div>
            <div class="col-md-6">
                <label class="form-label">تاريخ إصدار الفاتورة</label>
                <input type="date" name="invoice_date" class="form-control" 
                       value="{{ today }}">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">حالة الفاتورة</label>
                <select name="invoice_status" class="form-control">
                    <option value="مرسلة">مرسلة</option>
                    <option value="مستلمة">مستلمة</option>
                    <option value="مدفوعة">مدفوعة</option>
                    <option value="ملغاة">ملغاة</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">نوع الإيجار</label>
                <select name="rental_type" class="form-control">
                    <option value="شهري">شهري</option>
                    <option value="ربع سنوي">ربع سنوي</option>
                    <option value="نصف سنوي">نصف سنوي</option>
                    <option value="سنوي">سنوي</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-sticky-note"></i> ملاحظات
        </div>
        <div class="mb-3">
            <label class="form-label">تفاصيل إضافية</label>
            <textarea name="details" class="form-control" rows="3" 
                      placeholder="تفاصيل إضافية حول الإيجار..."></textarea>
        </div>
        <div class="mb-3">
            <label class="form-label">ملاحظات</label>
            <textarea name="notes" class="form-control" rows="2" 
                      placeholder="ملاحظات إضافية..."></textarea>
        </div>
    </div>

    <!-- ملخص مالي -->
    <div class="alert alert-success">
        <h6><strong>ملخص مالي:</strong></h6>
        <p><strong>مبلغ الإيجار الأساسي:</strong> <span id="baseRent">0.00</span> شيكل</p>
        <p><strong>رسوم التأخير:</strong> <span id="lateFeeDisplay">0.00</span> شيكل</p>
        <p><strong>رسوم إضافية:</strong> <span id="additionalFeesDisplay">0.00</span> شيكل</p>
        <p><strong>الخصومات:</strong> <span id="discountDisplay">0.00</span> شيكل</p>
        <p><strong>مبلغ العمولة:</strong> <span id="commissionAmount">0.00</span> شيكل</p>
        <p><strong>المبلغ الإجمالي:</strong> <span id="totalAmount">0.00</span> شيكل</p>
        <p><strong>المبلغ المتبقي:</strong> <span id="remainingAmount">0.00</span> شيكل</p>
    </div>

    <div class="text-center mt-4">
        <button type="submit" class="btn btn-success px-4">
            <i class="fa fa-save"></i> حفظ الإيجار
        </button>
        <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
            إلغاء
        </button>
    </div>
</form>

<script>
document.getElementById('addRentalIncomeForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            if (modal) modal.hide();
            showAlert('تم إضافة الإيجار بنجاح', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(data.message || 'حدث خطأ أثناء إضافة الإيجار', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
});

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
    document.body.appendChild(alertDiv);
    setTimeout(() => alertDiv.remove(), 5000);
}
</script>

<style>
.modal-form .form-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.modal-form .form-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
}

.modal-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.modal-form .text-danger {
    color: #dc3545 !important;
}

.modal-form .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.modal-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.alert {
    border-radius: 10px;
}
</style>

<script>
// حساب المبالغ تلقائياً
function calculateTotals() {
    var baseRent = parseFloat(document.querySelector('input[name="amount"]').value) || 0;
    var lateFee = parseFloat(document.querySelector('input[name="late_fee"]').value) || 0;
    var additionalFees = parseFloat(document.querySelector('input[name="additional_fees"]').value) || 0;
    var discount = parseFloat(document.querySelector('input[name="discount"]').value) || 0;
    var commissionRate = parseFloat(document.querySelector('input[name="commission_rate"]').value) || 0;
    var paidAmount = parseFloat(document.querySelector('input[name="paid_amount"]').value) || 0;
    
    var commissionAmount = (baseRent * commissionRate) / 100;
    var totalAmount = baseRent + lateFee + additionalFees - discount;
    var remainingAmount = totalAmount - paidAmount;
    
    // تحديث العرض
    document.getElementById('baseRent').textContent = baseRent.toFixed(2);
    document.getElementById('lateFeeDisplay').textContent = lateFee.toFixed(2);
    document.getElementById('additionalFeesDisplay').textContent = additionalFees.toFixed(2);
    document.getElementById('discountDisplay').textContent = discount.toFixed(2);
    document.getElementById('commissionAmount').textContent = commissionAmount.toFixed(2);
    document.getElementById('totalAmount').textContent = totalAmount.toFixed(2);
    document.getElementById('remainingAmount').textContent = remainingAmount.toFixed(2);
}

// ربط الأحداث
document.querySelector('input[name="amount"]').addEventListener('input', calculateTotals);
document.querySelector('input[name="late_fee"]').addEventListener('input', calculateTotals);
document.querySelector('input[name="additional_fees"]').addEventListener('input', calculateTotals);
document.querySelector('input[name="discount"]').addEventListener('input', calculateTotals);
document.querySelector('input[name="commission_rate"]').addEventListener('input', calculateTotals);
document.querySelector('input[name="paid_amount"]').addEventListener('input', calculateTotals);

// حساب أولي
calculateTotals();

// تعيين الشهر الحالي
var currentMonth = new Date().getMonth() + 1;
document.querySelector('select[name="month"]').value = currentMonth;
</script>
