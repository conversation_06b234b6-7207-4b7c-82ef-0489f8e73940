#!/usr/bin/env python3
"""
سكريبت لإنشاء جداول التنبيهات يدوياً
"""

import sqlite3
import os
from datetime import datetime

def create_notifications_tables():
    """إنشاء جداول التنبيهات في قاعدة البيانات"""
    
    # الاتصال بقاعدة البيانات
    db_path = 'lawoffice.db'
    
    if not os.path.exists(db_path):
        print(f"❌ قاعدة البيانات غير موجودة: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 بدء إنشاء جداول التنبيهات...")
        
        # إنشاء جدول notifications
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type VARCHAR(50) NOT NULL,
                title VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                priority VARCHAR(20) DEFAULT 'medium',
                is_read BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                property_id INTEGER,
                tenant_id INTEGER,
                lease_id INTEGER,
                user_id INTEGER NOT NULL,
                due_date DATETIME,
                reminder_date DATETIME,
                action_url VARCHAR(500),
                action_data TEXT,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                read_date DATETIME,
                FOREIGN KEY (property_id) REFERENCES properties (id),
                FOREIGN KEY (tenant_id) REFERENCES tenants (id),
                FOREIGN KEY (lease_id) REFERENCES leases (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        print("✅ تم إنشاء جدول notifications")
        
        # إنشاء جدول notification_settings
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notification_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                lease_expiry_enabled BOOLEAN DEFAULT 1,
                lease_expiry_days INTEGER DEFAULT 30,
                payment_due_enabled BOOLEAN DEFAULT 1,
                payment_due_days INTEGER DEFAULT 5,
                maintenance_due_enabled BOOLEAN DEFAULT 1,
                maintenance_due_days INTEGER DEFAULT 7,
                tenant_review_enabled BOOLEAN DEFAULT 1,
                tenant_review_months INTEGER DEFAULT 6,
                email_notifications BOOLEAN DEFAULT 1,
                sms_notifications BOOLEAN DEFAULT 0,
                push_notifications BOOLEAN DEFAULT 1,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        print("✅ تم إنشاء جدول notification_settings")
        
        # إنشاء فهارس لتحسين الأداء
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_notifications_priority ON notifications(priority)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_notifications_created_date ON notifications(created_date)')
        
        print("✅ تم إنشاء الفهارس")
        
        # إنشاء بعض التنبيهات التجريبية
        sample_notifications = [
            {
                'type': 'lease_expiry',
                'title': 'انتهاء عقد إيجار قريباً',
                'message': 'عقد الإيجار للعقار "فيلا الورود" سينتهي خلال 15 يوماً',
                'priority': 'high',
                'user_id': 1,
                'due_date': '2025-07-20 00:00:00'
            },
            {
                'type': 'payment_due',
                'title': 'استحقاق دفعة إيجار',
                'message': 'دفعة الإيجار للمستأجر "أحمد محمد" مستحقة خلال 3 أيام',
                'priority': 'urgent',
                'user_id': 1,
                'due_date': '2025-07-08 00:00:00'
            },
            {
                'type': 'maintenance_due',
                'title': 'موعد صيانة دورية',
                'message': 'حان موعد الصيانة الدورية للعقار "شقة النخيل"',
                'priority': 'medium',
                'user_id': 1,
                'due_date': '2025-07-10 00:00:00'
            },
            {
                'type': 'tenant_review',
                'title': 'مراجعة حالة مستأجر',
                'message': 'حان موعد مراجعة حالة المستأجر "فاطمة أحمد" وتجديد العقد',
                'priority': 'medium',
                'user_id': 1,
                'due_date': '2025-07-15 00:00:00'
            }
        ]
        
        for notification in sample_notifications:
            cursor.execute('''
                INSERT INTO notifications (type, title, message, priority, user_id, due_date)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                notification['type'],
                notification['title'],
                notification['message'],
                notification['priority'],
                notification['user_id'],
                notification['due_date']
            ))
        
        print("✅ تم إنشاء التنبيهات التجريبية")
        
        # إنشاء إعدادات افتراضية للمستخدم الأول
        cursor.execute('''
            INSERT OR IGNORE INTO notification_settings (user_id)
            VALUES (1)
        ''')
        
        print("✅ تم إنشاء الإعدادات الافتراضية")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("🎉 تم إنشاء جداول التنبيهات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جداول التنبيهات: {str(e)}")
        return False

if __name__ == "__main__":
    create_notifications_tables()
