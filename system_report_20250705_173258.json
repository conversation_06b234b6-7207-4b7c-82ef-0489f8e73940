{"timestamp": "2025-07-05T17:32:58.701615", "overall_score": 100, "overall_status": "مم<PERSON><PERSON><PERSON>", "detailed_data": {"database": {"status": "operational", "total_tables": 39, "user_accounts": 2, "main_tables_data": {"clients": 5, "cases": 5, "properties": 3, "tenants": 4, "leases": 3, "financial_transactions": 5, "fees": 3, "debts": 4, "tasks": 6, "appointments": 6}, "backup_logs": 0, "test_logs": 5, "performance_logs": 7, "db_size_mb": 0.********}, "authentication": {"status": "operational", "total_users": 2, "existing_accounts": ["office", "admin"], "missing_accounts": [], "user_details": [{"username": "office", "role": "مدير"}, {"username": "admin", "role": "مدير"}]}, "file_structure": {"status": "complete", "existing_files": ["app/routes.py", "app/models.py", "app/__init__.py", "run.py", "instance/lawoffice.db"], "missing_files": [], "existing_directories": ["app/templates", "app/static", "instance", "backups"], "missing_directories": [], "backup_files_count": 1}, "functionality": {"status": "operational", "details": {"database_connection": true, "test_data_available": true, "backup_system": true, "testing_system": true, "performance_monitoring": true}}}}