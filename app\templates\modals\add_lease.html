<form method="POST" action="{{ url_for('add_lease') }}" class="modal-form" id="addLeaseForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    
    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-file-contract"></i> معلومات العقد الأساسية
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">العقار <span class="text-danger">*</span></label>
                <select name="property_id" class="form-control" required>
                    <option value="">اختر العقار</option>
                    {% for property in properties %}
                    <option value="{{ property.id }}">{{ property.name }} - {{ property.address }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">المستأجر <span class="text-danger">*</span></label>
                <select name="tenant_id" class="form-control" required>
                    <option value="">اختر المستأجر</option>
                    {% for tenant in tenants %}
                    <option value="{{ tenant.id }}">{{ tenant.name }} - {{ tenant.phone }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">تاريخ بداية العقد <span class="text-danger">*</span></label>
                <input type="date" name="start_date" class="form-control" required>
            </div>
            <div class="col-md-6">
                <label class="form-label">تاريخ انتهاء العقد <span class="text-danger">*</span></label>
                <input type="date" name="end_date" class="form-control" required>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-money-bill"></i> التفاصيل المالية
        </div>
        <div class="row g-3">
            <div class="col-md-4">
                <label class="form-label">قيمة الإيجار الشهري <span class="text-danger">*</span></label>
                <input type="number" name="monthly_rent" class="form-control" step="0.01" required
                       placeholder="قيمة الإيجار الشهري" onchange="calculateAnnualRent()">
            </div>
            <div class="col-md-4">
                <label class="form-label">قيمة الإيجار السنوي</label>
                <input type="number" name="annual_rent" class="form-control" step="0.01" readonly
                       placeholder="يتم حسابها تلقائياً">
            </div>
            <div class="col-md-4">
                <label class="form-label">العملة</label>
                <select name="currency" class="form-control">
                    <option value="شيكل">شيكل</option>
                    <option value="دولار">دولار أمريكي</option>
                    <option value="دينار">دينار أردني</option>
                    <option value="يورو">يورو</option>
                </select>
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">مبلغ التأمين</label>
                <input type="number" name="security_deposit" class="form-control" step="0.01" 
                       placeholder="مبلغ التأمين">
            </div>
            <div class="col-md-6">
                <label class="form-label">طريقة الدفع</label>
                <select name="payment_method" class="form-control">
                    <option value="نقدي">نقدي</option>
                    <option value="شيك">شيك</option>
                    <option value="تحويل بنكي">تحويل بنكي</option>
                    <option value="أخرى">أخرى</option>
                </select>
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-4">
                <label class="form-label">تكرار الدفع</label>
                <select name="payment_frequency" class="form-control" onchange="updatePaymentSchedule()">
                    <option value="شهري">شهري</option>
                    <option value="كل شهرين">كل شهرين</option>
                    <option value="ربع سنوي">ربع سنوي (3 أشهر)</option>
                    <option value="نصف سنوي">نصف سنوي (6 أشهر)</option>
                    <option value="سنوي">سنوي</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">يوم استحقاق الإيجار</label>
                <select name="payment_due_day" class="form-control">
                    {% for day in range(1, 32) %}
                    <option value="{{ day }}" {% if day == 1 %}selected{% endif %}>{{ day }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">حالة العقد</label>
                <select name="status" class="form-control">
                    <option value="نشط">نشط</option>
                    <option value="منتهي">منتهي</option>
                    <option value="معلق">معلق</option>
                    <option value="ملغي">ملغي</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-info-circle"></i> تفاصيل إضافية
        </div>
        <div class="row g-3">
            <div class="col-12">
                <label class="form-label">شروط العقد</label>
                <textarea name="terms" class="form-control" rows="3" 
                          placeholder="شروط وأحكام العقد..."></textarea>
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">ملاحظات</label>
                <textarea name="notes" class="form-control" rows="2"
                          placeholder="ملاحظات إضافية..."></textarea>
            </div>
            <div class="col-md-6">
                <div class="form-check mt-4">
                    <input type="checkbox" name="auto_renewal" class="form-check-input" id="autoRenewal">
                    <label class="form-check-label" for="autoRenewal">
                        <strong>التجديد التلقائي للعقد</strong><br>
                        <small class="text-muted">سيتم تجديد العقد تلقائياً عند انتهاء المدة</small>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <button type="submit" class="btn btn-success px-4">
            <i class="fa fa-save"></i> حفظ العقد
        </button>
        <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
            إلغاء
        </button>
    </div>
</form>

<script>
document.getElementById('addLeaseForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            if (modal) modal.hide();
            showAlert('تم إضافة العقد بنجاح', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(data.message || 'حدث خطأ أثناء إضافة العقد', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
});

// حساب الإيجار السنوي تلقائياً
function calculateAnnualRent() {
    const monthlyRent = parseFloat(document.querySelector('input[name="monthly_rent"]').value) || 0;
    const annualRent = monthlyRent * 12;
    document.querySelector('input[name="annual_rent"]').value = annualRent.toFixed(2);
}

// تحديث جدول الدفعات حسب التكرار
function updatePaymentSchedule() {
    const frequency = document.querySelector('select[name="payment_frequency"]').value;
    const monthlyRent = parseFloat(document.querySelector('input[name="monthly_rent"]').value) || 0;

    const frequencyMultipliers = {
        'شهري': 1,
        'كل شهرين': 2,
        'ربع سنوي': 3,
        'نصف سنوي': 6,
        'سنوي': 12
    };

    const multiplier = frequencyMultipliers[frequency] || 1;
    const installmentAmount = monthlyRent * multiplier;

    // يمكن إضافة عرض مبلغ القسط هنا إذا لزم الأمر
    console.log(`مبلغ القسط ${frequency}: ${installmentAmount.toFixed(2)}`);
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
    document.body.appendChild(alertDiv);
    setTimeout(() => alertDiv.remove(), 5000);
}
</script>

<style>
.modal-form .form-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.modal-form .form-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
}

.modal-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.modal-form .text-danger {
    color: #dc3545 !important;
}

.modal-form .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.modal-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
</style>
