#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للإصلاحات المطبقة على النظام
"""

import requests
import sys
from bs4 import BeautifulSoup
import re

def test_login():
    """اختبار تسجيل الدخول"""
    session = requests.Session()

    # الحصول على صفحة تسجيل الدخول
    login_page = session.get('http://127.0.0.1:5000/login')
    if login_page.status_code != 200:
        # محاولة الوصول للصفحة الرئيسية أولاً
        main_page = session.get('http://127.0.0.1:5000/')
        if main_page.status_code != 200:
            print("❌ خطأ: لا يمكن الوصول للخادم")
            return None
        # إذا كانت الصفحة الرئيسية تعمل، جرب تسجيل الدخول مرة أخرى
        login_page = session.get('http://127.0.0.1:5000/login')
        if login_page.status_code != 200:
            print("❌ خطأ: لا يمكن الوصول لصفحة تسجيل الدخول")
            return None
    
    # استخراج CSRF token
    soup = BeautifulSoup(login_page.content, 'html.parser')
    csrf_token = soup.find('input', {'name': 'csrf_token'})
    if not csrf_token:
        print("❌ خطأ: لم يتم العثور على CSRF token")
        return None
    
    # تسجيل الدخول
    login_data = {
        'username': 'office',
        'password': '66889088',
        'csrf_token': csrf_token['value']
    }
    
    response = session.post('http://127.0.0.1:5000/login', data=login_data)
    if response.status_code == 200 and 'dashboard' in response.url:
        print("✅ تم تسجيل الدخول بنجاح")
        return session
    else:
        print("❌ خطأ في تسجيل الدخول")
        return None

def test_clients_section(session):
    """اختبار قسم إدارة العملاء"""
    print("\n🔍 اختبار قسم إدارة العملاء:")
    
    # اختبار صفحة قائمة العملاء
    response = session.get('http://127.0.0.1:5000/clients')
    if response.status_code != 200:
        print("❌ خطأ في الوصول لصفحة العملاء")
        return False
    
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # البحث عن أزرار الإضافة
    add_buttons = soup.find_all('a', href=re.compile(r'/clients/add'))
    quick_add_buttons = soup.find_all('button', string=re.compile(r'إضافة سريعة'))
    
    print(f"  📊 عدد أزرار الإضافة العادية: {len(add_buttons)}")
    print(f"  📊 عدد أزرار الإضافة السريعة: {len(quick_add_buttons)}")
    
    # اختبار زر الإضافة العادي
    if add_buttons:
        add_response = session.get('http://127.0.0.1:5000/clients/add')
        if add_response.status_code == 200:
            print("  ✅ زر إضافة عميل جديد يعمل بشكل صحيح")
        else:
            print(f"  ❌ زر إضافة عميل جديد لا يعمل (كود: {add_response.status_code})")
            return False
    else:
        print("  ❌ لم يتم العثور على زر إضافة عميل جديد")
        return False
    
    # اختبار زر الإضافة السريعة
    if quick_add_buttons:
        modal_response = session.get('http://127.0.0.1:5000/modal/add_client')
        if modal_response.status_code == 200:
            print("  ✅ زر الإضافة السريعة يعمل بشكل صحيح")
        else:
            print(f"  ❌ زر الإضافة السريعة لا يعمل (كود: {modal_response.status_code})")
            return False
    else:
        print("  ❌ لم يتم العثور على زر الإضافة السريعة")
        return False
    
    # التحقق من عدم وجود النموذج المنبثق القديم
    old_modal = soup.find('div', {'id': 'addClientModal'})
    if not old_modal:
        print("  ✅ تم حذف النموذج المنبثق القديم بنجاح")
    else:
        print("  ⚠️ النموذج المنبثق القديم ما زال موجود")
    
    return True

def test_cases_section(session):
    """اختبار قسم إدارة القضايا"""
    print("\n🔍 اختبار قسم إدارة القضايا:")
    
    # اختبار صفحة قائمة القضايا
    response = session.get('http://127.0.0.1:5000/cases')
    if response.status_code != 200:
        print("❌ خطأ في الوصول لصفحة القضايا")
        return False

    soup = BeautifulSoup(response.content, 'html.parser')

    # البحث عن أزرار الإضافة
    add_buttons = soup.find_all('a', href=re.compile(r'/cases/add'))
    quick_add_buttons = soup.find_all('button', string=re.compile(r'إضافة سريعة'))

    print(f"  📊 عدد أزرار الإضافة العادية: {len(add_buttons)}")
    print(f"  📊 عدد أزرار الإضافة السريعة: {len(quick_add_buttons)}")

    # اختبار زر الإضافة العادي
    if add_buttons:
        add_response = session.get('http://127.0.0.1:5000/cases/add')
        if add_response.status_code == 200:
            print("  ✅ زر إضافة قضية جديدة يعمل بشكل صحيح")
        else:
            print(f"  ❌ زر إضافة قضية جديدة لا يعمل (كود: {add_response.status_code})")
            return False
    else:
        print("  ❌ لم يتم العثور على زر إضافة قضية جديدة")
        return False

    # اختبار زر الإضافة السريعة
    if quick_add_buttons:
        modal_response = session.get('http://127.0.0.1:5000/modal/add_case')
        if modal_response.status_code == 200:
            print("  ✅ زر الإضافة السريعة يعمل بشكل صحيح")
        else:
            print(f"  ❌ زر الإضافة السريعة لا يعمل (كود: {modal_response.status_code})")
            return False
    else:
        print("  ❌ لم يتم العثور على زر الإضافة السريعة")
        return False
    
    # التحقق من عدم وجود النموذج المنبثق القديم
    old_modal = soup.find('div', {'id': 'addCaseModal'})
    if not old_modal:
        print("  ✅ تم حذف النموذج المنبثق القديم بنجاح")
    else:
        print("  ⚠️ النموذج المنبثق القديم ما زال موجود")
    
    return True

def test_properties_section(session):
    """اختبار قسم إدارة العقارات"""
    print("\n🔍 اختبار قسم إدارة العقارات:")
    
    # اختبار صفحة قائمة العقارات
    response = session.get('http://127.0.0.1:5000/properties')
    if response.status_code != 200:
        print("❌ خطأ في الوصول لصفحة العقارات")
        return False

    soup = BeautifulSoup(response.content, 'html.parser')

    # البحث عن أزرار الإضافة
    add_buttons = soup.find_all('a', href=re.compile(r'/properties/add'))
    quick_add_buttons = soup.find_all('button', string=re.compile(r'إضافة سريعة'))

    print(f"  📊 عدد أزرار الإضافة العادية: {len(add_buttons)}")
    print(f"  📊 عدد أزرار الإضافة السريعة: {len(quick_add_buttons)}")

    # التحقق من وجود زر واحد فقط للإضافة العادية
    if len(add_buttons) == 1:
        print("  ✅ تم إزالة الزر المكرر بنجاح - يوجد زر واحد فقط")

        # اختبار الزر الوحيد
        add_response = session.get('http://127.0.0.1:5000/properties/add')
        if add_response.status_code == 200:
            print("  ✅ زر إضافة عقار جديد يعمل بشكل صحيح")
        else:
            print(f"  ❌ زر إضافة عقار جديد لا يعمل (كود: {add_response.status_code})")
            return False
    elif len(add_buttons) > 1:
        print(f"  ⚠️ ما زال هناك أزرار مكررة ({len(add_buttons)} أزرار)")
        return False
    else:
        print("  ❌ لم يتم العثور على زر إضافة عقار")
        return False

    # اختبار زر الإضافة السريعة
    if quick_add_buttons:
        modal_response = session.get('http://127.0.0.1:5000/modal/add_property')
        if modal_response.status_code == 200:
            print("  ✅ زر الإضافة السريعة يعمل بشكل صحيح")
        else:
            print(f"  ❌ زر الإضافة السريعة لا يعمل (كود: {modal_response.status_code})")
            return False
    else:
        print("  ❌ لم يتم العثور على زر الإضافة السريعة")
        return False
    
    return True

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء الاختبار الشامل للإصلاحات المطبقة")
    print("=" * 50)
    
    # تسجيل الدخول
    session = test_login()
    if not session:
        print("❌ فشل في تسجيل الدخول - توقف الاختبار")
        sys.exit(1)
    
    # اختبار الأقسام
    results = []
    results.append(test_clients_section(session))
    results.append(test_cases_section(session))
    results.append(test_properties_section(session))
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 النتائج النهائية:")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"✅ الاختبارات الناجحة: {success_count}/{total_count}")
    print(f"❌ الاختبارات الفاشلة: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 جميع الإصلاحات تعمل بشكل صحيح!")
        return True
    else:
        print("⚠️ هناك مشاكل تحتاج إلى إصلاح")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
