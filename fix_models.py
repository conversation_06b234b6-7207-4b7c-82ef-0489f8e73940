#!/usr/bin/env python3
"""
Script to fix all table names and foreign key references in models.py
"""

import re

# Read the current models.py file
with open('app/models.py', 'r', encoding='utf-8') as f:
    content = f.read()

# Define table name mappings
table_mappings = {
    'user': 'users',
    'client': 'clients', 
    'case': 'cases',
    'session': 'sessions',
    'document': 'documents',
    'invoice': 'invoices',
    'payment': 'payments',
    'task': 'tasks',
    'appointment': 'appointments',
    'property': 'properties',
    'tenant': 'tenants',
    'lease': 'leases',
    'installment': 'installments',
    'financial_transaction': 'financial_transactions',
    'debt': 'debts',
    'debt_payment': 'debt_payments',
    'rental_income': 'rental_incomes',
    'expense': 'expenses',
    'client_document': 'client_documents',
    'court_fee': 'court_fees'
}

# Add __tablename__ to models that don't have it
models_to_fix = [
    ('class Task(db.Model):', 'class Task(db.Model):\n    __tablename__ = \'tasks\'\n'),
    ('class Appointment(db.Model):', 'class Appointment(db.Model):\n    __tablename__ = \'appointments\'\n'),
    ('class Property(db.Model):', 'class Property(db.Model):\n    __tablename__ = \'properties\'\n'),
    ('class Tenant(db.Model):', 'class Tenant(db.Model):\n    __tablename__ = \'tenants\'\n'),
    ('class Lease(db.Model):', 'class Lease(db.Model):\n    __tablename__ = \'leases\'\n'),
    ('class Installment(db.Model):', 'class Installment(db.Model):\n    __tablename__ = \'installments\'\n'),
    ('class FinancialTransaction(db.Model):', 'class FinancialTransaction(db.Model):\n    __tablename__ = \'financial_transactions\'\n'),
    ('class CourtFee(db.Model):', 'class CourtFee(db.Model):\n    __tablename__ = \'court_fees\'\n'),
    ('class Debt(db.Model):', 'class Debt(db.Model):\n    __tablename__ = \'debts\'\n'),
    ('class DebtPayment(db.Model):', 'class DebtPayment(db.Model):\n    __tablename__ = \'debt_payments\'\n'),
    ('class RentalIncome(db.Model):', 'class RentalIncome(db.Model):\n    __tablename__ = \'rental_incomes\'\n'),
    ('class Expense(db.Model):', 'class Expense(db.Model):\n    __tablename__ = \'expenses\'\n'),
]

# Apply model fixes
for old_pattern, new_pattern in models_to_fix:
    if old_pattern in content and '__tablename__' not in content[content.find(old_pattern):content.find(old_pattern) + 200]:
        content = content.replace(old_pattern, new_pattern)

# Fix all foreign key references
for old_table, new_table in table_mappings.items():
    # Fix ForeignKey references
    old_fk_pattern = f"ForeignKey('{old_table}.id')"
    new_fk_pattern = f"ForeignKey('{new_table}.id')"
    content = content.replace(old_fk_pattern, new_fk_pattern)

# Write the fixed content back
with open('app/models.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("Fixed all table names and foreign key references in models.py")
