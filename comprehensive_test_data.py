#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت شامل لإدخال بيانات تجريبية في جميع أقسام النظام
لفحص وظائف النظام والتأكد من عمل جميع الأقسام بشكل صحيح
"""

import sys
import os
from datetime import datetime, timedelta
import random

# إضافة مسار التطبيق
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from app.models import (
    User, Client, Case, Property, Tenant, Lease,
    FinancialTransaction, Appointment, Task,
    Fee, Debt, Expense
)

def create_test_data():
    """إنشاء بيانات تجريبية شاملة"""
    
    with app.app_context():
        print("🚀 بدء إنشاء البيانات التجريبية...")
        
        # 1. إنشاء عملاء تجريبيين
        print("📋 إنشاء عملاء تجريبيين...")
        clients_data = [
            {
                'name': 'أحمد محمد علي',
                'phone': '0501234567',
                'email': '<EMAIL>',
                'address': 'الرياض، حي النخيل، شارع الملك فهد',
                'national_id': '1234567890'
            },
            {
                'name': 'فاطمة عبدالله السالم',
                'phone': '0507654321',
                'email': '<EMAIL>',
                'address': 'جدة، حي الروضة، شارع التحلية',
                'national_id': '0987654321'
            },
            {
                'name': 'خالد سعد الغامدي',
                'phone': '0551122334',
                'email': '<EMAIL>',
                'address': 'الدمام، حي الفيصلية، شارع الأمير محمد',
                'national_id': '1122334455'
            },
            {
                'name': 'نورا إبراهيم المطيري',
                'phone': '0544556677',
                'email': '<EMAIL>',
                'address': 'مكة المكرمة، حي العزيزية، شارع الحرم',
                'national_id': '5566778899'
            },
            {
                'name': 'عبدالرحمن محمد القحطاني',
                'phone': '0533445566',
                'email': '<EMAIL>',
                'address': 'الطائف، حي الشفا، شارع الملك عبدالعزيز',
                'national_id': '3344556677'
            }
        ]
        
        clients = []
        for client_data in clients_data:
            client = Client(**client_data)
            db.session.add(client)
            clients.append(client)
        
        db.session.commit()
        print(f"✅ تم إنشاء {len(clients)} عملاء")
        
        # 2. إنشاء قضايا تجريبية
        print("⚖️ إنشاء قضايا تجريبية...")
        cases_data = [
            {
                'title': 'قضية نزاع تجاري - شركة النور',
                'case_number': 'TC-2024-001',
                'office_case_number': 'OFF-001-2024',
                'court': 'المحكمة التجارية بالرياض',
                'type': 'تجاري',
                'status': 'جارية',
                'client_role': 'مدعي',
                'fees_total': 15000.00,
                'fees_paid': 7500.00,
                'fees_remaining': 7500.00,
                'fees_currency': 'ريال سعودي',
                'case_value': 500000.00,
                'currency': 'ريال سعودي',
                'description': 'نزاع تجاري حول عقد توريد بقيمة 500,000 ريال',
                'priority': 'عالية'
            },
            {
                'title': 'قضية طلاق وحضانة',
                'case_number': 'FC-2024-002',
                'office_case_number': 'OFF-002-2024',
                'court': 'محكمة الأحوال الشخصية بجدة',
                'type': 'أسري',
                'status': 'جارية',
                'client_role': 'مدعية',
                'fees_total': 8000.00,
                'fees_paid': 4000.00,
                'fees_remaining': 4000.00,
                'fees_currency': 'ريال سعودي',
                'case_value': 0.00,
                'currency': 'ريال سعودي',
                'description': 'قضية طلاق مع نزاع حول حضانة الأطفال',
                'priority': 'متوسطة'
            },
            {
                'title': 'قضية استرداد عقار',
                'case_number': 'RE-2024-003',
                'office_case_number': 'OFF-003-2024',
                'court': 'المحكمة العامة بالدمام',
                'type': 'عقاري',
                'status': 'مكتملة',
                'client_role': 'مدعي',
                'fees_total': 12000.00,
                'fees_paid': 12000.00,
                'fees_remaining': 0.00,
                'fees_currency': 'ريال سعودي',
                'case_value': 800000.00,
                'currency': 'ريال سعودي',
                'description': 'استرداد عقار مغتصب بقيمة 800,000 ريال',
                'priority': 'عالية'
            },
            {
                'title': 'قضية تعويض إداري',
                'case_number': 'AD-2024-004',
                'office_case_number': 'OFF-004-2024',
                'court': 'محكمة القضاء الإداري بمكة',
                'type': 'إداري',
                'status': 'جارية',
                'client_role': 'مستأنف',
                'fees_total': 10000.00,
                'fees_paid': 5000.00,
                'fees_remaining': 5000.00,
                'fees_currency': 'ريال سعودي',
                'case_value': 50000.00,
                'currency': 'ريال سعودي',
                'description': 'طلب تعويض عن قرار إداري خاطئ',
                'priority': 'متوسطة'
            },
            {
                'title': 'قضية جنائية - اتهام كيدي',
                'case_number': 'CR-2024-005',
                'office_case_number': 'OFF-005-2024',
                'court': 'المحكمة الجزائية بالطائف',
                'type': 'جنائي',
                'status': 'جارية',
                'client_role': 'متهم',
                'fees_total': 20000.00,
                'fees_paid': 10000.00,
                'fees_remaining': 10000.00,
                'fees_currency': 'ريال سعودي',
                'case_value': 0.00,
                'currency': 'ريال سعودي',
                'description': 'الدفاع في قضية اتهام كيدي',
                'priority': 'عالية'
            }
        ]
        
        cases = []
        for i, case_data in enumerate(cases_data):
            case_data['client_id'] = clients[i].id
            case_data['open_date'] = (datetime.now() - timedelta(days=random.randint(30, 180))).date()

            case = Case(**case_data)
            db.session.add(case)
            cases.append(case)
        
        db.session.commit()
        print(f"✅ تم إنشاء {len(cases)} قضايا")
        
        # 3. إنشاء عقارات تجريبية
        print("🏢 إنشاء عقارات تجريبية...")
        properties_data = [
            {
                'name': 'مجمع الأعمال التجاري',
                'address': 'الرياض، حي العليا، شارع الملك فهد',
                'type': 'تجاري',
                'area': 500.0,
                'rooms_count': 0,
                'bathrooms_count': 4,
                'monthly_rent': 15000.00,
                'currency': 'ريال سعودي',
                'deposit_amount': 45000.00,
                'status': 'مؤجر',
                'description': 'مجمع تجاري في موقع استراتيجي',
                'owner_name': 'شركة الاستثمار العقاري',
                'owner_phone': '0112345678'
            },
            {
                'name': 'فيلا سكنية فاخرة',
                'address': 'جدة، حي الروضة، شارع التحلية',
                'type': 'سكني',
                'area': 400.0,
                'rooms_count': 6,
                'bathrooms_count': 5,
                'monthly_rent': 12000.00,
                'currency': 'ريال سعودي',
                'deposit_amount': 36000.00,
                'status': 'متاح',
                'description': 'فيلا سكنية بتصميم عصري',
                'owner_name': 'أحمد العبدالله',
                'owner_phone': '0126789012'
            },
            {
                'name': 'مكاتب إدارية',
                'address': 'الدمام، حي الفيصلية، برج الأعمال',
                'type': 'مكتبي',
                'area': 200.0,
                'rooms_count': 8,
                'bathrooms_count': 3,
                'monthly_rent': 8000.00,
                'currency': 'ريال سعودي',
                'deposit_amount': 24000.00,
                'status': 'مؤجر',
                'description': 'مكاتب إدارية في برج حديث',
                'owner_name': 'مؤسسة العقارات المتطورة',
                'owner_phone': '0138901234'
            }
        ]
        
        properties = []
        for prop_data in properties_data:
            property_obj = Property(**prop_data)
            db.session.add(property_obj)
            properties.append(property_obj)
        
        db.session.commit()
        print(f"✅ تم إنشاء {len(properties)} عقارات")
        
        # 4. إنشاء مستأجرين
        print("👥 إنشاء مستأجرين...")
        tenants_data = [
            {
                'name': 'شركة التقنية المتقدمة',
                'phone': '0112345678',
                'email': '<EMAIL>',
                'national_id': '7001234567',
                'address': 'الرياض، حي الملز',
                'notes': 'شركة تقنية رائدة'
            },
            {
                'name': 'مؤسسة الخدمات التجارية',
                'phone': '0126789012',
                'email': '<EMAIL>',
                'national_id': '7009876543',
                'address': 'الدمام، حي الشاطئ',
                'notes': 'مؤسسة تجارية كبيرة'
            }
        ]
        
        tenants = []
        for tenant_data in tenants_data:
            tenant = Tenant(**tenant_data)
            db.session.add(tenant)
            tenants.append(tenant)
        
        db.session.commit()
        print(f"✅ تم إنشاء {len(tenants)} مستأجرين")
        
        # 5. إنشاء عقود إيجار
        print("📄 إنشاء عقود إيجار...")
        contracts_data = [
            {
                'property_id': properties[0].id,
                'tenant_id': tenants[0].id,
                'start_date': datetime.now() - timedelta(days=90),
                'end_date': datetime.now() + timedelta(days=275),
                'rent_amount': 15000.00,
                'deposit': 45000.00,
                'commission': 1500.00,
                'currency': 'ريال سعودي',
                'contract_duration': 12,
                'payment_day': 1,
                'status': 'نشط',
                'terms_conditions': 'عقد إيجار تجاري لمدة سنة مع إمكانية التجديد',
                'notes': 'مستأجر ممتاز - دفع منتظم'
            },
            {
                'property_id': properties[2].id,
                'tenant_id': tenants[1].id,
                'start_date': datetime.now() - timedelta(days=60),
                'end_date': datetime.now() + timedelta(days=305),
                'rent_amount': 8000.00,
                'deposit': 24000.00,
                'commission': 800.00,
                'currency': 'ريال سعودي',
                'contract_duration': 12,
                'payment_day': 5,
                'status': 'نشط',
                'terms_conditions': 'عقد إيجار مكاتب إدارية مع خدمات',
                'notes': 'عقد جديد - مراقبة الدفع'
            }
        ]

        contracts = []
        for contract_data in contracts_data:
            contract = Lease(**contract_data)
            db.session.add(contract)
            contracts.append(contract)

        db.session.commit()
        print(f"✅ تم إنشاء {len(contracts)} عقود إيجار")

        # 6. إنشاء سندات مالية
        print("💰 إنشاء سندات مالية...")
        financial_transactions = []

        # سندات متعلقة بالقضايا
        for i, case in enumerate(cases):
            # سند استلام أتعاب
            transaction = FinancialTransaction(
                type='أتعاب',
                amount=case.fees_paid,
                description=f'دفعة أتعاب قضية: {case.title}',
                currency='ريال سعودي',
                case_id=case.id,
                client_id=case.client_id,
                date=datetime.combine(case.open_date, datetime.min.time()) + timedelta(days=1)
            )
            db.session.add(transaction)
            financial_transactions.append(transaction)

        # سندات إيجارات
        for contract in contracts:
            for month in range(3):  # 3 أشهر من الإيجارات
                transaction = FinancialTransaction(
                    type='تحصيل إيجار',
                    amount=contract.rent_amount,
                    description=f'إيجار شهر {month + 1} - عقار رقم {contract.property_id}',
                    currency='ريال سعودي',
                    property_id=contract.property_id,
                    tenant_id=contract.tenant_id,
                    date=contract.start_date + timedelta(days=30 * month)
                )
                db.session.add(transaction)
                financial_transactions.append(transaction)

        # مصروفات المكتب
        office_expenses = [
            {
                'type': 'صرف',
                'amount': 3000.00,
                'description': 'إيجار مكتب المحاماة - شهر ديسمبر',
                'currency': 'ريال سعودي',
                'date': datetime.now() - timedelta(days=15)
            },
            {
                'type': 'صرف',
                'amount': 1500.00,
                'description': 'فواتير كهرباء وماء',
                'currency': 'ريال سعودي',
                'date': datetime.now() - timedelta(days=10)
            },
            {
                'type': 'رسوم',
                'amount': 800.00,
                'description': 'رسوم محكمة وطوابع',
                'currency': 'ريال سعودي',
                'date': datetime.now() - timedelta(days=5)
            },
            {
                'type': 'صرف',
                'amount': 2000.00,
                'description': 'راتب موظف إداري',
                'currency': 'ريال سعودي',
                'date': datetime.now() - timedelta(days=3)
            }
        ]

        for expense_data in office_expenses:
            expense = FinancialTransaction(**expense_data)
            db.session.add(expense)
            financial_transactions.append(expense)

        db.session.commit()
        print(f"✅ تم إنشاء {len(financial_transactions)} سند مالي")

        # 7. إنشاء مواعيد
        print("📅 إنشاء مواعيد...")
        appointments_data = [
            {
                'subject': 'جلسة محكمة - قضية النزاع التجاري',
                'date': datetime.now() + timedelta(days=7),
                'location': 'المحكمة التجارية بالرياض - قاعة 3',
                'client_id': clients[0].id,
                'case_id': cases[0].id,
                'notes': 'حضور جلسة المرافعة الأولى'
            },
            {
                'subject': 'اجتماع مع العميل - مراجعة الأوراق',
                'date': datetime.now() + timedelta(days=3),
                'location': 'مكتب المحاماة',
                'client_id': clients[1].id,
                'case_id': cases[1].id,
                'notes': 'مراجعة أوراق القضية وتحضير الدفوع'
            },
            {
                'subject': 'موعد توقيع عقد إيجار جديد',
                'date': datetime.now() + timedelta(days=5),
                'location': 'مكتب المحاماة',
                'client_id': clients[2].id,
                'notes': 'توقيع عقد إيجار مع مستأجر جديد'
            },
            {
                'subject': 'استشارة قانونية - قضية جديدة',
                'date': datetime.now() + timedelta(days=2),
                'location': 'مكتب المحاماة',
                'client_id': clients[3].id,
                'notes': 'استشارة أولية لقضية إدارية'
            }
        ]

        appointments = []
        for appointment_data in appointments_data:
            appointment = Appointment(**appointment_data)
            db.session.add(appointment)
            appointments.append(appointment)

        db.session.commit()
        print(f"✅ تم إنشاء {len(appointments)} مواعيد")

        # 8. إنشاء مهام
        print("✅ إنشاء مهام...")
        tasks_data = [
            {
                'title': 'تحضير مذكرة دفاع',
                'description': 'إعداد مذكرة دفاع شاملة للقضية التجارية',
                'due_date': datetime.now() + timedelta(days=5),
                'priority': 'عالية',
                'status': 'قيد التنفيذ',
                'case_id': cases[0].id,
                'client_id': cases[0].client_id,
                'category': 'قانونية',
                'progress': 30,
                'estimated_hours': 8.0
            },
            {
                'title': 'جمع الأدلة والوثائق',
                'description': 'جمع جميع الأدلة المطلوبة لقضية الطلاق',
                'due_date': datetime.now() + timedelta(days=3),
                'priority': 'متوسطة',
                'status': 'مكتملة',
                'case_id': cases[1].id,
                'client_id': cases[1].client_id,
                'category': 'قانونية',
                'progress': 100,
                'estimated_hours': 4.0,
                'actual_hours': 3.5,
                'completed_date': datetime.now() - timedelta(days=1)
            },
            {
                'title': 'مراجعة عقد الإيجار',
                'description': 'مراجعة شروط عقد الإيجار الجديد',
                'due_date': datetime.now() + timedelta(days=2),
                'priority': 'عالية',
                'status': 'قيد التنفيذ',
                'category': 'قانونية',
                'progress': 60,
                'estimated_hours': 3.0
            },
            {
                'title': 'إعداد تقرير شهري',
                'description': 'إعداد تقرير شهري عن حالة القضايا',
                'due_date': datetime.now() + timedelta(days=10),
                'priority': 'منخفضة',
                'status': 'معلقة',
                'category': 'إدارية',
                'progress': 0,
                'estimated_hours': 2.0
            }
        ]

        tasks = []
        for task_data in tasks_data:
            task = Task(**task_data)
            db.session.add(task)
            tasks.append(task)

        db.session.commit()
        print(f"✅ تم إنشاء {len(tasks)} مهام")

        print("✅ تم إكمال إنشاء البيانات التجريبية الأساسية!")
        return clients, cases, properties, tenants, contracts, financial_transactions, appointments, tasks

if __name__ == "__main__":
    create_test_data()
    print("🎉 تم إكمال إنشاء جميع البيانات التجريبية بنجاح!")
