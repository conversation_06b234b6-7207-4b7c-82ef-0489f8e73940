from app import app, db
from app.models import Case, Client, FinancialTransaction
from sqlalchemy import func
import traceback

def test_stats():
    """فحص دالة الإحصائيات"""
    try:
        with app.app_context():
            print("🔍 فحص دالة الإحصائيات...")
            
            # فحص الاستعلامات الأساسية
            print("1. فحص عدد القضايا...")
            cases_count = Case.query.count()
            print(f"✅ عدد القضايا: {cases_count}")
            
            print("2. فحص إحصائيات المحاكم...")
            courts_stats = db.session.query(Case.court, func.count(Case.id)).group_by(Case.court).all()
            print(f"✅ إحصائيات المحاكم: {courts_stats}")
            
            print("3. فحص إحصائيات صفة الموكل...")
            client_role_stats = db.session.query(Case.client_role, func.count(Case.id)).group_by(Case.client_role).all()
            print(f"✅ إحصائيات صفة الموكل: {client_role_stats}")
            
            print("4. فحص إحصائيات حالة القضايا...")
            status_stats = db.session.query(Case.status, func.count(Case.id)).group_by(Case.status).all()
            print(f"✅ إحصائيات حالة القضايا: {status_stats}")
            
            print("5. فحص إحصائيات أنواع القضايا...")
            types_stats = db.session.query(Case.type, func.count(Case.id)).group_by(Case.type).all()
            print(f"✅ إحصائيات أنواع القضايا: {types_stats}")
            
            print("6. فحص إحصائيات العملاء...")
            clients_stats = db.session.query(Client.name, func.count(Case.id)).join(Case).group_by(Client.name).all()
            print(f"✅ إحصائيات العملاء: {clients_stats}")
            
            print("✅ جميع الاستعلامات تعمل بنجاح")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص الإحصائيات: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_stats()
