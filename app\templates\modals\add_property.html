<form method="POST" action="{{ url_for('add_property') }}" class="modal-form" id="addPropertyForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-building"></i> معلومات العقار الأساسية
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">اسم العقار <span class="text-danger">*</span></label>
                <input type="text" name="name" class="form-control" required 
                       placeholder="اسم العقار">
            </div>
            <div class="col-md-6">
                <label class="form-label">نوع العقار</label>
                <select name="type" class="form-control">
                    <option value="شقة">شقة</option>
                    <option value="فيلا">فيلا</option>
                    <option value="مكتب">مكتب</option>
                    <option value="محل تجاري">محل تجاري</option>
                    <option value="مستودع">مستودع</option>
                    <option value="أرض">أرض</option>
                    <option value="أخرى">أخرى</option>
                </select>
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-12">
                <label class="form-label">العنوان <span class="text-danger">*</span></label>
                <textarea name="address" class="form-control" rows="2" required 
                          placeholder="العنوان الكامل للعقار"></textarea>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-ruler"></i> مواصفات العقار
        </div>
        <div class="row g-3">
            <div class="col-md-3">
                <label class="form-label">المساحة (م²)</label>
                <input type="number" name="area" class="form-control" 
                       step="0.01" min="0" placeholder="المساحة">
            </div>
            <div class="col-md-3">
                <label class="form-label">عدد الغرف</label>
                <input type="number" name="rooms_count" class="form-control" 
                       min="0" placeholder="عدد الغرف">
            </div>
            <div class="col-md-3">
                <label class="form-label">عدد الحمامات</label>
                <input type="number" name="bathrooms_count" class="form-control" 
                       min="0" placeholder="عدد الحمامات">
            </div>
            <div class="col-md-3">
                <label class="form-label">الطابق</label>
                <input type="number" name="floor" class="form-control" 
                       placeholder="رقم الطابق">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">عمر البناء (سنوات)</label>
                <input type="number" name="building_age" class="form-control" 
                       min="0" placeholder="عمر البناء">
            </div>
            <div class="col-md-6">
                <label class="form-label">حالة العقار</label>
                <select name="status" class="form-control">
                    <option value="متاح">متاح</option>
                    <option value="مؤجر">مؤجر</option>
                    <option value="تحت الصيانة">تحت الصيانة</option>
                    <option value="غير متاح">غير متاح</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-check-circle"></i> المرافق والخدمات
        </div>
        <div class="row g-3">
            <div class="col-md-4">
                <div class="form-check">
                    <input type="checkbox" name="furnished" class="form-check-input" id="furnished">
                    <label class="form-check-label" for="furnished">مفروش</label>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-check">
                    <input type="checkbox" name="parking" class="form-check-input" id="parking">
                    <label class="form-check-label" for="parking">موقف سيارة</label>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-check">
                    <input type="checkbox" name="elevator" class="form-check-input" id="elevator">
                    <label class="form-check-label" for="elevator">مصعد</label>
                </div>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-money-bill"></i> المعلومات المالية
        </div>
        <div class="row g-3">
            <div class="col-md-4">
                <label class="form-label">الإيجار الشهري</label>
                <input type="number" name="monthly_rent" class="form-control" 
                       step="0.01" min="0" value="0" placeholder="الإيجار الشهري">
            </div>
            <div class="col-md-4">
                <label class="form-label">مبلغ التأمين</label>
                <input type="number" name="deposit_amount" class="form-control" 
                       step="0.01" min="0" value="0" placeholder="مبلغ التأمين">
            </div>
            <div class="col-md-4">
                <label class="form-label">نسبة العمولة (%)</label>
                <input type="number" name="commission_rate" class="form-control" 
                       step="0.01" min="0" max="100" value="0" placeholder="نسبة العمولة">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">العملة</label>
                <select name="currency" class="form-control">
                    <option value="شيكل">شيكل</option>
                    <option value="دولار">دولار أمريكي</option>
                    <option value="دينار">دينار أردني</option>
                    <option value="يورو">يورو</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-user"></i> معلومات المالك
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">اسم المالك</label>
                <input type="text" name="owner_name" class="form-control" 
                       placeholder="اسم مالك العقار">
            </div>
            <div class="col-md-6">
                <label class="form-label">هاتف المالك</label>
                <input type="tel" name="owner_phone" class="form-control" 
                       placeholder="رقم هاتف المالك">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-12">
                <label class="form-label">عنوان المالك</label>
                <textarea name="owner_address" class="form-control" rows="2" 
                          placeholder="عنوان المالك"></textarea>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-sticky-note"></i> ملاحظات
        </div>
        <div class="mb-3">
            <label class="form-label">وصف العقار</label>
            <textarea name="description" class="form-control" rows="3" 
                      placeholder="وصف تفصيلي للعقار..."></textarea>
        </div>
        <div class="mb-3">
            <label class="form-label">ملاحظات إضافية</label>
            <textarea name="notes" class="form-control" rows="2" 
                      placeholder="ملاحظات إضافية..."></textarea>
        </div>
    </div>

    <div class="text-center mt-4">
        <button type="submit" class="btn btn-success px-4">
            <i class="fa fa-save"></i> حفظ العقار
        </button>
        <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
            إلغاء
        </button>
    </div>
</form>

<script>
document.getElementById('addPropertyForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            if (modal) modal.hide();
            showAlert('تم إضافة العقار بنجاح', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(data.message || 'حدث خطأ أثناء إضافة العقار', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
});

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
    document.body.appendChild(alertDiv);
    setTimeout(() => alertDiv.remove(), 5000);
}
</script>

<style>
.modal-form .form-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.modal-form .form-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
}

.modal-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.modal-form .text-danger {
    color: #dc3545 !important;
}

.modal-form .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.modal-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.modal-form .form-check-label {
    font-weight: 500;
    color: #495057;
}
</style>
