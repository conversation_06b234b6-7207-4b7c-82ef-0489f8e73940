/**
 * نظام إدارة الإشعارات والتنبيهات
 */
class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.overdueItems = [];
        this.isDropdownOpen = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadOverdueItems();
        this.startPeriodicCheck();
    }

    bindEvents() {
        // زر الإشعارات
        const notificationToggle = document.getElementById('notificationToggle');
        if (notificationToggle) {
            notificationToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown();
            });
        }

        // زر تحديد الكل كمقروء
        const markAllRead = document.getElementById('markAllRead');
        if (markAllRead) {
            markAllRead.addEventListener('click', () => {
                this.markAllAsRead();
            });
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', (e) => {
            const dropdown = document.getElementById('notificationDropdown');
            const toggle = document.getElementById('notificationToggle');
            
            if (dropdown && toggle && !dropdown.contains(e.target) && !toggle.contains(e.target)) {
                this.closeDropdown();
            }
        });
    }

    toggleDropdown() {
        const dropdown = document.getElementById('notificationDropdown');
        if (dropdown) {
            if (this.isDropdownOpen) {
                this.closeDropdown();
            } else {
                this.openDropdown();
            }
        }
    }

    openDropdown() {
        const dropdown = document.getElementById('notificationDropdown');
        if (dropdown) {
            dropdown.style.display = 'block';
            this.isDropdownOpen = true;
            this.loadNotifications();
        }
    }

    closeDropdown() {
        const dropdown = document.getElementById('notificationDropdown');
        if (dropdown) {
            dropdown.style.display = 'none';
            this.isDropdownOpen = false;
        }
    }

    async loadOverdueItems() {
        try {
            // محاكاة البيانات المتأخرة
            this.overdueItems = await this.getOverdueItemsFromSystem();
            this.updateNotificationBadge();
            this.createPersistentAlerts();
        } catch (error) {
            console.error('خطأ في تحميل العناصر المتأخرة:', error);
        }
    }

    async getOverdueItemsFromSystem() {
        // محاكاة البيانات - في التطبيق الحقيقي ستأتي من API
        const overdueItems = [];
        
        // مهام متأخرة
        const today = new Date();
        const overdueTasks = [
            {
                id: 'task_1',
                type: 'task',
                title: 'مراجعة ملف القضية رقم 123',
                dueDate: new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000), // منذ يومين
                priority: 'عالية'
            },
            {
                id: 'task_2',
                type: 'task',
                title: 'إعداد مذكرة دفاع',
                dueDate: new Date(today.getTime() - 1 * 24 * 60 * 60 * 1000), // منذ يوم
                priority: 'متوسطة'
            }
        ];

        // مواعيد متأخرة
        const overdueAppointments = [
            {
                id: 'appointment_1',
                type: 'appointment',
                title: 'موعد مع العميل أحمد محمد',
                dueDate: new Date(today.getTime() - 3 * 60 * 60 * 1000), // منذ 3 ساعات
                location: 'المكتب'
            }
        ];

        // استحقاقات مالية متأخرة
        const overduePayments = [
            {
                id: 'payment_1',
                type: 'payment',
                title: 'أتعاب القضية رقم 456',
                amount: 5000,
                currency: 'شيكل',
                dueDate: new Date(today.getTime() - 5 * 24 * 60 * 60 * 1000), // منذ 5 أيام
                client: 'محمد أحمد'
            },
            {
                id: 'payment_2',
                type: 'payment',
                title: 'إيجار العقار - شارع الملك',
                amount: 1200,
                currency: 'شيكل',
                dueDate: new Date(today.getTime() - 10 * 24 * 60 * 60 * 1000), // منذ 10 أيام
                tenant: 'سارة محمود'
            }
        ];

        return [...overdueTasks, ...overdueAppointments, ...overduePayments];
    }

    updateNotificationBadge() {
        const badge = document.getElementById('notificationBadge');
        if (badge) {
            const count = this.overdueItems.length;
            if (count > 0) {
                badge.textContent = count;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    createPersistentAlerts() {
        // إنشاء تنبيهات مستمرة للعناصر المتأخرة
        this.overdueItems.forEach(item => {
            this.createNotification(item);
        });
    }

    createNotification(item) {
        const notification = {
            id: `notification_${item.id}`,
            type: item.type,
            title: this.getNotificationTitle(item),
            message: this.getNotificationMessage(item),
            time: new Date(),
            isRead: false,
            isOverdue: true,
            originalItem: item
        };

        // إضافة الإشعار إلى القائمة إذا لم يكن موجوداً
        const existingIndex = this.notifications.findIndex(n => n.id === notification.id);
        if (existingIndex === -1) {
            this.notifications.unshift(notification);
        }
    }

    getNotificationTitle(item) {
        switch (item.type) {
            case 'task':
                return '⚠️ مهمة متأخرة';
            case 'appointment':
                return '📅 موعد متأخر';
            case 'payment':
                return '💰 استحقاق متأخر';
            default:
                return '🔔 تنبيه';
        }
    }

    getNotificationMessage(item) {
        const daysOverdue = Math.floor((new Date() - item.dueDate) / (1000 * 60 * 60 * 24));
        const timeText = daysOverdue > 0 ? `منذ ${daysOverdue} يوم` : 'اليوم';
        
        switch (item.type) {
            case 'task':
                return `${item.title} - مطلوب منذ ${timeText}`;
            case 'appointment':
                return `${item.title} - كان مقرراً ${timeText}`;
            case 'payment':
                return `${item.title} - ${item.amount} ${item.currency} - متأخر ${timeText}`;
            default:
                return item.title;
        }
    }

    loadNotifications() {
        const notificationList = document.getElementById('notificationList');
        if (!notificationList) return;

        if (this.notifications.length === 0) {
            notificationList.innerHTML = '<div class="text-center p-3 text-muted">لا توجد إشعارات جديدة</div>';
            return;
        }

        const notificationsHtml = this.notifications.map(notification => {
            const timeAgo = this.getTimeAgo(notification.time);
            const unreadClass = notification.isRead ? '' : 'unread';
            const overdueClass = notification.isOverdue ? 'overdue' : '';
            
            return `
                <div class="notification-item ${unreadClass} ${overdueClass}" data-id="${notification.id}">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-text">${notification.message}</div>
                    <div class="notification-time">${timeAgo}</div>
                </div>
            `;
        }).join('');

        notificationList.innerHTML = notificationsHtml;

        // إضافة مستمعات الأحداث للإشعارات
        notificationList.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', () => {
                const notificationId = item.dataset.id;
                this.markAsRead(notificationId);
                this.handleNotificationClick(notificationId);
            });
        });
    }

    getTimeAgo(date) {
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'الآن';
        if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
        
        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
        
        const diffInDays = Math.floor(diffInHours / 24);
        return `منذ ${diffInDays} يوم`;
    }

    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
            notification.isRead = true;
            this.updateNotificationBadge();
        }
    }

    markAllAsRead() {
        this.notifications.forEach(notification => {
            notification.isRead = true;
        });
        this.updateNotificationBadge();
        this.loadNotifications();
    }

    handleNotificationClick(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification && notification.originalItem) {
            // توجيه المستخدم إلى الصفحة المناسبة
            this.navigateToItem(notification.originalItem);
        }
    }

    navigateToItem(item) {
        switch (item.type) {
            case 'task':
                window.location.href = '/manage_tasks';
                break;
            case 'appointment':
                window.location.href = '/appointments_list';
                break;
            case 'payment':
                if (item.title.includes('أتعاب')) {
                    window.location.href = '/fees_list';
                } else {
                    window.location.href = '/finance_list';
                }
                break;
        }
    }

    startPeriodicCheck() {
        // فحص دوري كل 5 دقائق للعناصر المتأخرة الجديدة
        setInterval(() => {
            this.loadOverdueItems();
        }, 5 * 60 * 1000);
    }
}

// تهيئة نظام الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    new NotificationSystem();
});
