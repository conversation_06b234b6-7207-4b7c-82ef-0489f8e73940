#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import re
from bs4 import BeautifulSoup

BASE_URL = "http://localhost:5000"

def test_modal_direct():
    """اختبار الـ modal مباشرة"""
    print("🧪 اختبار الـ modal مباشرة...")
    
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {
        'username': 'office',
        'password': '66889088'
    }
    
    login_response = session.post(f"{BASE_URL}/lawyersameh", data=login_data)
    if login_response.status_code != 200:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # الحصول على modal إضافة العميل مباشرة
    modal_response = session.get(f"{BASE_URL}/modal/add_client")
    if modal_response.status_code != 200:
        print(f"❌ فشل في الوصول للـ modal: {modal_response.status_code}")
        return False
    
    print("✅ تم الوصول للـ modal بنجاح")
    
    # البحث عن CSRF token في الـ modal
    soup = BeautifulSoup(modal_response.text, 'html.parser')
    csrf_input = soup.find('input', {'name': 'csrf_token'})
    
    if csrf_input:
        csrf_token = csrf_input.get('value')
        print(f"✅ تم العثور على CSRF token في الـ modal: {csrf_token[:20]}...")
        
        # اختبار إضافة عميل باستخدام CSRF من الـ modal
        client_data = {
            'name': 'عميل تجريبي Modal مباشر',
            'phone': '0501234569',
            'email': '<EMAIL>',
            'address': 'عنوان تجريبي مباشر',
            'role': 'موكل',
            'csrf_token': csrf_token
        }
        
        print("🔄 محاولة إضافة عميل باستخدام CSRF من الـ modal...")
        add_response = session.post(f"{BASE_URL}/clients/add", data=client_data)
        
        print(f"📊 رمز الاستجابة: {add_response.status_code}")
        
        if add_response.status_code == 200:
            print("✅ تم إضافة العميل بنجاح!")
            return True
        elif add_response.status_code == 302:
            print("✅ تم إضافة العميل بنجاح (تم التوجيه)!")
            return True
        else:
            print(f"❌ فشل في إضافة العميل: {add_response.status_code}")
            if "CSRF" in add_response.text:
                print("❌ مشكلة في CSRF token")
                print(f"📄 محتوى الاستجابة: {add_response.text[:500]}")
            return False
    else:
        print("❌ لم يتم العثور على CSRF token في الـ modal")
        print(f"📄 محتوى الـ modal: {modal_response.text[:500]}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار الـ modal المباشر...")
    print("=" * 60)
    
    success = test_modal_direct()
    
    print("=" * 60)
    if success:
        print("🎉 الاختبار نجح! الـ modal يعمل بشكل صحيح")
    else:
        print("💥 الاختبار فشل! هناك مشكلة في الـ modal")
