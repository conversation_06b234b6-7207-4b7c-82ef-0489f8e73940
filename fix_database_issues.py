#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل قاعدة البيانات المكتشفة
Fix Database Issues Script
"""

import sqlite3
import hashlib
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_admin_users():
    """إنشاء المستخدمين الإداريين"""
    print("👥 إنشاء المستخدمين الإداريين...")
    
    conn = sqlite3.connect('instance/lawoffice.db')
    cursor = conn.cursor()
    
    # حذف المستخدمين الموجودين (إن وجدوا)
    cursor.execute('DELETE FROM users')
    
    # إنشاء مستخدم المكتب
    office_password = hashlib.sha256('66889088'.encode()).hexdigest()
    cursor.execute('''
        INSERT INTO users (username, password, role, last_login)
        VALUES (?, ?, ?, ?)
    ''', ('office', office_password, 'مدير', datetime.now()))
    
    # إنشاء مستخدم الإدارة
    admin_password = hashlib.sha256('admin123'.encode()).hexdigest()
    cursor.execute('''
        INSERT INTO users (username, password, role, last_login)
        VALUES (?, ?, ?, ?)
    ''', ('admin', admin_password, 'مدير', datetime.now()))
    
    conn.commit()
    
    # التحقق من إنشاء المستخدمين
    cursor.execute('SELECT id, username, role FROM users')
    users = cursor.fetchall()
    
    print(f"   ✅ تم إنشاء {len(users)} مستخدم:")
    for user in users:
        print(f"      - ID: {user[0]}, Username: {user[1]}, Role: {user[2]}")
    
    conn.close()
    return True

def create_sample_clients():
    """إنشاء عملاء تجريبيين"""
    print("\n👤 إنشاء عملاء تجريبيين...")
    
    conn = sqlite3.connect('instance/lawoffice.db')
    cursor = conn.cursor()
    
    sample_clients = [
        ('أحمد محمد علي', '123456789', '0599123456', '<EMAIL>', 'غزة - الرمال', '1985-05-15', 'محامي', 'عميل مهم', 'موكل'),
        ('فاطمة أحمد سالم', '987654321', '0598765432', '<EMAIL>', 'غزة - الشجاعية', '1990-08-20', 'طبيبة', 'عميلة جديدة', 'موكل'),
        ('محمد سعيد حسن', '456789123', '0597456789', '<EMAIL>', 'غزة - النصر', '1982-12-10', 'مهندس', 'عميل قديم', 'موكل'),
        ('سارة خالد يوسف', '789123456', '0596789123', '<EMAIL>', 'غزة - الزيتون', '1988-03-25', 'معلمة', 'عميلة مميزة', 'موكل'),
        ('عبد الله عمر محمد', '321654987', '0595321654', '<EMAIL>', 'غزة - التفاح', '1975-11-30', 'تاجر', 'عميل كبير', 'موكل')
    ]
    
    for client_data in sample_clients:
        cursor.execute('''
            INSERT INTO clients (name, national_id, phone, email, address, birth_date, occupation, notes, role)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', client_data)
    
    conn.commit()
    
    # التحقق من إنشاء العملاء
    cursor.execute('SELECT COUNT(*) FROM clients')
    clients_count = cursor.fetchone()[0]
    print(f"   ✅ تم إنشاء {clients_count} عميل")
    
    conn.close()
    return True

def create_sample_cases():
    """إنشاء قضايا تجريبية"""
    print("\n⚖️ إنشاء قضايا تجريبية...")
    
    conn = sqlite3.connect('instance/lawoffice.db')
    cursor = conn.cursor()
    
    # الحصول على معرفات العملاء
    cursor.execute('SELECT id FROM clients LIMIT 5')
    client_ids = [row[0] for row in cursor.fetchall()]
    
    # الحصول على معرف المستخدم الأول
    cursor.execute('SELECT id FROM users LIMIT 1')
    lawyer_id = cursor.fetchone()[0]
    
    sample_cases = [
        ('2024/001', 'مكتب/2024/001', 'قضية ميراث', 'أحوال شخصية', 'مفتوحة', 'محكمة غزة الشرعية', 'الورثة الآخرون', 'قضية تقسيم ميراث', datetime.now().date(), 5000.0, 1000.0, 4000.0, 'شيكل', 500.0, 100.0, 400.0, 'عالية', datetime.now() + timedelta(days=30), 50000.0, 'شيكل', 'مدعي', client_ids[0], lawyer_id),
        ('2024/002', 'مكتب/2024/002', 'قضية عمالية', 'عمالي', 'جارية', 'محكمة العمل', 'الشركة المدعى عليها', 'قضية فصل تعسفي', datetime.now().date(), 3000.0, 500.0, 2500.0, 'شيكل', 300.0, 50.0, 250.0, 'متوسطة', datetime.now() + timedelta(days=15), 25000.0, 'شيكل', 'مدعي', client_ids[1], lawyer_id),
        ('2024/003', 'مكتب/2024/003', 'قضية تجارية', 'تجاري', 'مفتوحة', 'محكمة غزة التجارية', 'الشريك السابق', 'نزاع شراكة تجارية', datetime.now().date(), 8000.0, 2000.0, 6000.0, 'شيكل', 800.0, 200.0, 600.0, 'عالية', datetime.now() + timedelta(days=45), 100000.0, 'شيكل', 'مدعي', client_ids[2], lawyer_id),
        ('2024/004', 'مكتب/2024/004', 'قضية عقارية', 'عقاري', 'جارية', 'محكمة غزة المدنية', 'المالك السابق', 'نزاع ملكية عقار', datetime.now().date(), 6000.0, 1500.0, 4500.0, 'شيكل', 600.0, 150.0, 450.0, 'عالية', datetime.now() + timedelta(days=60), 200000.0, 'شيكل', 'مدعي', client_ids[3], lawyer_id),
        ('2024/005', 'مكتب/2024/005', 'قضية جنائية', 'جنائي', 'مفتوحة', 'محكمة غزة الجنائية', 'النيابة العامة', 'قضية دفاع جنائي', datetime.now().date(), 4000.0, 800.0, 3200.0, 'شيكل', 400.0, 80.0, 320.0, 'عالية', datetime.now() + timedelta(days=20), 0.0, 'شيكل', 'مدافع عنه', client_ids[4], lawyer_id)
    ]
    
    for case_data in sample_cases:
        cursor.execute('''
            INSERT INTO cases (case_number, office_case_number, title, type, status, court, opponent, description, 
                             open_date, fees_total, fees_paid, fees_remaining, fees_currency, court_fees_total, 
                             court_fees_paid, court_fees_remaining, priority, next_session_date, case_value, 
                             currency, client_role, client_id, lawyer_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', case_data)
    
    conn.commit()
    
    # التحقق من إنشاء القضايا
    cursor.execute('SELECT COUNT(*) FROM cases')
    cases_count = cursor.fetchone()[0]
    print(f"   ✅ تم إنشاء {cases_count} قضية")
    
    conn.close()
    return True

def create_financial_transactions():
    """إنشاء معاملات مالية تجريبية"""
    print("\n💰 إنشاء معاملات مالية تجريبية...")
    
    conn = sqlite3.connect('instance/lawoffice.db')
    cursor = conn.cursor()
    
    # الحصول على معرفات العملاء والقضايا
    cursor.execute('SELECT id FROM clients LIMIT 5')
    client_ids = [row[0] for row in cursor.fetchall()]
    
    cursor.execute('SELECT id FROM cases LIMIT 5')
    case_ids = [row[0] for row in cursor.fetchall()]
    
    sample_transactions = [
        (1000.0, datetime.now(), 'دفعة أتعاب', 'دفعة أولى من أتعاب القضية', case_ids[0], client_ids[0], None, None, 'شيكل'),
        (500.0, datetime.now(), 'دفعة أتعاب', 'دفعة ثانية من أتعاب القضية', case_ids[1], client_ids[1], None, None, 'شيكل'),
        (2000.0, datetime.now(), 'دفعة أتعاب', 'دفعة كبيرة من أتعاب القضية', case_ids[2], client_ids[2], None, None, 'شيكل'),
        (800.0, datetime.now(), 'إيراد إيجار', 'إيراد شهري من العقار', None, None, 1, 1, 'شيكل'),
        (800.0, datetime.now(), 'إيراد إيجار', 'إيراد شهري من العقار', None, None, 2, 2, 'شيكل')
    ]
    
    for transaction_data in sample_transactions:
        cursor.execute('''
            INSERT INTO financial_transactions (amount, date, type, description, case_id, client_id, property_id, tenant_id, currency)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', transaction_data)
    
    conn.commit()
    
    # التحقق من إنشاء المعاملات
    cursor.execute('SELECT COUNT(*) FROM financial_transactions')
    transactions_count = cursor.fetchone()[0]
    print(f"   ✅ تم إنشاء {transactions_count} معاملة مالية")
    
    conn.close()
    return True

if __name__ == "__main__":
    print("🔧 بدء إصلاح مشاكل قاعدة البيانات...")
    print("=" * 60)
    
    success = True
    success &= create_admin_users()
    success &= create_sample_clients()
    success &= create_sample_cases()
    success &= create_financial_transactions()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 تم إصلاح جميع مشاكل قاعدة البيانات بنجاح!")
        print("📊 النظام جاهز للاستخدام مع البيانات التجريبية")
    else:
        print("⚠️ حدثت مشاكل أثناء الإصلاح")
    print("=" * 60)
