{% extends "base.html" %}

{% block title %}إدارة المستندات{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-primary mb-1">
                        <i class="fas fa-folder-open me-2"></i>
                        إدارة المستندات والملفات
                    </h2>
                    <p class="text-muted">إدارة شاملة لجميع مستندات العقارات والمستأجرين والعقود</p>
                </div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                    <i class="fas fa-upload me-2"></i>
                    رفع مستند جديد
                </button>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="propertyDocsCount">0</h4>
                                    <p class="mb-0">مستندات العقارات</p>
                                </div>
                                <i class="fas fa-building fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="tenantDocsCount">0</h4>
                                    <p class="mb-0">مستندات المستأجرين</p>
                                </div>
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="leaseDocsCount">0</h4>
                                    <p class="mb-0">مستندات العقود</p>
                                </div>
                                <i class="fas fa-file-contract fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="totalSize">0 MB</h4>
                                    <p class="mb-0">إجمالي الحجم</p>
                                </div>
                                <i class="fas fa-hdd fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">نوع المستند</label>
                            <select class="form-select" id="documentTypeFilter">
                                <option value="">جميع الأنواع</option>
                                <option value="property">مستندات العقارات</option>
                                <option value="tenant">مستندات المستأجرين</option>
                                <option value="lease">مستندات العقود</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">فئة المستند</label>
                            <select class="form-select" id="documentCategoryFilter">
                                <option value="">جميع الفئات</option>
                                <option value="هوية">هوية</option>
                                <option value="عقد">عقد</option>
                                <option value="صورة">صورة</option>
                                <option value="تقرير">تقرير</option>
                                <option value="عام">عام</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="البحث في أسماء المستندات...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-outline-primary w-100" onclick="applyFilters()">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Documents Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة المستندات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="documentsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>اسم المستند</th>
                                    <th>النوع</th>
                                    <th>الفئة</th>
                                    <th>الحجم</th>
                                    <th>تاريخ الرفع</th>
                                    <th>المرتبط بـ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="documentsTableBody">
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Empty State -->
                    <div id="emptyState" class="text-center py-5" style="display: none;">
                        <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مستندات</h5>
                        <p class="text-muted">لم يتم العثور على أي مستندات مطابقة للفلاتر المحددة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفع مستند جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">نوع المستند <span class="text-danger">*</span></label>
                            <select class="form-select" id="uploadDocumentType" required>
                                <option value="">اختر النوع</option>
                                <option value="property">مستند عقار</option>
                                <option value="tenant">مستند مستأجر</option>
                                <option value="lease">مستند عقد</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">العنصر المرتبط <span class="text-danger">*</span></label>
                            <select class="form-select" id="relatedItem" required>
                                <option value="">اختر العنصر</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">فئة المستند</label>
                            <select class="form-select" id="documentCategory">
                                <option value="عام">عام</option>
                                <option value="هوية">هوية</option>
                                <option value="عقد">عقد</option>
                                <option value="صورة">صورة</option>
                                <option value="تقرير">تقرير</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الملف <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="documentFile" required 
                                   accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif">
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="documentDescription" rows="3" 
                                  placeholder="وصف اختياري للمستند..."></textarea>
                    </div>
                    
                    <!-- File Preview -->
                    <div id="filePreview" class="mt-3" style="display: none;">
                        <div class="border rounded p-3 bg-light">
                            <h6>معاينة الملف:</h6>
                            <div id="previewContent"></div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="uploadDocument()">
                    <i class="fas fa-upload me-2"></i>
                    رفع المستند
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Document Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewTitle">معاينة المستند</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="documentPreviewContent" class="text-center">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="downloadBtn">
                    <i class="fas fa-download me-2"></i>
                    تحميل
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-action {
    padding: 0.25rem 0.5rem;
    margin: 0 0.125rem;
    border-radius: 0.25rem;
}

.file-size {
    font-size: 0.875rem;
    color: #6c757d;
}

.document-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

#filePreview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 0.25rem;
}

.opacity-75 {
    opacity: 0.75;
}
</style>
{% endblock %}

{% block scripts %}
<script>
let allDocuments = [];
let currentDocuments = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadDocuments();
    loadRelatedItems();
    
    // إعداد معاينة الملف
    document.getElementById('documentFile').addEventListener('change', previewFile);
    
    // إعداد تغيير نوع المستند
    document.getElementById('uploadDocumentType').addEventListener('change', loadRelatedItems);
});

// تحميل جميع المستندات
function loadDocuments() {
    // هنا يمكن إضافة استدعاء API لجلب جميع المستندات
    // مؤقتاً سنستخدم بيانات تجريبية
    updateStatistics();
    displayDocuments(allDocuments);
}

// تحميل العناصر المرتبطة حسب النوع
function loadRelatedItems() {
    const documentType = document.getElementById('uploadDocumentType').value;
    const relatedItemSelect = document.getElementById('relatedItem');
    
    relatedItemSelect.innerHTML = '<option value="">اختر العنصر</option>';
    
    if (documentType === 'property') {
        // جلب العقارات
        fetch('/api/properties/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.properties.forEach(property => {
                    const option = document.createElement('option');
                    option.value = property.id;
                    option.textContent = `${property.name} (${property.property_code || 'بدون رمز'})`;
                    relatedItemSelect.appendChild(option);
                });
            }
        });
    } else if (documentType === 'tenant') {
        // جلب المستأجرين
        fetch('/api/tenants/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.tenants.forEach(tenant => {
                    const option = document.createElement('option');
                    option.value = tenant.id;
                    option.textContent = `${tenant.full_name} (${tenant.phone_number || 'بدون هاتف'})`;
                    relatedItemSelect.appendChild(option);
                });
            }
        });
    } else if (documentType === 'lease') {
        // جلب العقود
        fetch('/api/leases/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.leases.forEach(lease => {
                    const option = document.createElement('option');
                    option.value = lease.id;
                    option.textContent = `${lease.lease_number} - ${lease.property.name}`;
                    relatedItemSelect.appendChild(option);
                });
            }
        });
    }
}

// معاينة الملف المحدد
function previewFile() {
    const file = document.getElementById('documentFile').files[0];
    const previewDiv = document.getElementById('filePreview');
    const previewContent = document.getElementById('previewContent');
    
    if (file) {
        previewDiv.style.display = 'block';
        
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewContent.innerHTML = `
                    <img src="${e.target.result}" alt="معاينة الصورة" class="img-fluid">
                    <p class="mt-2 mb-0"><strong>اسم الملف:</strong> ${file.name}</p>
                    <p class="mb-0"><strong>الحجم:</strong> ${formatFileSize(file.size)}</p>
                `;
            };
            reader.readAsDataURL(file);
        } else {
            previewContent.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-file fa-3x text-muted mb-2"></i>
                    <p class="mb-1"><strong>اسم الملف:</strong> ${file.name}</p>
                    <p class="mb-0"><strong>الحجم:</strong> ${formatFileSize(file.size)}</p>
                </div>
            `;
        }
    } else {
        previewDiv.style.display = 'none';
    }
}

// رفع المستند
function uploadDocument() {
    const form = document.getElementById('uploadForm');
    const formData = new FormData();
    
    const documentType = document.getElementById('uploadDocumentType').value;
    const relatedItemId = document.getElementById('relatedItem').value;
    const documentCategory = document.getElementById('documentCategory').value;
    const file = document.getElementById('documentFile').files[0];
    const description = document.getElementById('documentDescription').value;
    
    if (!documentType || !relatedItemId || !file) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    formData.append('file', file);
    formData.append('document_type', documentCategory);
    formData.append('description', description);
    
    // تحديد URL الرفع حسب النوع
    let uploadUrl;
    if (documentType === 'property') {
        uploadUrl = `/api/upload/property-document/${relatedItemId}`;
    } else if (documentType === 'tenant') {
        uploadUrl = `/api/upload/tenant-document/${relatedItemId}`;
    } else if (documentType === 'lease') {
        uploadUrl = `/api/upload/lease-document/${relatedItemId}`;
    }
    
    fetch(uploadUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم رفع المستند بنجاح');
            bootstrap.Modal.getInstance(document.getElementById('uploadModal')).hide();
            form.reset();
            document.getElementById('filePreview').style.display = 'none';
            loadDocuments();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء رفع المستند');
    });
}

// تطبيق الفلاتر
function applyFilters() {
    const typeFilter = document.getElementById('documentTypeFilter').value;
    const categoryFilter = document.getElementById('documentCategoryFilter').value;
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    
    let filteredDocuments = allDocuments;
    
    if (typeFilter) {
        filteredDocuments = filteredDocuments.filter(doc => doc.type === typeFilter);
    }
    
    if (categoryFilter) {
        filteredDocuments = filteredDocuments.filter(doc => doc.category === categoryFilter);
    }
    
    if (searchTerm) {
        filteredDocuments = filteredDocuments.filter(doc => 
            doc.name.toLowerCase().includes(searchTerm) ||
            doc.description.toLowerCase().includes(searchTerm)
        );
    }
    
    displayDocuments(filteredDocuments);
}

// عرض المستندات في الجدول
function displayDocuments(documents) {
    const tbody = document.getElementById('documentsTableBody');
    const emptyState = document.getElementById('emptyState');
    
    if (documents.length === 0) {
        tbody.innerHTML = '';
        emptyState.style.display = 'block';
        return;
    }
    
    emptyState.style.display = 'none';
    
    tbody.innerHTML = documents.map(doc => `
        <tr>
            <td>
                <div class="d-flex align-items-center">
                    <i class="fas fa-file me-2 text-muted"></i>
                    <span>${doc.name}</span>
                </div>
            </td>
            <td>
                <span class="badge document-type-badge ${getTypeBadgeClass(doc.type)}">
                    ${getTypeLabel(doc.type)}
                </span>
            </td>
            <td>${doc.category}</td>
            <td class="file-size">${formatFileSize(doc.size)}</td>
            <td>${formatDate(doc.uploaded_date)}</td>
            <td>${doc.related_name}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary btn-action" onclick="previewDocument(${doc.id}, '${doc.type}')" title="معاينة">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-success btn-action" onclick="downloadDocument(${doc.id}, '${doc.type}')" title="تحميل">
                    <i class="fas fa-download"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteDocument(${doc.id}, '${doc.type}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// تحديث الإحصائيات
function updateStatistics() {
    // هنا يمكن إضافة استدعاء API لجلب الإحصائيات
    document.getElementById('propertyDocsCount').textContent = '0';
    document.getElementById('tenantDocsCount').textContent = '0';
    document.getElementById('leaseDocsCount').textContent = '0';
    document.getElementById('totalSize').textContent = '0 MB';
}

// دوال مساعدة
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function getTypeBadgeClass(type) {
    switch(type) {
        case 'property': return 'bg-primary';
        case 'tenant': return 'bg-success';
        case 'lease': return 'bg-info';
        default: return 'bg-secondary';
    }
}

function getTypeLabel(type) {
    switch(type) {
        case 'property': return 'عقار';
        case 'tenant': return 'مستأجر';
        case 'lease': return 'عقد';
        default: return 'غير محدد';
    }
}

// معاينة المستند
function previewDocument(documentId, documentType) {
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    const previewContent = document.getElementById('documentPreviewContent');
    
    previewContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">جاري التحميل...</p></div>';
    modal.show();
    
    // هنا يمكن إضافة استدعاء API لمعاينة المستند
}

// تحميل المستند
function downloadDocument(documentId, documentType) {
    window.open(`/api/documents/download/${documentId}/${documentType}`, '_blank');
}

// حذف المستند
function deleteDocument(documentId, documentType) {
    if (confirm('هل أنت متأكد من حذف هذا المستند؟')) {
        fetch(`/api/documents/delete/${documentId}/${documentType}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حذف المستند بنجاح');
                loadDocuments();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف المستند');
        });
    }
}
</script>
{% endblock %}
