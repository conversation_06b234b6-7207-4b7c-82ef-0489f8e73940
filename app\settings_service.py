# خدمة إدارة الإعدادات المتقدمة
from flask import current_app
from app import db
from .archive_models import SystemSettings
from datetime import datetime
import json


class SettingsService:
    """خدمة إدارة الإعدادات المتقدمة"""
    
    # الإعدادات الافتراضية
    DEFAULT_SETTINGS = {
        # الإعدادات العامة
        'office_name': {
            'value': 'مكتب المحامي سامح',
            'type': 'string',
            'category': 'general',
            'description': 'اسم المكتب القانوني'
        },
        'office_address': {
            'value': '',
            'type': 'string',
            'category': 'general',
            'description': 'عنوان المكتب'
        },
        'office_phone': {
            'value': '',
            'type': 'string',
            'category': 'general',
            'description': 'رقم هاتف المكتب'
        },
        'office_email': {
            'value': '',
            'type': 'string',
            'category': 'general',
            'description': 'البريد الإلكتروني للمكتب'
        },
        'office_logo': {
            'value': '',
            'type': 'string',
            'category': 'general',
            'description': 'شعار المكتب'
        },
        'default_currency': {
            'value': 'ريال سعودي',
            'type': 'string',
            'category': 'general',
            'description': 'العملة الافتراضية'
        },
        'timezone': {
            'value': 'Asia/Riyadh',
            'type': 'string',
            'category': 'general',
            'description': 'المنطقة الزمنية'
        },
        'language': {
            'value': 'ar',
            'type': 'string',
            'category': 'general',
            'description': 'لغة النظام'
        },
        
        # إعدادات الأرشفة
        'auto_archive_enabled': {
            'value': 'true',
            'type': 'boolean',
            'category': 'archive',
            'description': 'تفعيل الأرشفة التلقائية'
        },
        'auto_archive_days': {
            'value': '365',
            'type': 'integer',
            'category': 'archive',
            'description': 'عدد الأيام قبل الأرشفة التلقائية'
        },
        'archive_closed_cases': {
            'value': 'true',
            'type': 'boolean',
            'category': 'archive',
            'description': 'أرشفة القضايا المغلقة تلقائياً'
        },
        'archive_inactive_clients': {
            'value': 'false',
            'type': 'boolean',
            'category': 'archive',
            'description': 'أرشفة العملاء غير النشطين'
        },
        'archive_old_documents': {
            'value': 'true',
            'type': 'boolean',
            'category': 'archive',
            'description': 'أرشفة المستندات القديمة'
        },
        
        # إعدادات النسخ الاحتياطي
        'auto_backup_enabled': {
            'value': 'true',
            'type': 'boolean',
            'category': 'backup',
            'description': 'تفعيل النسخ الاحتياطي التلقائي'
        },
        'backup_frequency': {
            'value': 'weekly',
            'type': 'string',
            'category': 'backup',
            'description': 'تكرار النسخ الاحتياطي'
        },
        'backup_time': {
            'value': '02:00',
            'type': 'string',
            'category': 'backup',
            'description': 'وقت النسخ الاحتياطي'
        },
        'backup_retention_days': {
            'value': '30',
            'type': 'integer',
            'category': 'backup',
            'description': 'مدة الاحتفاظ بالنسخ الاحتياطية (أيام)'
        },
        'backup_compression': {
            'value': 'true',
            'type': 'boolean',
            'category': 'backup',
            'description': 'ضغط النسخ الاحتياطية'
        },
        'backup_encryption': {
            'value': 'false',
            'type': 'boolean',
            'category': 'backup',
            'description': 'تشفير النسخ الاحتياطية'
        },
        'backup_location': {
            'value': 'local',
            'type': 'string',
            'category': 'backup',
            'description': 'موقع حفظ النسخ الاحتياطية'
        },
        
        # إعدادات الأمان
        'session_timeout': {
            'value': '30',
            'type': 'integer',
            'category': 'security',
            'description': 'مهلة انتهاء الجلسة (دقائق)'
        },
        'password_min_length': {
            'value': '8',
            'type': 'integer',
            'category': 'security',
            'description': 'الحد الأدنى لطول كلمة المرور'
        },
        'login_attempts_limit': {
            'value': '5',
            'type': 'integer',
            'category': 'security',
            'description': 'عدد محاولات تسجيل الدخول المسموحة'
        },
        'enable_two_factor': {
            'value': 'false',
            'type': 'boolean',
            'category': 'security',
            'description': 'تفعيل المصادقة الثنائية'
        },
        'audit_log_enabled': {
            'value': 'true',
            'type': 'boolean',
            'category': 'security',
            'description': 'تفعيل سجل المراجعة'
        },
        
        # إعدادات واجهة المستخدم
        'theme': {
            'value': 'light',
            'type': 'string',
            'category': 'ui',
            'description': 'سمة الواجهة'
        },
        'sidebar_collapsed': {
            'value': 'false',
            'type': 'boolean',
            'category': 'ui',
            'description': 'طي الشريط الجانبي افتراضياً'
        },
        'items_per_page': {
            'value': '25',
            'type': 'integer',
            'category': 'ui',
            'description': 'عدد العناصر في الصفحة'
        },
        'show_tooltips': {
            'value': 'true',
            'type': 'boolean',
            'category': 'ui',
            'description': 'إظهار التلميحات'
        },
        'enable_animations': {
            'value': 'true',
            'type': 'boolean',
            'category': 'ui',
            'description': 'تفعيل الحركات والتأثيرات'
        },
        
        # إعدادات التنبيهات
        'email_notifications': {
            'value': 'true',
            'type': 'boolean',
            'category': 'notifications',
            'description': 'تفعيل التنبيهات عبر البريد الإلكتروني'
        },
        'sms_notifications': {
            'value': 'false',
            'type': 'boolean',
            'category': 'notifications',
            'description': 'تفعيل التنبيهات عبر الرسائل النصية'
        },
        'desktop_notifications': {
            'value': 'true',
            'type': 'boolean',
            'category': 'notifications',
            'description': 'تفعيل تنبيهات سطح المكتب'
        },
        'notification_sound': {
            'value': 'true',
            'type': 'boolean',
            'category': 'notifications',
            'description': 'تفعيل أصوات التنبيهات'
        },
        
        # إعدادات التقارير
        'default_report_format': {
            'value': 'pdf',
            'type': 'string',
            'category': 'reports',
            'description': 'تنسيق التقارير الافتراضي'
        },
        'report_logo_enabled': {
            'value': 'true',
            'type': 'boolean',
            'category': 'reports',
            'description': 'إضافة الشعار للتقارير'
        },
        'report_watermark': {
            'value': '',
            'type': 'string',
            'category': 'reports',
            'description': 'العلامة المائية للتقارير'
        }
    }
    
    @classmethod
    def initialize_default_settings(cls, user_id=None):
        """تهيئة الإعدادات الافتراضية"""
        for key, config in cls.DEFAULT_SETTINGS.items():
            existing = SystemSettings.query.filter_by(setting_key=key).first()
            if not existing:
                setting = SystemSettings(
                    setting_key=key,
                    setting_value=config['value'],
                    setting_type=config['type'],
                    category=config['category'],
                    description=config['description'],
                    is_system=True,
                    updated_by=user_id
                )
                db.session.add(setting)
        
        db.session.commit()
    
    @classmethod
    def get_setting(cls, key, default=None):
        """الحصول على قيمة إعداد"""
        setting = SystemSettings.query.filter_by(setting_key=key).first()
        if setting:
            return setting.get_value()
        
        # البحث في الإعدادات الافتراضية
        if key in cls.DEFAULT_SETTINGS:
            config = cls.DEFAULT_SETTINGS[key]
            if config['type'] == 'boolean':
                return config['value'].lower() in ['true', '1', 'yes']
            elif config['type'] == 'integer':
                return int(config['value'])
            else:
                return config['value']
        
        return default
    
    @classmethod
    def set_setting(cls, key, value, user_id=None):
        """تعيين قيمة إعداد"""
        setting = SystemSettings.query.filter_by(setting_key=key).first()
        
        if setting:
            setting.set_value(value)
            setting.updated_by = user_id
            setting.updated_date = datetime.now()
        else:
            # إنشاء إعداد جديد
            setting_type = 'string'
            category = 'general'
            description = ''
            
            # البحث في الإعدادات الافتراضية
            if key in cls.DEFAULT_SETTINGS:
                config = cls.DEFAULT_SETTINGS[key]
                setting_type = config['type']
                category = config['category']
                description = config['description']
            
            setting = SystemSettings(
                setting_key=key,
                setting_type=setting_type,
                category=category,
                description=description,
                updated_by=user_id
            )
            setting.set_value(value)
            db.session.add(setting)
        
        db.session.commit()
        return setting
    
    @classmethod
    def get_settings_by_category(cls, category):
        """الحصول على الإعدادات حسب الفئة"""
        settings = SystemSettings.query.filter_by(category=category).all()
        result = {}
        
        for setting in settings:
            result[setting.setting_key] = {
                'value': setting.get_value(),
                'type': setting.setting_type,
                'description': setting.description,
                'updated_date': setting.updated_date.isoformat() if setting.updated_date else None
            }
        
        # إضافة الإعدادات الافتراضية المفقودة
        for key, config in cls.DEFAULT_SETTINGS.items():
            if config['category'] == category and key not in result:
                value = config['value']
                if config['type'] == 'boolean':
                    value = value.lower() in ['true', '1', 'yes']
                elif config['type'] == 'integer':
                    value = int(value)
                
                result[key] = {
                    'value': value,
                    'type': config['type'],
                    'description': config['description'],
                    'updated_date': None
                }
        
        return result
    
    @classmethod
    def update_settings_batch(cls, settings_dict, user_id=None):
        """تحديث مجموعة من الإعدادات"""
        updated_settings = []
        
        for key, value in settings_dict.items():
            try:
                setting = cls.set_setting(key, value, user_id)
                updated_settings.append(setting)
            except Exception as e:
                print(f"خطأ في تحديث الإعداد {key}: {e}")
        
        return updated_settings
    
    @classmethod
    def export_settings(cls, category=None):
        """تصدير الإعدادات"""
        query = SystemSettings.query
        if category:
            query = query.filter_by(category=category)
        
        settings = query.all()
        export_data = {}
        
        for setting in settings:
            export_data[setting.setting_key] = {
                'value': setting.setting_value,
                'type': setting.setting_type,
                'category': setting.category,
                'description': setting.description,
                'updated_date': setting.updated_date.isoformat() if setting.updated_date else None
            }
        
        return export_data
    
    @classmethod
    def import_settings(cls, settings_data, user_id=None):
        """استيراد الإعدادات"""
        imported_count = 0
        
        for key, data in settings_data.items():
            try:
                cls.set_setting(key, data['value'], user_id)
                imported_count += 1
            except Exception as e:
                print(f"خطأ في استيراد الإعداد {key}: {e}")
        
        return imported_count
    
    @classmethod
    def reset_settings(cls, category=None, user_id=None):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        if category:
            # إعادة تعيين فئة معينة
            settings_to_reset = {k: v for k, v in cls.DEFAULT_SETTINGS.items() if v['category'] == category}
        else:
            # إعادة تعيين جميع الإعدادات
            settings_to_reset = cls.DEFAULT_SETTINGS
        
        reset_count = 0
        for key, config in settings_to_reset.items():
            try:
                cls.set_setting(key, config['value'], user_id)
                reset_count += 1
            except Exception as e:
                print(f"خطأ في إعادة تعيين الإعداد {key}: {e}")
        
        return reset_count
    
    @classmethod
    def get_all_categories(cls):
        """الحصول على جميع فئات الإعدادات"""
        categories = set()
        
        # من قاعدة البيانات
        db_categories = db.session.query(SystemSettings.category).distinct().all()
        categories.update([cat[0] for cat in db_categories])
        
        # من الإعدادات الافتراضية
        default_categories = set([config['category'] for config in cls.DEFAULT_SETTINGS.values()])
        categories.update(default_categories)
        
        return sorted(list(categories))
    
    @classmethod
    def validate_setting(cls, key, value):
        """التحقق من صحة قيمة الإعداد"""
        if key in cls.DEFAULT_SETTINGS:
            config = cls.DEFAULT_SETTINGS[key]
            setting_type = config['type']
            
            if setting_type == 'boolean':
                return isinstance(value, bool) or str(value).lower() in ['true', 'false', '1', '0', 'yes', 'no']
            elif setting_type == 'integer':
                try:
                    int(value)
                    return True
                except (ValueError, TypeError):
                    return False
            elif setting_type == 'string':
                return isinstance(value, str) or value is None
        
        return True  # السماح بالقيم المخصصة


# إنشاء مثيل الخدمة
settings_service = SettingsService()
