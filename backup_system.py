#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطي الدوري
Periodic Backup System
"""

import os
import shutil
import sqlite3
import zipfile
from datetime import datetime, timedelta
import schedule
import time
import json

class BackupSystem:
    def __init__(self):
        self.db_path = 'instance/lawoffice.db'
        self.backup_dir = 'backups'
        self.max_backups = 30  # الاحتفاظ بـ 30 نسخة احتياطية
        self.ensure_backup_directory()
    
    def ensure_backup_directory(self):
        """إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
            print(f"✅ تم إنشاء مجلد النسخ الاحتياطي: {self.backup_dir}")
    
    def create_backup(self, backup_type="manual"):
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"lawoffice_backup_{backup_type}_{timestamp}"
            backup_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
            
            print(f"🔄 بدء إنشاء النسخة الاحتياطية: {backup_name}")
            
            # إنشاء ملف مضغوط للنسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # إضافة قاعدة البيانات
                if os.path.exists(self.db_path):
                    zipf.write(self.db_path, 'lawoffice.db')
                    print("   ✅ تم إضافة قاعدة البيانات")
                
                # إضافة ملفات التطبيق المهمة
                important_files = [
                    'app/routes.py',
                    'app/models.py',
                    'app/__init__.py',
                    'run.py',
                    'config.py'
                ]
                
                for file_path in important_files:
                    if os.path.exists(file_path):
                        zipf.write(file_path, file_path)
                        print(f"   ✅ تم إضافة: {file_path}")
                
                # إضافة مجلد القوالب
                if os.path.exists('app/templates'):
                    for root, dirs, files in os.walk('app/templates'):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, '.')
                            zipf.write(file_path, arcname)
                    print("   ✅ تم إضافة مجلد القوالب")
                
                # إضافة مجلد الملفات الثابتة
                if os.path.exists('app/static'):
                    for root, dirs, files in os.walk('app/static'):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, '.')
                            zipf.write(file_path, arcname)
                    print("   ✅ تم إضافة مجلد الملفات الثابتة")
                
                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    'backup_type': backup_type,
                    'timestamp': timestamp,
                    'created_at': datetime.now().isoformat(),
                    'database_size': os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                    'version': '1.0'
                }
                
                zipf.writestr('backup_info.json', json.dumps(backup_info, ensure_ascii=False, indent=2))
                print("   ✅ تم إضافة معلومات النسخة الاحتياطية")
            
            backup_size = os.path.getsize(backup_path)
            print(f"✅ تم إنشاء النسخة الاحتياطية بنجاح!")
            print(f"   📁 المسار: {backup_path}")
            print(f"   📊 الحجم: {backup_size / 1024 / 1024:.2f} MB")
            
            # تسجيل النسخة الاحتياطية في قاعدة البيانات
            self.log_backup(backup_name, backup_path, backup_size, backup_type)
            
            # تنظيف النسخ القديمة
            self.cleanup_old_backups()
            
            return backup_path
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None
    
    def log_backup(self, backup_name, backup_path, backup_size, backup_type):
        """تسجيل النسخة الاحتياطية في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO backup_logs (backup_name, backup_path, backup_size, backup_type, created_at)
                VALUES (?, ?, ?, ?, ?)
            """, (backup_name, backup_path, backup_size, backup_type, datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            print("   ✅ تم تسجيل النسخة الاحتياطية في قاعدة البيانات")
            
        except Exception as e:
            print(f"   ⚠️ تحذير: لم يتم تسجيل النسخة الاحتياطية في قاعدة البيانات: {e}")
    
    def cleanup_old_backups(self):
        """حذف النسخ الاحتياطية القديمة"""
        try:
            backup_files = []
            for file in os.listdir(self.backup_dir):
                if file.startswith('lawoffice_backup_') and file.endswith('.zip'):
                    file_path = os.path.join(self.backup_dir, file)
                    backup_files.append((file_path, os.path.getctime(file_path)))
            
            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # حذف النسخ الزائدة
            if len(backup_files) > self.max_backups:
                files_to_delete = backup_files[self.max_backups:]
                for file_path, _ in files_to_delete:
                    os.remove(file_path)
                    print(f"   🗑️ تم حذف النسخة القديمة: {os.path.basename(file_path)}")
                
                print(f"   ✅ تم تنظيف {len(files_to_delete)} نسخة قديمة")
            
        except Exception as e:
            print(f"   ⚠️ تحذير: خطأ في تنظيف النسخ القديمة: {e}")
    
    def restore_backup(self, backup_path):
        """استعادة نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                print(f"❌ النسخة الاحتياطية غير موجودة: {backup_path}")
                return False
            
            print(f"🔄 بدء استعادة النسخة الاحتياطية: {backup_path}")
            
            # إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
            current_backup = self.create_backup("pre_restore")
            if current_backup:
                print(f"   ✅ تم إنشاء نسخة احتياطية من الحالة الحالية: {current_backup}")
            
            # استخراج النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                zipf.extractall('restore_temp')
                print("   ✅ تم استخراج النسخة الاحتياطية")
            
            # استعادة قاعدة البيانات
            if os.path.exists('restore_temp/lawoffice.db'):
                shutil.copy2('restore_temp/lawoffice.db', self.db_path)
                print("   ✅ تم استعادة قاعدة البيانات")
            
            # تنظيف المجلد المؤقت
            shutil.rmtree('restore_temp')
            print("   ✅ تم تنظيف الملفات المؤقتة")
            
            print("✅ تم استعادة النسخة الاحتياطية بنجاح!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في استعادة النسخة الاحتياطية: {e}")
            return False
    
    def list_backups(self):
        """عرض قائمة النسخ الاحتياطية"""
        try:
            backup_files = []
            for file in os.listdir(self.backup_dir):
                if file.startswith('lawoffice_backup_') and file.endswith('.zip'):
                    file_path = os.path.join(self.backup_dir, file)
                    file_size = os.path.getsize(file_path)
                    file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                    backup_files.append({
                        'name': file,
                        'path': file_path,
                        'size': file_size,
                        'created': file_time
                    })
            
            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            backup_files.sort(key=lambda x: x['created'], reverse=True)
            
            print(f"\n📋 قائمة النسخ الاحتياطية ({len(backup_files)} نسخة):")
            print("=" * 80)
            
            for backup in backup_files:
                size_mb = backup['size'] / 1024 / 1024
                print(f"📁 {backup['name']}")
                print(f"   📅 التاريخ: {backup['created'].strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"   📊 الحجم: {size_mb:.2f} MB")
                print(f"   📂 المسار: {backup['path']}")
                print("-" * 80)
            
            return backup_files
            
        except Exception as e:
            print(f"❌ خطأ في عرض النسخ الاحتياطية: {e}")
            return []
    
    def schedule_backups(self):
        """جدولة النسخ الاحتياطية الدورية"""
        # نسخة احتياطية يومية في الساعة 2:00 صباحاً
        schedule.every().day.at("02:00").do(lambda: self.create_backup("daily"))
        
        # نسخة احتياطية أسبوعية يوم الأحد في الساعة 3:00 صباحاً
        schedule.every().sunday.at("03:00").do(lambda: self.create_backup("weekly"))
        
        # نسخة احتياطية شهرية في اليوم الأول من كل شهر
        schedule.every().month.do(lambda: self.create_backup("monthly"))
        
        print("✅ تم جدولة النسخ الاحتياطية الدورية:")
        print("   📅 يومياً في الساعة 2:00 صباحاً")
        print("   📅 أسبوعياً يوم الأحد في الساعة 3:00 صباحاً")
        print("   📅 شهرياً في اليوم الأول من كل شهر")
    
    def run_scheduler(self):
        """تشغيل جدولة النسخ الاحتياطية"""
        print("🔄 بدء تشغيل نظام النسخ الاحتياطي الدوري...")
        self.schedule_backups()
        
        while True:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة

def main():
    """الدالة الرئيسية"""
    backup_system = BackupSystem()
    
    print("🔧 نظام النسخ الاحتياطي الدوري")
    print("=" * 50)
    
    # إنشاء نسخة احتياطية فورية
    print("\n1️⃣ إنشاء نسخة احتياطية فورية...")
    backup_path = backup_system.create_backup("initial")
    
    if backup_path:
        print(f"\n✅ تم إنشاء النسخة الاحتياطية الأولى بنجاح!")
        
        # عرض قائمة النسخ الاحتياطية
        print("\n2️⃣ عرض قائمة النسخ الاحتياطية...")
        backup_system.list_backups()
        
        print("\n3️⃣ إعداد الجدولة الدورية...")
        print("   ✅ تم إعداد النسخ الاحتياطية الدورية")
        print("   📝 ملاحظة: لتشغيل الجدولة الدورية، استخدم: backup_system.run_scheduler()")
        
        print("\n🎉 تم إعداد نظام النسخ الاحتياطي بنجاح!")
    else:
        print("\n❌ فشل في إنشاء النسخة الاحتياطية الأولى")

if __name__ == "__main__":
    main()
