<!-- نموذج تعديل الأتعاب -->
<form id="editFeeForm" method="POST" action="{{ url_for('modal_edit_fee', fee_id=fee.id) }}">
    <div class="modal-body" dir="rtl">
        <div class="row">
            <!-- معلومات أساسية -->
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fa fa-info-circle"></i> المعلومات الأساسية</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">رقم الأتعاب</label>
                            <input type="text" class="form-control bg-light" value="{{ fee.fee_number }}" readonly>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">العميل <span class="text-danger">*</span></label>
                            <select name="client_id" class="form-select" required>
                                <option value="">اختر العميل</option>
                                {% for client in clients %}
                                <option value="{{ client.id }}" {{ 'selected' if client.id == fee.client_id }}>
                                    {{ client.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">القضية (اختياري)</label>
                            <select name="case_id" class="form-select">
                                <option value="">اختر القضية</option>
                                {% for case in cases %}
                                <option value="{{ case.id }}" {{ 'selected' if case.id == fee.case_id }}>
                                    {{ case.case_number }} - {{ case.title }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع الأتعاب <span class="text-danger">*</span></label>
                                    <select name="fee_type" class="form-select" required>
                                        <option value="">اختر النوع</option>
                                        <option value="استشارة قانونية" {{ 'selected' if fee.fee_type == 'استشارة قانونية' }}>استشارة قانونية</option>
                                        <option value="ترافع أمام المحاكم" {{ 'selected' if fee.fee_type == 'ترافع أمام المحاكم' }}>ترافع أمام المحاكم</option>
                                        <option value="صياغة عقود" {{ 'selected' if fee.fee_type == 'صياغة عقود' }}>صياغة عقود</option>
                                        <option value="تحصيل ديون" {{ 'selected' if fee.fee_type == 'تحصيل ديون' }}>تحصيل ديون</option>
                                        <option value="إجراءات إدارية" {{ 'selected' if fee.fee_type == 'إجراءات إدارية' }}>إجراءات إدارية</option>
                                        <option value="تمثيل قانوني" {{ 'selected' if fee.fee_type == 'تمثيل قانوني' }}>تمثيل قانوني</option>
                                        <option value="مراجعة قانونية" {{ 'selected' if fee.fee_type == 'مراجعة قانونية' }}>مراجعة قانونية</option>
                                        <option value="أخرى" {{ 'selected' if fee.fee_type == 'أخرى' }}>أخرى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">فئة الخدمة <span class="text-danger">*</span></label>
                                    <select name="service_category" class="form-select" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="قانون مدني" {{ 'selected' if fee.service_category == 'قانون مدني' }}>قانون مدني</option>
                                        <option value="قانون جنائي" {{ 'selected' if fee.service_category == 'قانون جنائي' }}>قانون جنائي</option>
                                        <option value="قانون تجاري" {{ 'selected' if fee.service_category == 'قانون تجاري' }}>قانون تجاري</option>
                                        <option value="قانون عمل" {{ 'selected' if fee.service_category == 'قانون عمل' }}>قانون عمل</option>
                                        <option value="قانون أسرة" {{ 'selected' if fee.service_category == 'قانون أسرة' }}>قانون أسرة</option>
                                        <option value="قانون عقاري" {{ 'selected' if fee.service_category == 'قانون عقاري' }}>قانون عقاري</option>
                                        <option value="قانون إداري" {{ 'selected' if fee.service_category == 'قانون إداري' }}>قانون إداري</option>
                                        <option value="أخرى" {{ 'selected' if fee.service_category == 'أخرى' }}>أخرى</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وصف الخدمة <span class="text-danger">*</span></label>
                            <textarea name="description" class="form-control" rows="3" required 
                                      placeholder="وصف تفصيلي للخدمة المقدمة...">{{ fee.description }}</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المعلومات المالية -->
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fa fa-money-bill-wave"></i> المعلومات المالية</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-triangle"></i>
                            <strong>تنبيه:</strong> تم دفع {{ fee.paid_amount | number_format }} {{ fee.currency }} من إجمالي الأتعاب
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المبلغ الأساسي <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" name="base_amount" class="form-control" 
                                               step="0.01" min="0" required id="baseAmount" 
                                               value="{{ fee.base_amount }}">
                                        <span class="input-group-text">₪</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رسوم إضافية</label>
                                    <div class="input-group">
                                        <input type="number" name="additional_fees" class="form-control" 
                                               step="0.01" min="0" id="additionalFees" 
                                               value="{{ fee.additional_fees }}">
                                        <span class="input-group-text">₪</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">خصم</label>
                                    <div class="input-group">
                                        <input type="number" name="discount_amount" class="form-control" 
                                               step="0.01" min="0" id="discountAmount" 
                                               value="{{ fee.discount_amount }}">
                                        <span class="input-group-text">₪</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ضريبة</label>
                                    <div class="input-group">
                                        <input type="number" name="tax_amount" class="form-control" 
                                               step="0.01" min="0" id="taxAmount" 
                                               value="{{ fee.tax_amount }}">
                                        <span class="input-group-text">₪</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">المجموع الإجمالي</label>
                            <div class="input-group">
                                <input type="number" name="total_amount" class="form-control bg-light" 
                                       readonly id="totalAmount" value="{{ fee.total_amount }}">
                                <span class="input-group-text">₪</span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الأولوية</label>
                                    <select name="priority" class="form-select">
                                        <option value="منخفضة" {{ 'selected' if fee.priority == 'منخفضة' }}>منخفضة</option>
                                        <option value="متوسطة" {{ 'selected' if fee.priority == 'متوسطة' }}>متوسطة</option>
                                        <option value="عالية" {{ 'selected' if fee.priority == 'عالية' }}>عالية</option>
                                        <option value="عاجلة" {{ 'selected' if fee.priority == 'عاجلة' }}>عاجلة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">العملة</label>
                                    <select name="currency" class="form-select">
                                        <option value="شيكل" {{ 'selected' if fee.currency == 'شيكل' }}>شيكل إسرائيلي</option>
                                        <option value="دولار" {{ 'selected' if fee.currency == 'دولار' }}>دولار أمريكي</option>
                                        <option value="يورو" {{ 'selected' if fee.currency == 'يورو' }}>يورو</option>
                                        <option value="دينار" {{ 'selected' if fee.currency == 'دينار' }}>دينار أردني</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- التواريخ وشروط الدفع -->
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fa fa-calendar"></i> التواريخ</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الخدمة <span class="text-danger">*</span></label>
                                    <input type="date" name="service_date" class="form-control" 
                                           value="{{ fee.service_date.strftime('%Y-%m-%d') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الاستحقاق <span class="text-danger">*</span></label>
                                    <input type="date" name="due_date" class="form-control" 
                                           value="{{ fee.due_date.strftime('%Y-%m-%d') }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">شروط الدفع</label>
                            <select name="payment_terms" class="form-select">
                                <option value="فوري" {{ 'selected' if fee.payment_terms == 'فوري' }}>دفع فوري</option>
                                <option value="خلال 7 أيام" {{ 'selected' if fee.payment_terms == 'خلال 7 أيام' }}>خلال 7 أيام</option>
                                <option value="خلال 15 يوم" {{ 'selected' if fee.payment_terms == 'خلال 15 يوم' }}>خلال 15 يوم</option>
                                <option value="خلال 30 يوم" {{ 'selected' if fee.payment_terms == 'خلال 30 يوم' }}>خلال 30 يوم</option>
                                <option value="خلال 60 يوم" {{ 'selected' if fee.payment_terms == 'خلال 60 يوم' }}>خلال 60 يوم</option>
                                <option value="خلال 90 يوم" {{ 'selected' if fee.payment_terms == 'خلال 90 يوم' }}>خلال 90 يوم</option>
                                <option value="حسب الاتفاق" {{ 'selected' if fee.payment_terms == 'حسب الاتفاق' }}>حسب الاتفاق</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0"><i class="fa fa-cog"></i> إعدادات إضافية</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="is_recurring" class="form-check-input" 
                                       id="isRecurring" {{ 'checked' if fee.is_recurring }}>
                                <label class="form-check-label" for="isRecurring">
                                    أتعاب دورية
                                </label>
                            </div>
                        </div>

                        <div class="mb-3" id="recurringPeriodDiv" 
                             style="display: {{ 'block' if fee.is_recurring else 'none' }};">
                            <label class="form-label">فترة التكرار</label>
                            <select name="recurring_period" class="form-select">
                                <option value="شهري" {{ 'selected' if fee.recurring_period == 'شهري' }}>شهري</option>
                                <option value="ربع سنوي" {{ 'selected' if fee.recurring_period == 'ربع سنوي' }}>ربع سنوي</option>
                                <option value="نصف سنوي" {{ 'selected' if fee.recurring_period == 'نصف سنوي' }}>نصف سنوي</option>
                                <option value="سنوي" {{ 'selected' if fee.recurring_period == 'سنوي' }}>سنوي</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea name="notes" class="form-control" rows="3" 
                                      placeholder="أي ملاحظات إضافية...">{{ fee.notes }}</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الدفع الحالية -->
        <div class="card">
            <div class="card-header bg-light">
                <h6 class="mb-0 text-muted"><i class="fa fa-info-circle"></i> معلومات الدفع الحالية</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="text-muted small">المبلغ المدفوع</div>
                            <div class="fw-bold text-success">{{ fee.paid_amount | number_format }} {{ fee.currency }}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="text-muted small">المبلغ المتبقي</div>
                            <div class="fw-bold text-danger">{{ fee.remaining_amount | number_format }} {{ fee.currency }}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="text-muted small">حالة الدفع</div>
                            <div class="fw-bold">
                                <span class="badge 
                                            {% if fee.payment_status == 'غير مدفوع' %}bg-danger
                                            {% elif fee.payment_status == 'مدفوع جزئياً' %}bg-warning
                                            {% else %}bg-success
                                            {% endif %}">
                                    {{ fee.payment_status }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="text-muted small">عدد الدفعات</div>
                            <div class="fw-bold">{{ fee.payments.count() }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="fa fa-times"></i> إلغاء
        </button>
        <button type="submit" class="btn btn-success">
            <i class="fa fa-save"></i> حفظ التعديلات
        </button>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // حساب المجموع الإجمالي تلقائياً
    function calculateTotal() {
        const baseAmount = parseFloat(document.getElementById('baseAmount').value) || 0;
        const additionalFees = parseFloat(document.getElementById('additionalFees').value) || 0;
        const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
        const taxAmount = parseFloat(document.getElementById('taxAmount').value) || 0;
        
        const total = baseAmount + additionalFees - discountAmount + taxAmount;
        document.getElementById('totalAmount').value = total.toFixed(2);
    }

    // ربط الأحداث
    document.getElementById('baseAmount').addEventListener('input', calculateTotal);
    document.getElementById('additionalFees').addEventListener('input', calculateTotal);
    document.getElementById('discountAmount').addEventListener('input', calculateTotal);
    document.getElementById('taxAmount').addEventListener('input', calculateTotal);

    // إظهار/إخفاء فترة التكرار
    document.getElementById('isRecurring').addEventListener('change', function() {
        const recurringDiv = document.getElementById('recurringPeriodDiv');
        recurringDiv.style.display = this.checked ? 'block' : 'none';
    });

    // تحديث القضايا عند تغيير العميل
    document.querySelector('select[name="client_id"]').addEventListener('change', function() {
        const clientId = this.value;
        const caseSelect = document.querySelector('select[name="case_id"]');
        
        // مسح القضايا الحالية
        caseSelect.innerHTML = '<option value="">اختر القضية</option>';
        
        if (clientId) {
            // جلب قضايا العميل
            fetch(`/api/client/${clientId}/cases`)
                .then(response => response.json())
                .then(data => {
                    data.cases.forEach(case => {
                        const option = document.createElement('option');
                        option.value = case.id;
                        option.textContent = `${case.case_number} - ${case.title}`;
                        caseSelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Error fetching cases:', error));
        }
    });
});
</script>
