{% extends "base.html" %}

{% block title %}تعديل العقار - {{ property.name }}{% endblock %}

{% block extra_css %}
<style>
    .edit-header {
        background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
        color: white;
        padding: 20px 0;
        border-radius: 15px;
        margin-bottom: 30px;
    }
    
    .form-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        border: 1px solid #e9ecef;
    }
    
    .form-section h5 {
        color: #495057;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #ffc107;
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        color: white;
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
        color: white;
    }
    
    .image-preview {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border-radius: 8px;
        margin: 5px;
        border: 2px solid #ddd;
    }
    
    .image-upload-area {
        border: 2px dashed #ddd;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .image-upload-area:hover {
        border-color: #ffc107;
        background: #fff9e6;
    }
    
    .required-field {
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="edit-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-0">
                        <i class="fas fa-edit"></i>
                        تعديل العقار
                    </h2>
                    <p class="mb-0">{{ property.name }} - {{ property.property_code }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('property_details', id=property.id) }}" class="btn btn-light">
                        <i class="fas fa-arrow-right"></i>
                        العودة للتفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ url_for('edit_property', id=property.id) }}" enctype="multipart/form-data">
        {{ csrf_token() }}
        
        <div class="row">
            <div class="col-lg-8">
                <!-- المعلومات الأساسية -->
                <div class="form-section">
                    <h5><i class="fas fa-info-circle"></i> المعلومات الأساسية</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="property_code" class="form-label">رمز العقار <span class="required-field">*</span></label>
                                <input type="text" class="form-control" id="property_code" name="property_code" 
                                       value="{{ property.property_code }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم العقار <span class="required-field">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ property.name }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="property_type" class="form-label">نوع العقار <span class="required-field">*</span></label>
                                <select class="form-select" id="property_type" name="property_type" required>
                                    <option value="">اختر نوع العقار</option>
                                    <option value="شقة" {{ 'selected' if property.property_type == 'شقة' }}>شقة</option>
                                    <option value="فيلا" {{ 'selected' if property.property_type == 'فيلا' }}>فيلا</option>
                                    <option value="مكتب" {{ 'selected' if property.property_type == 'مكتب' }}>مكتب</option>
                                    <option value="محل تجاري" {{ 'selected' if property.property_type == 'محل تجاري' }}>محل تجاري</option>
                                    <option value="مستودع" {{ 'selected' if property.property_type == 'مستودع' }}>مستودع</option>
                                    <option value="أرض" {{ 'selected' if property.property_type == 'أرض' }}>أرض</option>
                                    <option value="مبنى" {{ 'selected' if property.property_type == 'مبنى' }}>مبنى</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category" class="form-label">فئة العقار</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">اختر الفئة</option>
                                    <option value="سكني" {{ 'selected' if property.category == 'سكني' }}>سكني</option>
                                    <option value="تجاري" {{ 'selected' if property.category == 'تجاري' }}>تجاري</option>
                                    <option value="إداري" {{ 'selected' if property.category == 'إداري' }}>إداري</option>
                                    <option value="صناعي" {{ 'selected' if property.category == 'صناعي' }}>صناعي</option>
                                    <option value="مختلط" {{ 'selected' if property.category == 'مختلط' }}>مختلط</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">حالة العقار</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="متاح" {{ 'selected' if property.status == 'متاح' }}>متاح</option>
                                    <option value="مؤجر" {{ 'selected' if property.status == 'مؤجر' }}>مؤجر</option>
                                    <option value="صيانة" {{ 'selected' if property.status == 'صيانة' }}>صيانة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف العقار</label>
                                <textarea class="form-control" id="description" name="description" rows="3">{{ property.description or '' }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الموقع والعنوان -->
                <div class="form-section">
                    <h5><i class="fas fa-map-marker-alt"></i> الموقع والعنوان</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="city" class="form-label">المدينة <span class="required-field">*</span></label>
                                <input type="text" class="form-control" id="city" name="city" 
                                       value="{{ property.city }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="district" class="form-label">الحي <span class="required-field">*</span></label>
                                <input type="text" class="form-control" id="district" name="district" 
                                       value="{{ property.district }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="street" class="form-label">الشارع</label>
                                <input type="text" class="form-control" id="street" name="street" 
                                       value="{{ property.street or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="building_number" class="form-label">رقم المبنى</label>
                                <input type="text" class="form-control" id="building_number" name="building_number" 
                                       value="{{ property.building_number or '' }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="floor_number" class="form-label">رقم الطابق</label>
                                <input type="number" class="form-control" id="floor_number" name="floor_number" 
                                       value="{{ property.floor_number or '' }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="apartment_number" class="form-label">رقم الشقة</label>
                                <input type="text" class="form-control" id="apartment_number" name="apartment_number" 
                                       value="{{ property.apartment_number or '' }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="postal_code" class="form-label">الرمز البريدي</label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code" 
                                       value="{{ property.postal_code or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">خط العرض (GPS)</label>
                                <input type="number" step="any" class="form-control" id="latitude" name="latitude" 
                                       value="{{ property.latitude or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">خط الطول (GPS)</label>
                                <input type="number" step="any" class="form-control" id="longitude" name="longitude" 
                                       value="{{ property.longitude or '' }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل العقار -->
                <div class="form-section">
                    <h5><i class="fas fa-home"></i> تفاصيل العقار</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="area" class="form-label">المساحة (م²)</label>
                                <input type="number" class="form-control" id="area" name="area" 
                                       value="{{ property.area or '' }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="bedrooms" class="form-label">عدد الغرف</label>
                                <input type="number" class="form-control" id="bedrooms" name="bedrooms" 
                                       value="{{ property.bedrooms or '' }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="bathrooms" class="form-label">عدد الحمامات</label>
                                <input type="number" class="form-control" id="bathrooms" name="bathrooms" 
                                       value="{{ property.bathrooms or '' }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="living_rooms" class="form-label">عدد الصالات</label>
                                <input type="number" class="form-control" id="living_rooms" name="living_rooms" 
                                       value="{{ property.living_rooms or '' }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="kitchens" class="form-label">عدد المطابخ</label>
                                <input type="number" class="form-control" id="kitchens" name="kitchens" 
                                       value="{{ property.kitchens or '' }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="parking_spaces" class="form-label">مواقف السيارات</label>
                                <input type="number" class="form-control" id="parking_spaces" name="parking_spaces" 
                                       value="{{ property.parking_spaces or '' }}">
                            </div>
                        </div>
                        
                        <!-- المرافق والخدمات -->
                        <div class="col-12">
                            <h6 class="mb-3">المرافق والخدمات</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_elevator" name="has_elevator" 
                                               {{ 'checked' if property.has_elevator }}>
                                        <label class="form-check-label" for="has_elevator">مصعد</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_balcony" name="has_balcony" 
                                               {{ 'checked' if property.has_balcony }}>
                                        <label class="form-check-label" for="has_balcony">شرفة</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_garden" name="has_garden" 
                                               {{ 'checked' if property.has_garden }}>
                                        <label class="form-check-label" for="has_garden">حديقة</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_pool" name="has_pool" 
                                               {{ 'checked' if property.has_pool }}>
                                        <label class="form-check-label" for="has_pool">مسبح</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_ac" name="has_ac" 
                                               {{ 'checked' if property.has_ac }}>
                                        <label class="form-check-label" for="has_ac">تكييف</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_heating" name="has_heating" 
                                               {{ 'checked' if property.has_heating }}>
                                        <label class="form-check-label" for="has_heating">تدفئة</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="furnished" name="furnished" 
                                               {{ 'checked' if property.furnished }}>
                                        <label class="form-check-label" for="furnished">مفروش</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_security" name="has_security" 
                                               {{ 'checked' if property.has_security }}>
                                        <label class="form-check-label" for="has_security">حراسة</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- المعلومات المالية -->
                <div class="form-section">
                    <h5><i class="fas fa-dollar-sign"></i> المعلومات المالية</h5>
                    <div class="mb-3">
                        <label for="monthly_rent" class="form-label">الإيجار الشهري</label>
                        <input type="number" step="0.01" class="form-control" id="monthly_rent" name="monthly_rent" 
                               value="{{ property.monthly_rent or '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="annual_rent" class="form-label">الإيجار السنوي</label>
                        <input type="number" step="0.01" class="form-control" id="annual_rent" name="annual_rent" 
                               value="{{ property.annual_rent or '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="security_deposit" class="form-label">مبلغ التأمين</label>
                        <input type="number" step="0.01" class="form-control" id="security_deposit" name="security_deposit" 
                               value="{{ property.security_deposit or '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="commission_rate" class="form-label">نسبة العمولة (%)</label>
                        <input type="number" step="0.01" class="form-control" id="commission_rate" name="commission_rate" 
                               value="{{ property.commission_rate or '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="property_value" class="form-label">قيمة العقار</label>
                        <input type="number" step="0.01" class="form-control" id="property_value" name="property_value" 
                               value="{{ property.property_value or '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="currency" class="form-label">العملة</label>
                        <select class="form-select" id="currency" name="currency">
                            <option value="JOD" {{ 'selected' if property.currency == 'JOD' }}>دينار أردني</option>
                            <option value="USD" {{ 'selected' if property.currency == 'USD' }}>دولار أمريكي</option>
                            <option value="EUR" {{ 'selected' if property.currency == 'EUR' }}>يورو</option>
                        </select>
                    </div>
                </div>

                <!-- معلومات المالك -->
                <div class="form-section">
                    <h5><i class="fas fa-user"></i> معلومات المالك</h5>
                    <div class="mb-3">
                        <label for="owner_name" class="form-label">اسم المالك</label>
                        <input type="text" class="form-control" id="owner_name" name="owner_name" 
                               value="{{ property.owner_name or '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="owner_phone" class="form-label">هاتف المالك</label>
                        <input type="tel" class="form-control" id="owner_phone" name="owner_phone" 
                               value="{{ property.owner_phone or '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="owner_email" class="form-label">بريد المالك الإلكتروني</label>
                        <input type="email" class="form-control" id="owner_email" name="owner_email" 
                               value="{{ property.owner_email or '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="owner_id_number" class="form-label">رقم هوية المالك</label>
                        <input type="text" class="form-control" id="owner_id_number" name="owner_id_number" 
                               value="{{ property.owner_id_number or '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="owner_address" class="form-label">عنوان المالك</label>
                        <textarea class="form-control" id="owner_address" name="owner_address" rows="2">{{ property.owner_address or '' }}</textarea>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="form-section">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-save">
                            <i class="fas fa-save"></i>
                            حفظ التعديلات
                        </button>
                        <a href="{{ url_for('property_details', id=property.id) }}" class="btn btn-cancel">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-calculate annual rent when monthly rent changes
document.getElementById('monthly_rent').addEventListener('input', function() {
    const monthlyRent = parseFloat(this.value) || 0;
    const annualRent = monthlyRent * 12;
    document.getElementById('annual_rent').value = annualRent;
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const requiredFields = ['property_code', 'name', 'property_type', 'city', 'district'];
    let isValid = true;
    
    requiredFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
    }
});
</script>
{% endblock %}
