from . import db
from datetime import datetime

# ==================== نماذج التقويم والمهام ====================

class Event(db.Model):
    """نموذج الأحداث والمواعيد"""
    __tablename__ = 'events'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    start_datetime = db.Column(db.DateTime, nullable=False)
    end_datetime = db.Column(db.DateTime, nullable=False)
    location = db.Column(db.String(200))
    event_type = db.Column(db.String(50), nullable=False, default='موعد عام')  # موعد عام، جلسة محكمة، اجتماع عميل، مهمة
    priority = db.Column(db.String(20), default='متوسطة')  # منخفضة، متوسطة، عالية، عاجلة
    status = db.Column(db.String(20), default='مجدول')  # مجدول، جاري، مكتمل، ملغي، مؤجل
    color = db.Column(db.String(7), default='#007bff')  # لون الحدث في التقويم
    is_all_day = db.Column(db.Boolean, default=False)
    is_recurring = db.Column(db.Boolean, default=False)
    recurring_pattern = db.Column(db.String(50))  # يومي، أسبوعي، شهري، سنوي
    recurring_end_date = db.Column(db.Date)
    
    # العلاقات
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # التواريخ
    created_date = db.Column(db.DateTime, default=datetime.now)
    updated_date = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    def is_overdue(self):
        """التحقق من تأخر الحدث"""
        return self.end_datetime < datetime.now() and self.status not in ['مكتمل', 'ملغي']
    
    def duration_minutes(self):
        """مدة الحدث بالدقائق"""
        return int((self.end_datetime - self.start_datetime).total_seconds() / 60)
    
    def get_status_color(self):
        """الحصول على لون حالة الحدث"""
        colors = {
            'مجدول': 'primary',
            'جاري': 'warning',
            'مكتمل': 'success',
            'ملغي': 'danger',
            'مؤجل': 'secondary'
        }
        return colors.get(self.status, 'primary')
    
    def get_priority_color(self):
        """الحصول على لون الأولوية"""
        colors = {
            'منخفضة': 'secondary',
            'متوسطة': 'info',
            'عالية': 'warning',
            'عاجلة': 'danger'
        }
        return colors.get(self.priority, 'info')
    
    def __repr__(self):
        return f'<Event {self.title}: {self.start_datetime}>'

class TaskItem(db.Model):
    """نموذج المهام"""
    __tablename__ = 'task_items'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    due_date = db.Column(db.DateTime)
    completed_date = db.Column(db.DateTime)
    priority = db.Column(db.String(20), default='متوسطة')  # منخفضة، متوسطة، عالية، عاجلة
    status = db.Column(db.String(20), default='جديدة')  # جديدة، قيد التنفيذ، مكتملة، ملغاة، مؤجلة
    category = db.Column(db.String(50), default='عامة')  # عامة، قانونية، إدارية، مالية
    estimated_hours = db.Column(db.Float, default=1.0)
    actual_hours = db.Column(db.Float, default=0.0)
    completion_percentage = db.Column(db.Integer, default=0)
    
    # العلاقات
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))
    assigned_to = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    parent_task_id = db.Column(db.Integer, db.ForeignKey('task_items.id'))
    
    # التواريخ
    created_date = db.Column(db.DateTime, default=datetime.now)
    updated_date = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    def is_overdue(self):
        """التحقق من تأخر المهمة"""
        return (self.due_date and 
                self.due_date < datetime.now() and 
                self.status not in ['مكتملة', 'ملغاة'])
    
    def days_until_due(self):
        """عدد الأيام المتبقية للموعد النهائي"""
        if not self.due_date:
            return None
        delta = self.due_date - datetime.now()
        return delta.days
    
    def mark_completed(self):
        """تحديد المهمة كمكتملة"""
        self.status = 'مكتملة'
        self.completed_date = datetime.now()
        self.completion_percentage = 100
    
    def get_status_color(self):
        """الحصول على لون حالة المهمة"""
        colors = {
            'جديدة': 'info',
            'قيد التنفيذ': 'warning',
            'مكتملة': 'success',
            'ملغاة': 'danger',
            'مؤجلة': 'secondary'
        }
        return colors.get(self.status, 'info')
    
    def get_priority_color(self):
        """الحصول على لون الأولوية"""
        colors = {
            'منخفضة': 'secondary',
            'متوسطة': 'info',
            'عالية': 'warning',
            'عاجلة': 'danger'
        }
        return colors.get(self.priority, 'info')
    
    def __repr__(self):
        return f'<TaskItem {self.title}: {self.status}>'

class Reminder(db.Model):
    """نموذج التذكيرات"""
    __tablename__ = 'reminders'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text)
    reminder_datetime = db.Column(db.DateTime, nullable=False)
    reminder_type = db.Column(db.String(50), default='تذكير عام')  # تذكير عام، موعد، مهمة، دفعة
    is_sent = db.Column(db.Boolean, default=False)
    sent_datetime = db.Column(db.DateTime)
    
    # العلاقات
    event_id = db.Column(db.Integer, db.ForeignKey('events.id'))
    task_id = db.Column(db.Integer, db.ForeignKey('task_items.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # التواريخ
    created_date = db.Column(db.DateTime, default=datetime.now)
    
    def mark_as_sent(self):
        """تحديد التذكير كمرسل"""
        self.is_sent = True
        self.sent_datetime = datetime.now()
    
    def is_due(self):
        """التحقق من حان وقت التذكير"""
        return self.reminder_datetime <= datetime.now() and not self.is_sent
    
    def __repr__(self):
        return f'<Reminder {self.title}: {self.reminder_datetime}>'
