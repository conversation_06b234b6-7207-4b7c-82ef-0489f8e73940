#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime

# إضافة مسار التطبيق
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app import app, db

def simple_recreate():
    """إعادة إنشاء قاعدة البيانات بشكل بسيط"""
    print("🔄 بدء إعادة إنشاء قاعدة البيانات...")
    
    with app.app_context():
        try:
            # حذف قاعدة البيانات إذا كانت موجودة
            db_path = 'app/database.db'
            if os.path.exists(db_path):
                try:
                    os.remove(db_path)
                    print("🗑️ تم حذف قاعدة البيانات القديمة")
                except:
                    print("⚠️ لا يمكن حذف قاعدة البيانات القديمة")
            
            # إنشاء قاعدة البيانات الجديدة
            print("🔄 إنشاء قاعدة البيانات الجديدة...")
            db.create_all()
            print("✅ تم إنشاء هيكل قاعدة البيانات")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False

if __name__ == "__main__":
    print("🚀 بدء إعادة إنشاء قاعدة البيانات...")
    print("=" * 60)
    
    success = simple_recreate()
    
    print("=" * 60)
    if success:
        print("🎉 تم إنشاء قاعدة البيانات بنجاح!")
    else:
        print("💥 فشل في إنشاء قاعدة البيانات")
