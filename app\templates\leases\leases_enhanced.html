{% extends "base.html" %}

{% block title %}إدارة عقود الإيجار المحسنة{% endblock %}

{% block extra_css %}
<style>
    .lease-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        position: relative;
    }
    
    .lease-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    
    .lease-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        position: relative;
    }
    
    .lease-code {
        font-size: 1.1rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .lease-property {
        opacity: 0.9;
        font-size: 0.95rem;
    }
    
    .lease-status {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .status-active {
        background: rgba(40, 167, 69, 0.9);
        color: white;
    }
    
    .status-expired {
        background: rgba(220, 53, 69, 0.9);
        color: white;
    }
    
    .status-pending {
        background: rgba(255, 193, 7, 0.9);
        color: white;
    }
    
    .status-terminated {
        background: rgba(108, 117, 125, 0.9);
        color: white;
    }
    
    .lease-info {
        padding: 20px;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .info-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .info-label {
        color: #6c757d;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .info-value {
        font-weight: 500;
        color: #2c3e50;
    }
    
    .lease-progress {
        margin: 15px 0;
    }
    
    .progress-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .progress {
        height: 8px;
        border-radius: 4px;
        background: #e9ecef;
    }
    
    .progress-bar {
        border-radius: 4px;
        transition: width 0.3s ease;
    }
    
    .progress-active { background: linear-gradient(90deg, #28a745, #20c997); }
    .progress-warning { background: linear-gradient(90deg, #ffc107, #fd7e14); }
    .progress-danger { background: linear-gradient(90deg, #dc3545, #fd7e14); }
    
    .lease-actions {
        padding: 15px 20px;
        background: #f8f9fa;
        display: flex;
        gap: 10px;
        justify-content: space-between;
    }
    
    .btn-action {
        flex: 1;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
        border: none;
    }
    
    .btn-view {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
    }
    
    .btn-edit {
        background: linear-gradient(45deg, #f39c12, #e67e22);
        color: white;
    }
    
    .btn-renew {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }
    
    .search-filters {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    }
    
    .stats-cards {
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 0 0 20px 20px;
    }
    
    .floating-add-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 20px rgba(40, 167, 69, 0.4);
        transition: all 0.3s ease;
        z-index: 1000;
    }
    
    .floating-add-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(40, 167, 69, 0.6);
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
    
    .expiry-warning {
        background: linear-gradient(45deg, #ffc107, #fd7e14);
        color: white;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        margin-right: 5px;
    }
    
    .payment-indicator {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .payment-current {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
        border: 1px solid rgba(40, 167, 69, 0.2);
    }
    
    .payment-late {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
        border: 1px solid rgba(220, 53, 69, 0.2);
    }
    
    .payment-upcoming {
        background: rgba(255, 193, 7, 0.1);
        color: #ffc107;
        border: 1px solid rgba(255, 193, 7, 0.2);
    }
    
    .quick-stats {
        display: flex;
        gap: 15px;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #f1f3f4;
    }
    
    .quick-stat {
        text-align: center;
        flex: 1;
    }
    
    .quick-stat-number {
        font-size: 1.2rem;
        font-weight: bold;
        color: #667eea;
    }
    
    .quick-stat-label {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 2px;
    }
    
    .tenant-info {
        background: rgba(102, 126, 234, 0.1);
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 15px;
    }
    
    .tenant-name {
        font-weight: bold;
        color: #667eea;
        margin-bottom: 5px;
    }
    
    .tenant-contact {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .financial-summary {
        background: rgba(40, 167, 69, 0.1);
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 15px;
    }
    
    .monthly-rent {
        font-size: 1.1rem;
        font-weight: bold;
        color: #28a745;
        margin-bottom: 5px;
    }
    
    .total-value {
        font-size: 0.9rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-0">
                    <i class="fas fa-file-contract me-3"></i>
                    إدارة عقود الإيجار المحسنة
                </h1>
                <p class="mb-0 mt-2 opacity-75">إدارة شاملة ومتطورة لجميع عقود الإيجار</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#addLeaseModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة عقد جديد
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- إحصائيات سريعة -->
    <div class="row stats-cards">
        <div class="col-md-3">
            <div class="card stat-card">
                <div class="stat-number">{{ total_leases or 0 }}</div>
                <div class="stat-label">إجمالي العقود</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="stat-number">{{ active_leases or 0 }}</div>
                <div class="stat-label">عقود نشطة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <div class="stat-number">{{ expiring_soon or 0 }}</div>
                <div class="stat-label">تنتهي قريباً</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);">
                <div class="stat-number">{{ expired_leases or 0 }}</div>
                <div class="stat-label">عقود منتهية</div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="search-filters">
        <form method="GET" id="searchForm">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" class="form-control" name="search" value="{{ request.args.get('search', '') }}" 
                           placeholder="رمز العقد، العقار، أو المستأجر">
                </div>
                <div class="col-md-2">
                    <label class="form-label">حالة العقد</label>
                    <select class="form-select" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="نشط" {{ 'selected' if request.args.get('status') == 'نشط' }}>نشط</option>
                        <option value="منتهي" {{ 'selected' if request.args.get('status') == 'منتهي' }}>منتهي</option>
                        <option value="في الانتظار" {{ 'selected' if request.args.get('status') == 'في الانتظار' }}>في الانتظار</option>
                        <option value="ملغي" {{ 'selected' if request.args.get('status') == 'ملغي' }}>ملغي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">نوع العقار</label>
                    <select class="form-select" name="property_type">
                        <option value="">جميع الأنواع</option>
                        <option value="شقة" {{ 'selected' if request.args.get('property_type') == 'شقة' }}>شقة</option>
                        <option value="فيلا" {{ 'selected' if request.args.get('property_type') == 'فيلا' }}>فيلا</option>
                        <option value="مكتب" {{ 'selected' if request.args.get('property_type') == 'مكتب' }}>مكتب</option>
                        <option value="محل" {{ 'selected' if request.args.get('property_type') == 'محل' }}>محل</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">فترة الانتهاء</label>
                    <select class="form-select" name="expiry_period">
                        <option value="">جميع الفترات</option>
                        <option value="week" {{ 'selected' if request.args.get('expiry_period') == 'week' }}>خلال أسبوع</option>
                        <option value="month" {{ 'selected' if request.args.get('expiry_period') == 'month' }}>خلال شهر</option>
                        <option value="quarter" {{ 'selected' if request.args.get('expiry_period') == 'quarter' }}>خلال 3 أشهر</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">نطاق الإيجار الشهري</label>
                    <div class="row">
                        <div class="col-6">
                            <input type="number" class="form-control" name="min_rent" 
                                   value="{{ request.args.get('min_rent', '') }}" placeholder="من">
                        </div>
                        <div class="col-6">
                            <input type="number" class="form-control" name="max_rent" 
                                   value="{{ request.args.get('max_rent', '') }}" placeholder="إلى">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{{ url_for('leases_enhanced') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        مسح الفلاتر
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- قائمة العقود -->
    <div class="row">
        {% if leases %}
            {% for lease in leases %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card lease-card">
                    <div class="lease-header">
                        <div class="lease-status 
                            {% if lease.status == 'نشط' %}status-active
                            {% elif lease.status == 'منتهي' %}status-expired
                            {% elif lease.status == 'في الانتظار' %}status-pending
                            {% else %}status-terminated{% endif %}">
                            {{ lease.status }}
                        </div>
                        
                        <div class="lease-code">{{ lease.lease_code }}</div>
                        <div class="lease-property">{{ lease.property.name if lease.property else 'عقار غير محدد' }}</div>
                        
                        {% if lease.is_expiring_soon() %}
                        <div class="expiry-warning mt-2">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            ينتهي قريباً
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="lease-info">
                        <!-- معلومات المستأجر -->
                        <div class="tenant-info">
                            <div class="tenant-name">
                                <i class="fas fa-user me-2"></i>
                                {{ lease.tenant.full_name if lease.tenant else 'مستأجر غير محدد' }}
                            </div>
                            <div class="tenant-contact">
                                <i class="fas fa-phone me-1"></i>
                                {{ lease.tenant.phone_number if lease.tenant and lease.tenant.phone_number else 'غير محدد' }}
                            </div>
                        </div>
                        
                        <!-- الملخص المالي -->
                        <div class="financial-summary">
                            <div class="monthly-rent">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                {{ lease.monthly_rent|number_format }} {{ lease.currency or 'ريال' }}/شهر
                            </div>
                            <div class="total-value">
                                إجمالي العقد: {{ lease.total_amount|number_format }} {{ lease.currency or 'ريال' }}
                            </div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-calendar-alt"></i>
                                تاريخ البداية
                            </div>
                            <div class="info-value">{{ lease.start_date.strftime('%Y-%m-%d') if lease.start_date else 'غير محدد' }}</div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-calendar-times"></i>
                                تاريخ الانتهاء
                            </div>
                            <div class="info-value">{{ lease.end_date.strftime('%Y-%m-%d') if lease.end_date else 'غير محدد' }}</div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-clock"></i>
                                مدة العقد
                            </div>
                            <div class="info-value">{{ lease.duration_months or 0 }} شهر</div>
                        </div>
                        
                        <!-- شريط التقدم -->
                        <div class="lease-progress">
                            {% set progress = lease.get_progress_percentage() %}
                            <div class="progress-label">
                                <span>تقدم العقد</span>
                                <span>{{ progress }}%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar 
                                    {% if progress < 70 %}progress-active
                                    {% elif progress < 90 %}progress-warning
                                    {% else %}progress-danger{% endif %}" 
                                    style="width: {{ progress }}%"></div>
                            </div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-credit-card"></i>
                                حالة الدفع
                            </div>
                            <div class="info-value">
                                {% set payment_status = lease.get_payment_status() %}
                                <span class="payment-indicator 
                                    {% if payment_status == 'محدث' %}payment-current
                                    {% elif payment_status == 'متأخر' %}payment-late
                                    {% else %}payment-upcoming{% endif %}">
                                    <i class="fas fa-circle" style="font-size: 0.5rem;"></i>
                                    {{ payment_status }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="quick-stats">
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ lease.installments.count() }}</div>
                                <div class="quick-stat-label">أقساط</div>
                            </div>
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ lease.payments.count() }}</div>
                                <div class="quick-stat-label">مدفوعات</div>
                            </div>
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ lease.documents.count() }}</div>
                                <div class="quick-stat-label">مستندات</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="lease-actions">
                        <a href="{{ url_for('lease_details', id=lease.id) }}" class="btn btn-action btn-view">
                            <i class="fas fa-eye me-1"></i>
                            عرض
                        </a>
                        <a href="{{ url_for('edit_lease', id=lease.id) }}" class="btn btn-action btn-edit">
                            <i class="fas fa-edit me-1"></i>
                            تعديل
                        </a>
                        {% if lease.status == 'نشط' and lease.is_expiring_soon() %}
                        <button class="btn btn-action btn-renew" onclick="renewLease({{ lease.id }}, '{{ lease.lease_code }}')">
                            <i class="fas fa-redo me-1"></i>
                            تجديد
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="empty-state">
                    <i class="fas fa-file-contract"></i>
                    <h4>لا توجد عقود</h4>
                    <p>لم يتم العثور على عقود تطابق معايير البحث</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLeaseModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة عقد جديد
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- زر الإضافة العائم -->
<button class="floating-add-btn" data-bs-toggle="modal" data-bs-target="#addLeaseModal">
    <i class="fas fa-plus"></i>
</button>
{% endblock %}
