{% extends "base.html" %}

{% block title %}الإشعارات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-bell"></i> إدارة الإشعارات</h1>
                    <p>عرض وإدارة جميع الإشعارات والتنبيهات</p>
                </div>
                <div class="page-actions">
                    <button type="button" class="btn btn-primary" onclick="markAllAsRead()">
                        <i class="fas fa-check-double me-2"></i>
                        تمييز الكل كمقروء
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="clearAllNotifications()">
                        <i class="fas fa-trash me-2"></i>
                        حذف الكل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list text-primary me-2"></i>
                        قائمة الإشعارات
                    </h5>
                </div>
                <div class="card-body">
                    <!-- إحصائيات الإشعارات -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stats-card bg-primary">
                                <h3 id="total-notifications">0</h3>
                                <p>إجمالي الإشعارات</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-warning">
                                <h3 id="unread-notifications">0</h3>
                                <p>غير مقروءة</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-success">
                                <h3 id="read-notifications">0</h3>
                                <p>مقروءة</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-info">
                                <h3 id="today-notifications">0</h3>
                                <p>اليوم</p>
                            </div>
                        </div>
                    </div>

                    <!-- فلاتر الإشعارات -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-control" id="filter-type" onchange="filterNotifications()">
                                <option value="">جميع الأنواع</option>
                                <option value="success">نجاح</option>
                                <option value="info">معلومات</option>
                                <option value="warning">تحذير</option>
                                <option value="error">خطأ</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-control" id="filter-status" onchange="filterNotifications()">
                                <option value="">جميع الحالات</option>
                                <option value="unread">غير مقروءة</option>
                                <option value="read">مقروءة</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="search-notifications" 
                                   placeholder="البحث في الإشعارات..." onkeyup="searchNotifications()">
                        </div>
                    </div>

                    <!-- قائمة الإشعارات -->
                    <div id="notifications-container">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2">جاري تحميل الإشعارات...</p>
                        </div>
                    </div>

                    <!-- التصفح -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <button class="btn btn-outline-primary" id="load-more-btn" onclick="loadMoreNotifications()" style="display: none;">
                                تحميل المزيد
                            </button>
                        </div>
                        <div>
                            <small class="text-muted" id="notifications-count">0 إشعار</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إعدادات الإشعارات -->
<div class="modal fade" id="notificationSettingsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعدادات الإشعارات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="notification-settings-form">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="notifications-enabled">
                            <label class="form-check-label" for="notifications-enabled">
                                تفعيل الإشعارات
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="sound-enabled">
                            <label class="form-check-label" for="sound-enabled">
                                تفعيل الأصوات
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="desktop-enabled">
                            <label class="form-check-label" for="desktop-enabled">
                                إشعارات سطح المكتب
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="auto-hide-delay" class="form-label">مدة إخفاء الإشعار (ثواني)</label>
                        <input type="number" class="form-control" id="auto-hide-delay" min="1" max="60" value="5">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveNotificationSettings()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let hasMoreNotifications = true;
let allNotifications = [];

// تحميل الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadNotifications();
    loadNotificationStats();
    loadNotificationSettings();
});

// تحميل الإشعارات
async function loadNotifications(page = 1) {
    try {
        const response = await fetch(`/api/notifications?page=${page}&limit=20`);
        const data = await response.json();
        
        if (data.success) {
            if (page === 1) {
                allNotifications = data.notifications;
                displayNotifications(data.notifications);
            } else {
                allNotifications = [...allNotifications, ...data.notifications];
                appendNotifications(data.notifications);
            }
            
            hasMoreNotifications = data.has_more;
            updateLoadMoreButton();
            updateNotificationsCount();
        } else {
            showError('فشل في تحميل الإشعارات');
        }
    } catch (error) {
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض الإشعارات
function displayNotifications(notifications) {
    const container = document.getElementById('notifications-container');
    
    if (notifications.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد إشعارات</h5>
                <p class="text-muted">ستظهر الإشعارات هنا عند وصولها</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = notifications.map(notification => createNotificationHTML(notification)).join('');
}

// إنشاء HTML للإشعار
function createNotificationHTML(notification) {
    const typeIcons = {
        'success': 'fa-check-circle text-success',
        'info': 'fa-info-circle text-info',
        'warning': 'fa-exclamation-triangle text-warning',
        'error': 'fa-times-circle text-danger'
    };
    
    const icon = typeIcons[notification.type] || 'fa-bell text-primary';
    const readClass = notification.read ? 'notification-read' : 'notification-unread';
    
    return `
        <div class="notification-item ${readClass} mb-3 p-3 border rounded" data-id="${notification.id}">
            <div class="d-flex justify-content-between align-items-start">
                <div class="d-flex">
                    <div class="me-3">
                        <i class="fas ${icon} fa-lg"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${notification.title}</h6>
                        <p class="mb-1 text-muted">${notification.message}</p>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            ${formatDate(notification.created_at)}
                        </small>
                    </div>
                </div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu">
                        ${!notification.read ? '<li><a class="dropdown-item" href="#" onclick="markAsRead(' + notification.id + ')"><i class="fas fa-check me-2"></i>تمييز كمقروء</a></li>' : ''}
                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteNotification(' + notification.id + ')"><i class="fas fa-trash me-2"></i>حذف</a></li>
                    </ul>
                </div>
            </div>
        </div>
    `;
}

// تحميل إحصائيات الإشعارات
async function loadNotificationStats() {
    try {
        const response = await fetch('/api/notifications/stats');
        const data = await response.json();
        
        if (data.success) {
            document.getElementById('total-notifications').textContent = data.stats.total;
            document.getElementById('unread-notifications').textContent = data.stats.unread;
            document.getElementById('read-notifications').textContent = data.stats.read;
            document.getElementById('today-notifications').textContent = data.stats.today;
        }
    } catch (error) {
        console.error('خطأ في تحميل الإحصائيات:', error);
    }
}

// تمييز إشعار كمقروء
async function markAsRead(notificationId) {
    try {
        const response = await fetch(`/api/notifications/${notificationId}/read`, {
            method: 'POST'
        });
        const data = await response.json();
        
        if (data.success) {
            // تحديث الواجهة
            const notificationElement = document.querySelector(`[data-id="${notificationId}"]`);
            notificationElement.classList.remove('notification-unread');
            notificationElement.classList.add('notification-read');
            
            // إعادة تحميل الإحصائيات
            loadNotificationStats();
        }
    } catch (error) {
        showError('فشل في تمييز الإشعار كمقروء');
    }
}

// تمييز جميع الإشعارات كمقروءة
async function markAllAsRead() {
    try {
        const response = await fetch('/api/notifications/mark-all-read', {
            method: 'POST'
        });
        const data = await response.json();
        
        if (data.success) {
            // تحديث جميع الإشعارات في الواجهة
            document.querySelectorAll('.notification-unread').forEach(element => {
                element.classList.remove('notification-unread');
                element.classList.add('notification-read');
            });
            
            loadNotificationStats();
            showSuccess(`تم تمييز ${data.count} إشعار كمقروء`);
        }
    } catch (error) {
        showError('فشل في تمييز الإشعارات كمقروءة');
    }
}

// حذف إشعار
async function deleteNotification(notificationId) {
    if (!confirm('هل أنت متأكد من حذف هذا الإشعار؟')) return;
    
    try {
        const response = await fetch(`/api/notifications/${notificationId}`, {
            method: 'DELETE'
        });
        const data = await response.json();
        
        if (data.success) {
            // إزالة الإشعار من الواجهة
            const notificationElement = document.querySelector(`[data-id="${notificationId}"]`);
            notificationElement.remove();
            
            loadNotificationStats();
            showSuccess('تم حذف الإشعار');
        }
    } catch (error) {
        showError('فشل في حذف الإشعار');
    }
}

// حذف جميع الإشعارات
async function clearAllNotifications() {
    if (!confirm('هل أنت متأكد من حذف جميع الإشعارات؟ هذا الإجراء لا يمكن التراجع عنه.')) return;
    
    try {
        const response = await fetch('/api/notifications/clear-all', {
            method: 'POST'
        });
        const data = await response.json();
        
        if (data.success) {
            document.getElementById('notifications-container').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد إشعارات</h5>
                </div>
            `;
            
            loadNotificationStats();
            showSuccess(`تم حذف ${data.count} إشعار`);
        }
    } catch (error) {
        showError('فشل في حذف الإشعارات');
    }
}



// دوال مساعدة
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('ar-SA');
}

function showSuccess(message) {
    // يمكن استخدام نظام الإشعارات هنا
    alert(message);
}

function showError(message) {
    alert('خطأ: ' + message);
}

function updateNotificationsCount() {
    const count = allNotifications.length;
    document.getElementById('notifications-count').textContent = `${count} إشعار`;
}

function updateLoadMoreButton() {
    const button = document.getElementById('load-more-btn');
    button.style.display = hasMoreNotifications ? 'block' : 'none';
}

function loadMoreNotifications() {
    currentPage++;
    loadNotifications(currentPage);
}

// دوال الفلترة والبحث
function filterNotifications() {
    // تنفيذ الفلترة
}

function searchNotifications() {
    // تنفيذ البحث
}

// تحميل وحفظ إعدادات الإشعارات
async function loadNotificationSettings() {
    // تنفيذ تحميل الإعدادات
}

async function saveNotificationSettings() {
    // تنفيذ حفظ الإعدادات
}
</script>

<style>
.notification-item {
    transition: all 0.3s ease;
}

.notification-unread {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
}

.notification-read {
    opacity: 0.8;
}

.notification-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stats-card {
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    color: white;
    margin-bottom: 10px;
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-card p {
    margin: 0;
    opacity: 0.9;
}
</style>
{% endblock %}
