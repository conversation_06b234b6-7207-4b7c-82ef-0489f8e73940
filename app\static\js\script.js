/**
 * ملف JavaScript الأساسي للنظام
 * يحتوي على الوظائف الأساسية المشتركة
 */

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
});

/**
 * تهيئة النظام الأساسي
 */
function initializeSystem() {
    // تهيئة Bootstrap tooltips
    initializeTooltips();
    
    // تهيئة Bootstrap popovers
    initializePopovers();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة الإشعارات
    initializeNotifications();
}

/**
 * تهيئة Bootstrap tooltips
 */
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تهيئة Bootstrap popovers
 */
function initializePopovers() {
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    // تفعيل التحقق من صحة النماذج
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
    
    // تهيئة حقول التاريخ
    initializeDateFields();
    
    // تهيئة حقول الأرقام
    initializeNumberFields();
}

/**
 * تهيئة حقول التاريخ
 */
function initializeDateFields() {
    var dateFields = document.querySelectorAll('input[type="date"]');
    dateFields.forEach(function(field) {
        // تعيين التاريخ الحالي كقيمة افتراضية إذا لم تكن محددة
        if (!field.value && field.hasAttribute('data-default-today')) {
            var today = new Date().toISOString().split('T')[0];
            field.value = today;
        }
    });
}

/**
 * تهيئة حقول الأرقام
 */
function initializeNumberFields() {
    var numberFields = document.querySelectorAll('input[type="number"]');
    numberFields.forEach(function(field) {
        // منع إدخال الأرقام السالبة في الحقول المالية
        if (field.hasAttribute('data-positive-only')) {
            field.addEventListener('input', function() {
                if (this.value < 0) {
                    this.value = 0;
                }
            });
        }
    });
}

/**
 * تهيئة الجداول
 */
function initializeTables() {
    // إضافة فئات Bootstrap للجداول
    var tables = document.querySelectorAll('table:not(.no-bootstrap)');
    tables.forEach(function(table) {
        if (!table.classList.contains('table')) {
            table.classList.add('table', 'table-striped', 'table-hover');
        }
    });
    
    // تهيئة البحث في الجداول
    initializeTableSearch();
}

/**
 * تهيئة البحث في الجداول
 */
function initializeTableSearch() {
    var searchInputs = document.querySelectorAll('.table-search');
    searchInputs.forEach(function(input) {
        var tableId = input.getAttribute('data-table');
        var table = document.getElementById(tableId);
        
        if (table) {
            input.addEventListener('keyup', function() {
                filterTable(table, this.value);
            });
        }
    });
}

/**
 * تصفية الجدول بناءً على النص المدخل
 */
function filterTable(table, searchText) {
    var rows = table.querySelectorAll('tbody tr');
    var searchLower = searchText.toLowerCase();
    
    rows.forEach(function(row) {
        var text = row.textContent.toLowerCase();
        if (text.includes(searchLower)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

/**
 * تهيئة الإشعارات
 */
function initializeNotifications() {
    // إخفاء الإشعارات تلقائياً بعد 5 ثوانٍ
    var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
}

/**
 * عرض إشعار نجاح
 */
function showSuccessMessage(message) {
    showAlert(message, 'success');
}

/**
 * عرض إشعار خطأ
 */
function showErrorMessage(message) {
    showAlert(message, 'danger');
}

/**
 * عرض إشعار تحذير
 */
function showWarningMessage(message) {
    showAlert(message, 'warning');
}

/**
 * عرض إشعار معلومات
 */
function showInfoMessage(message) {
    showAlert(message, 'info');
}

/**
 * عرض إشعار عام
 */
function showAlert(message, type) {
    var alertContainer = document.getElementById('alert-container');
    if (!alertContainer) {
        // إنشاء حاوية الإشعارات إذا لم تكن موجودة
        alertContainer = document.createElement('div');
        alertContainer.id = 'alert-container';
        alertContainer.className = 'position-fixed top-0 end-0 p-3';
        alertContainer.style.zIndex = '9999';
        document.body.appendChild(alertContainer);
    }
    
    var alertId = 'alert-' + Date.now();
    var alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHtml);
    
    // إخفاء الإشعار تلقائياً بعد 5 ثوانٍ
    setTimeout(function() {
        var alert = document.getElementById(alertId);
        if (alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}

/**
 * تأكيد الحذف
 */
function confirmDelete(message) {
    return confirm(message || 'هل أنت متأكد من الحذف؟');
}

/**
 * تنسيق الأرقام
 */
function formatNumber(number, decimals = 2) {
    return parseFloat(number).toFixed(decimals);
}

/**
 * تنسيق العملة
 */
function formatCurrency(amount, currency = 'شيكل') {
    return formatNumber(amount) + ' ' + currency;
}

/**
 * تحويل التاريخ إلى تنسيق عربي
 */
function formatArabicDate(dateString) {
    var date = new Date(dateString);
    var options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        calendar: 'islamic'
    };
    return date.toLocaleDateString('ar-SA', options);
}

/**
 * طباعة الصفحة
 */
function printPage() {
    window.print();
}

/**
 * تصدير البيانات إلى CSV
 */
function exportToCSV(tableId, filename) {
    var table = document.getElementById(tableId);
    if (!table) return;
    
    var csv = [];
    var rows = table.querySelectorAll('tr');
    
    for (var i = 0; i < rows.length; i++) {
        var row = [], cols = rows[i].querySelectorAll('td, th');
        
        for (var j = 0; j < cols.length; j++) {
            row.push(cols[j].innerText);
        }
        
        csv.push(row.join(','));
    }
    
    downloadCSV(csv.join('\n'), filename);
}

/**
 * تحميل ملف CSV
 */
function downloadCSV(csv, filename) {
    var csvFile = new Blob([csv], {type: 'text/csv'});
    var downloadLink = document.createElement('a');
    
    downloadLink.download = filename;
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';
    
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

// تصدير الوظائف للاستخدام العام
window.showSuccessMessage = showSuccessMessage;
window.showErrorMessage = showErrorMessage;
window.showWarningMessage = showWarningMessage;
window.showInfoMessage = showInfoMessage;
window.confirmDelete = confirmDelete;
window.formatNumber = formatNumber;
window.formatCurrency = formatCurrency;
window.formatArabicDate = formatArabicDate;
window.printPage = printPage;
window.exportToCSV = exportToCSV;
