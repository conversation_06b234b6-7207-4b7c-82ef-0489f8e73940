#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص هيكل الجداول
Check Table Structure
"""

import sqlite3

def check_table_structure():
    """فحص هيكل الجداول المطلوبة"""
    print("🔍 فحص هيكل الجداول...")
    print("=" * 60)
    
    db_path = 'instance/lawoffice.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    tables = ['fees', 'debts', 'tasks', 'appointments']
    
    for table in tables:
        print(f"\n📋 جدول {table}:")
        print("-" * 40)
        
        try:
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            if columns:
                print("   الأعمدة الموجودة:")
                for col in columns:
                    print(f"     - {col[1]} ({col[2]})")
            else:
                print("   ❌ الجدول غير موجود أو فارغ")
                
        except Exception as e:
            print(f"   ❌ خطأ في فحص الجدول: {e}")
    
    # فحص جميع الجداول الموجودة
    print(f"\n📊 جميع الجداول في قاعدة البيانات:")
    print("-" * 40)
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    all_tables = cursor.fetchall()
    
    for table in all_tables:
        print(f"   - {table[0]}")
    
    conn.close()

if __name__ == "__main__":
    check_table_structure()
