<!-- Add <PERSON>se <PERSON> -->
<div class="modal fade" id="addLeaseModal" tabindex="-1" aria-labelledby="addLeaseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <h5 class="modal-title" id="addLeaseModalLabel">
                    <i class="fas fa-file-contract"></i>
                    إنشاء عقد إيجار جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            
            <form method="POST" action="{{ url_for('add_lease') }}" id="addLeaseForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                
                <div class="modal-body">
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs mb-4" id="leaseTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" 
                                    type="button" role="tab" aria-controls="basic" aria-selected="true">
                                <i class="fas fa-info-circle"></i> المعلومات الأساسية
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="financial-tab" data-bs-toggle="tab" data-bs-target="#financial" 
                                    type="button" role="tab" aria-controls="financial" aria-selected="false">
                                <i class="fas fa-dollar-sign"></i> المعلومات المالية
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="terms-tab" data-bs-toggle="tab" data-bs-target="#terms" 
                                    type="button" role="tab" aria-controls="terms" aria-selected="false">
                                <i class="fas fa-file-alt"></i> الشروط والأحكام
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="additional-tab" data-bs-toggle="tab" data-bs-target="#additional" 
                                    type="button" role="tab" aria-controls="additional" aria-selected="false">
                                <i class="fas fa-plus-circle"></i> معلومات إضافية
                            </button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="leaseTabContent">
                        <!-- Basic Information Tab -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="lease_number" class="form-label">رقم العقد</label>
                                        <input type="text" class="form-control" id="lease_number" name="lease_number"
                                               placeholder="سيتم إنشاؤه تلقائياً إذا ترك فارغاً">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="property_id" class="form-label">العقار <span class="text-danger">*</span></label>
                                        <select class="form-select" id="property_id" name="property_id" required>
                                            <option value="">اختر العقار</option>
                                            {% for property in available_properties %}
                                            <option value="{{ property.id }}">{{ property.name }} - {{ property.address }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="tenant_id" class="form-label">المستأجر <span class="text-danger">*</span></label>
                                        <select class="form-select" id="tenant_id" name="tenant_id" required>
                                            <option value="">اختر المستأجر</option>
                                            {% for tenant in available_tenants %}
                                            <option value="{{ tenant.id }}">{{ tenant.full_name }} - {{ tenant.phone }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="lease_type" class="form-label">نوع العقد <span class="text-danger">*</span></label>
                                        <select class="form-select" id="lease_type" name="lease_type" required>
                                            <option value="">اختر نوع العقد</option>
                                            <option value="سكني">سكني</option>
                                            <option value="تجاري">تجاري</option>
                                            <option value="مكتبي">مكتبي</option>
                                            <option value="صناعي">صناعي</option>
                                            <option value="مختلط">مختلط</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="start_date" class="form-label">تاريخ بداية العقد <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="start_date" name="start_date" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="end_date" class="form-label">تاريخ انتهاء العقد <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="end_date" name="end_date" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="lease_duration_months" class="form-label">مدة العقد (بالأشهر)</label>
                                        <input type="number" class="form-control" id="lease_duration_months" name="lease_duration_months" 
                                               min="1" readonly style="background-color: #f8f9fa;">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">حالة العقد</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="معلق">معلق</option>
                                            <option value="نشط" selected>نشط</option>
                                            <option value="منتهي">منتهي</option>
                                            <option value="ملغي">ملغي</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Financial Information Tab -->
                        <div class="tab-pane fade" id="financial" role="tabpanel" aria-labelledby="financial-tab">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="monthly_rent" class="form-label">الإيجار الشهري <span class="text-danger">*</span></label>
                                        <input type="number" step="0.01" class="form-control" id="monthly_rent" name="monthly_rent" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="currency" class="form-label">العملة</label>
                                        <select class="form-select" id="currency" name="currency">
                                            <option value="JOD" selected>دينار أردني</option>
                                            <option value="USD">دولار أمريكي</option>
                                            <option value="EUR">يورو</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="security_deposit" class="form-label">مبلغ التأمين</label>
                                        <input type="number" step="0.01" class="form-control" id="security_deposit" name="security_deposit">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="commission_amount" class="form-label">مبلغ العمولة</label>
                                        <input type="number" step="0.01" class="form-control" id="commission_amount" name="commission_amount">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="payment_frequency" class="form-label">دورية الدفع</label>
                                        <select class="form-select" id="payment_frequency" name="payment_frequency">
                                            <option value="شهري" selected>شهري</option>
                                            <option value="ربع سنوي">ربع سنوي</option>
                                            <option value="نصف سنوي">نصف سنوي</option>
                                            <option value="سنوي">سنوي</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="payment_due_day" class="form-label">يوم استحقاق الدفع</label>
                                        <input type="number" class="form-control" id="payment_due_day" name="payment_due_day" 
                                               min="1" max="31" value="1">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="late_fee_amount" class="form-label">رسوم التأخير</label>
                                        <input type="number" step="0.01" class="form-control" id="late_fee_amount" name="late_fee_amount">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="grace_period_days" class="form-label">فترة السماح (بالأيام)</label>
                                        <input type="number" class="form-control" id="grace_period_days" name="grace_period_days" 
                                               min="0" value="5">
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="total_lease_value" class="form-label">إجمالي قيمة العقد</label>
                                        <input type="number" step="0.01" class="form-control" id="total_lease_value" name="total_lease_value" 
                                               readonly style="background-color: #f8f9fa;">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Terms and Conditions Tab -->
                        <div class="tab-pane fade" id="terms" role="tabpanel" aria-labelledby="terms-tab">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="terms_and_conditions" class="form-label">الشروط والأحكام</label>
                                        <textarea class="form-control" id="terms_and_conditions" name="terms_and_conditions" 
                                                  rows="6" placeholder="اكتب الشروط والأحكام الخاصة بالعقد..."></textarea>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="special_conditions" class="form-label">شروط خاصة</label>
                                        <textarea class="form-control" id="special_conditions" name="special_conditions" 
                                                  rows="4" placeholder="أي شروط خاصة أو استثناءات..."></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="pets_allowed" name="pets_allowed">
                                            <label class="form-check-label" for="pets_allowed">
                                                السماح بالحيوانات الأليفة
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="smoking_allowed" name="smoking_allowed">
                                            <label class="form-check-label" for="smoking_allowed">
                                                السماح بالتدخين
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="subletting_allowed" name="subletting_allowed">
                                            <label class="form-check-label" for="subletting_allowed">
                                                السماح بالإيجار من الباطن
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="auto_renewal" name="auto_renewal">
                                            <label class="form-check-label" for="auto_renewal">
                                                التجديد التلقائي
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information Tab -->
                        <div class="tab-pane fade" id="additional" role="tabpanel" aria-labelledby="additional-tab">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="utilities_included" class="form-label">الخدمات المشمولة</label>
                                        <textarea class="form-control" id="utilities_included" name="utilities_included" 
                                                  rows="3" placeholder="الكهرباء، الماء، الإنترنت، إلخ..."></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="maintenance_responsibility" class="form-label">مسؤولية الصيانة</label>
                                        <select class="form-select" id="maintenance_responsibility" name="maintenance_responsibility">
                                            <option value="">اختر المسؤول</option>
                                            <option value="المالك">المالك</option>
                                            <option value="المستأجر">المستأجر</option>
                                            <option value="مشتركة">مشتركة</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="parking_spaces" class="form-label">عدد مواقف السيارات</label>
                                        <input type="number" class="form-control" id="parking_spaces" name="parking_spaces" 
                                               min="0" value="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="furnished_status" class="form-label">حالة الأثاث</label>
                                        <select class="form-select" id="furnished_status" name="furnished_status">
                                            <option value="">اختر الحالة</option>
                                            <option value="مفروش">مفروش</option>
                                            <option value="نصف مفروش">نصف مفروش</option>
                                            <option value="غير مفروش">غير مفروش</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                                        <textarea class="form-control" id="notes" name="notes" 
                                                  rows="4" placeholder="أي ملاحظات أو تفاصيل إضافية..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> إنشاء العقد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Calculate lease duration when dates change
function calculateLeaseDuration() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = Math.abs(end - start);
        const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30.44)); // Average days per month
        
        document.getElementById('lease_duration_months').value = diffMonths;
        calculateTotalLeaseValue();
    }
}

// Calculate total lease value
function calculateTotalLeaseValue() {
    const monthlyRent = parseFloat(document.getElementById('monthly_rent').value) || 0;
    const duration = parseInt(document.getElementById('lease_duration_months').value) || 0;
    const total = monthlyRent * duration;
    
    document.getElementById('total_lease_value').value = total.toFixed(2);
}

// Event listeners
document.getElementById('start_date').addEventListener('change', calculateLeaseDuration);
document.getElementById('end_date').addEventListener('change', calculateLeaseDuration);
document.getElementById('monthly_rent').addEventListener('input', calculateTotalLeaseValue);

// Form validation
document.getElementById('addLeaseForm').addEventListener('submit', function(e) {
    const requiredFields = ['property_id', 'tenant_id', 'lease_type', 'start_date', 'end_date', 'monthly_rent'];
    let isValid = true;
    
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        // Switch to first tab with errors
        document.getElementById('basic-tab').click();
    }
});

// Remove validation styling on input
document.querySelectorAll('.form-control, .form-select').forEach(field => {
    field.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });
});
</script>
