#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للمعاملات المالية فقط
"""

import requests
import re
from datetime import datetime

BASE_URL = "http://localhost:5000"
LOGIN_DATA = {
    'username': 'office',
    'password': '66889088'
}

def test_financial_save():
    session = requests.Session()
    
    # تسجيل الدخول
    print("🔐 تسجيل الدخول...")
    login_page = session.get(f"{BASE_URL}/lawyersameh")
    csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', login_page.text)
    csrf_token = csrf_match.group(1) if csrf_match else None
    
    if not csrf_token:
        print("❌ لم يتم العثور على CSRF token في صفحة تسجيل الدخول")
        return
    
    login_data = LOGIN_DATA.copy()
    login_data['csrf_token'] = csrf_token
    
    response = session.post(f"{BASE_URL}/lawyersameh", data=login_data)
    if response.status_code != 200 or "dashboard" not in response.url:
        print("❌ فشل في تسجيل الدخول")
        return
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # اختبار المعاملة المالية
    print("💰 اختبار حفظ معاملة مالية جديدة...")
    
    # الحصول على CSRF token من صفحة إضافة المعاملة المالية
    add_page = session.get(f"{BASE_URL}/finance/add")
    if add_page.status_code != 200:
        print(f"❌ فشل في الوصول لصفحة إضافة المعاملة المالية: {add_page.status_code}")
        return
    
    csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', add_page.text)
    csrf_token = csrf_match.group(1) if csrf_match else None
    
    if not csrf_token:
        print("❌ لم يتم العثور على CSRF token في صفحة إضافة المعاملة المالية")
        print("محتوى الصفحة:")
        print(add_page.text[:500])
        return
    
    print(f"✅ تم الحصول على CSRF token: {csrf_token[:20]}...")
    
    transaction_data = {
        'csrf_token': csrf_token,
        'type': 'قبض',
        'amount': '500',
        'currency': 'شيكل',
        'description': f'معاملة اختبار {datetime.now().strftime("%H:%M:%S")}',
        'date': datetime.now().strftime('%Y-%m-%d'),
        'client_id': '1',
        'case_id': '1',
        'payment_method': 'نقدي',
        'reference_number': '',
        'notes': ''
    }
    
    response = session.post(f"{BASE_URL}/finance/add", data=transaction_data)
    
    if response.status_code == 200 and ("تمت إضافة المعاملة بنجاح" in response.text or "finance" in response.url):
        print("✅ تم حفظ المعاملة المالية بنجاح")
    else:
        print(f"❌ فشل في حفظ المعاملة المالية: {response.status_code}")
        print(f"URL: {response.url}")

        # البحث عن رسائل الخطأ
        if "alert-danger" in response.text:
            error_match = re.search(r'<div class="alert alert-danger[^>]*>([^<]+)', response.text)
            if error_match:
                print(f"رسالة الخطأ: {error_match.group(1).strip()}")

        print("محتوى الاستجابة:")
        print(response.text[:1000])

if __name__ == "__main__":
    test_financial_save()
