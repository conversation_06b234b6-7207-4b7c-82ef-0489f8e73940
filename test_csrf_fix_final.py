#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup

BASE_URL = "http://localhost:5000"

def test_csrf_fix():
    """اختبار إصلاح CSRF في الإضافة السريعة"""
    print("🧪 اختبار إصلاح CSRF في الإضافة السريعة...")
    
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {'username': 'office', 'password': '66889088'}
    login_response = session.post(f"{BASE_URL}/lawyersameh", data=login_data)
    
    if login_response.status_code != 200:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # اختبار modal إضافة العميل
    print("\n🔍 اختبار modal إضافة العميل...")
    modal_response = session.get(f"{BASE_URL}/modal/add_client")
    
    if modal_response.status_code != 200:
        print(f"❌ فشل في الوصول للـ modal: {modal_response.status_code}")
        return False
    
    soup = BeautifulSoup(modal_response.text, 'html.parser')
    csrf_input = soup.find('input', {'name': 'csrf_token'})
    
    if not csrf_input or not csrf_input.get('value'):
        print("❌ CSRF token مفقود في modal")
        return False
    
    csrf_token = csrf_input.get('value')
    print(f"✅ CSRF token موجود: {csrf_token[:15]}...")
    
    # اختبار إضافة عميل
    client_data = {
        'name': 'عميل اختبار CSRF',
        'phone': '0501234570',
        'email': '<EMAIL>',
        'address': 'عنوان اختبار',
        'role': 'موكل',
        'csrf_token': csrf_token
    }
    
    print("🔄 محاولة إضافة عميل...")
    add_response = session.post(f"{BASE_URL}/clients/add", data=client_data)
    
    if add_response.status_code in [200, 302]:
        print("✅ تم إضافة العميل بنجاح!")
        
        # التحقق من وجود العميل
        clients_response = session.get(f"{BASE_URL}/clients")
        if 'عميل اختبار CSRF' in clients_response.text:
            print("✅ تم التأكد من حفظ العميل في قاعدة البيانات")
            return True
        else:
            print("⚠️ العميل لم يظهر في قائمة العملاء")
            return False
    else:
        print(f"❌ فشل في إضافة العميل: {add_response.status_code}")
        if "CSRF" in add_response.text:
            print("❌ مشكلة في CSRF token")
        return False

if __name__ == "__main__":
    print("🚀 اختبار إصلاح CSRF النهائي...")
    print("=" * 50)
    
    success = test_csrf_fix()
    
    print("=" * 50)
    if success:
        print("🎉 تم إصلاح مشكلة CSRF بنجاح!")
        print("✅ الإضافة السريعة تعمل بشكل مثالي")
    else:
        print("💥 لا تزال هناك مشكلة في CSRF")
