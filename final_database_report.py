#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التقرير النهائي الشامل لحالة قاعدة البيانات والنظام
Final Comprehensive Database and System Status Report
"""

import sqlite3
import os
from datetime import datetime

def generate_final_report():
    """إنشاء التقرير النهائي الشامل"""
    print("=" * 80)
    print("📊 التقرير النهائي الشامل لحالة قاعدة البيانات والنظام")
    print("=" * 80)
    print(f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. فحص قاعدة البيانات
    print("🗄️ 1. حالة قاعدة البيانات:")
    print("-" * 40)
    
    db_path = 'instance/lawoffice.db'
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # عدد الجداول
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
        tables_count = cursor.fetchone()[0]
        print(f"   ✅ إجمالي الجداول: {tables_count}")
        
        # فحص البيانات الرئيسية
        main_tables = {
            'users': 'المستخدمين',
            'clients': 'العملاء', 
            'cases': 'القضايا',
            'properties': 'العقارات',
            'tenants': 'المستأجرين',
            'leases': 'عقود الإيجار',
            'financial_transactions': 'المعاملات المالية',
            'fees': 'الأتعاب',
            'debts': 'الديون',
            'tasks': 'المهام',
            'appointments': 'المواعيد'
        }
        
        print("\n   📋 البيانات الموجودة:")
        total_records = 0
        for table, arabic_name in main_tables.items():
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                total_records += count
                status = "✅" if count > 0 else "⚠️"
                print(f"      {status} {arabic_name}: {count} سجل")
            except Exception as e:
                print(f"      ❌ {arabic_name}: خطأ - {e}")
        
        print(f"\n   📊 إجمالي السجلات: {total_records}")
        
        # 2. فحص العلاقات
        print("\n🔗 2. فحص العلاقات بين الجداول:")
        print("-" * 40)
        
        # فحص علاقة القضايا بالعملاء
        cursor.execute("""
            SELECT c.title, cl.name 
            FROM cases c 
            JOIN clients cl ON c.client_id = cl.id 
            LIMIT 3
        """)
        case_client_relations = cursor.fetchall()
        
        if case_client_relations:
            print("   ✅ علاقة القضايا بالعملاء:")
            for case_title, client_name in case_client_relations:
                print(f"      - {case_title} ← {client_name}")
        
        # فحص علاقة عقود الإيجار
        cursor.execute("""
            SELECT l.id, p.name, t.name, l.rent_amount 
            FROM leases l 
            JOIN properties p ON l.property_id = p.id 
            JOIN tenants t ON l.tenant_id = t.id 
            LIMIT 3
        """)
        lease_relations = cursor.fetchall()
        
        if lease_relations:
            print("\n   ✅ علاقة عقود الإيجار:")
            for lease_id, property_name, tenant_name, rent in lease_relations:
                print(f"      - عقد {lease_id}: {property_name} ← {tenant_name} ({rent} شيكل)")
        
        # فحص المعاملات المالية
        cursor.execute("""
            SELECT ft.type, ft.amount, c.title 
            FROM financial_transactions ft 
            LEFT JOIN cases c ON ft.case_id = c.id 
            LIMIT 3
        """)
        financial_relations = cursor.fetchall()
        
        if financial_relations:
            print("\n   ✅ المعاملات المالية:")
            for trans_type, amount, case_title in financial_relations:
                case_info = f" ← {case_title}" if case_title else ""
                print(f"      - {trans_type}: {amount} شيكل{case_info}")
        
        # 3. فحص المستخدمين
        print("\n👥 3. حالة المستخدمين:")
        print("-" * 40)
        
        cursor.execute("SELECT username, role FROM users")
        users = cursor.fetchall()
        
        if users:
            print("   ✅ المستخدمين المسجلين:")
            for username, role in users:
                print(f"      - {username} ({role})")
        else:
            print("   ❌ لا يوجد مستخدمين مسجلين!")
        
        # 4. فحص سلامة البيانات
        print("\n🔍 4. فحص سلامة البيانات:")
        print("-" * 40)
        
        # فحص القضايا بدون عملاء
        cursor.execute("SELECT COUNT(*) FROM cases WHERE client_id IS NULL")
        cases_without_clients = cursor.fetchone()[0]
        
        # فحص العقود بدون عقارات أو مستأجرين
        cursor.execute("SELECT COUNT(*) FROM leases WHERE property_id IS NULL OR tenant_id IS NULL")
        invalid_leases = cursor.fetchone()[0]
        
        # فحص المعاملات المالية بدون مرجع
        cursor.execute("SELECT COUNT(*) FROM financial_transactions WHERE case_id IS NULL AND property_id IS NULL")
        orphaned_transactions = cursor.fetchone()[0]
        
        issues_found = 0
        
        if cases_without_clients > 0:
            print(f"   ⚠️ قضايا بدون عملاء: {cases_without_clients}")
            issues_found += 1
        else:
            print("   ✅ جميع القضايا مرتبطة بعملاء")
        
        if invalid_leases > 0:
            print(f"   ⚠️ عقود إيجار غير صحيحة: {invalid_leases}")
            issues_found += 1
        else:
            print("   ✅ جميع عقود الإيجار صحيحة")
        
        if orphaned_transactions > 0:
            print(f"   ⚠️ معاملات مالية بدون مرجع: {orphaned_transactions}")
            issues_found += 1
        else:
            print("   ✅ جميع المعاملات المالية مرتبطة")
        
        # 5. التقييم النهائي
        print("\n🎯 5. التقييم النهائي:")
        print("-" * 40)
        
        if issues_found == 0 and total_records > 0:
            print("   🎉 ممتاز! قاعدة البيانات في حالة مثالية")
            print("   ✅ جميع العلاقات صحيحة")
            print("   ✅ البيانات متسقة ومترابطة")
            print("   ✅ النظام جاهز للاستخدام الكامل")
            status = "EXCELLENT"
        elif issues_found <= 2 and total_records > 0:
            print("   👍 جيد! قاعدة البيانات تعمل بشكل جيد مع مشاكل بسيطة")
            print(f"   ⚠️ عدد المشاكل: {issues_found}")
            print("   💡 يُنصح بإصلاح المشاكل البسيطة")
            status = "GOOD"
        elif total_records == 0:
            print("   ⚠️ قاعدة البيانات فارغة!")
            print("   💡 يجب إضافة بيانات تجريبية")
            status = "EMPTY"
        else:
            print("   ❌ قاعدة البيانات تحتاج إلى إصلاح")
            print(f"   ❌ عدد المشاكل: {issues_found}")
            print("   🔧 يجب إصلاح المشاكل قبل الاستخدام")
            status = "NEEDS_REPAIR"
        
        conn.close()
        
        # 6. التوصيات
        print("\n💡 6. التوصيات:")
        print("-" * 40)
        
        if status == "EXCELLENT":
            print("   ✅ النظام جاهز للاستخدام الإنتاجي")
            print("   📊 يمكن البدء في إدخال البيانات الحقيقية")
            print("   🔄 يُنصح بعمل نسخة احتياطية دورية")
        elif status == "GOOD":
            print("   🔧 إصلاح المشاكل البسيطة المذكورة أعلاه")
            print("   ✅ بعد الإصلاح، النظام جاهز للاستخدام")
        elif status == "EMPTY":
            print("   📝 تشغيل سكريبت إضافة البيانات التجريبية")
            print("   🧪 اختبار جميع الوظائف قبل الاستخدام")
        else:
            print("   🔧 إعادة إنشاء قاعدة البيانات")
            print("   📝 تشغيل سكريبت الإصلاح الشامل")
        
        print("\n" + "=" * 80)
        print(f"📋 حالة النظام النهائية: {status}")
        print("=" * 80)
        
        return status == "EXCELLENT" or status == "GOOD"
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    success = generate_final_report()
    
    if success:
        print("\n🎉 تم إكمال المراجعة الشاملة بنجاح!")
        print("✅ النظام جاهز للاستخدام")
    else:
        print("\n⚠️ تم اكتشاف مشاكل تحتاج إلى إصلاح")
        print("🔧 يُرجى تشغيل سكريبت الإصلاح")
