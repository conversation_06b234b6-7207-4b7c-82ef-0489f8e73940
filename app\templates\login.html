<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المكتب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="{{ url_for('static', filename='css/design-system.css') }}" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-lg);
        }

        .login-container {
            background: var(--bg-card);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .login-header {
            background: var(--primary-gradient);
            color: var(--text-white);
            padding: var(--spacing-2xl);
            text-align: center;
            position: relative;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .login-logo {
            font-size: var(--text-4xl);
            margin-bottom: var(--spacing-md);
            position: relative;
            z-index: 1;
        }

        .login-title {
            font-size: var(--text-2xl);
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
            position: relative;
            z-index: 1;
        }

        .login-subtitle {
            font-size: var(--text-base);
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .login-body {
            padding: var(--spacing-2xl);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
            position: relative;
        }

        .form-control {
            padding: var(--spacing-md) var(--spacing-lg);
            padding-right: 3rem;
            font-size: var(--text-base);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            transition: all var(--transition-normal);
            background: var(--bg-secondary);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            background: var(--bg-primary);
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        .form-icon {
            position: absolute;
            right: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            font-size: var(--text-lg);
            transition: var(--transition-fast);
        }

        .form-control:focus + .form-icon {
            color: var(--primary-color);
        }

        .form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            font-size: var(--text-sm);
        }

        .login-btn {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            font-size: var(--text-lg);
            font-weight: 600;
            border-radius: var(--radius-lg);
            background: var(--primary-gradient);
            border: none;
            color: var(--text-white);
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .alert {
            border-radius: var(--radius-lg);
            border: none;
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            font-weight: 500;
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(220, 38, 38, 0.1), rgba(239, 68, 68, 0.1));
            color: var(--danger-dark);
            border-left: 4px solid var(--danger-color);
        }

        .login-footer {
            text-align: center;
            padding: var(--spacing-lg);
            background: var(--bg-tertiary);
            color: var(--text-muted);
            font-size: var(--text-sm);
        }

        /* تأثيرات الحركة */
        .login-container {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .login-container {
                margin: var(--spacing-md);
                max-width: none;
            }

            .login-header {
                padding: var(--spacing-xl);
            }

            .login-body {
                padding: var(--spacing-xl);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-logo">
                <i class="fas fa-balance-scale"></i>
            </div>
            <h1 class="login-title">مرحباً بك</h1>
            <p class="login-subtitle">نظام إدارة مكتب المحاماة</p>
        </div>

        <div class="login-body">
            {% with messages = get_flashed_messages(with_categories=true) %}
              {% if messages %}
                {% for category, message in messages %}
                  <div class="alert alert-{{ category }}">
                      <i class="fas fa-exclamation-triangle me-2"></i>
                      {{ message }}
                  </div>
                {% endfor %}
              {% endif %}
            {% endwith %}

            <form method="POST" id="loginForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-1"></i>
                        اسم المستخدم
                    </label>
                    <div class="position-relative">
                        <input type="text"
                               class="form-control"
                               id="username"
                               name="username"
                               required
                               autocomplete="username"
                               placeholder="أدخل اسم المستخدم">
                        <i class="fas fa-user form-icon"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-1"></i>
                        كلمة المرور
                    </label>
                    <div class="position-relative">
                        <input type="password"
                               class="form-control"
                               id="password"
                               name="password"
                               required
                               autocomplete="current-password"
                               placeholder="أدخل كلمة المرور">
                        <i class="fas fa-lock form-icon"></i>
                    </div>
                </div>

                <button type="submit" class="btn login-btn">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </form>
        </div>

        <div class="login-footer">
            <i class="fas fa-shield-alt me-1"></i>
            نظام آمن ومحمي
        </div>
    </div>

    <script>
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const inputs = form.querySelectorAll('.form-control');

            // تأثير التركيز على الحقول
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });

            // تأثير الإرسال
            form.addEventListener('submit', function(e) {
                const btn = this.querySelector('.login-btn');
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحقق...';
                btn.disabled = true;
            });
        });
    </script>
</body>
</html>
