#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل نهائي لجميع عمليات النظام
"""

import requests
import re
from datetime import datetime

BASE_URL = "http://localhost:5000"
LOGIN_DATA = {
    'username': 'office',
    'password': '66889088'
}

class FinalSystemTester:
    def __init__(self):
        self.session = requests.Session()
        
    def login(self):
        """تسجيل الدخول"""
        print("🔐 تسجيل الدخول...")
        login_page = self.session.get(f"{BASE_URL}/lawyersameh")
        csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', login_page.text)
        csrf_token = csrf_match.group(1) if csrf_match else None
        
        if not csrf_token:
            print("❌ لم يتم العثور على CSRF token في صفحة تسجيل الدخول")
            return False
        
        login_data = LOGIN_DATA.copy()
        login_data['csrf_token'] = csrf_token
        
        response = self.session.post(f"{BASE_URL}/lawyersameh", data=login_data)
        if response.status_code != 200 or "dashboard" not in response.url:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        return True
    
    def test_all_sections_access(self):
        """اختبار الوصول لجميع الأقسام"""
        print("\n📋 اختبار الوصول لجميع الأقسام...")
        
        sections = [
            {'name': 'العملاء', 'url': '/clients'},
            {'name': 'القضايا', 'url': '/cases'},
            {'name': 'العقارات', 'url': '/properties'},
            {'name': 'المستأجرين', 'url': '/tenants'},
            {'name': 'المواعيد', 'url': '/appointments'},
            {'name': 'المهام', 'url': '/tasks'},
            {'name': 'المعاملات المالية', 'url': '/finance'},
            {'name': 'الديون', 'url': '/debts'},
            {'name': 'الإشعارات', 'url': '/notifications'},
            {'name': 'الأرشيف', 'url': '/archive'},
            {'name': 'التقارير', 'url': '/reports'},
            {'name': 'الإعدادات', 'url': '/settings'},
            {'name': 'النسخ الاحتياطي', 'url': '/backup'}
        ]
        
        success_count = 0
        for section in sections:
            response = self.session.get(f"{BASE_URL}{section['url']}")
            if response.status_code == 200:
                print(f"  ✅ {section['name']}: يعمل")
                success_count += 1
            else:
                print(f"  ❌ {section['name']}: خطأ {response.status_code}")
        
        print(f"\n📊 نتائج الوصول للأقسام: {success_count}/{len(sections)}")
        return success_count == len(sections)
    
    def test_all_add_pages(self):
        """اختبار جميع صفحات الإضافة"""
        print("\n➕ اختبار جميع صفحات الإضافة...")
        
        add_pages = [
            {'name': 'إضافة عميل', 'url': '/clients/add'},
            {'name': 'إضافة قضية', 'url': '/cases/add'},
            {'name': 'إضافة عقار', 'url': '/properties/add'},
            {'name': 'إضافة مستأجر', 'url': '/tenants/add'},
            {'name': 'إضافة موعد', 'url': '/appointments/add'},
            {'name': 'إضافة مهمة', 'url': '/tasks/add'},
            {'name': 'إضافة معاملة مالية', 'url': '/finance/add'},
            {'name': 'إضافة دين', 'url': '/debts/add'}
        ]
        
        success_count = 0
        for page in add_pages:
            response = self.session.get(f"{BASE_URL}{page['url']}")
            if response.status_code == 200:
                # التحقق من وجود النموذج و CSRF token
                if 'form' in response.text and 'csrf_token' in response.text:
                    print(f"  ✅ {page['name']}: يعمل مع CSRF")
                    success_count += 1
                else:
                    print(f"  ⚠️ {page['name']}: يعمل لكن بدون نموذج أو CSRF")
            else:
                print(f"  ❌ {page['name']}: خطأ {response.status_code}")
        
        print(f"\n📊 نتائج صفحات الإضافة: {success_count}/{len(add_pages)}")
        return success_count == len(add_pages)
    
    def test_save_functionality(self):
        """اختبار وظائف الحفظ"""
        print("\n💾 اختبار وظائف الحفظ...")
        
        # اختبار إضافة عميل
        print("  🧪 اختبار حفظ عميل جديد...")
        add_page = self.session.get(f"{BASE_URL}/clients/add")
        csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', add_page.text)
        
        if csrf_match:
            client_data = {
                'name': f'عميل اختبار نهائي {datetime.now().strftime("%H%M%S")}',
                'phone': '0123456789',
                'email': '<EMAIL>',
                'address': 'عنوان اختبار نهائي',
                'role': 'موكل',
                'csrf_token': csrf_match.group(1)
            }
            
            response = self.session.post(f"{BASE_URL}/clients/add", data=client_data)
            if response.status_code == 200 and "clients" in response.url:
                print("    ✅ حفظ العميل يعمل بنجاح")
                return True
            else:
                print(f"    ❌ فشل في حفظ العميل: {response.status_code}")
                return False
        else:
            print("    ❌ لم يتم العثور على CSRF token")
            return False
    
    def test_edit_functionality(self):
        """اختبار وظائف التعديل"""
        print("\n✏️ اختبار وظائف التعديل...")
        
        # الحصول على قائمة العملاء
        clients_page = self.session.get(f"{BASE_URL}/clients")
        client_id_match = re.search(r'/clients/(\d+)/edit', clients_page.text)
        
        if client_id_match:
            client_id = client_id_match.group(1)
            edit_page = self.session.get(f"{BASE_URL}/clients/{client_id}/edit")
            
            if edit_page.status_code == 200:
                print(f"    ✅ صفحة تعديل العميل {client_id} تعمل")
                return True
            else:
                print(f"    ❌ فشل في الوصول لصفحة التعديل: {edit_page.status_code}")
                return False
        else:
            print("    ❌ لم يتم العثور على عميل للتعديل")
            return False
    
    def test_delete_functionality(self):
        """اختبار وظائف الحذف"""
        print("\n🗑️ اختبار وظائف الحذف...")

        # الحصول على قائمة العملاء
        clients_page = self.session.get(f"{BASE_URL}/clients")

        # البحث عن أزرار الحذف بطرق متعددة
        delete_patterns = [
            r'confirmDelete\(\d+',
            r'btn-delete',
            r'fas fa-trash',
            r'/delete'
        ]

        found_patterns = 0
        for pattern in delete_patterns:
            if re.search(pattern, clients_page.text):
                found_patterns += 1

        if found_patterns >= 2:
            print("    ✅ أزرار الحذف موجودة ومرتبطة بوظائف JavaScript")
            return True
        else:
            print(f"    ❌ أزرار الحذف غير مكتملة: وُجد {found_patterns}/{len(delete_patterns)} عناصر")
            return False
    
    def test_dashboard_functionality(self):
        """اختبار وظائف اللوحة الرئيسية"""
        print("\n🏠 اختبار وظائف اللوحة الرئيسية...")

        dashboard_page = self.session.get(f"{BASE_URL}/dashboard")
        if dashboard_page.status_code == 200:
            # التحقق من وجود العناصر الأساسية
            elements_to_check = [
                'اللوحة الرئيسية',
                'العملاء',
                'القضايا',
                'المواعيد',
                'المهام',
                'الإحصائيات',
                'dashboard',
                'card',
                'btn'
            ]

            found_elements = 0
            for element in elements_to_check:
                if element in dashboard_page.text:
                    found_elements += 1

            print(f"    📊 العناصر الموجودة: {found_elements}/{len(elements_to_check)}")

            if found_elements >= 5:
                print("    ✅ اللوحة الرئيسية تعمل بشكل جيد")
                return True
            else:
                print("    ⚠️ اللوحة الرئيسية تحتاج تحسين")
                return False
        else:
            print(f"    ❌ فشل في الوصول للوحة الرئيسية: {dashboard_page.status_code}")
            return False
    
    def run_final_test(self):
        """تشغيل الاختبار النهائي الشامل"""
        print("🚀 بدء الاختبار النهائي الشامل للنظام...")
        print("=" * 70)
        
        if not self.login():
            return False
        
        tests = [
            ('الوصول للأقسام', self.test_all_sections_access),
            ('صفحات الإضافة', self.test_all_add_pages),
            ('وظائف الحفظ', self.test_save_functionality),
            ('وظائف التعديل', self.test_edit_functionality),
            ('وظائف الحذف', self.test_delete_functionality),
            ('اللوحة الرئيسية', self.test_dashboard_functionality)
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
                results.append((test_name, False))
        
        print("\n" + "=" * 70)
        print("📊 النتائج النهائية:")
        
        success_count = 0
        for test_name, result in results:
            status = "✅ نجح" if result else "❌ فشل"
            print(f"  {status}: {test_name}")
            if result:
                success_count += 1
        
        total_tests = len(results)
        success_percentage = (success_count / total_tests) * 100
        
        print(f"\n🎯 معدل النجاح: {success_count}/{total_tests} ({success_percentage:.1f}%)")
        
        if success_percentage >= 90:
            print("🎉 النظام يعمل بشكل ممتاز!")
        elif success_percentage >= 75:
            print("✅ النظام يعمل بشكل جيد")
        elif success_percentage >= 50:
            print("⚠️ النظام يحتاج تحسينات")
        else:
            print("❌ النظام يحتاج إصلاحات جوهرية")
        
        return success_percentage >= 75

if __name__ == "__main__":
    tester = FinalSystemTester()
    success = tester.run_final_test()
    exit(0 if success else 1)
