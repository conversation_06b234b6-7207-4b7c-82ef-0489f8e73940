{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
    <h2 class="mb-4 text-center">إدارة المستخدمين</h2>
    <div class="d-flex justify-content-end mb-3">
        <a href="/users/add" class="btn btn-success"><i class="fa fa-user-plus"></i> إضافة مستخدم جديد</a>
    </div>
    <table class="table table-bordered table-striped text-center">
        <thead class="table-primary">
            <tr>
                <th>الرقم</th>
                <th>اسم المستخدم</th>
                <th>الصلاحية</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for user in users %}
            <tr>
                <td>{{ loop.index }}</td>
                <td>{{ user.username }}</td>
                <td>{{ user.role if user.role else 'غير محدد' }}</td>
                <td>
                    <a href="/users/{{ user.id }}/edit" class="btn btn-sm btn-primary"><i class="fa fa-edit"></i> تعديل</a>
                    <a href="/users/{{ user.id }}/delete" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف المستخدم؟');"><i class="fa fa-trash"></i> حذف</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
