from app import db, app
from app.models import User

def rename_user(old_username, new_username, new_password=None):
    with app.app_context():
        user = User.query.filter_by(username=old_username).first()
        if user:
            user.username = new_username
            if new_password:
                user.password = new_password
            db.session.commit()
            print(f"تم تغيير اسم المستخدم إلى {new_username} وتحديث كلمة المرور.")
        else:
            print(f"المستخدم {old_username} غير موجود.")

if __name__ == "__main__":
    rename_user("admin", "office", "66889088")
