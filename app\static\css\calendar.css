/* Calendar Styles */
.calendar-table {
    table-layout: fixed;
}

.calendar-table th,
.calendar-table td {
    width: 14.28%;
    height: 120px;
    vertical-align: top;
    padding: 5px;
    border: 1px solid #dee2e6;
}

.calendar-day {
    position: relative;
    cursor: pointer;
}

.calendar-day:hover {
    background-color: #f8f9fa;
}

.calendar-day.today {
    background-color: #e3f2fd;
}

.calendar-day.other-month {
    color: #6c757d;
    background-color: #f8f9fa;
}

.day-number {
    font-weight: bold;
    margin-bottom: 5px;
}

.calendar-event {
    font-size: 10px;
    padding: 2px 4px;
    margin: 1px 0;
    border-radius: 3px;
    color: white;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.calendar-task {
    font-size: 10px;
    padding: 2px 4px;
    margin: 1px 0;
    border-radius: 3px;
    border-left: 3px solid;
    background-color: #f8f9fa;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.priority-urgent {
    border-left-color: #dc3545 !important;
    background-color: #f8d7da !important;
}

.priority-high {
    border-left-color: #fd7e14 !important;
    background-color: #ffeaa7 !important;
}

.priority-medium {
    border-left-color: #ffc107 !important;
    background-color: #fff3cd !important;
}

.priority-low {
    border-left-color: #28a745 !important;
    background-color: #d4edda !important;
}

/* Week View Styles */
.week-view {
    display: flex;
    overflow-x: auto;
}

.time-column {
    min-width: 80px;
    border-right: 1px solid #dee2e6;
}

.day-column {
    flex: 1;
    min-width: 120px;
    border-right: 1px solid #dee2e6;
}

.day-header {
    padding: 10px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: bold;
    text-align: center;
}

.hour-slot {
    height: 40px;
    border-bottom: 1px solid #eee;
    padding: 2px;
    position: relative;
}

.week-event {
    background-color: #007bff;
    color: white;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
    margin: 1px 0;
}

/* Day View Styles */
.day-view {
    padding: 20px;
}

.event-item,
.task-item {
    transition: all 0.2s ease;
}

.event-item:hover,
.task-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Sidebar Styles */
.task-item-small,
.event-item-small {
    transition: all 0.2s ease;
}

.task-item-small:hover,
.event-item-small:hover {
    transform: translateX(-2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .calendar-table th,
    .calendar-table td {
        height: 80px;
        padding: 2px;
    }
    
    .calendar-event,
    .calendar-task {
        font-size: 8px;
        padding: 1px 2px;
    }
    
    .week-view {
        flex-direction: column;
    }
    
    .day-column {
        min-width: auto;
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }
}

/* Print Styles */
@media print {
    .calendar-table {
        page-break-inside: avoid;
    }
    
    .calendar-event,
    .calendar-task {
        background-color: #f0f0f0 !important;
        color: #000 !important;
        border: 1px solid #000 !important;
    }
    
    .week-view,
    .day-view {
        page-break-inside: avoid;
    }
}
