// تقويم عمودي للأيام السبعة القادمة مع تنبيهات داخل كل يوم
window.renderDashboardCalendar = function(tasks, installments, alerts) {
    const today = new Date();
    const days = ["الأحد","الاثنين","الثلاثاء","الأربعاء","الخميس","الجمعة","السبت"];
    let calendarHtml = '<table class="calendar-vertical-table">';
    for(let i=0;i<7;i++){
        let d = new Date(); d.setDate(today.getDate()+i);
        let dateStr = d.toISOString().slice(0,10);
        let isToday = (i === 0);
        let taskList = tasks.filter(t=>t.due_date && t.due_date.slice(0,10)===dateStr);
        let instList = installments.filter(ins=>ins.due_date && ins.due_date.slice(0,10)===dateStr);
        let hasAlert = (taskList.length > 0 || instList.length > 0 || (alerts && alerts[dateStr]));
        let tdClass = (hasAlert ? 'has-alert ' : '') + (isToday ? 'today' : '');
        calendarHtml += `<tr><td class='${tdClass}'>`;
        calendarHtml += `<div><b>${days[d.getDay()]}</b><br><span style='font-size:1.2rem'>${d.getDate()}/${d.getMonth()+1}</span></div>`;
        if(taskList.length > 0) {
            calendarHtml += `<div class='calendar-alert-content'><i class='fa fa-tasks text-success'></i> ${taskList.length} مهمة</div>`;
        }
        if(instList.length > 0) {
            calendarHtml += `<div class='calendar-alert-content'><i class='fa fa-money-bill-wave text-warning'></i> ${instList.length} قسط</div>`;
        }
        if(alerts && alerts[dateStr]) {
            calendarHtml += `<div class='calendar-alert-content'><i class='fa fa-bell text-danger'></i> ${alerts[dateStr]}</div>`;
        }
        calendarHtml += `</td></tr>`;
    }
    calendarHtml += '</table>';
    document.getElementById('calendar').innerHTML = calendarHtml;
};

document.addEventListener('DOMContentLoaded', function() {
    var calendarData = document.getElementById('calendar-data');
    if(calendarData) {
        var tasks = JSON.parse(calendarData.getAttribute('data-tasks'));
        var installments = JSON.parse(calendarData.getAttribute('data-installments'));
        var alerts = {};
        try { alerts = JSON.parse(calendarData.getAttribute('data-alerts')); } catch(e){}
        window.renderDashboardCalendar(tasks, installments, alerts);
        // إظهار التقويم إذا كان اليوم فيه تنبيه
        var todayStr = (new Date()).toISOString().slice(0,10);
        var todayHasAlert = tasks.some(t=>t.due_date && t.due_date.slice(0,10)===todayStr) || installments.some(ins=>ins.due_date && ins.due_date.slice(0,10)===todayStr) || (alerts && alerts[todayStr]);
        var calCard = document.querySelector('.calendar-card');
        if(todayHasAlert && calCard) calCard.classList.add('active-calendar');
        // إظهار عند المرور على منطقة hover
        var hoverArea = document.querySelector('.calendar-hover-area');
        if(calCard && hoverArea) {
            hoverArea.addEventListener('mouseenter', function(){ calCard.classList.add('calendar-hovered'); });
            hoverArea.addEventListener('mouseleave', function(){ if(!todayHasAlert) calCard.classList.remove('calendar-hovered'); });
        }
        // إظهار عند المرور على التقويم نفسه
        if(calCard) {
            calCard.addEventListener('mouseenter', function(){ this.classList.add('active-calendar'); });
            calCard.addEventListener('mouseleave', function(){ if(!todayHasAlert) this.classList.remove('active-calendar'); });
        }
    }
});
