{% extends 'base.html' %}
{% block title %}النسخ الاحتياطي{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-database"></i> النسخ الاحتياطي</h1>
                    <p>إدارة النسخ الاحتياطية وتصدير واستيراد البيانات</p>
                </div>
                <div class="page-actions">
                    <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>رجوع للإعدادات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- إنشاء نسخة احتياطية -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-download text-primary me-2"></i>
                        إنشاء نسخة احتياطية
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">قم بإنشاء نسخة احتياطية من جميع بيانات النظام</p>
                    
                    <form method="POST" action="{{ url_for('create_backup') }}">
                        <div class="mb-3">
                            <label class="form-label">نوع النسخة الاحتياطية</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="backup_type" value="full" id="fullBackup" checked>
                                <label class="form-check-label" for="fullBackup">
                                    <strong>نسخة كاملة</strong> - جميع البيانات والملفات
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="backup_type" value="data_only" id="dataBackup">
                                <label class="form-check-label" for="dataBackup">
                                    <strong>البيانات فقط</strong> - قاعدة البيانات فقط
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">تنسيق التصدير</label>
                            <select name="export_format" class="form-select">
                                <option value="sql">SQL Database</option>
                                <option value="json">JSON</option>
                                <option value="csv">CSV</option>
                                <option value="excel">Excel (XLSX)</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="include_files" id="includeFiles" checked>
                                <label class="form-check-label" for="includeFiles">
                                    تضمين الملفات المرفقة
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-download me-2"></i>إنشاء وتحميل النسخة الاحتياطية
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- استيراد نسخة احتياطية -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-upload text-success me-2"></i>
                        استيراد نسخة احتياطية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> استيراد نسخة احتياطية سيؤدي إلى استبدال البيانات الحالية
                    </div>
                    
                    <form method="POST" action="{{ url_for('restore_backup') }}" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label class="form-label">اختر ملف النسخة الاحتياطية</label>
                            <input type="file" name="backup_file" class="form-control" 
                                   accept=".sql,.json,.csv,.xlsx,.zip" required>
                            <div class="form-text">
                                الصيغ المدعومة: SQL, JSON, CSV, Excel, ZIP
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="confirm_restore" id="confirmRestore" required>
                                <label class="form-check-label" for="confirmRestore">
                                    أؤكد أنني أريد استبدال البيانات الحالية
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-upload me-2"></i>استيراد النسخة الاحتياطية
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- النسخ الاحتياطية المحفوظة -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history text-info me-2"></i>
                        النسخ الاحتياطية المحفوظة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th><i class="fas fa-file me-1"></i>اسم الملف</th>
                                    <th><i class="fas fa-calendar me-1"></i>تاريخ الإنشاء</th>
                                    <th><i class="fas fa-hdd me-1"></i>الحجم</th>
                                    <th><i class="fas fa-tag me-1"></i>النوع</th>
                                    <th><i class="fas fa-cogs me-1"></i>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if backups %}
                                    {% for backup in backups %}
                                    <tr>
                                        <td>
                                            <i class="fas fa-file-archive text-primary me-2"></i>
                                            {{ backup.filename }}
                                        </td>
                                        <td>
                                            <span class="badge bg-light text-dark">
                                                {{ backup.created_at.strftime('%Y-%m-%d %H:%M') if backup.created_at else '-' }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ backup.size if backup.size else '-' }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ backup.type if backup.type else 'كامل' }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ url_for('download_backup', backup_id=backup.id) }}" 
                                                   class="btn btn-outline-primary" title="تحميل">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-success" 
                                                        onclick="restoreBackup({{ backup.id }})" title="استعادة">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" 
                                                        onclick="deleteBackup({{ backup.id }})" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-2x mb-2"></i>
                                        <br>لا توجد نسخ احتياطية محفوظة
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إعدادات النسخ التلقائي -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock text-warning me-2"></i>
                        إعدادات النسخ التلقائي
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('update_auto_backup_settings') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="enable_auto_backup" 
                                           id="enableAutoBackup" {{ 'checked' if auto_backup_enabled else '' }}>
                                    <label class="form-check-label" for="enableAutoBackup">
                                        تفعيل النسخ التلقائي
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">التكرار</label>
                                <select name="backup_frequency" class="form-select">
                                    <option value="daily" {{ 'selected' if backup_frequency == 'daily' else '' }}>يومياً</option>
                                    <option value="weekly" {{ 'selected' if backup_frequency == 'weekly' else '' }}>أسبوعياً</option>
                                    <option value="monthly" {{ 'selected' if backup_frequency == 'monthly' else '' }}>شهرياً</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الوقت</label>
                                <input type="time" name="backup_time" class="form-control" 
                                       value="{{ backup_time if backup_time else '02:00' }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الاحتفاظ بـ</label>
                                <select name="keep_backups" class="form-select">
                                    <option value="5" {{ 'selected' if keep_backups == 5 else '' }}>5 نسخ</option>
                                    <option value="10" {{ 'selected' if keep_backups == 10 else '' }}>10 نسخ</option>
                                    <option value="30" {{ 'selected' if keep_backups == 30 else '' }}>30 نسخة</option>
                                    <option value="0" {{ 'selected' if keep_backups == 0 else '' }}>جميع النسخ</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>حفظ إعدادات النسخ التلقائي
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function restoreBackup(backupId) {
    if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
        window.location.href = `/backup/restore/${backupId}`;
    }
}

function deleteBackup(backupId) {
    if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
        fetch(`/backup/delete/${backupId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف النسخة الاحتياطية');
            }
        });
    }
}
</script>
{% endblock %}
