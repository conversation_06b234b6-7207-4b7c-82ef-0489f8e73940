<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة مكتب المحاماة</title>
    <meta name="description" content="نظام إدارة مكتب المحاماة - إدارة شاملة للقضايا والعملاء والمالية">

    <!-- الخطوط والأيقونات -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- نظام التصميم الأساسي -->
    <link href="{{ url_for('static', filename='css/design-system.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/components.css') }}" rel="stylesheet">

    <style>
        /* ===== تصميم الصفحة الترحيبية الاحترافية ===== */
        body {
            background: var(--primary-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-lg);
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .welcome-container {
            background: var(--bg-card);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-2xl);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .welcome-header {
            background: var(--primary-gradient);
            color: var(--text-white);
            padding: var(--spacing-2xl);
            text-align: center;
            position: relative;
        }

        .welcome-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .welcome-logo {
            font-size: 4rem;
            margin-bottom: var(--spacing-md);
            color: var(--text-white);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
        }

        .welcome-title {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            margin-bottom: var(--spacing-sm);
            position: relative;
            z-index: 1;
        }

        .welcome-subtitle {
            font-size: var(--font-size-lg);
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .welcome-body {
            padding: var(--spacing-2xl);
            text-align: center;
        }

        .welcome-description {
            font-size: var(--font-size-lg);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xl);
            line-height: 1.6;
        }

        .last-login-info {
            background: var(--bg-info);
            border: 1px solid var(--border-info);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            color: var(--text-info);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
        }

        .welcome-actions {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
            flex-wrap: wrap;
        }

        .welcome-btn {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-radius: var(--radius-lg);
            font-weight: var(--font-weight-semibold);
            text-decoration: none;
            transition: all var(--transition-normal);
            min-width: 160px;
            justify-content: center;
        }

        .welcome-btn-primary {
            background: var(--primary-gradient);
            color: var(--text-white);
            border: 2px solid transparent;
        }

        .welcome-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: var(--text-white);
        }

        .welcome-btn-secondary {
            background: transparent;
            color: var(--danger-color);
            border: 2px solid var(--danger-color);
        }

        .welcome-btn-secondary:hover {
            background: var(--danger-color);
            color: var(--text-white);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* تأثيرات الرسوم المتحركة */
        .welcome-container {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .welcome-logo {
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .welcome-container {
                margin: var(--spacing-md);
            }

            .welcome-header {
                padding: var(--spacing-xl);
            }

            .welcome-logo {
                font-size: 3rem;
            }

            .welcome-title {
                font-size: var(--font-size-xl);
            }

            .welcome-actions {
                flex-direction: column;
                align-items: center;
            }

            .welcome-btn {
                width: 100%;
                max-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <!-- رأس الصفحة -->
        <div class="welcome-header">
            <div class="welcome-logo">
                <i class="fas fa-balance-scale"></i>
            </div>
            <h1 class="welcome-title">نظام إدارة مكتب المحاماة</h1>
            <p class="welcome-subtitle">نظام مكتب المحامي سامح غسان قعدان</p>
        </div>

        <!-- محتوى الصفحة -->
        <div class="welcome-body">
            <p class="welcome-description">
                مرحباً بك في النظام المتكامل لإدارة مكتب المحاماة<br>
                نظام شامل لإدارة القضايا والعملاء والمالية والمواعيد
            </p>

            {% if last_login %}
            <div class="last-login-info">
                <i class="fas fa-clock"></i>
                <span>آخر مرة تم تسجيل الدخول فيها: <strong>{{ last_login }}</strong></span>
            </div>
            {% endif %}

            <div class="welcome-actions">
                <a href="{{ url_for('login') }}" class="welcome-btn welcome-btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>تسجيل الدخول</span>
                </a>
                <a href="{{ url_for('logout') }}" class="welcome-btn welcome-btn-secondary">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        // محاولة أخذ نسخة احتياطية عند إغلاق الصفحة
        window.addEventListener('beforeunload', function (e) {
            navigator.sendBeacon("{{ url_for('backup_db') }}");
        });

        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للأزرار
            const buttons = document.querySelectorAll('.welcome-btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.02)';
                });

                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
