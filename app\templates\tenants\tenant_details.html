{% extends "base.html" %}

{% block title %}تفاصيل المستأجر - {{ tenant.full_name }}{% endblock %}

{% block extra_css %}
<style>
    .tenant-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px 0;
        margin-bottom: 30px;
        border-radius: 0 0 20px 20px;
    }
    
    .tenant-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 5px solid white;
        box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    }
    
    .info-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }
    
    .info-card h5 {
        color: #495057;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
        margin-bottom: 20px;
        font-weight: bold;
    }
    
    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #6c757d;
        flex: 1;
    }
    
    .info-value {
        color: #495057;
        flex: 2;
        text-align: right;
        font-weight: 500;
    }
    
    .credit-score-display {
        text-align: center;
        padding: 20px;
        border-radius: 15px;
        margin: 20px 0;
    }
    
    .score-excellent { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; }
    .score-good { background: linear-gradient(135deg, #007bff 0%, #6610f2 100%); color: white; }
    .score-fair { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white; }
    .score-poor { background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); color: white; }
    
    .score-number {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .action-buttons {
        display: flex;
        gap: 15px;
        margin-bottom: 30px;
        flex-wrap: wrap;
    }
    
    .btn-action {
        flex: 1;
        min-width: 150px;
        padding: 12px 20px;
        border-radius: 10px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .lease-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        border: 2px solid #dee2e6;
    }
    
    .lease-active {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border-color: #28a745;
    }
    
    .lease-expired {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        border-color: #dc3545;
    }
    
    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: bold;
        display: inline-block;
    }
    
    .status-active { background: #28a745; color: white; }
    .status-expired { background: #dc3545; color: white; }
    .status-pending { background: #ffc107; color: #212529; }
    
    .contact-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px 0;
    }
    
    .contact-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }
    
    .phone-icon { background: #28a745; }
    .email-icon { background: #007bff; }
    .whatsapp-icon { background: #25d366; }
    
    @media print {
        .action-buttons, .btn, .no-print {
            display: none !important;
        }
        
        .tenant-header {
            background: #667eea !important;
            -webkit-print-color-adjust: exact;
        }
        
        .info-card {
            box-shadow: none;
            border: 1px solid #ddd;
            break-inside: avoid;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="tenant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2 text-center">
                    {% if tenant.photo %}
                    <img src="{{ tenant.photo }}" class="tenant-avatar" alt="{{ tenant.full_name }}">
                    {% else %}
                    <img src="{{ url_for('static', filename='images/default-avatar.png') }}" 
                         class="tenant-avatar" alt="صورة افتراضية">
                    {% endif %}
                </div>
                <div class="col-md-8">
                    <h2 class="mb-2">{{ tenant.full_name }}</h2>
                    <p class="mb-1">
                        <i class="fas fa-id-card"></i>
                        رقم الهوية: {{ tenant.id_number or 'غير محدد' }}
                    </p>
                    <p class="mb-1">
                        <i class="fas fa-flag"></i>
                        الجنسية: {{ tenant.nationality or 'غير محدد' }}
                    </p>
                    <p class="mb-0">
                        <i class="fas fa-calendar-plus"></i>
                        تاريخ التسجيل: {{ tenant.created_at.strftime('%Y-%m-%d') if tenant.created_at }}
                    </p>
                </div>
                <div class="col-md-2 text-center">
                    {% set score_class = 'score-poor' %}
                    {% if tenant.credit_score >= 800 %}
                        {% set score_class = 'score-excellent' %}
                    {% elif tenant.credit_score >= 700 %}
                        {% set score_class = 'score-good' %}
                    {% elif tenant.credit_score >= 600 %}
                        {% set score_class = 'score-fair' %}
                    {% endif %}
                    
                    <div class="credit-score-display {{ score_class }}">
                        <div class="score-number">{{ tenant.credit_score or 0 }}</div>
                        <div>التقييم الائتماني</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons no-print">
        <a href="{{ url_for('edit_tenant', id=tenant.id) }}" class="btn btn-warning btn-action">
            <i class="fas fa-edit"></i> تعديل المستأجر
        </a>
        <button class="btn btn-success btn-action" onclick="createLease()">
            <i class="fas fa-file-contract"></i> إنشاء عقد إيجار
        </button>
        <button class="btn btn-info btn-action" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة التفاصيل
        </button>
        <button class="btn btn-primary btn-action" onclick="exportTenant()">
            <i class="fas fa-download"></i> تصدير البيانات
        </button>
        <button class="btn btn-danger btn-action" onclick="deleteTenant()">
            <i class="fas fa-trash"></i> حذف المستأجر
        </button>
    </div>

    <div class="row">
        <!-- المعلومات الشخصية -->
        <div class="col-lg-6">
            <div class="info-card">
                <h5><i class="fas fa-user text-primary"></i> المعلومات الشخصية</h5>
                <div class="info-item">
                    <span class="info-label">الاسم الكامل:</span>
                    <span class="info-value">{{ tenant.full_name or 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">رقم الهوية:</span>
                    <span class="info-value">{{ tenant.id_number or 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الجنسية:</span>
                    <span class="info-value">{{ tenant.nationality or 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ الميلاد:</span>
                    <span class="info-value">{{ tenant.date_of_birth.strftime('%Y-%m-%d') if tenant.date_of_birth else 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الجنس:</span>
                    <span class="info-value">{{ tenant.gender or 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الحالة الاجتماعية:</span>
                    <span class="info-value">{{ tenant.marital_status or 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">عدد أفراد الأسرة:</span>
                    <span class="info-value">{{ tenant.family_members or 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">عدد الأطفال:</span>
                    <span class="info-value">{{ tenant.children_count or 'غير محدد' }}</span>
                </div>
            </div>
        </div>

        <!-- معلومات الاتصال -->
        <div class="col-lg-6">
            <div class="info-card">
                <h5><i class="fas fa-phone text-success"></i> معلومات الاتصال</h5>
                
                {% if tenant.phone %}
                <div class="contact-item">
                    <div class="contact-icon phone-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div>
                        <strong>الهاتف الأساسي</strong><br>
                        <a href="tel:{{ tenant.phone }}">{{ tenant.phone }}</a>
                    </div>
                </div>
                {% endif %}
                
                {% if tenant.mobile %}
                <div class="contact-item">
                    <div class="contact-icon phone-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div>
                        <strong>الجوال</strong><br>
                        <a href="tel:{{ tenant.mobile }}">{{ tenant.mobile }}</a>
                    </div>
                </div>
                {% endif %}
                
                {% if tenant.email %}
                <div class="contact-item">
                    <div class="contact-icon email-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div>
                        <strong>البريد الإلكتروني</strong><br>
                        <a href="mailto:{{ tenant.email }}">{{ tenant.email }}</a>
                    </div>
                </div>
                {% endif %}
                
                {% if tenant.whatsapp %}
                <div class="contact-item">
                    <div class="contact-icon whatsapp-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div>
                        <strong>واتساب</strong><br>
                        <a href="https://wa.me/{{ tenant.whatsapp }}" target="_blank">{{ tenant.whatsapp }}</a>
                    </div>
                </div>
                {% endif %}
                
                {% if tenant.current_address %}
                <div class="info-item">
                    <span class="info-label">العنوان الحالي:</span>
                    <span class="info-value">{{ tenant.current_address }}</span>
                </div>
                {% endif %}
                
                {% if tenant.permanent_address %}
                <div class="info-item">
                    <span class="info-label">العنوان الدائم:</span>
                    <span class="info-value">{{ tenant.permanent_address }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- معلومات العمل -->
        <div class="col-lg-6">
            <div class="info-card">
                <h5><i class="fas fa-briefcase text-info"></i> معلومات العمل</h5>
                <div class="info-item">
                    <span class="info-label">حالة العمل:</span>
                    <span class="info-value">{{ tenant.employment_status or 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">المسمى الوظيفي:</span>
                    <span class="info-value">{{ tenant.job_title or 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">جهة العمل:</span>
                    <span class="info-value">{{ tenant.employer_name or 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">هاتف العمل:</span>
                    <span class="info-value">{{ tenant.work_phone or 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">عنوان العمل:</span>
                    <span class="info-value">{{ tenant.work_address or 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ بداية العمل:</span>
                    <span class="info-value">{{ tenant.employment_start_date.strftime('%Y-%m-%d') if tenant.employment_start_date else 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">سنوات الخبرة:</span>
                    <span class="info-value">{{ tenant.work_experience_years or 'غير محدد' }}</span>
                </div>
            </div>
        </div>

        <!-- المعلومات المالية -->
        <div class="col-lg-6">
            <div class="info-card">
                <h5><i class="fas fa-dollar-sign text-warning"></i> المعلومات المالية</h5>
                <div class="info-item">
                    <span class="info-label">الراتب الشهري:</span>
                    <span class="info-value">{{ tenant.monthly_salary or 0 }} {{ tenant.salary_currency or 'JOD' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">دخل إضافي:</span>
                    <span class="info-value">{{ tenant.additional_income or 0 }} {{ tenant.salary_currency or 'JOD' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">إجمالي الدخل الشهري:</span>
                    <span class="info-value">{{ tenant.total_monthly_income or 0 }} {{ tenant.salary_currency or 'JOD' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">اسم البنك:</span>
                    <span class="info-value">{{ tenant.bank_name or 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">رقم الحساب:</span>
                    <span class="info-value">{{ tenant.bank_account_number or 'غير محدد' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">التقييم الائتماني:</span>
                    <span class="info-value">{{ tenant.credit_score or 0 }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">نسبة الدين إلى الدخل:</span>
                    <span class="info-value">{{ tenant.debt_to_income_ratio or 0 }}%</span>
                </div>
            </div>
        </div>

        <!-- العقود الحالية والسابقة -->
        <div class="col-12">
            <div class="info-card">
                <h5><i class="fas fa-file-contract text-primary"></i> عقود الإيجار</h5>
                
                {% if tenant.leases %}
                    {% for lease in tenant.leases %}
                    <div class="lease-card {% if lease.status == 'نشط' %}lease-active{% elif lease.status == 'منتهي' %}lease-expired{% endif %}">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 class="mb-2">
                                    <i class="fas fa-home"></i>
                                    {{ lease.property.name if lease.property else 'عقار محذوف' }}
                                </h6>
                                <p class="mb-1">
                                    <strong>فترة العقد:</strong> 
                                    {{ lease.start_date.strftime('%Y-%m-%d') if lease.start_date }} 
                                    إلى 
                                    {{ lease.end_date.strftime('%Y-%m-%d') if lease.end_date }}
                                </p>
                                <p class="mb-1">
                                    <strong>الإيجار الشهري:</strong> 
                                    {{ lease.monthly_rent or 0 }} {{ lease.currency or 'JOD' }}
                                </p>
                            </div>
                            <div class="col-md-4 text-end">
                                <span class="status-badge status-{{ 'active' if lease.status == 'نشط' else 'expired' if lease.status == 'منتهي' else 'pending' }}">
                                    {{ lease.status }}
                                </span>
                                <div class="mt-2">
                                    <a href="{{ url_for('lease_details', id=lease.id) }}" class="btn btn-sm btn-outline-primary">
                                        عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-file-contract fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا يوجد عقود إيجار</h6>
                        <button class="btn btn-success mt-2" onclick="createLease()">
                            <i class="fas fa-plus"></i> إنشاء عقد جديد
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- المراجع وجهات الاتصال الطارئة -->
        {% if tenant.emergency_contact_name or tenant.reference1_name %}
        <div class="col-12">
            <div class="info-card">
                <h5><i class="fas fa-users text-danger"></i> المراجع وجهات الاتصال الطارئة</h5>
                
                {% if tenant.emergency_contact_name %}
                <div class="row mb-3">
                    <div class="col-12">
                        <h6 class="text-primary">جهة الاتصال في حالات الطوارئ</h6>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">الاسم:</span>
                            <span class="info-value">{{ tenant.emergency_contact_name }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">صلة القرابة:</span>
                            <span class="info-value">{{ tenant.emergency_contact_relationship or 'غير محدد' }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">{{ tenant.emergency_contact_phone or 'غير محدد' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">العنوان:</span>
                            <span class="info-value">{{ tenant.emergency_contact_address or 'غير محدد' }}</span>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                {% if tenant.reference1_name or tenant.reference2_name %}
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary">المراجع</h6>
                    </div>
                    {% if tenant.reference1_name %}
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">المرجع الأول:</span>
                            <span class="info-value">{{ tenant.reference1_name }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">{{ tenant.reference1_phone or 'غير محدد' }}</span>
                        </div>
                    </div>
                    {% endif %}
                    {% if tenant.reference2_name %}
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">المرجع الثاني:</span>
                            <span class="info-value">{{ tenant.reference2_name }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">{{ tenant.reference2_phone or 'غير محدد' }}</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- ملاحظات إضافية -->
        {% if tenant.notes %}
        <div class="col-12">
            <div class="info-card">
                <h5><i class="fas fa-sticky-note text-secondary"></i> ملاحظات إضافية</h5>
                <p class="mb-0">{{ tenant.notes }}</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function createLease() {
    window.location.href = "{{ url_for('add_lease', tenant_id=tenant.id) }}";
}

function exportTenant() {
    window.location.href = "{{ url_for('export_tenant', id=tenant.id) }}";
}

function deleteTenant() {
    if (confirm('هل أنت متأكد من حذف هذا المستأجر؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
        fetch(`/tenants/delete/{{ tenant.id }}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = "{{ url_for('tenants') }}";
            } else {
                alert('حدث خطأ أثناء الحذف: ' + data.message);
            }
        });
    }
}
</script>
{% endblock %}
