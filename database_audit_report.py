#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تقرير مراجعة شاملة لقواعد البيانات والنماذج
Database and Models Comprehensive Audit Report
"""

import sqlite3
import os
from datetime import datetime

def audit_database_structure():
    """فحص بنية قاعدة البيانات"""
    print("=" * 80)
    print("🔍 تقرير مراجعة شاملة لقواعد البيانات والنماذج")
    print("=" * 80)
    
    db_path = 'instance/lawoffice.db'
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. فحص الجداول الموجودة
        print("\n📋 1. الجداول الموجودة في قاعدة البيانات:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        
        expected_tables = [
            'users', 'clients', 'cases', 'properties', 'tenants', 'leases',
            'financial_transactions', 'fees', 'fee_payments', 'court_fees',
            'debts', 'debt_payments', 'rental_incomes', 'expenses',
            'tasks', 'appointments', 'events', 'sessions', 'documents',
            'invoices', 'payments', 'installments', 'client_documents'
        ]
        
        print(f"   ✅ إجمالي الجداول: {len(table_names)}")
        
        missing_tables = []
        for table in expected_tables:
            if table in table_names:
                print(f"   ✅ {table}")
            else:
                print(f"   ❌ {table} - مفقود")
                missing_tables.append(table)
        
        # 2. فحص العلاقات والمفاتيح الخارجية
        print("\n🔗 2. فحص العلاقات والمفاتيح الخارجية:")
        
        relationships = {
            'cases': [('client_id', 'clients.id'), ('lawyer_id', 'users.id')],
            'leases': [('property_id', 'properties.id'), ('tenant_id', 'tenants.id')],
            'financial_transactions': [('case_id', 'cases.id'), ('client_id', 'clients.id'), 
                                     ('property_id', 'properties.id'), ('tenant_id', 'tenants.id')],
            'fees': [('case_id', 'cases.id'), ('client_id', 'clients.id')],
            'debts': [('case_id', 'cases.id'), ('client_id', 'clients.id')],
            'tasks': [('case_id', 'cases.id'), ('user_id', 'users.id')],
            'appointments': [('client_id', 'clients.id'), ('case_id', 'cases.id')],
            'installments': [('lease_id', 'leases.id')]
        }
        
        for table, expected_fks in relationships.items():
            if table in table_names:
                print(f"\n   📋 جدول {table}:")
                cursor.execute(f"PRAGMA foreign_key_list({table})")
                actual_fks = cursor.fetchall()
                
                for fk_column, target_table in expected_fks:
                    found = False
                    for fk in actual_fks:
                        if fk[3] == fk_column and f"{fk[2]}.{fk[4]}" == target_table:
                            print(f"      ✅ {fk_column} -> {target_table}")
                            found = True
                            break
                    if not found:
                        print(f"      ❌ {fk_column} -> {target_table} - مفقود")
        
        # 3. فحص البيانات الموجودة
        print("\n📊 3. فحص البيانات الموجودة:")
        main_tables = ['users', 'clients', 'cases', 'properties', 'tenants', 'leases', 'financial_transactions']
        
        for table in main_tables:
            if table in table_names:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    if count > 0:
                        print(f"   ✅ {table}: {count} سجل")
                    else:
                        print(f"   ⚠️ {table}: فارغ")
                except Exception as e:
                    print(f"   ❌ {table}: خطأ - {e}")
        
        # 4. فحص الحقول المطلوبة
        print("\n🔍 4. فحص الحقول المطلوبة:")
        
        required_fields = {
            'clients': ['id', 'name', 'national_id', 'phone', 'email', 'address', 'birth_date', 'occupation', 'notes', 'role'],
            'cases': ['id', 'case_number', 'office_case_number', 'title', 'type', 'status', 'court', 'opponent', 
                     'client_id', 'fees_total', 'fees_paid', 'fees_currency', 'client_role'],
            'properties': ['id', 'name', 'type', 'address', 'monthly_rent', 'currency', 'status', 'owner_name', 'notes'],
            'tenants': ['id', 'name', 'phone', 'email', 'national_id', 'occupation', 'notes'],
            'leases': ['id', 'property_id', 'tenant_id', 'start_date', 'end_date', 'rent_amount', 
                      'annual_rent', 'payment_frequency', 'auto_renewal']
        }
        
        for table, fields in required_fields.items():
            if table in table_names:
                print(f"\n   📋 جدول {table}:")
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                actual_fields = [col[1] for col in columns]
                
                for field in fields:
                    if field in actual_fields:
                        print(f"      ✅ {field}")
                    else:
                        print(f"      ❌ {field} - مفقود")
        
        conn.close()
        
        # 5. تقرير المشاكل والحلول
        print("\n🔧 5. المشاكل المكتشفة والحلول المقترحة:")
        
        if missing_tables:
            print(f"   ❌ جداول مفقودة: {', '.join(missing_tables)}")
            print("      💡 الحل: إعادة إنشاء قاعدة البيانات بالنماذج المحدثة")
        
        print("\n✅ انتهى فحص قاعدة البيانات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def check_model_route_consistency():
    """فحص التطابق بين النماذج والروابط"""
    print("\n🔄 6. فحص التطابق بين النماذج والروابط:")
    
    # فحص ملف النماذج
    try:
        with open('app/models.py', 'r', encoding='utf-8') as f:
            models_content = f.read()
        
        # فحص ملف الروابط
        with open('app/routes.py', 'r', encoding='utf-8') as f:
            routes_content = f.read()
        
        # البحث عن النماذج المعرفة
        import re
        model_classes = re.findall(r'class (\w+)\(db\.Model\):', models_content)
        print(f"   ✅ النماذج المعرفة: {len(model_classes)}")
        for model in model_classes:
            print(f"      - {model}")
        
        # فحص الروابط المطابقة
        route_patterns = re.findall(r"@app\.route\('([^']+)'", routes_content)
        add_routes = [route for route in route_patterns if '/add' in route]
        print(f"\n   ✅ روابط الإضافة: {len(add_routes)}")
        for route in add_routes:
            print(f"      - {route}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص التطابق: {e}")
        return False

def check_modal_templates():
    """فحص قوالب النوافذ المنبثقة"""
    print("\n🪟 7. فحص قوالب النوافذ المنبثقة:")
    
    modal_dir = 'app/templates/modals'
    if os.path.exists(modal_dir):
        modal_files = os.listdir(modal_dir)
        print(f"   ✅ النوافذ المنبثقة: {len(modal_files)}")
        for modal in modal_files:
            print(f"      - {modal}")
        
        # فحص محتوى النوافذ المنبثقة للتأكد من وجود CSRF tokens
        csrf_issues = []
        for modal in modal_files:
            if modal.endswith('.html'):
                try:
                    with open(f'{modal_dir}/{modal}', 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if 'csrf_token' not in content:
                        csrf_issues.append(modal)
                except:
                    pass
        
        if csrf_issues:
            print(f"   ⚠️ نوافذ بدون CSRF token: {', '.join(csrf_issues)}")
        else:
            print("   ✅ جميع النوافذ تحتوي على CSRF tokens")
        
        return True
    else:
        print("   ❌ مجلد النوافذ المنبثقة غير موجود")
        return False

if __name__ == "__main__":
    print(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = True
    success &= audit_database_structure()
    success &= check_model_route_consistency()
    success &= check_modal_templates()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 تم إكمال مراجعة قواعد البيانات بنجاح!")
    else:
        print("⚠️ تم اكتشاف مشاكل تحتاج إلى إصلاح")
    print("=" * 80)
