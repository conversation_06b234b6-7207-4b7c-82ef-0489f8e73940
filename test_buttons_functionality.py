#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوظائف أزرار الحفظ والتعديل والحذف
"""

import requests
import re
from datetime import datetime

BASE_URL = "http://localhost:5000"
LOGIN_DATA = {
    'username': 'office',
    'password': '66889088'
}

class ButtonsFunctionalityTester:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = []
        
    def login(self):
        """تسجيل الدخول"""
        print("🔐 تسجيل الدخول...")
        login_page = self.session.get(f"{BASE_URL}/lawyersameh")
        csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', login_page.text)
        csrf_token = csrf_match.group(1) if csrf_match else None
        
        if not csrf_token:
            print("❌ لم يتم العثور على CSRF token في صفحة تسجيل الدخول")
            return False
        
        login_data = LOGIN_DATA.copy()
        login_data['csrf_token'] = csrf_token
        
        response = self.session.post(f"{BASE_URL}/lawyersameh", data=login_data)
        if response.status_code != 200 or "dashboard" not in response.url:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        return True
    
    def test_delete_buttons(self):
        """اختبار أزرار الحذف"""
        print("\n🗑️ اختبار أزرار الحذف...")
        
        sections = [
            {'name': 'العملاء', 'list_url': '/clients', 'delete_pattern': r'confirmDelete\(\s*(\d+)'},
            {'name': 'القضايا', 'list_url': '/cases', 'delete_pattern': r'confirmDelete\(\s*(\d+)'},
            {'name': 'العقارات', 'list_url': '/properties', 'delete_pattern': r'confirmDelete\(\s*(\d+)'},
            {'name': 'المستأجرين', 'list_url': '/tenants', 'delete_pattern': r'confirmDelete\(\s*(\d+)'},
            {'name': 'المواعيد', 'list_url': '/appointments', 'delete_pattern': r'confirmDelete\(\s*(\d+)'},
            {'name': 'المهام', 'list_url': '/tasks', 'delete_pattern': r'confirmDelete\(\s*(\d+)'}
        ]
        
        for section in sections:
            print(f"  📋 فحص قسم {section['name']}...")
            response = self.session.get(f"{BASE_URL}{section['list_url']}")
            
            if response.status_code != 200:
                print(f"    ❌ فشل في الوصول لصفحة {section['name']}: {response.status_code}")
                continue
            
            # البحث عن أزرار الحذف
            delete_buttons = re.findall(r'حذف|delete', response.text, re.IGNORECASE)
            confirm_delete_functions = re.findall(r'function confirmDelete|onclick.*confirm', response.text, re.IGNORECASE)
            
            if delete_buttons and confirm_delete_functions:
                print(f"    ✅ أزرار الحذف موجودة في {section['name']}")
                
                # فحص JavaScript function
                if 'fetch(' in response.text and 'delete' in response.text.lower():
                    print(f"    ✅ وظيفة JavaScript للحذف موجودة")
                else:
                    print(f"    ❌ وظيفة JavaScript للحذف مفقودة أو معطلة")
            else:
                print(f"    ❌ أزرار الحذف مفقودة في {section['name']}")
    
    def test_edit_buttons(self):
        """اختبار أزرار التعديل"""
        print("\n✏️ اختبار أزرار التعديل...")
        
        sections = [
            {'name': 'العملاء', 'list_url': '/clients', 'edit_url_pattern': r'/edit_client/'},
            {'name': 'القضايا', 'list_url': '/cases', 'edit_url_pattern': r'/edit_case/'},
            {'name': 'العقارات', 'list_url': '/properties', 'edit_url_pattern': r'/edit_property/'},
            {'name': 'المستأجرين', 'list_url': '/tenants', 'edit_url_pattern': r'/edit_tenant/'},
            {'name': 'المواعيد', 'list_url': '/appointments', 'edit_url_pattern': r'/edit_appointment/'},
            {'name': 'المهام', 'list_url': '/tasks', 'edit_url_pattern': r'/edit_task/'}
        ]
        
        for section in sections:
            print(f"  📋 فحص قسم {section['name']}...")
            response = self.session.get(f"{BASE_URL}{section['list_url']}")
            
            if response.status_code != 200:
                print(f"    ❌ فشل في الوصول لصفحة {section['name']}: {response.status_code}")
                continue
            
            # البحث عن أزرار التعديل
            edit_buttons = re.findall(r'تعديل|edit', response.text, re.IGNORECASE)
            edit_links = re.findall(r'edit_\w+|/edit', response.text)
            
            if edit_buttons and edit_links:
                print(f"    ✅ أزرار التعديل موجودة في {section['name']}")
            else:
                print(f"    ❌ أزرار التعديل مفقودة في {section['name']}")
    
    def test_save_buttons(self):
        """اختبار أزرار الحفظ"""
        print("\n💾 اختبار أزرار الحفظ...")
        
        add_pages = [
            {'name': 'إضافة عميل', 'url': '/clients/add'},
            {'name': 'إضافة قضية', 'url': '/cases/add'},
            {'name': 'إضافة عقار', 'url': '/properties/add'},
            {'name': 'إضافة مستأجر', 'url': '/tenants/add'},
            {'name': 'إضافة معاملة مالية', 'url': '/finance/add'},
            {'name': 'إضافة موعد', 'url': '/appointments/add'},
            {'name': 'إضافة مهمة', 'url': '/tasks/add'}
        ]
        
        for page in add_pages:
            print(f"  📋 فحص صفحة {page['name']}...")
            response = self.session.get(f"{BASE_URL}{page['url']}")
            
            if response.status_code != 200:
                print(f"    ❌ فشل في الوصول لصفحة {page['name']}: {response.status_code}")
                continue
            
            # البحث عن أزرار الحفظ
            save_buttons = re.findall(r'type="submit"|btn.*submit|حفظ', response.text, re.IGNORECASE)
            csrf_tokens = re.findall(r'csrf_token', response.text)
            
            if save_buttons:
                print(f"    ✅ زر الحفظ موجود في {page['name']}")
                if csrf_tokens:
                    print(f"    ✅ CSRF token موجود")
                else:
                    print(f"    ❌ CSRF token مفقود")
            else:
                print(f"    ❌ زر الحفظ مفقود في {page['name']}")
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار وظائف الأزرار...")
        print("=" * 50)
        
        if not self.login():
            return
        
        self.test_delete_buttons()
        self.test_edit_buttons()
        self.test_save_buttons()
        
        print("\n" + "=" * 50)
        print("📊 انتهى اختبار وظائف الأزرار")

if __name__ == "__main__":
    tester = ButtonsFunctionalityTester()
    tester.run_all_tests()
