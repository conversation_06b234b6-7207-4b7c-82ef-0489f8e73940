{% extends "base.html" %}

{% block title %}إضافة عقد إيجار جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-file-contract"></i> إضافة عقد إيجار جديد</h1>
                    <p>إضافة عقد إيجار جديد مع ربط العقار والمستأجر</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-handshake text-primary me-2"></i>
                        بيانات عقد الإيجار
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="leaseForm">
                        <!-- اختيار العقار والمستأجر -->
                        <div class="section-divider">
                            <h5 class="text-primary"><i class="fa fa-building"></i> العقار والمستأجر</h5>
                        </div>
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <label class="form-label">العقار <span class="text-danger">*</span></label>
                                <select name="property_id" class="form-control" required id="propertySelect">
                                    <option value="">اختر العقار</option>
                                    {% for property in properties %}
                                    <option value="{{ property.id }}" 
                                            data-rent="{{ property.monthly_rent }}"
                                            data-deposit="{{ property.deposit_amount }}"
                                            data-commission="{{ property.commission_rate }}">
                                        {{ property.name }} - {{ property.address }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">المستأجر <span class="text-danger">*</span></label>
                                <select name="tenant_id" class="form-control" required>
                                    <option value="">اختر المستأجر</option>
                                    {% for tenant in tenants %}
                                    <option value="{{ tenant.id }}">{{ tenant.name }} - {{ tenant.phone }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <!-- تواريخ العقد -->
                        <div class="section-divider">
                            <h5 class="text-info"><i class="fa fa-calendar"></i> تواريخ العقد</h5>
                        </div>
                        <div class="row g-3 mb-3">
                            <div class="col-md-4">
                                <label class="form-label">تاريخ البداية <span class="text-danger">*</span></label>
                                <input type="date" name="start_date" class="form-control" required id="startDate">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">تاريخ النهاية <span class="text-danger">*</span></label>
                                <input type="date" name="end_date" class="form-control" required id="endDate">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">مدة العقد (بالأشهر)</label>
                                <input type="number" name="contract_duration" class="form-control" 
                                       value="12" min="1" max="120" id="contractDuration">
                            </div>
                        </div>

                        <!-- التفاصيل المالية -->
                        <div class="section-divider">
                            <h5 class="text-success"><i class="fa fa-money-bill"></i> التفاصيل المالية</h5>
                        </div>
                        <div class="row g-3 mb-3">
                            <div class="col-md-3">
                                <label class="form-label">الإيجار الشهري <span class="text-danger">*</span></label>
                                <input type="number" name="rent_amount" class="form-control" 
                                       step="0.01" min="0" required id="rentAmount">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">مبلغ التأمين</label>
                                <input type="number" name="deposit" class="form-control" 
                                       step="0.01" min="0" value="0" id="depositAmount">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">العمولة</label>
                                <input type="number" name="commission" class="form-control" 
                                       step="0.01" min="0" value="0" id="commissionAmount">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">العملة</label>
                                <select name="currency" class="form-control">
                                    <option value="شيكل">شيكل</option>
                                    <option value="دولار">دولار أمريكي</option>
                                    <option value="دينار">دينار أردني</option>
                                    <option value="يورو">يورو</option>
                                </select>
                            </div>
                        </div>

                        <!-- شروط الدفع -->
                        <div class="section-divider">
                            <h5 class="text-warning"><i class="fa fa-calendar-check"></i> شروط الدفع</h5>
                        </div>
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <label class="form-label">يوم الدفع من كل شهر</label>
                                <select name="payment_day" class="form-control">
                                    {% for day in range(1, 32) %}
                                    <option value="{{ day }}" {% if day == 1 %}selected{% endif %}>{{ day }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">نسبة غرامة التأخير (%)</label>
                                <input type="number" name="late_fee_rate" class="form-control" 
                                       step="0.01" min="0" max="100" value="0">
                            </div>
                        </div>

                        <!-- خيارات إضافية -->
                        <div class="section-divider">
                            <h5 class="text-secondary"><i class="fa fa-cog"></i> خيارات إضافية</h5>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="renewal_option" class="form-check-input" 
                                       id="renewalOption" checked>
                                <label class="form-check-label" for="renewalOption">
                                    خيار التجديد متاح
                                </label>
                            </div>
                        </div>

                        <!-- الشروط والأحكام -->
                        <div class="mb-3">
                            <label class="form-label">الشروط والأحكام</label>
                            <textarea name="terms_conditions" class="form-control" rows="4" 
                                      placeholder="الشروط والأحكام العامة للعقد..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">شروط خاصة</label>
                            <textarea name="special_terms" class="form-control" rows="3" 
                                      placeholder="شروط خاصة بهذا العقد..."></textarea>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">ملاحظات</label>
                            <textarea name="notes" class="form-control" rows="3" 
                                      placeholder="ملاحظات إضافية..."></textarea>
                        </div>

                        <!-- ملخص مالي -->
                        <div class="alert alert-info">
                            <h6><strong>ملخص مالي:</strong></h6>
                            <p><strong>إجمالي الإيجار للعقد:</strong> <span id="totalRent">0.00</span> شيكل</p>
                            <p><strong>إجمالي المبالغ المطلوبة:</strong> <span id="totalRequired">0.00</span> شيكل</p>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-success px-5 py-2">
                                <i class="fa fa-save"></i> إنشاء العقد
                            </button>
                            <a href="{{ url_for('leases_list') }}" class="btn btn-secondary px-4 py-2">
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.section-divider {
    border-bottom: 2px solid #e9ecef;
    margin: 20px 0 15px 0;
    padding-bottom: 10px;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.text-danger {
    color: #dc3545 !important;
}

.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.alert {
    border-radius: 10px;
}
</style>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تحديث البيانات عند اختيار العقار
    $('#propertySelect').change(function() {
        var selectedOption = $(this).find('option:selected');
        var rent = selectedOption.data('rent') || 0;
        var deposit = selectedOption.data('deposit') || 0;
        var commission = selectedOption.data('commission') || 0;
        
        $('#rentAmount').val(rent);
        $('#depositAmount').val(deposit);
        $('#commissionAmount').val(commission);
        
        calculateTotals();
    });
    
    // حساب تاريخ النهاية عند تغيير تاريخ البداية أو مدة العقد
    $('#startDate, #contractDuration').change(function() {
        var startDate = new Date($('#startDate').val());
        var duration = parseInt($('#contractDuration').val()) || 12;
        
        if (startDate) {
            var endDate = new Date(startDate);
            endDate.setMonth(endDate.getMonth() + duration);
            $('#endDate').val(endDate.toISOString().split('T')[0]);
        }
        
        calculateTotals();
    });
    
    // حساب الإجماليات عند تغيير المبالغ
    $('#rentAmount, #depositAmount, #commissionAmount, #contractDuration').on('input', calculateTotals);
    
    function calculateTotals() {
        var rent = parseFloat($('#rentAmount').val()) || 0;
        var deposit = parseFloat($('#depositAmount').val()) || 0;
        var commission = parseFloat($('#commissionAmount').val()) || 0;
        var duration = parseInt($('#contractDuration').val()) || 12;
        
        var totalRent = rent * duration;
        var totalRequired = totalRent + deposit + commission;
        
        $('#totalRent').text(totalRent.toFixed(2));
        $('#totalRequired').text(totalRequired.toFixed(2));
    }
});
</script>
{% endblock %}
