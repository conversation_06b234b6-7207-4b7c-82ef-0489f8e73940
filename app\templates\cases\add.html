{% extends 'base.html' %}
{% block title %}إضافة قضية جديدة{% endblock %}
{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-scale-balanced"></i> إضافة قضية جديدة</h1>
                    <p>إضافة قضية جديدة مع البيانات المالية والقانونية</p>
                </div>
                <div class="page-actions">
                    <a href="{{ url_for('cases_list') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>رجوع
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-gavel text-primary me-2"></i>
                        بيانات القضية
                    </h5>
                </div>
                <div class="card-body">
        <form method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

            <div class="form-section">
                <div class="form-section-title">
                    <i class="fa fa-gavel"></i> معلومات القضية الأساسية
                </div>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">عنوان القضية <span class="text-danger">*</span></label>
                        <input type="text" name="title" class="form-control" required
                               placeholder="عنوان القضية">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">العميل <span class="text-danger">*</span></label>
                        <select name="client_id" class="form-control" required>
                            <option value="">اختر العميل</option>
                            {% for client in clients %}
                            <option value="{{ client.id }}">{{ client.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="row g-3 mt-2">
                    <div class="col-md-6">
                        <label class="form-label">صفة الموكل <span class="text-danger">*</span></label>
                        <select name="client_role" class="form-control" required>
                            <option value="مدعي">مدعي</option>
                            <option value="مدعى عليه">مدعى عليه</option>
                            <option value="محكوم له">محكوم له</option>
                            <option value="محكوم عليه">محكوم عليه</option>
                            <option value="مشتكي">مشتكي</option>
                            <option value="متهم">متهم</option>
                            <option value="مستأنف">مستأنف</option>
                            <option value="مستأنف ضده">مستأنف ضده</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">نوع القضية</label>
                        <select name="type" class="form-control">
                            <option value="">اختر نوع القضية</option>
                            <option value="مدني">مدني</option>
                            <option value="جنائي">جنائي</option>
                            <option value="تجاري">تجاري</option>
                            <option value="عمالي">عمالي</option>
                            <option value="أحوال شخصية">أحوال شخصية</option>
                            <option value="إداري">إداري</option>
                            <option value="عقاري">عقاري</option>
                        </select>
                    </div>
                </div>
                <div class="row g-3 mt-2">
                    <div class="col-md-6">
                        <label class="form-label">رقم القضية في المحكمة</label>
                        <input type="text" name="case_number" class="form-control"
                               placeholder="رقم القضية في المحكمة">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">رقم القضية في المكتب</label>
                        <input type="text" name="office_case_number" class="form-control"
                               placeholder="رقم القضية في المكتب">
                    </div>
                </div>
            </div>
                <div class="col-md-6">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-control">
                        <option value="جارية">جارية</option>
                        <option value="مفصولة">مفصولة</option>
                        <option value="متروكة">متروكة</option>
                        <option value="مسددة">مسددة</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">اسم المحكمة</label>
                    <select name="court" class="form-control">
                        <option value="">اختر المحكمة</option>
                        <option value="محكمة صلح رام الله">محكمة صلح رام الله</option>
                        <option value="محكمة بداية رام الله">محكمة بداية رام الله</option>
                        <option value="محكمة استئناف رام الله">محكمة استئناف رام الله</option>
                        <option value="محكمة صلح نابلس">محكمة صلح نابلس</option>
                        <option value="محكمة بداية نابلس">محكمة بداية نابلس</option>
                        <option value="محكمة استئناف نابلس">محكمة استئناف نابلس</option>
                        <option value="محكمة صلح الخليل">محكمة صلح الخليل</option>
                        <option value="محكمة بداية الخليل">محكمة بداية الخليل</option>
                        <option value="محكمة استئناف الخليل">محكمة استئناف الخليل</option>
                        <option value="محكمة صلح القدس">محكمة صلح القدس</option>
                        <option value="محكمة بداية القدس">محكمة بداية القدس</option>
                        <option value="محكمة استئناف القدس">محكمة استئناف القدس</option>
                        <option value="محكمة صلح طولكرم">محكمة صلح طولكرم</option>
                        <option value="محكمة بداية طولكرم">محكمة بداية طولكرم</option>
                        <option value="محكمة صلح جنين">محكمة صلح جنين</option>
                        <option value="محكمة بداية جنين">محكمة بداية جنين</option>
                        <option value="محكمة صلح بيت لحم">محكمة صلح بيت لحم</option>
                        <option value="محكمة بداية بيت لحم">محكمة بداية بيت لحم</option>
                        <option value="محكمة صلح قلقيلية">محكمة صلح قلقيلية</option>
                        <option value="محكمة بداية قلقيلية">محكمة بداية قلقيلية</option>
                        <option value="محكمة صلح أريحا">محكمة صلح أريحا</option>
                        <option value="محكمة بداية أريحا">محكمة بداية أريحا</option>
                        <option value="محكمة صلح سلفيت">محكمة صلح سلفيت</option>
                        <option value="محكمة بداية سلفيت">محكمة بداية سلفيت</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">اسم الخصم</label>
                    <input type="text" name="opponent" class="form-control" placeholder="اسم الخصم في القضية">
                </div>

                <div class="col-12">
                    <label class="form-label">الوصف</label>
                    <textarea name="description" class="form-control" rows="3" placeholder="تفاصيل إضافية عن القضية"></textarea>
                </div>

                <!-- قسم البيانات الإضافية -->
                <div class="section-divider">
                    <h5 class="text-primary"><i class="fa fa-info-circle"></i> بيانات إضافية</h5>
                </div>
                <div class="col-md-4">
                    <label class="form-label">أولوية القضية</label>
                    <select name="priority" class="form-control">
                        <option value="منخفضة">منخفضة</option>
                        <option value="متوسطة" selected>متوسطة</option>
                        <option value="عالية">عالية</option>
                        <option value="عاجلة">عاجلة</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">قيمة القضية</label>
                    <input type="number" name="case_value" class="form-control" step="0.01" placeholder="0.00">
                </div>
                <div class="col-md-4">
                    <label class="form-label">العملة</label>
                    <select name="currency" class="form-control">
                        <option value="شيكل" selected>شيكل</option>
                        <option value="دولار">دولار أمريكي</option>
                        <option value="دينار">دينار أردني</option>
                        <option value="يورو">يورو</option>
                    </select>
                </div>
            </div>

            <!-- القسم المالي -->
            <div class="financial-section">
                <h5 class="text-success mb-3"><i class="fa fa-money-bill-wave"></i> البيانات المالية</h5>
                <div class="row g-3">
                    <!-- قسم الأتعاب -->
                    <div class="col-12">
                        <h6 class="text-info"><i class="fa fa-handshake"></i> الأتعاب</h6>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إجمالي الأتعاب</label>
                        <input type="number" name="fees_total" id="fees_total" class="form-control" step="0.01" placeholder="0.00" onchange="calculateFeesRemaining()">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">عملة الأتعاب</label>
                        <select name="fees_currency" class="form-control">
                            <option value="شيكل" selected>شيكل</option>
                            <option value="دولار">دولار أمريكي</option>
                            <option value="دينار">دينار أردني</option>
                            <option value="يورو">يورو</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">المدفوع من الأتعاب</label>
                        <input type="number" name="fees_paid" id="fees_paid" class="form-control" step="0.01" placeholder="0.00" onchange="calculateFeesRemaining()">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">المتبقي من الأتعاب</label>
                        <input type="number" id="fees_remaining" class="form-control calculated-field" readonly placeholder="0.00">
                    </div>

                    <!-- قسم الرسوم -->
                    <div class="col-12 mt-3">
                        <h6 class="text-warning"><i class="fa fa-gavel"></i> الرسوم القضائية</h6>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">إجمالي الرسوم</label>
                        <input type="number" name="court_fees_total" id="court_fees_total" class="form-control" step="0.01" placeholder="0.00" onchange="calculateCourtFeesRemaining()">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">المدفوع من الرسوم</label>
                        <input type="number" name="court_fees_paid" id="court_fees_paid" class="form-control" step="0.01" placeholder="0.00" onchange="calculateCourtFeesRemaining()">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">المتبقي من الرسوم</label>
                        <input type="number" id="court_fees_remaining" class="form-control calculated-field" readonly placeholder="0.00">
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary px-5 py-2"><i class="fa-solid fa-plus"></i> حفظ القضية</button>
                <a href="{{ url_for('cases_list') }}" class="btn btn-secondary px-4 py-2">إلغاء</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function calculateFeesRemaining() {
    const total = parseFloat(document.getElementById('fees_total').value) || 0;
    const paid = parseFloat(document.getElementById('fees_paid').value) || 0;
    const remaining = total - paid;
    document.getElementById('fees_remaining').value = remaining.toFixed(2);
}

function calculateCourtFeesRemaining() {
    const total = parseFloat(document.getElementById('court_fees_total').value) || 0;
    const paid = parseFloat(document.getElementById('court_fees_paid').value) || 0;
    const remaining = total - paid;
    document.getElementById('court_fees_remaining').value = remaining.toFixed(2);
}

// حساب المتبقي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    calculateFeesRemaining();
    calculateCourtFeesRemaining();
});
</script>
{% endblock %}
