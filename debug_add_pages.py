#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تشخيص مشاكل صفحات الإضافة
"""

import requests
from urllib.parse import urljoin

BASE_URL = 'http://localhost:5000'
LOGIN_URL = urljoin(BASE_URL, '/lawyersameh')

def login_and_test():
    """تسجيل الدخول واختبار صفحة واحدة"""
    session = requests.Session()
    
    try:
        # تسجيل الدخول
        login_data = {
            'username': 'office',
            'password': '66889088'
        }
        
        print("🔐 محاولة تسجيل الدخول...")
        login_response = session.post(LOGIN_URL, data=login_data, timeout=10)
        print(f"Login response status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print(f"❌ فشل تسجيل الدخول: {login_response.status_code}")
            return
        
        # اختبار صفحة إضافة العملاء
        print("\n🧪 اختبار صفحة إضافة العملاء...")
        clients_add_url = urljoin(BASE_URL, '/clients/add')
        response = session.get(clients_add_url, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Content Length: {len(response.text)}")
        
        if response.status_code == 200:
            print("✅ صفحة إضافة العملاء تعمل بشكل صحيح")
            # التحقق من وجود النموذج
            if 'form' in response.text and 'name' in response.text:
                print("✅ النموذج موجود في الصفحة")
            else:
                print("⚠️ النموذج قد يكون مفقود")
        elif response.status_code == 500:
            print("❌ خطأ خادم 500 - مشكلة في الكود")
            print("أول 500 حرف من الاستجابة:")
            print(response.text[:500])
        else:
            print(f"❌ خطأ غير متوقع: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    login_and_test()
