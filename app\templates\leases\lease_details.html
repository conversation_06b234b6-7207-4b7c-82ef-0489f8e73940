{% extends "base.html" %}

{% block title %}تفاصيل العقد - {{ lease.lease_number or lease.id }}{% endblock %}

{% block extra_css %}
<style>
    .lease-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px 0;
        margin-bottom: 30px;
        border-radius: 0 0 25px 25px;
        position: relative;
        overflow: hidden;
    }
    
    .lease-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.1;
    }
    
    .lease-status {
        display: inline-block;
        padding: 8px 20px;
        border-radius: 25px;
        font-weight: bold;
        font-size: 0.9rem;
        margin-bottom: 15px;
    }
    
    .status-active { background: #28a745; color: white; }
    .status-expired { background: #dc3545; color: white; }
    .status-pending { background: #ffc107; color: #212529; }
    .status-terminated { background: #6c757d; color: white; }
    
    .info-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .info-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    }
    
    .info-card h5 {
        color: #495057;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 12px;
        margin-bottom: 20px;
        font-weight: bold;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #6c757d;
        flex: 1;
    }
    
    .info-value {
        color: #495057;
        font-weight: 500;
        text-align: right;
        flex: 1;
    }
    
    .progress-card {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        text-align: center;
    }
    
    .progress-circle {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: conic-gradient(white 0deg, white var(--progress), transparent var(--progress), transparent 360deg);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        position: relative;
    }
    
    .progress-circle::before {
        content: '';
        width: 90px;
        height: 90px;
        border-radius: 50%;
        background: #28a745;
        position: absolute;
    }
    
    .progress-text {
        position: relative;
        z-index: 1;
        font-size: 1.5rem;
        font-weight: bold;
    }
    
    .action-buttons {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        justify-content: center;
        margin-bottom: 30px;
    }
    
    .btn-action {
        flex: 1;
        min-width: 150px;
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
        border: none;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .btn-edit {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: white;
    }
    
    .btn-payment {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }
    
    .btn-renew {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
    }
    
    .btn-terminate {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
    }
    
    .btn-print {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
    }
    
    .contact-link {
        color: #007bff;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .contact-link:hover {
        color: #0056b3;
        text-decoration: underline;
    }
    
    .payment-history {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .payment-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        border-left: 4px solid #28a745;
    }
    
    .payment-overdue {
        border-left-color: #dc3545;
        background: #fff5f5;
    }
    
    .payment-upcoming {
        border-left-color: #ffc107;
        background: #fffbf0;
    }
    
    .timeline-item {
        position: relative;
        padding-left: 30px;
        margin-bottom: 20px;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: 8px;
        top: 8px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #007bff;
    }
    
    .timeline-item::after {
        content: '';
        position: absolute;
        left: 13px;
        top: 20px;
        width: 2px;
        height: calc(100% + 10px);
        background: #e9ecef;
    }
    
    .timeline-item:last-child::after {
        display: none;
    }
    
    .document-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .document-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }
    
    .pdf-icon { background: #dc3545; color: white; }
    .image-icon { background: #28a745; color: white; }
    .doc-icon { background: #007bff; color: white; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="lease-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="lease-status status-{{ 'active' if lease.status == 'نشط' else 'expired' if lease.status == 'منتهي' else 'pending' if lease.status == 'معلق' else 'terminated' }}">
                        {{ lease.status }}
                    </div>
                    <h2 class="mb-2">
                        <i class="fas fa-file-contract"></i>
                        عقد رقم: {{ lease.lease_number or lease.id }}
                    </h2>
                    <p class="mb-0 opacity-75">
                        {{ lease.lease_type or 'عقد إيجار' }} - 
                        من {{ lease.start_date.strftime('%Y/%m/%d') if lease.start_date }} 
                        إلى {{ lease.end_date.strftime('%Y/%m/%d') if lease.end_date }}
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('leases') }}" class="btn btn-light">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{{ url_for('edit_lease', id=lease.id) }}" class="btn btn-action btn-edit">
            <i class="fas fa-edit"></i>
            تعديل العقد
        </a>
        <button class="btn btn-action btn-payment" onclick="addPayment()">
            <i class="fas fa-money-bill"></i>
            إضافة دفعة
        </button>
        <button class="btn btn-action btn-renew" onclick="renewLease()">
            <i class="fas fa-redo"></i>
            تجديد العقد
        </button>
        <button class="btn btn-action btn-print" onclick="printLease()">
            <i class="fas fa-print"></i>
            طباعة العقد
        </button>
        {% if lease.status == 'نشط' %}
        <button class="btn btn-action btn-terminate" onclick="terminateLease()">
            <i class="fas fa-ban"></i>
            إنهاء العقد
        </button>
        {% endif %}
    </div>

    <div class="row">
        <!-- Left Column -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="info-card">
                <h5><i class="fas fa-info-circle text-primary"></i> المعلومات الأساسية</h5>
                <div class="info-row">
                    <span class="info-label">رقم العقد:</span>
                    <span class="info-value">{{ lease.lease_number or lease.id }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">نوع العقد:</span>
                    <span class="info-value">{{ lease.lease_type or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">تاريخ البداية:</span>
                    <span class="info-value">{{ lease.start_date.strftime('%Y/%m/%d') if lease.start_date else 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">تاريخ الانتهاء:</span>
                    <span class="info-value">{{ lease.end_date.strftime('%Y/%m/%d') if lease.end_date else 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">مدة العقد:</span>
                    <span class="info-value">{{ lease.lease_duration_months or 0 }} شهر</span>
                </div>
                <div class="info-row">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value">
                        <span class="lease-status status-{{ 'active' if lease.status == 'نشط' else 'expired' if lease.status == 'منتهي' else 'pending' if lease.status == 'معلق' else 'terminated' }}">
                            {{ lease.status }}
                        </span>
                    </span>
                </div>
            </div>

            <!-- Property Information -->
            <div class="info-card">
                <h5><i class="fas fa-home text-success"></i> معلومات العقار</h5>
                {% if lease.property %}
                <div class="info-row">
                    <span class="info-label">اسم العقار:</span>
                    <span class="info-value">
                        <a href="{{ url_for('property_details', id=lease.property.id) }}" class="contact-link">
                            {{ lease.property.name }}
                        </a>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">{{ lease.property.address or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">نوع العقار:</span>
                    <span class="info-value">{{ lease.property.property_type or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">المساحة:</span>
                    <span class="info-value">{{ lease.property.area or 0 }} متر مربع</span>
                </div>
                <div class="info-row">
                    <span class="info-label">عدد الغرف:</span>
                    <span class="info-value">{{ lease.property.bedrooms or 0 }} غرفة</span>
                </div>
                {% else %}
                <p class="text-muted">العقار غير متوفر أو محذوف</p>
                {% endif %}
            </div>

            <!-- Tenant Information -->
            <div class="info-card">
                <h5><i class="fas fa-user text-info"></i> معلومات المستأجر</h5>
                {% if lease.tenant %}
                <div class="info-row">
                    <span class="info-label">الاسم الكامل:</span>
                    <span class="info-value">
                        <a href="{{ url_for('tenant_details', id=lease.tenant.id) }}" class="contact-link">
                            {{ lease.tenant.full_name }}
                        </a>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم الهوية:</span>
                    <span class="info-value">{{ lease.tenant.id_number or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value">
                        {% if lease.tenant.phone %}
                        <a href="tel:{{ lease.tenant.phone }}" class="contact-link">{{ lease.tenant.phone }}</a>
                        {% else %}
                        غير محدد
                        {% endif %}
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">البريد الإلكتروني:</span>
                    <span class="info-value">
                        {% if lease.tenant.email %}
                        <a href="mailto:{{ lease.tenant.email }}" class="contact-link">{{ lease.tenant.email }}</a>
                        {% else %}
                        غير محدد
                        {% endif %}
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">الجنسية:</span>
                    <span class="info-value">{{ lease.tenant.nationality or 'غير محدد' }}</span>
                </div>
                {% else %}
                <p class="text-muted">المستأجر غير متوفر أو محذوف</p>
                {% endif %}
            </div>

            <!-- Financial Information -->
            <div class="info-card">
                <h5><i class="fas fa-dollar-sign text-warning"></i> المعلومات المالية</h5>
                <div class="info-row">
                    <span class="info-label">الإيجار الشهري:</span>
                    <span class="info-value">{{ lease.monthly_rent or 0 }} {{ lease.currency or 'JOD' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">مبلغ التأمين:</span>
                    <span class="info-value">{{ lease.security_deposit or 0 }} {{ lease.currency or 'JOD' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">العمولة:</span>
                    <span class="info-value">{{ lease.commission_amount or 0 }} {{ lease.currency or 'JOD' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">إجمالي قيمة العقد:</span>
                    <span class="info-value">{{ lease.total_lease_value or 0 }} {{ lease.currency or 'JOD' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">دورية الدفع:</span>
                    <span class="info-value">{{ lease.payment_frequency or 'شهري' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">يوم استحقاق الدفع:</span>
                    <span class="info-value">{{ lease.payment_due_day or 1 }} من كل شهر</span>
                </div>
                <div class="info-row">
                    <span class="info-label">رسوم التأخير:</span>
                    <span class="info-value">{{ lease.late_fee_amount or 0 }} {{ lease.currency or 'JOD' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">فترة السماح:</span>
                    <span class="info-value">{{ lease.grace_period_days or 0 }} يوم</span>
                </div>
            </div>

            <!-- Terms and Conditions -->
            {% if lease.terms_and_conditions or lease.special_conditions %}
            <div class="info-card">
                <h5><i class="fas fa-file-alt text-secondary"></i> الشروط والأحكام</h5>
                {% if lease.terms_and_conditions %}
                <div class="mb-3">
                    <h6>الشروط العامة:</h6>
                    <p class="text-muted">{{ lease.terms_and_conditions }}</p>
                </div>
                {% endif %}
                {% if lease.special_conditions %}
                <div class="mb-3">
                    <h6>الشروط الخاصة:</h6>
                    <p class="text-muted">{{ lease.special_conditions }}</p>
                </div>
                {% endif %}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {{ 'checked' if lease.pets_allowed }} disabled>
                            <label class="form-check-label">السماح بالحيوانات الأليفة</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {{ 'checked' if lease.smoking_allowed }} disabled>
                            <label class="form-check-label">السماح بالتدخين</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {{ 'checked' if lease.subletting_allowed }} disabled>
                            <label class="form-check-label">السماح بالإيجار من الباطن</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {{ 'checked' if lease.auto_renewal }} disabled>
                            <label class="form-check-label">التجديد التلقائي</label>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Right Column -->
        <div class="col-lg-4">
            <!-- Progress Card -->
            {% if lease.status == 'نشط' %}
            <div class="progress-card">
                <div class="progress-circle" style="--progress: {{ (lease.progress_percentage or 0) * 3.6 }}deg;">
                    <div class="progress-text">{{ lease.progress_percentage or 0 }}%</div>
                </div>
                <h5>تقدم العقد</h5>
                <p class="mb-0 opacity-75">
                    {% if lease.days_remaining %}
                        {{ lease.days_remaining }} يوم متبقي
                    {% else %}
                        العقد منتهي
                    {% endif %}
                </p>
            </div>
            {% endif %}

            <!-- Quick Stats -->
            <div class="info-card">
                <h5><i class="fas fa-chart-bar text-primary"></i> إحصائيات سريعة</h5>
                <div class="info-row">
                    <span class="info-label">إجمالي المدفوعات:</span>
                    <span class="info-value">{{ lease.total_paid or 0 }} {{ lease.currency or 'JOD' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">المبلغ المتبقي:</span>
                    <span class="info-value">{{ lease.remaining_amount or 0 }} {{ lease.currency or 'JOD' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">عدد الدفعات:</span>
                    <span class="info-value">{{ lease.payment_count or 0 }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">الدفعات المتأخرة:</span>
                    <span class="info-value text-danger">{{ lease.overdue_payments or 0 }}</span>
                </div>
            </div>

            <!-- Recent Payments -->
            <div class="info-card">
                <h5><i class="fas fa-money-bill text-success"></i> آخر المدفوعات</h5>
                <div class="payment-history">
                    {% if lease.recent_payments %}
                        {% for payment in lease.recent_payments %}
                        <div class="payment-item {{ 'payment-overdue' if payment.status == 'متأخر' else 'payment-upcoming' if payment.status == 'قادم' }}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ payment.amount }} {{ payment.currency }}</strong>
                                    <br>
                                    <small class="text-muted">{{ payment.payment_date.strftime('%Y/%m/%d') if payment.payment_date }}</small>
                                </div>
                                <span class="badge bg-{{ 'success' if payment.status == 'مدفوع' else 'danger' if payment.status == 'متأخر' else 'warning' }}">
                                    {{ payment.status }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                    <p class="text-muted text-center">لا توجد مدفوعات</p>
                    {% endif %}
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('lease_payments', id=lease.id) }}" class="btn btn-outline-primary btn-sm">
                        عرض جميع المدفوعات
                    </a>
                </div>
            </div>

            <!-- Additional Information -->
            {% if lease.utilities_included or lease.maintenance_responsibility or lease.parking_spaces or lease.furnished_status %}
            <div class="info-card">
                <h5><i class="fas fa-plus-circle text-info"></i> معلومات إضافية</h5>
                {% if lease.utilities_included %}
                <div class="info-row">
                    <span class="info-label">الخدمات المشمولة:</span>
                    <span class="info-value">{{ lease.utilities_included }}</span>
                </div>
                {% endif %}
                {% if lease.maintenance_responsibility %}
                <div class="info-row">
                    <span class="info-label">مسؤولية الصيانة:</span>
                    <span class="info-value">{{ lease.maintenance_responsibility }}</span>
                </div>
                {% endif %}
                {% if lease.parking_spaces %}
                <div class="info-row">
                    <span class="info-label">مواقف السيارات:</span>
                    <span class="info-value">{{ lease.parking_spaces }}</span>
                </div>
                {% endif %}
                {% if lease.furnished_status %}
                <div class="info-row">
                    <span class="info-label">حالة الأثاث:</span>
                    <span class="info-value">{{ lease.furnished_status }}</span>
                </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- Notes -->
            {% if lease.notes %}
            <div class="info-card">
                <h5><i class="fas fa-sticky-note text-warning"></i> ملاحظات</h5>
                <p class="text-muted">{{ lease.notes }}</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function addPayment() {
    window.location.href = `{{ url_for('add_lease_payment', id=lease.id) }}`;
}

function renewLease() {
    if (confirm('هل تريد تجديد هذا العقد؟')) {
        window.location.href = `{{ url_for('renew_lease', id=lease.id) }}`;
    }
}

function terminateLease() {
    if (confirm('هل أنت متأكد من إنهاء هذا العقد؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`{{ url_for('terminate_lease', id=lease.id) }}`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إنهاء العقد بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ أثناء إنهاء العقد: ' + data.message);
            }
        });
    }
}

function printLease() {
    window.open(`{{ url_for('print_lease', id=lease.id) }}`, '_blank');
}

// Print styles
const printStyles = `
    @media print {
        .action-buttons, .btn, .navbar, .sidebar { display: none !important; }
        .lease-header { background: #667eea !important; -webkit-print-color-adjust: exact; }
        .info-card { break-inside: avoid; margin-bottom: 20px; }
        body { font-size: 12px; }
        h5 { font-size: 14px; }
    }
`;

const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}
