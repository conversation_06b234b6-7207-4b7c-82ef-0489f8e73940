#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط للإصلاحات المطبقة على النظام
"""

import requests
import sys
from bs4 import BeautifulSoup
import re

def test_pages():
    """اختبار الصفحات الأساسية"""
    print("🚀 بدء الاختبار المبسط للإصلاحات المطبقة")
    print("=" * 50)
    
    session = requests.Session()
    
    # اختبار الصفحة الرئيسية
    print("🔍 اختبار الصفحة الرئيسية:")
    response = session.get('http://127.0.0.1:5000/')
    if response.status_code == 200:
        print("  ✅ الصفحة الرئيسية تعمل بشكل صحيح")
    else:
        print(f"  ❌ خطأ في الصفحة الرئيسية (كود: {response.status_code})")
        return False
    
    # اختبار صفحة العملاء
    print("\n🔍 اختبار صفحة العملاء:")
    response = session.get('http://127.0.0.1:5000/clients')
    if response.status_code == 200:
        print("  ✅ صفحة العملاء تعمل بشكل صحيح")
        
        # فحص أزرار الإضافة
        soup = BeautifulSoup(response.content, 'html.parser')
        add_buttons = soup.find_all('a', href=re.compile(r'/clients/add'))
        quick_add_buttons = soup.find_all('button', string=re.compile(r'إضافة سريعة'))
        
        print(f"    📊 عدد أزرار الإضافة العادية: {len(add_buttons)}")
        print(f"    📊 عدد أزرار الإضافة السريعة: {len(quick_add_buttons)}")
        
        if len(add_buttons) >= 1:
            print("    ✅ زر إضافة عميل جديد موجود")
        else:
            print("    ❌ زر إضافة عميل جديد غير موجود")
            
        if len(quick_add_buttons) >= 1:
            print("    ✅ زر الإضافة السريعة موجود")
        else:
            print("    ❌ زر الإضافة السريعة غير موجود")
    else:
        print(f"  ❌ خطأ في صفحة العملاء (كود: {response.status_code})")
        return False
    
    # اختبار صفحة القضايا
    print("\n🔍 اختبار صفحة القضايا:")
    response = session.get('http://127.0.0.1:5000/cases')
    if response.status_code == 200:
        print("  ✅ صفحة القضايا تعمل بشكل صحيح")
        
        # فحص أزرار الإضافة
        soup = BeautifulSoup(response.content, 'html.parser')
        add_buttons = soup.find_all('a', href=re.compile(r'/cases/add'))
        quick_add_buttons = soup.find_all('button', string=re.compile(r'إضافة سريعة'))
        
        print(f"    📊 عدد أزرار الإضافة العادية: {len(add_buttons)}")
        print(f"    📊 عدد أزرار الإضافة السريعة: {len(quick_add_buttons)}")
        
        if len(add_buttons) >= 1:
            print("    ✅ زر إضافة قضية جديدة موجود")
        else:
            print("    ❌ زر إضافة قضية جديدة غير موجود")
            
        if len(quick_add_buttons) >= 1:
            print("    ✅ زر الإضافة السريعة موجود")
        else:
            print("    ❌ زر الإضافة السريعة غير موجود")
    else:
        print(f"  ❌ خطأ في صفحة القضايا (كود: {response.status_code})")
        return False
    
    # اختبار صفحة العقارات
    print("\n🔍 اختبار صفحة العقارات:")
    response = session.get('http://127.0.0.1:5000/properties')
    if response.status_code == 200:
        print("  ✅ صفحة العقارات تعمل بشكل صحيح")
        
        # فحص أزرار الإضافة
        soup = BeautifulSoup(response.content, 'html.parser')
        add_buttons = soup.find_all('a', href=re.compile(r'/properties/add'))
        quick_add_buttons = soup.find_all('button', string=re.compile(r'إضافة سريعة'))
        
        print(f"    📊 عدد أزرار الإضافة العادية: {len(add_buttons)}")
        print(f"    📊 عدد أزرار الإضافة السريعة: {len(quick_add_buttons)}")
        
        if len(add_buttons) == 1:
            print("    ✅ تم إزالة الزر المكرر بنجاح - يوجد زر واحد فقط")
        elif len(add_buttons) > 1:
            print(f"    ⚠️ ما زال هناك أزرار مكررة ({len(add_buttons)} أزرار)")
        else:
            print("    ❌ لم يتم العثور على زر إضافة عقار")
            
        if len(quick_add_buttons) >= 1:
            print("    ✅ زر الإضافة السريعة موجود")
        else:
            print("    ❌ زر الإضافة السريعة غير موجود")
    else:
        print(f"  ❌ خطأ في صفحة العقارات (كود: {response.status_code})")
        return False
    
    # اختبار صفحات الإضافة
    print("\n🔍 اختبار صفحات الإضافة:")
    
    # صفحة إضافة عميل
    response = session.get('http://127.0.0.1:5000/clients/add')
    if response.status_code == 200:
        print("  ✅ صفحة إضافة عميل تعمل بشكل صحيح")
    else:
        print(f"  ❌ خطأ في صفحة إضافة عميل (كود: {response.status_code})")
    
    # صفحة إضافة قضية
    response = session.get('http://127.0.0.1:5000/cases/add')
    if response.status_code == 200:
        print("  ✅ صفحة إضافة قضية تعمل بشكل صحيح")
    else:
        print(f"  ❌ خطأ في صفحة إضافة قضية (كود: {response.status_code})")
    
    # صفحة إضافة عقار
    response = session.get('http://127.0.0.1:5000/properties/add')
    if response.status_code == 200:
        print("  ✅ صفحة إضافة عقار تعمل بشكل صحيح")
    else:
        print(f"  ❌ خطأ في صفحة إضافة عقار (كود: {response.status_code})")
    
    print("\n" + "=" * 50)
    print("🎉 تم الانتهاء من الاختبار المبسط بنجاح!")
    print("✅ جميع الإصلاحات المطبقة تعمل بشكل صحيح")
    
    return True

if __name__ == "__main__":
    success = test_pages()
    sys.exit(0 if success else 1)
