#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تسجيل الدخول المباشر
Direct Login Test
"""

import sqlite3
import hashlib
import requests
import time
from datetime import datetime

def test_password_verification():
    """اختبار التحقق من كلمة المرور"""
    print("🔐 اختبار التحقق من كلمة المرور...")
    print("=" * 60)
    
    db_path = 'instance/lawoffice.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب بيانات المستخدمين
        cursor.execute("SELECT username, password FROM users")
        users = cursor.fetchall()
        
        for username, stored_password in users:
            print(f"\n👤 اختبار {username}:")
            print("-" * 30)
            
            if username == 'office':
                test_password = '66889088'
            elif username == 'admin':
                test_password = 'admin123'
            else:
                continue
            
            # اختبار المقارنة المباشرة
            direct_match = stored_password == test_password
            print(f"   مقارنة مباشرة: {'✅' if direct_match else '❌'}")
            
            # اختبار SHA256
            sha256_hash = hashlib.sha256(test_password.encode()).hexdigest()
            sha256_match = stored_password == sha256_hash
            print(f"   SHA256: {'✅' if sha256_match else '❌'}")
            
            # اختبار Werkzeug hash
            try:
                from werkzeug.security import check_password_hash
                werkzeug_match = check_password_hash(stored_password, test_password)
                print(f"   Werkzeug: {'✅' if werkzeug_match else '❌'}")
            except:
                print("   Werkzeug: غير متاح")
            
            print(f"   كلمة المرور المحفوظة: {stored_password[:20]}...")
            print(f"   SHA256 المتوقع: {sha256_hash[:20]}...")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار كلمة المرور: {e}")

def test_login_request():
    """اختبار طلب تسجيل الدخول"""
    print("\n🌐 اختبار طلب تسجيل الدخول...")
    print("=" * 60)
    
    try:
        # إنشاء جلسة
        session = requests.Session()
        
        # جلب صفحة تسجيل الدخول أولاً للحصول على CSRF token
        print("📄 جلب صفحة تسجيل الدخول...")
        login_page = session.get('http://127.0.0.1:5000/lawyersameh', timeout=10)
        
        if login_page.status_code != 200:
            print(f"❌ فشل في جلب صفحة تسجيل الدخول: {login_page.status_code}")
            return False
        
        print("✅ تم جلب صفحة تسجيل الدخول بنجاح")
        
        # محاولة تسجيل الدخول بحساب office
        print("\n🔑 محاولة تسجيل الدخول بحساب office...")
        
        login_data = {
            'username': 'office',
            'password': '66889088'
        }
        
        # إرسال طلب تسجيل الدخول
        response = session.post(
            'http://127.0.0.1:5000/lawyersameh',
            data=login_data,
            timeout=10,
            allow_redirects=False
        )
        
        print(f"📊 رمز الاستجابة: {response.status_code}")
        print(f"📍 الموقع المُعاد التوجيه إليه: {response.headers.get('Location', 'لا يوجد')}")
        
        if response.status_code == 302:
            # تم التوجيه - نجح تسجيل الدخول
            redirect_location = response.headers.get('Location', '')
            if 'dashboard' in redirect_location:
                print("✅ نجح تسجيل الدخول - تم التوجيه إلى لوحة التحكم")
                return True
            else:
                print(f"⚠️ تم التوجيه إلى موقع غير متوقع: {redirect_location}")
        elif response.status_code == 200:
            # لم يتم التوجيه - فشل تسجيل الدخول
            if 'بيانات الدخول غير صحيحة' in response.text:
                print("❌ فشل تسجيل الدخول - بيانات غير صحيحة")
            else:
                print("❌ فشل تسجيل الدخول - سبب غير معروف")
        else:
            print(f"❌ خطأ غير متوقع: {response.status_code}")
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def fix_password_format():
    """إصلاح تنسيق كلمة المرور"""
    print("\n🔧 إصلاح تنسيق كلمة المرور...")
    print("=" * 60)
    
    db_path = 'instance/lawoffice.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # تحديث كلمة مرور office
        office_password_sha256 = hashlib.sha256('66889088'.encode()).hexdigest()
        cursor.execute("UPDATE users SET password = ? WHERE username = ?", 
                      (office_password_sha256, 'office'))
        
        # تحديث كلمة مرور admin
        admin_password_sha256 = hashlib.sha256('admin123'.encode()).hexdigest()
        cursor.execute("UPDATE users SET password = ? WHERE username = ?", 
                      (admin_password_sha256, 'admin'))
        
        conn.commit()
        
        print("✅ تم تحديث كلمات المرور بتنسيق SHA256")
        
        # التحقق من التحديث
        cursor.execute("SELECT username, password FROM users")
        users = cursor.fetchall()
        
        for username, password in users:
            print(f"   {username}: {password[:20]}...")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح كلمة المرور: {e}")
        return False

if __name__ == "__main__":
    print("🔍 بدء اختبار تسجيل الدخول المباشر...")
    print()
    
    # اختبار التحقق من كلمة المرور
    test_password_verification()
    
    # اختبار طلب تسجيل الدخول
    login_success = test_login_request()
    
    if not login_success:
        print("\n🔧 محاولة إصلاح المشكلة...")
        if fix_password_format():
            print("\n🧪 إعادة اختبار تسجيل الدخول...")
            time.sleep(2)
            test_login_request()
    
    print("\n" + "=" * 60)
    print("✅ انتهى اختبار تسجيل الدخول المباشر")
