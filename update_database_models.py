#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import shutil
from datetime import datetime

# إضافة مسار التطبيق
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app import app, db
from app.models import *

def update_database():
    """تحديث قاعدة البيانات مع النماذج الجديدة"""
    print("🔄 بدء تحديث قاعدة البيانات...")
    
    with app.app_context():
        try:
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
            if os.path.exists('app/database.db'):
                backup_name = f'database_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
                shutil.copy2('app/database.db', f'app/{backup_name}')
                print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
            
            # إنشاء الجداول الجديدة أو تحديث الموجودة
            print("🔄 تحديث هيكل قاعدة البيانات...")
            db.create_all()
            
            # التحقق من وجود العمود الجديد في جدول properties
            try:
                result = db.engine.execute("PRAGMA table_info(properties)")
                columns = [row[1] for row in result]
                
                if 'notes' not in columns:
                    print("🔄 إضافة عمود notes لجدول properties...")
                    db.engine.execute("ALTER TABLE properties ADD COLUMN notes TEXT")
                    print("✅ تم إضافة عمود notes")
                
            except Exception as e:
                print(f"⚠️ تحذير في تحديث جدول properties: {e}")
            
            # التحقق من وجود الأعمدة الجديدة في جدول leases
            try:
                result = db.engine.execute("PRAGMA table_info(leases)")
                columns = [row[1] for row in result]
                
                new_lease_columns = [
                    ('annual_rent', 'REAL'),
                    ('payment_frequency', 'VARCHAR(50) DEFAULT "شهري"'),
                    ('auto_renewal', 'BOOLEAN DEFAULT 0')
                ]
                
                for column_name, column_type in new_lease_columns:
                    if column_name not in columns:
                        print(f"🔄 إضافة عمود {column_name} لجدول leases...")
                        db.engine.execute(f"ALTER TABLE leases ADD COLUMN {column_name} {column_type}")
                        print(f"✅ تم إضافة عمود {column_name}")
                
            except Exception as e:
                print(f"⚠️ تحذير في تحديث جدول leases: {e}")
            
            # التحقق من وجود الأعمدة الجديدة في جدول installments
            try:
                result = db.engine.execute("PRAGMA table_info(installments)")
                columns = [row[1] for row in result]
                
                new_installment_columns = [
                    ('installment_number', 'INTEGER'),
                    ('installment_type', 'VARCHAR(50) DEFAULT "إيجار"'),
                    ('late_fee', 'REAL DEFAULT 0.0'),
                    ('discount', 'REAL DEFAULT 0.0'),
                    ('notes', 'TEXT'),
                    ('payment_method', 'VARCHAR(50)'),
                    ('payment_reference', 'VARCHAR(100)'),
                    ('created_date', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
                    ('updated_date', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ]
                
                for column_name, column_type in new_installment_columns:
                    if column_name not in columns:
                        print(f"🔄 إضافة عمود {column_name} لجدول installments...")
                        db.engine.execute(f"ALTER TABLE installments ADD COLUMN {column_name} {column_type}")
                        print(f"✅ تم إضافة عمود {column_name}")
                
            except Exception as e:
                print(f"⚠️ تحذير في تحديث جدول installments: {e}")
            
            print("✅ تم تحديث قاعدة البيانات بنجاح!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
            return False

if __name__ == "__main__":
    print("🚀 بدء تحديث قاعدة البيانات...")
    print("=" * 60)
    
    success = update_database()
    
    print("=" * 60)
    if success:
        print("🎉 تم تحديث قاعدة البيانات بنجاح!")
        print("✅ النماذج الجديدة جاهزة للاستخدام")
        print("📝 تم إضافة حقول الأجرة السنوية وطرق الدفع")
        print("🔄 تم إضافة نظام الأقساط المحسن")
    else:
        print("💥 فشل في تحديث قاعدة البيانات")
