{% extends "base.html" %}

{% block title %}قائمة المهام{% endblock %}
{% block page_title %}قائمة المهام{% endblock %}

{% block content %}
<style>
    /* ===== تصميم صفحة المهام الاحترافية ===== */
    .page-header {
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-2xl);
        box-shadow: var(--shadow-md);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .page-header h1 {
        font-size: var(--text-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }

    .header-actions {
        display: flex;
        gap: var(--spacing-md);
        flex-wrap: wrap;
    }

    .tasks-table {
        background: var(--bg-card);
        border-radius: var(--radius-xl);
        overflow: hidden;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
    }

    .table {
        margin: 0;
    }

    .table thead th {
        background: var(--primary-gradient);
        color: var(--text-white);
        font-weight: var(--font-weight-semibold);
        padding: var(--spacing-lg);
        border: none;
        text-align: center;
    }

    .table tbody td {
        padding: var(--spacing-lg);
        vertical-align: middle;
        border-bottom: 1px solid var(--border-color);
        text-align: center;
    }

    .table tbody tr:hover {
        background: var(--bg-hover);
    }

    .task-status {
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-full);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
    }

    .status-pending {
        background: var(--bg-warning);
        color: var(--text-warning);
    }

    .status-completed {
        background: var(--bg-success);
        color: var(--text-success);
    }

    .task-actions {
        display: flex;
        gap: var(--spacing-sm);
        justify-content: center;
    }

    .btn-action {
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-md);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
        transition: all var(--transition-normal);
        border: none;
        cursor: pointer;
    }

    .btn-edit {
        background: var(--warning-color);
        color: var(--text-white);
    }

    .btn-edit:hover {
        background: var(--warning-hover);
        transform: translateY(-1px);
        color: var(--text-white);
    }

    .btn-delete {
        background: var(--danger-color);
        color: var(--text-white);
    }

    .btn-delete:hover {
        background: var(--danger-hover);
        transform: translateY(-1px);
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: var(--spacing-md);
            text-align: center;
        }

        .header-actions {
            flex-direction: column;
            width: 100%;
        }

        .task-actions {
            flex-direction: column;
        }
    }
</style>

<div class="page-header">
    <h1>
        <i class="fas fa-list-check"></i>
        قائمة المهام
    </h1>
    <div class="header-actions">
        <a href="{{ url_for('add_task') }}" class="btn btn-success">
            <i class="fas fa-plus"></i>
            إضافة مهمة جديدة
        </a>
        <a href="{{ url_for('manage_tasks') }}" class="btn btn-outline-secondary">
            <i class="fas fa-cogs"></i>
            إدارة المهام
        </a>
    </div>
</div>
    <div class="table-responsive">
    <table class="table table-bordered table-hover align-middle text-center bg-white shadow-sm">
        <thead class="table-info">
            <tr>
                <th>العنوان</th>
                <th>الوصف</th>
                <th>تاريخ الاستحقاق</th>
                <th>الحالة</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
        {% for task in tasks %}
            <tr>
                <td>{{ task.title }}</td>
                <td>{{ task.description or '' }}</td>
                <td>{{ task.due_date.strftime('%Y-%m-%d %H:%M') if task.due_date else '' }}</td>
                <td>{{ task.status }}</td>
                <td>
                    <div class="task-actions">
                        <a href="{{ url_for('edit_task', task_id=task.id) }}" class="btn-action btn-edit">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </a>
                        <form action="{{ url_for('delete_task', task_id=task.id) }}" method="post" style="display:inline;">
                            {{ csrf_token() }}
                            <button type="submit" class="btn-action btn-delete" onclick="return confirm('هل أنت متأكد من الحذف؟');">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </form>
                    </div>
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
