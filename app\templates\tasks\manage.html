{% extends 'base.html' %}
{% block title %}إدارة المهام{% endblock %}
{% block content %}
<div class="container mt-4" dir="rtl">
    <h2 class="mb-4">إدارة المهام</h2>
    <div class="mb-3">
        <a href="{{ url_for('manage_tasks') }}" class="btn btn-secondary {% if not status_filter %}active{% endif %}">الكل</a>
        <a href="{{ url_for('manage_tasks', status='not_done') }}" class="btn btn-warning {% if status_filter == 'not_done' %}active{% endif %}">غير منجزة</a>
        <a href="{{ url_for('manage_tasks', status='done') }}" class="btn btn-success {% if status_filter == 'done' %}active{% endif %}">منجزة</a>
        <a href="{{ url_for('manage_tasks', status='deleted') }}" class="btn btn-danger {% if status_filter == 'deleted' %}active{% endif %}">المهام المحذوفة</a>
        <a href="{{ url_for('add_task') }}" class="btn btn-primary float-start">إضافة مهمة جديدة</a>
        <a href="{{ url_for('tasks_list') }}" class="btn btn-outline-info float-start me-2"><i class="fa fa-list"></i> قائمة المهام</a>
    </div>
    <table class="table table-bordered table-hover bg-white text-center align-middle">
        <thead class="table-dark">
            <tr>
                <th>العنوان</th>
                <th>الوصف</th>
                <th>تاريخ الاستحقاق</th>
                <th>الحالة</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for task in tasks %}
            <tr {% if task.status == 'منجزة' %}class="table-success"{% elif task.due_date and task.due_date < current_time and task.status != 'منجزة' %}class="table-danger"{% endif %}>
                <td>{{ task.title }}</td>
                <td>{{ task.description }}</td>
                <td>{{ task.due_date.strftime('%Y-%m-%d %H:%M') if task.due_date else '-' }}</td>
                <td>
                    {% if task.status == 'منجزة' %}
                        <span class="badge bg-success">منجزة</span>
                    {% else %}
                        <span class="badge bg-warning text-dark">غير منجزة</span>
                    {% endif %}
                </td>
                <td>
                    <form method="post" action="{{ url_for('toggle_task_status', task_id=task.id) }}" style="display:inline;">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <button type="submit" class="btn btn-sm {% if task.status == 'منجزة' %}btn-warning{% else %}btn-success{% endif %}" title="تبديل حالة الإنجاز">
                            {% if task.status == 'منجزة' %}إلغاء الإنجاز{% else %}إنجاز{% endif %}
                        </button>
                    </form>
                    <a href="{{ url_for('edit_task', task_id=task.id) }}" class="btn btn-sm btn-info">تعديل</a>
                    <form method="post" action="{{ url_for('delete_task', task_id=task.id) }}" style="display:inline;" onsubmit="return confirm('هل أنت متأكد من حذف المهمة؟');">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <button type="submit" class="btn btn-sm btn-danger">حذف</button>
                    </form>
                </td>
            </tr>
            {% else %}
            <tr><td colspan="5">لا توجد مهام حالياً.</td></tr>
            {% endfor %}
        </tbody>
    </table>
    <div class="alert alert-info mt-4">
        <ul class="mb-0">
            <li>المهام المنجزة تظهر باللون الأخضر.</li>
            <li>المهام المتأخرة تظهر باللون الأحمر.</li>
            <li>يمكنك تبديل حالة المهمة مباشرة من الجدول.</li>
            <li>استخدم الفلاتر أعلاه لعرض المهام حسب الحالة.</li>
        </ul>
    </div>
</div>
{% endblock %}
