#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import shutil
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

# إضافة مسار التطبيق
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app import app, db
from app.models import *

def recreate_database_with_enhancements():
    """إعادة إنشاء قاعدة البيانات مع التحسينات الجديدة"""
    print("🔄 بدء إعادة إنشاء قاعدة البيانات...")
    
    with app.app_context():
        try:
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
            if os.path.exists('app/database.db'):
                backup_name = f'database_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
                shutil.copy2('app/database.db', f'app/{backup_name}')
                print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
                
                # حذف قاعدة البيانات القديمة
                os.remove('app/database.db')
                print("🗑️ تم حذف قاعدة البيانات القديمة")
            
            # إنشاء قاعدة البيانات الجديدة
            print("🔄 إنشاء قاعدة البيانات الجديدة...")
            db.create_all()
            print("✅ تم إنشاء هيكل قاعدة البيانات")
            
            # إضافة بيانات تجريبية محسنة
            print("📝 إضافة بيانات تجريبية...")
            
            # إضافة عملاء
            clients = [
                Client(name='أحمد محمد علي', phone='0501234567', email='<EMAIL>', 
                      address='رام الله - البيرة', role='موكل', birth_date=datetime(1980, 5, 15),
                      occupation='مهندس', notes='عميل مهم'),
                Client(name='فاطمة أحمد سالم', phone='0509876543', email='<EMAIL>',
                      address='نابلس - المدينة', role='موكل', birth_date=datetime(1985, 8, 22),
                      occupation='طبيبة', notes='عميلة دائمة'),
                Client(name='محمد سعيد خالد', phone='0507654321', email='<EMAIL>',
                      address='الخليل - الدوار', role='موكل', birth_date=datetime(1975, 12, 10),
                      occupation='تاجر', notes='عميل تجاري')
            ]
            
            for client in clients:
                db.session.add(client)
            
            # إضافة قضايا
            cases = [
                Case(title='قضية ملكية عقار', case_number='2024/001', type='مدني', 
                    status='جارية', court='محكمة رام الله الابتدائية', 
                    opponent='شركة الإنشاءات المتحدة', description='نزاع حول ملكية قطعة أرض',
                    client_id=1, client_role='مدعي'),
                Case(title='قضية تعويض أضرار', case_number='2024/002', type='مدني',
                    status='معلقة', court='محكمة نابلس الابتدائية',
                    opponent='شركة التأمين الوطنية', description='مطالبة بتعويض أضرار حادث',
                    client_id=2, client_role='مدعي'),
                Case(title='قضية عقد عمل', case_number='2024/003', type='عمالي',
                    status='جارية', court='محكمة العمل - الخليل',
                    opponent='شركة الصناعات الغذائية', description='نزاع حول فصل تعسفي',
                    client_id=3, client_role='مدعي')
            ]
            
            for case in cases:
                db.session.add(case)
            
            # إضافة عقارات محسنة
            properties = [
                Property(name='شقة سكنية - رام الله', type='شقة', address='رام الله - حي الماصيون',
                        description='شقة 3 غرف وصالة', area=120.0, rooms_count=3, bathrooms_count=2,
                        monthly_rent=800.0, currency='دولار', status='متاح',
                        owner_name='خالد أحمد', owner_phone='0501111111',
                        notes='شقة مفروشة بالكامل'),
                Property(name='مكتب تجاري - نابلس', type='مكتب', address='نابلس - شارع فيصل',
                        description='مكتب للإيجار', area=80.0, rooms_count=4, bathrooms_count=1,
                        monthly_rent=600.0, currency='دولار', status='متاح',
                        owner_name='سعد محمد', owner_phone='0502222222',
                        notes='مكتب في موقع ممتاز'),
                Property(name='محل تجاري - الخليل', type='محل', address='الخليل - شارع الشلالة',
                        description='محل في الطابق الأرضي', area=50.0, rooms_count=1, bathrooms_count=1,
                        monthly_rent=400.0, currency='دولار', status='مؤجر',
                        owner_name='عمر سالم', owner_phone='0503333333',
                        notes='محل في منطقة تجارية')
            ]
            
            for property in properties:
                db.session.add(property)
            
            # إضافة مستأجرين
            tenants = [
                Tenant(name='يوسف علي أحمد', phone='0504444444', email='<EMAIL>',
                      address='رام الله - البيرة', national_id='123456789',
                      occupation='موظف', emergency_contact='أحمد علي - 0505555555'),
                Tenant(name='مريم سعد خليل', phone='0506666666', email='<EMAIL>',
                      address='نابلس - رفيديا', national_id='987654321',
                      occupation='مدرسة', emergency_contact='سعد خليل - 0507777777')
            ]
            
            for tenant in tenants:
                db.session.add(tenant)
            
            db.session.commit()
            print("✅ تم إضافة البيانات الأساسية")
            
            # إضافة عقود إيجار محسنة
            print("📋 إضافة عقود الإيجار...")
            
            lease1 = Lease(
                property_id=1, tenant_id=1,
                start_date=datetime(2024, 1, 1),
                end_date=datetime(2024, 12, 31),
                rent_amount=800.0, annual_rent=9600.0,
                deposit=1600.0, commission=400.0,
                currency='دولار', contract_duration=12,
                payment_day=1, payment_frequency='شهري',
                auto_renewal=True, status='نشط',
                terms_conditions='شروط عامة للإيجار',
                notes='عقد إيجار سنوي'
            )
            
            lease2 = Lease(
                property_id=3, tenant_id=2,
                start_date=datetime(2024, 3, 1),
                end_date=datetime(2025, 2, 28),
                rent_amount=400.0, annual_rent=4800.0,
                deposit=800.0, commission=200.0,
                currency='دولار', contract_duration=12,
                payment_day=1, payment_frequency='ربع سنوي',
                auto_renewal=False, status='نشط',
                terms_conditions='شروط خاصة للمحل التجاري',
                notes='عقد محل تجاري'
            )
            
            db.session.add(lease1)
            db.session.add(lease2)
            db.session.commit()
            
            # توليد الأقساط للعقود
            print("💰 توليد أقساط الإيجار...")
            lease1.generate_installments()
            lease2.generate_installments()
            
            print("✅ تم إنشاء قاعدة البيانات المحسنة بنجاح!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    print("🚀 بدء إعادة إنشاء قاعدة البيانات المحسنة...")
    print("=" * 60)
    
    success = recreate_database_with_enhancements()
    
    print("=" * 60)
    if success:
        print("🎉 تم إنشاء قاعدة البيانات المحسنة بنجاح!")
        print("✅ النماذج الجديدة جاهزة للاستخدام")
        print("📝 تم إضافة حقول الأجرة السنوية وطرق الدفع")
        print("🔄 تم إضافة نظام الأقساط المحسن")
        print("📋 تم إنشاء عقود إيجار تجريبية مع أقساط")
        print("🏠 تم إضافة عقارات ومستأجرين محسنين")
    else:
        print("💥 فشل في إنشاء قاعدة البيانات")
