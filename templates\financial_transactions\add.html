{% extends "base.html" %}

{% block title %}إضافة سند مالي{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إضافة سند مالي جديد</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('add_financial_transaction') }}">
                        {{ csrf_token() }}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="type">نوع المعاملة</label>
                                    <select class="form-control" id="type" name="type" required>
                                        <option value="">اختر نوع المعاملة</option>
                                        <option value="قبض">قبض</option>
                                        <option value="دفعة">دفعة</option>
                                        <option value="أتعاب">أتعاب</option>
                                        <option value="تحصيل إيجار">تحصيل إيجار</option>
                                        <option value="صرف">صرف</option>
                                        <option value="مصاريف مكتب">مصاريف مكتب</option>
                                        <option value="رسوم">رسوم</option>
                                        <option value="مصاريف">مصاريف</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="amount">المبلغ</label>
                                    <input type="number" step="0.01" class="form-control" id="amount" name="amount" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="currency">العملة</label>
                                    <select class="form-control" id="currency" name="currency">
                                        <option value="شيكل">شيكل</option>
                                        <option value="دولار">دولار</option>
                                        <option value="دينار">دينار</option>
                                        <option value="يورو">يورو</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="date">التاريخ</label>
                                    <input type="date" class="form-control" id="date" name="date">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="client_id">العميل (اختياري)</label>
                                    <select class="form-control" id="client_id" name="client_id">
                                        <option value="">اختر العميل</option>
                                        {% for client in clients %}
                                        <option value="{{ client.id }}">{{ client.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="case_id">القضية (اختياري)</label>
                                    <select class="form-control" id="case_id" name="case_id">
                                        <option value="">اختر القضية</option>
                                        {% for case in cases %}
                                        <option value="{{ case.id }}">{{ case.case_number }} - {{ case.title }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="payment_method">طريقة الدفع</label>
                                    <select class="form-control" id="payment_method" name="payment_method">
                                        <option value="نقدي">نقدي</option>
                                        <option value="شيك">شيك</option>
                                        <option value="تحويل بنكي">تحويل بنكي</option>
                                        <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="reference_number">رقم المرجع</label>
                                    <input type="text" class="form-control" id="reference_number" name="reference_number">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="notes">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">حفظ السند المالي</button>
                            <a href="{{ url_for('finance_dashboard') }}" class="btn btn-secondary">إلغاء</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تحديث القضايا عند اختيار العميل
    $('#client_id').change(function() {
        var clientId = $(this).val();
        if (clientId) {
            $.get('/api/cases/by_client/' + clientId, function(data) {
                $('#case_id').empty().append('<option value="">اختر القضية</option>');
                data.cases.forEach(function(case_item) {
                    $('#case_id').append('<option value="' + case_item.id + '">' + case_item.case_number + ' - ' + case_item.title + '</option>');
                });
            });
        } else {
            $('#case_id').empty().append('<option value="">اختر القضية</option>');
        }
    });
});
</script>
{% endblock %}
