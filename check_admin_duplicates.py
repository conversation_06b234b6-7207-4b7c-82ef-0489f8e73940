from app import db, app
from app.models import User

def check_admin_duplicates():
    with app.app_context():
        admins = User.query.filter_by(username="admin").all()
        print(f"عدد المستخدمين باسم admin: {len(admins)}")
        for i, user in enumerate(admins, 1):
            print(f"admin #{i}: id={user.id}, password={user.password}")

if __name__ == "__main__":
    check_admin_duplicates()
