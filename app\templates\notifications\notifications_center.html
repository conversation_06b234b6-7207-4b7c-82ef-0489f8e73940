{% extends "base.html" %}

{% block title %}مركز التنبيهات{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-primary mb-1">
                        <i class="fas fa-bell me-2"></i>
                        مركز التنبيهات والإشعارات
                    </h2>
                    <p class="text-muted">إدارة جميع التنبيهات والإشعارات المتعلقة بالعقارات والمستأجرين</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="markAllAsRead()">
                        <i class="fas fa-check-double me-2"></i>
                        تحديد الكل كمقروء
                    </button>
                    <button class="btn btn-success" onclick="openNotificationSettings()">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات التنبيهات
                    </button>
                </div>
            </div>

            <!-- Notification Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="urgentCount">0</h4>
                                    <p class="mb-0">عاجل</p>
                                </div>
                                <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="highCount">0</h4>
                                    <p class="mb-0">مهم</p>
                                </div>
                                <i class="fas fa-exclamation fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="unreadCount">0</h4>
                                    <p class="mb-0">غير مقروء</p>
                                </div>
                                <i class="fas fa-envelope fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="totalCount">0</h4>
                                    <p class="mb-0">إجمالي التنبيهات</p>
                                </div>
                                <i class="fas fa-bell fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التنبيهات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">نوع التنبيه</label>
                            <select class="form-select" id="typeFilter">
                                <option value="">جميع الأنواع</option>
                                <option value="lease_expiry">انتهاء العقود</option>
                                <option value="payment_due">استحقاق المدفوعات</option>
                                <option value="maintenance_due">موعد الصيانة</option>
                                <option value="tenant_review">مراجعة المستأجر</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الأولوية</label>
                            <select class="form-select" id="priorityFilter">
                                <option value="">جميع الأولويات</option>
                                <option value="urgent">عاجل</option>
                                <option value="high">مهم</option>
                                <option value="medium">متوسط</option>
                                <option value="low">منخفض</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="unread">غير مقروء</option>
                                <option value="read">مقروء</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-primary w-100" onclick="loadNotifications()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة التنبيهات
                    </h5>
                </div>
                <div class="card-body">
                    <div id="notificationsList">
                        <!-- سيتم ملء التنبيهات بواسطة JavaScript -->
                    </div>
                    
                    <!-- Empty State -->
                    <div id="emptyNotifications" class="text-center py-5" style="display: none;">
                        <i class="fas fa-bell-slash fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد تنبيهات</h5>
                        <p class="text-muted">لم يتم العثور على تنبيهات للمعايير المحددة</p>
                    </div>
                    
                    <!-- Loading State -->
                    <div id="loadingNotifications" class="text-center py-5" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2 text-muted">جاري تحميل التنبيهات...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notification Settings Modal -->
<div class="modal fade" id="notificationSettingsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog me-2"></i>
                    إعدادات التنبيهات
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="notificationSettingsForm">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">تنبيهات انتهاء العقود</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="leaseExpiryEnabled">
                                <label class="form-check-label" for="leaseExpiryEnabled">
                                    تفعيل تنبيهات انتهاء العقود
                                </label>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">عدد الأيام قبل انتهاء العقد</label>
                                <input type="number" class="form-control" id="leaseExpiryDays" value="30" min="1" max="365">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">تنبيهات استحقاق المدفوعات</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="paymentDueEnabled">
                                <label class="form-check-label" for="paymentDueEnabled">
                                    تفعيل تنبيهات استحقاق المدفوعات
                                </label>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">عدد الأيام قبل استحقاق الدفع</label>
                                <input type="number" class="form-control" id="paymentDueDays" value="5" min="1" max="30">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">تنبيهات الصيانة</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="maintenanceDueEnabled">
                                <label class="form-check-label" for="maintenanceDueEnabled">
                                    تفعيل تنبيهات الصيانة
                                </label>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">عدد الأيام قبل موعد الصيانة</label>
                                <input type="number" class="form-control" id="maintenanceDueDays" value="7" min="1" max="30">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">تنبيهات مراجعة المستأجرين</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="tenantReviewEnabled">
                                <label class="form-check-label" for="tenantReviewEnabled">
                                    تفعيل تنبيهات مراجعة المستأجرين
                                </label>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">عدد الشهور لمراجعة المستأجر</label>
                                <input type="number" class="form-control" id="tenantReviewMonths" value="6" min="1" max="24">
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">طرق الإشعار</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="emailNotifications">
                                        <label class="form-check-label" for="emailNotifications">
                                            إشعارات البريد الإلكتروني
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="smsNotifications">
                                        <label class="form-check-label" for="smsNotifications">
                                            إشعارات الرسائل النصية
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="pushNotifications">
                                        <label class="form-check-label" for="pushNotifications">
                                            الإشعارات الفورية
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveNotificationSettings()">
                    <i class="fas fa-save me-2"></i>
                    حفظ الإعدادات
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.notification-item {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.notification-item:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.notification-item.unread {
    border-left: 4px solid #007bff;
    background-color: #f8f9fa;
}

.notification-item.urgent {
    border-left: 4px solid #dc3545;
}

.notification-item.high {
    border-left: 4px solid #fd7e14;
}

.notification-item.medium {
    border-left: 4px solid #ffc107;
}

.notification-item.low {
    border-left: 4px solid #28a745;
}

.priority-badge.urgent {
    background-color: #dc3545;
}

.priority-badge.high {
    background-color: #fd7e14;
}

.priority-badge.medium {
    background-color: #ffc107;
}

.priority-badge.low {
    background-color: #28a745;
}

.notification-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}

.opacity-75 {
    opacity: 0.75;
}
</style>
{% endblock %}

{% block scripts %}
<script>
// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تعيين التواريخ الافتراضية
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    document.getElementById('startDate').value = lastWeek.toISOString().split('T')[0];
    document.getElementById('endDate').value = today.toISOString().split('T')[0];
    
    // تحميل البيانات الأولية
    loadNotificationStats();
    loadNotifications();
    loadNotificationSettings();
});

// تحميل إحصائيات التنبيهات
function loadNotificationStats() {
    fetch('/api/notifications/stats')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('urgentCount').textContent = data.stats.urgent || 0;
            document.getElementById('highCount').textContent = data.stats.high || 0;
            document.getElementById('unreadCount').textContent = data.stats.unread || 0;
            document.getElementById('totalCount').textContent = data.stats.total || 0;
        }
    })
    .catch(error => {
        console.error('Error loading notification stats:', error);
    });
}

// تحميل التنبيهات
function loadNotifications() {
    const loadingElement = document.getElementById('loadingNotifications');
    const listElement = document.getElementById('notificationsList');
    const emptyElement = document.getElementById('emptyNotifications');
    
    // إظهار حالة التحميل
    loadingElement.style.display = 'block';
    listElement.innerHTML = '';
    emptyElement.style.display = 'none';
    
    const filters = {
        type: document.getElementById('typeFilter').value,
        priority: document.getElementById('priorityFilter').value,
        status: document.getElementById('statusFilter').value,
        start_date: document.getElementById('startDate').value,
        end_date: document.getElementById('endDate').value
    };
    
    fetch('/api/notifications/list', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters)
    })
    .then(response => response.json())
    .then(data => {
        loadingElement.style.display = 'none';
        
        if (data.success && data.notifications.length > 0) {
            displayNotifications(data.notifications);
        } else {
            emptyElement.style.display = 'block';
        }
    })
    .catch(error => {
        console.error('Error loading notifications:', error);
        loadingElement.style.display = 'none';
        emptyElement.style.display = 'block';
    });
}

// عرض التنبيهات
function displayNotifications(notifications) {
    const listElement = document.getElementById('notificationsList');
    
    listElement.innerHTML = notifications.map(notification => {
        const priorityText = {
            'urgent': 'عاجل',
            'high': 'مهم',
            'medium': 'متوسط',
            'low': 'منخفض'
        }[notification.priority] || 'متوسط';
        
        const typeText = {
            'lease_expiry': 'انتهاء عقد',
            'payment_due': 'استحقاق دفع',
            'maintenance_due': 'موعد صيانة',
            'tenant_review': 'مراجعة مستأجر'
        }[notification.type] || 'تنبيه';
        
        const createdDate = new Date(notification.created_date).toLocaleDateString('ar-SA');
        const readClass = notification.is_read ? 'read' : 'unread';
        
        return `
            <div class="notification-item ${readClass} ${notification.priority} p-3">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge priority-badge ${notification.priority} me-2">${priorityText}</span>
                            <span class="badge bg-secondary me-2">${typeText}</span>
                            <small class="text-muted">${createdDate}</small>
                        </div>
                        <h6 class="mb-1">${notification.title}</h6>
                        <p class="mb-2 text-muted">${notification.message}</p>
                        ${notification.property_name ? `<small class="text-info">العقار: ${notification.property_name}</small>` : ''}
                        ${notification.tenant_name ? `<small class="text-success ms-3">المستأجر: ${notification.tenant_name}</small>` : ''}
                    </div>
                    <div class="notification-actions">
                        ${!notification.is_read ? `
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="markAsRead(${notification.id})">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : ''}
                        ${notification.action_url ? `
                            <button class="btn btn-sm btn-outline-success me-1" onclick="window.location.href='${notification.action_url}'">
                                <i class="fas fa-external-link-alt"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(${notification.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// تحديد تنبيه كمقروء
function markAsRead(notificationId) {
    fetch(`/api/notifications/${notificationId}/mark-read`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotifications();
            loadNotificationStats();
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
    });
}

// تحديد جميع التنبيهات كمقروءة
function markAllAsRead() {
    if (confirm('هل تريد تحديد جميع التنبيهات كمقروءة؟')) {
        fetch('/api/notifications/mark-all-read', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadNotifications();
                loadNotificationStats();
                alert('تم تحديد جميع التنبيهات كمقروءة');
            }
        })
        .catch(error => {
            console.error('Error marking all notifications as read:', error);
        });
    }
}

// حذف تنبيه
function deleteNotification(notificationId) {
    if (confirm('هل تريد حذف هذا التنبيه؟')) {
        fetch(`/api/notifications/${notificationId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadNotifications();
                loadNotificationStats();
            }
        })
        .catch(error => {
            console.error('Error deleting notification:', error);
        });
    }
}

// فتح إعدادات التنبيهات
function openNotificationSettings() {
    const modal = new bootstrap.Modal(document.getElementById('notificationSettingsModal'));
    modal.show();
}

// تحميل إعدادات التنبيهات
function loadNotificationSettings() {
    fetch('/api/notifications/settings')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const settings = data.settings;
            
            document.getElementById('leaseExpiryEnabled').checked = settings.lease_expiry_enabled;
            document.getElementById('leaseExpiryDays').value = settings.lease_expiry_days;
            
            document.getElementById('paymentDueEnabled').checked = settings.payment_due_enabled;
            document.getElementById('paymentDueDays').value = settings.payment_due_days;
            
            document.getElementById('maintenanceDueEnabled').checked = settings.maintenance_due_enabled;
            document.getElementById('maintenanceDueDays').value = settings.maintenance_due_days;
            
            document.getElementById('tenantReviewEnabled').checked = settings.tenant_review_enabled;
            document.getElementById('tenantReviewMonths').value = settings.tenant_review_months;
            
            document.getElementById('emailNotifications').checked = settings.email_notifications;
            document.getElementById('smsNotifications').checked = settings.sms_notifications;
            document.getElementById('pushNotifications').checked = settings.push_notifications;
        }
    })
    .catch(error => {
        console.error('Error loading notification settings:', error);
    });
}

// حفظ إعدادات التنبيهات
function saveNotificationSettings() {
    const settings = {
        lease_expiry_enabled: document.getElementById('leaseExpiryEnabled').checked,
        lease_expiry_days: parseInt(document.getElementById('leaseExpiryDays').value),
        
        payment_due_enabled: document.getElementById('paymentDueEnabled').checked,
        payment_due_days: parseInt(document.getElementById('paymentDueDays').value),
        
        maintenance_due_enabled: document.getElementById('maintenanceDueEnabled').checked,
        maintenance_due_days: parseInt(document.getElementById('maintenanceDueDays').value),
        
        tenant_review_enabled: document.getElementById('tenantReviewEnabled').checked,
        tenant_review_months: parseInt(document.getElementById('tenantReviewMonths').value),
        
        email_notifications: document.getElementById('emailNotifications').checked,
        sms_notifications: document.getElementById('smsNotifications').checked,
        push_notifications: document.getElementById('pushNotifications').checked
    };
    
    fetch('/api/notifications/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم حفظ الإعدادات بنجاح');
            const modal = bootstrap.Modal.getInstance(document.getElementById('notificationSettingsModal'));
            modal.hide();
        } else {
            alert('حدث خطأ أثناء حفظ الإعدادات');
        }
    })
    .catch(error => {
        console.error('Error saving notification settings:', error);
        alert('حدث خطأ أثناء حفظ الإعدادات');
    });
}
</script>
{% endblock %}
