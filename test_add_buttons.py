#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت فحص أزرار الإضافة في النظام
يتحقق من أن جميع أزرار الإضافة تعمل بشكل صحيح في كل قسم
"""

import requests
import sys
from urllib.parse import urljoin

# إعدادات الاختبار
BASE_URL = 'http://localhost:5000'
LOGIN_DATA = {
    'username': 'office',
    'password': '66889088'
}

class AddButtonTester:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = BASE_URL
        self.test_results = []
        
    def login(self):
        """تسجيل الدخول للنظام"""
        try:
            login_url = urljoin(self.base_url, '/login')
            response = self.session.post(login_url, data=LOGIN_DATA)
            
            if response.status_code == 200 and 'لوحة التحكم' in response.text:
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print("❌ فشل في تسجيل الدخول")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_add_page(self, section_name, add_url, expected_title):
        """اختبار صفحة إضافة معينة"""
        try:
            url = urljoin(self.base_url, add_url)
            response = self.session.get(url)
            
            if response.status_code == 200:
                if expected_title in response.text:
                    print(f"✅ {section_name}: صفحة الإضافة تعمل بشكل صحيح")
                    self.test_results.append({
                        'section': section_name,
                        'url': add_url,
                        'status': 'نجح',
                        'details': 'صفحة الإضافة تحتوي على العنوان المطلوب'
                    })
                    return True
                else:
                    print(f"⚠️ {section_name}: صفحة الإضافة لا تحتوي على العنوان المطلوب")
                    self.test_results.append({
                        'section': section_name,
                        'url': add_url,
                        'status': 'تحذير',
                        'details': 'صفحة الإضافة لا تحتوي على العنوان المطلوب'
                    })
                    return False
            else:
                print(f"❌ {section_name}: خطأ في الوصول لصفحة الإضافة (كود: {response.status_code})")
                self.test_results.append({
                    'section': section_name,
                    'url': add_url,
                    'status': 'فشل',
                    'details': f'خطأ HTTP: {response.status_code}'
                })
                return False
                
        except Exception as e:
            print(f"❌ {section_name}: خطأ في اختبار صفحة الإضافة: {e}")
            self.test_results.append({
                'section': section_name,
                'url': add_url,
                'status': 'خطأ',
                'details': str(e)
            })
            return False
    
    def test_list_page_add_buttons(self, section_name, list_url, add_button_texts):
        """اختبار أزرار الإضافة في صفحة القائمة"""
        try:
            url = urljoin(self.base_url, list_url)
            response = self.session.get(url)
            
            if response.status_code == 200:
                found_buttons = []
                for button_text in add_button_texts:
                    if button_text in response.text:
                        found_buttons.append(button_text)
                
                if found_buttons:
                    print(f"✅ {section_name}: أزرار الإضافة موجودة ({len(found_buttons)}/{len(add_button_texts)})")
                    self.test_results.append({
                        'section': section_name,
                        'url': list_url,
                        'status': 'نجح',
                        'details': f'أزرار موجودة: {", ".join(found_buttons)}'
                    })
                    return True
                else:
                    print(f"❌ {section_name}: لا توجد أزرار إضافة في صفحة القائمة")
                    self.test_results.append({
                        'section': section_name,
                        'url': list_url,
                        'status': 'فشل',
                        'details': 'لا توجد أزرار إضافة'
                    })
                    return False
            else:
                print(f"❌ {section_name}: خطأ في الوصول لصفحة القائمة (كود: {response.status_code})")
                return False
                
        except Exception as e:
            print(f"❌ {section_name}: خطأ في اختبار صفحة القائمة: {e}")
            return False
    
    def run_comprehensive_test(self):
        """تشغيل اختبار شامل لجميع أزرار الإضافة"""
        print("🚀 بدء اختبار شامل لأزرار الإضافة في النظام...")
        print("=" * 60)
        
        # تسجيل الدخول أولاً
        if not self.login():
            return False
        
        # قائمة الاختبارات
        tests = [
            # العملاء
            {
                'section': 'العملاء',
                'list_url': '/clients',
                'add_url': '/clients/add',
                'expected_title': 'إضافة عميل/وكيل',
                'add_buttons': ['إضافة عميل جديد', 'إضافة سريعة']
            },
            # القضايا
            {
                'section': 'القضايا',
                'list_url': '/cases',
                'add_url': '/cases/add',
                'expected_title': 'إضافة قضية جديدة',
                'add_buttons': ['إضافة قضية جديدة', 'إضافة سريعة']
            },
            # العقارات
            {
                'section': 'العقارات',
                'list_url': '/properties',
                'add_url': '/properties/add',
                'expected_title': 'إضافة عقار',
                'add_buttons': ['إضافة عقار جديد', 'إضافة سريعة']
            },
            # المستأجرين
            {
                'section': 'المستأجرين',
                'list_url': '/tenants',
                'add_url': '/tenants/add',
                'expected_title': 'إضافة مستأجر',
                'add_buttons': ['إضافة مستأجر جديد', 'إضافة سريعة']
            },
            # المعاملات المالية
            {
                'section': 'المعاملات المالية',
                'list_url': '/financial_transactions',
                'add_url': '/financial_transactions/add',
                'expected_title': 'إضافة سند مالي',
                'add_buttons': ['إضافة سند مالي', 'إضافة سريعة']
            },
            # المهام
            {
                'section': 'المهام',
                'list_url': '/tasks',
                'add_url': '/tasks/add',
                'expected_title': 'إضافة مهمة',
                'add_buttons': ['إضافة مهمة جديدة']
            },
            # المواعيد
            {
                'section': 'المواعيد',
                'list_url': '/appointments',
                'add_url': '/appointments/add',
                'expected_title': 'إضافة موعد',
                'add_buttons': ['إضافة موعد جديد']
            },
            # الأتعاب
            {
                'section': 'الأتعاب',
                'list_url': '/fees',
                'add_url': '/fees/add',
                'expected_title': 'إضافة أتعاب',
                'add_buttons': ['إضافة أتعاب']
            }
        ]
        
        print("\n📋 اختبار صفحات القوائم وأزرار الإضافة:")
        print("-" * 50)
        
        for test in tests:
            print(f"\n🔍 اختبار قسم: {test['section']}")
            
            # اختبار صفحة القائمة وأزرار الإضافة
            self.test_list_page_add_buttons(
                test['section'] + " - صفحة القائمة",
                test['list_url'],
                test['add_buttons']
            )
            
            # اختبار صفحة الإضافة
            self.test_add_page(
                test['section'] + " - صفحة الإضافة",
                test['add_url'],
                test['expected_title']
            )
        
        print("\n" + "=" * 60)
        self.print_summary()
        
    def print_summary(self):
        """طباعة ملخص نتائج الاختبار"""
        print("📊 ملخص نتائج الاختبار:")
        print("-" * 50)
        
        success_count = len([r for r in self.test_results if r['status'] == 'نجح'])
        warning_count = len([r for r in self.test_results if r['status'] == 'تحذير'])
        failure_count = len([r for r in self.test_results if r['status'] in ['فشل', 'خطأ']])
        total_count = len(self.test_results)
        
        print(f"✅ نجح: {success_count}")
        print(f"⚠️ تحذير: {warning_count}")
        print(f"❌ فشل: {failure_count}")
        print(f"📈 المجموع: {total_count}")
        print(f"🎯 معدل النجاح: {(success_count/total_count)*100:.1f}%")
        
        if failure_count > 0:
            print(f"\n❌ الاختبارات الفاشلة:")
            for result in self.test_results:
                if result['status'] in ['فشل', 'خطأ']:
                    print(f"   - {result['section']}: {result['details']}")

if __name__ == "__main__":
    tester = AddButtonTester()
    tester.run_comprehensive_test()
