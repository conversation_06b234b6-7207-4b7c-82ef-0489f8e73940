{% extends "base.html" %}

{% block title %}التقويم والمهام{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-calendar-alt"></i> التقويم والمهام</h2>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEventModal">
                        <i class="fas fa-plus"></i> إضافة حدث
                    </button>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addTaskModal">
                        <i class="fas fa-tasks"></i> إضافة مهمة
                    </button>
                </div>
            </div>

            <!-- Calendar Navigation -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="changeView('month')">شهري</button>
                        <button type="button" class="btn btn-outline-primary" onclick="changeView('week')">أسبوعي</button>
                        <button type="button" class="btn btn-outline-primary" onclick="changeView('day')">يومي</button>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary" onclick="navigateCalendar('prev')">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="navigateCalendar('today')">اليوم</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="navigateCalendar('next')">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Calendar Display -->
            <div class="row">
                <div class="col-md-9">
                    <div class="card">
                        <div class="card-body">
                            <div id="calendar-container">
                                <div id="calendar-header" class="text-center mb-3">
                                    <h4 id="calendar-title"></h4>
                                </div>
                                <div id="calendar-content">
                                    <!-- Calendar will be rendered here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Sidebar -->
                <div class="col-md-3">
                    <!-- Today's Tasks -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6><i class="fas fa-tasks"></i> مهام اليوم</h6>
                        </div>
                        <div class="card-body">
                            <div id="today-tasks">
                                <!-- Today's tasks will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Upcoming Events -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6><i class="fas fa-calendar-check"></i> الأحداث القادمة</h6>
                        </div>
                        <div class="card-body">
                            <div id="upcoming-events">
                                <!-- Upcoming events will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Quick Filters -->
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-filter"></i> تصفية سريعة</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showEvents" checked>
                                <label class="form-check-label" for="showEvents">الأحداث</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showTasks" checked>
                                <label class="form-check-label" for="showTasks">المهام</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showReminders" checked>
                                <label class="form-check-label" for="showReminders">التذكيرات</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Event Modal -->
<div class="modal fade" id="addEventModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة حدث جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addEventForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="eventTitle" class="form-label">عنوان الحدث *</label>
                                <input type="text" class="form-control" id="eventTitle" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="eventType" class="form-label">نوع الحدث *</label>
                                <select class="form-select" id="eventType" required>
                                    <option value="">اختر نوع الحدث</option>
                                    <option value="meeting">اجتماع</option>
                                    <option value="court">جلسة محكمة</option>
                                    <option value="consultation">استشارة</option>
                                    <option value="deadline">موعد نهائي</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="eventDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="eventDescription" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="eventStartDate" class="form-label">تاريخ البداية *</label>
                                <input type="datetime-local" class="form-control" id="eventStartDate" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="eventEndDate" class="form-label">تاريخ النهاية *</label>
                                <input type="datetime-local" class="form-control" id="eventEndDate" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="eventLocation" class="form-label">المكان</label>
                                <input type="text" class="form-control" id="eventLocation">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="eventPriority" class="form-label">الأولوية</label>
                                <select class="form-select" id="eventPriority">
                                    <option value="low">منخفضة</option>
                                    <option value="medium" selected>متوسطة</option>
                                    <option value="high">عالية</option>
                                    <option value="urgent">عاجلة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="eventClient" class="form-label">العميل</label>
                                <select class="form-select" id="eventClient">
                                    <option value="">اختر عميل (اختياري)</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}">{{ client.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="eventCase" class="form-label">القضية</label>
                                <select class="form-select" id="eventCase">
                                    <option value="">اختر قضية (اختياري)</option>
                                    {% for case in cases %}
                                    <option value="{{ case.id }}">{{ case.title }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="eventAllDay">
                                <label class="form-check-label" for="eventAllDay">طوال اليوم</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="eventRecurring">
                                <label class="form-check-label" for="eventRecurring">حدث متكرر</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="eventColor" class="form-label">اللون</label>
                                <input type="color" class="form-control form-control-color" id="eventColor" value="#007bff">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ الحدث</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Task Modal -->
<div class="modal fade" id="addTaskModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مهمة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addTaskForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="taskTitle" class="form-label">عنوان المهمة *</label>
                                <input type="text" class="form-control" id="taskTitle" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="taskPriority" class="form-label">الأولوية</label>
                                <select class="form-select" id="taskPriority">
                                    <option value="low">منخفضة</option>
                                    <option value="medium" selected>متوسطة</option>
                                    <option value="high">عالية</option>
                                    <option value="urgent">عاجلة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="taskDescription" class="form-label">وصف المهمة</label>
                        <textarea class="form-control" id="taskDescription" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="taskDueDate" class="form-label">تاريخ الاستحقاق</label>
                                <input type="datetime-local" class="form-control" id="taskDueDate">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="taskCategory" class="form-label">الفئة</label>
                                <select class="form-select" id="taskCategory">
                                    <option value="legal">قانونية</option>
                                    <option value="administrative">إدارية</option>
                                    <option value="research">بحث</option>
                                    <option value="documentation">توثيق</option>
                                    <option value="follow_up">متابعة</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="taskClient" class="form-label">العميل</label>
                                <select class="form-select" id="taskClient">
                                    <option value="">اختر عميل (اختياري)</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}">{{ client.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="taskCase" class="form-label">القضية</label>
                                <select class="form-select" id="taskCase">
                                    <option value="">اختر قضية (اختياري)</option>
                                    {% for case in cases %}
                                    <option value="{{ case.id }}">{{ case.title }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="taskEstimatedHours" class="form-label">الساعات المقدرة</label>
                                <input type="number" class="form-control" id="taskEstimatedHours" step="0.5" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="taskAssignedTo" class="form-label">مُكلف إلى</label>
                                <select class="form-select" id="taskAssignedTo">
                                    <option value="{{ current_user.id }}" selected>{{ current_user.username }}</option>
                                    {% for user in users %}
                                    {% if user.id != current_user.id %}
                                    <option value="{{ user.id }}">{{ user.username }}</option>
                                    {% endif %}
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ المهمة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/calendar.js') }}"></script>
{% endblock %}
