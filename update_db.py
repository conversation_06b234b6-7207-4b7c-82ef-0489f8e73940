#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db
from sqlalchemy import text

def update_database():
    """تحديث قاعدة البيانات لإضافة الأعمدة الجديدة"""
    with app.app_context():
        try:
            # إضافة الأعمدة الجديدة إلى جدول system_settings
            try:
                with db.engine.connect() as conn:
                    conn.execute(text('ALTER TABLE system_settings ADD COLUMN category VARCHAR(50) DEFAULT "general"'))
                    conn.commit()
                print('✅ تم إضافة عمود category')
            except Exception as e:
                print(f'عمود category موجود بالفعل: {e}')

            try:
                with db.engine.connect() as conn:
                    conn.execute(text('ALTER TABLE system_settings ADD COLUMN is_system BOOLEAN DEFAULT 0'))
                    conn.commit()
                print('✅ تم إضافة عمود is_system')
            except Exception as e:
                print(f'عمود is_system موجود بالفعل: {e}')

            try:
                with db.engine.connect() as conn:
                    conn.execute(text('ALTER TABLE system_settings ADD COLUMN created_date DATETIME'))
                    conn.commit()
                print('✅ تم إضافة عمود created_date')
            except Exception as e:
                print(f'عمود created_date موجود بالفعل: {e}')
            
            # إنشاء جداول النسخ الاحتياطي إذا لم تكن موجودة
            try:
                db.create_all()
                print('✅ تم إنشاء جميع الجداول')
            except Exception as e:
                print(f'خطأ في إنشاء الجداول: {e}')
            
            print('✅ تم تحديث قاعدة البيانات بنجاح')
            
        except Exception as e:
            print(f'❌ خطأ في تحديث قاعدة البيانات: {e}')

if __name__ == '__main__':
    update_database()
