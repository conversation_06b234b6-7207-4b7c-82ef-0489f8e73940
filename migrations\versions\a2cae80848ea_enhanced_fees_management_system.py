"""Enhanced fees management system

Revision ID: a2cae80848ea
Revises: 1089207c07d9
Create Date: 2025-07-02 19:34:36.408464

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a2cae80848ea'
down_revision = '1089207c07d9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('fees',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('fee_number', sa.String(length=50), nullable=False),
    sa.Column('case_id', sa.Integer(), nullable=True),
    sa.Column('client_id', sa.Integer(), nullable=False),
    sa.Column('fee_type', sa.String(length=100), nullable=False),
    sa.Column('service_category', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('service_date', sa.DateTime(), nullable=False),
    sa.Column('due_date', sa.DateTime(), nullable=False),
    sa.Column('base_amount', sa.Float(), nullable=False),
    sa.Column('additional_fees', sa.Float(), nullable=True),
    sa.Column('discount_amount', sa.Float(), nullable=True),
    sa.Column('tax_amount', sa.Float(), nullable=True),
    sa.Column('total_amount', sa.Float(), nullable=False),
    sa.Column('payment_status', sa.String(length=50), nullable=True),
    sa.Column('paid_amount', sa.Float(), nullable=True),
    sa.Column('remaining_amount', sa.Float(), nullable=False),
    sa.Column('payment_method', sa.String(length=50), nullable=True),
    sa.Column('payment_reference', sa.String(length=100), nullable=True),
    sa.Column('payment_terms', sa.String(length=200), nullable=True),
    sa.Column('priority', sa.String(length=50), nullable=True),
    sa.Column('currency', sa.String(length=20), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('is_recurring', sa.Boolean(), nullable=True),
    sa.Column('recurring_period', sa.String(length=50), nullable=True),
    sa.Column('created_date', sa.DateTime(), nullable=True),
    sa.Column('updated_date', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['case_id'], ['case.id'], ),
    sa.ForeignKeyConstraint(['client_id'], ['client.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('fee_number')
    )
    op.create_table('fee_payments',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('fee_id', sa.Integer(), nullable=False),
    sa.Column('payment_date', sa.DateTime(), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('payment_method', sa.String(length=50), nullable=False),
    sa.Column('reference', sa.String(length=100), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('receipt_number', sa.String(length=50), nullable=True),
    sa.Column('created_date', sa.DateTime(), nullable=True),
    sa.Column('updated_date', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['fee_id'], ['fees.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('fee')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('fee',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('case_id', sa.INTEGER(), nullable=False),
    sa.Column('client_id', sa.INTEGER(), nullable=False),
    sa.Column('amount', sa.FLOAT(), nullable=False),
    sa.Column('paid_amount', sa.FLOAT(), nullable=True),
    sa.Column('remaining_amount', sa.FLOAT(), nullable=True),
    sa.Column('fee_type', sa.VARCHAR(length=50), nullable=True),
    sa.Column('payment_method', sa.VARCHAR(length=50), nullable=True),
    sa.Column('due_date', sa.DATETIME(), nullable=True),
    sa.Column('status', sa.VARCHAR(length=50), nullable=True),
    sa.Column('notes', sa.TEXT(), nullable=True),
    sa.Column('created_date', sa.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['case_id'], ['case.id'], ),
    sa.ForeignKeyConstraint(['client_id'], ['client.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('fee_payments')
    op.drop_table('fees')
    # ### end Alembic commands ###
