#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
from datetime import datetime

BASE_URL = "http://localhost:5000"

def test_all_modals():
    """اختبار جميع الـ modals"""
    print("🧪 اختبار جميع الـ modals...")
    
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {'username': 'office', 'password': '66889088'}
    login_response = session.post(f"{BASE_URL}/lawyersameh", data=login_data)
    
    if login_response.status_code != 200:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # قائمة الـ modals للاختبار
    modals_to_test = [
        {
            'name': 'إضافة عميل',
            'modal_url': '/modal/add_client',
            'submit_url': '/clients/add',
            'data': {
                'name': f'عميل اختبار {datetime.now().strftime("%H%M%S")}',
                'phone': '0501234567',
                'email': '<EMAIL>',
                'address': 'عنوان اختبار',
                'role': 'موكل'
            }
        },
        {
            'name': 'إضافة قضية',
            'modal_url': '/modal/add_case',
            'submit_url': '/cases/add',
            'data': {
                'title': f'قضية اختبار {datetime.now().strftime("%H%M%S")}',
                'type': 'مدنية',
                'client_id': '1',
                'status': 'جارية'
            }
        }
    ]
    
    successful_tests = 0
    total_tests = len(modals_to_test)
    
    for modal in modals_to_test:
        print(f"\n🔍 اختبار {modal['name']}...")
        
        try:
            # الحصول على الـ modal
            modal_response = session.get(f"{BASE_URL}{modal['modal_url']}")
            
            if modal_response.status_code != 200:
                print(f"  ❌ فشل في الوصول للـ modal: {modal_response.status_code}")
                continue
            
            # البحث عن CSRF token
            soup = BeautifulSoup(modal_response.text, 'html.parser')
            csrf_input = soup.find('input', {'name': 'csrf_token'})
            
            if not csrf_input or not csrf_input.get('value'):
                print(f"  ❌ CSRF token مفقود")
                continue
            
            csrf_token = csrf_input.get('value')
            print(f"  ✅ CSRF token موجود: {csrf_token[:15]}...")
            
            # إضافة CSRF token للبيانات
            modal['data']['csrf_token'] = csrf_token
            
            # إرسال البيانات
            submit_response = session.post(f"{BASE_URL}{modal['submit_url']}", data=modal['data'])
            
            if submit_response.status_code in [200, 302]:
                print(f"  ✅ تم حفظ البيانات بنجاح!")
                successful_tests += 1
            else:
                print(f"  ❌ فشل في حفظ البيانات: {submit_response.status_code}")
                
        except Exception as e:
            print(f"  ❌ خطأ في الاختبار: {e}")
    
    print(f"\n📊 النتائج النهائية:")
    print(f"  ✅ اختبارات ناجحة: {successful_tests}/{total_tests}")
    print(f"  📈 معدل النجاح: {(successful_tests/total_tests)*100:.1f}%")
    
    return successful_tests == total_tests

if __name__ == "__main__":
    print("🚀 بدء اختبار جميع الـ modals...")
    print("=" * 60)
    
    success = test_all_modals()
    
    print("=" * 60)
    if success:
        print("🎉 جميع الـ modals تعمل بشكل ممتاز!")
    else:
        print("⚠️ بعض الـ modals تحتاج مراجعة")
