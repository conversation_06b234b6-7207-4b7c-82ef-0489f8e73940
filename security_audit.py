#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys
import time

def security_audit():
    """فحص شامل لحماية النظام"""
    base_url = 'http://localhost:5000'
    
    print("🔒 بدء فحص الحماية الشامل للنظام...")
    print("=" * 60)
    
    # 1. فحص الحماية من CSRF
    print("\n1️⃣ فحص الحماية من CSRF...")
    try:
        # محاولة الوصول بدون CSRF token
        response = requests.post(f'{base_url}/cases/add', data={
            'title': 'test',
            'description': 'test'
        })
        if response.status_code == 400 or 'CSRF' in response.text:
            print("✅ الحماية من CSRF مفعلة")
        else:
            print("❌ الحماية من CSRF غير مفعلة")
    except Exception as e:
        print(f"⚠️ خطأ في فحص CSRF: {str(e)}")
    
    # 2. فحص الحماية من SQL Injection
    print("\n2️⃣ فحص الحماية من SQL Injection...")
    try:
        # محاولة SQL injection في البحث
        response = requests.get(f'{base_url}/search?q=\' OR 1=1 --')
        if response.status_code == 500 or 'error' in response.text.lower():
            print("⚠️ قد يكون هناك ثغرة SQL injection")
        else:
            print("✅ الحماية من SQL Injection تبدو جيدة")
    except Exception as e:
        print(f"⚠️ خطأ في فحص SQL Injection: {str(e)}")
    
    # 3. فحص الحماية من XSS
    print("\n3️⃣ فحص الحماية من XSS...")
    try:
        # محاولة XSS في البحث
        xss_payload = '<script>alert("XSS")</script>'
        response = requests.get(f'{base_url}/search?q={xss_payload}')
        if xss_payload in response.text:
            print("❌ ثغرة XSS موجودة")
        else:
            print("✅ الحماية من XSS تبدو جيدة")
    except Exception as e:
        print(f"⚠️ خطأ في فحص XSS: {str(e)}")
    
    # 4. فحص الحماية من الوصول غير المصرح
    print("\n4️⃣ فحص الحماية من الوصول غير المصرح...")
    try:
        # محاولة الوصول للوحة التحكم بدون تسجيل دخول
        response = requests.get(f'{base_url}/dashboard')
        if response.status_code == 302 or 'login' in response.url:
            print("✅ الحماية من الوصول غير المصرح مفعلة")
        else:
            print("❌ الحماية من الوصول غير المصرح غير مفعلة")
    except Exception as e:
        print(f"⚠️ خطأ في فحص الوصول غير المصرح: {str(e)}")
    
    # 5. فحص قوة كلمات المرور
    print("\n5️⃣ فحص قوة كلمات المرور...")
    try:
        # محاولة تسجيل دخول بكلمات مرور ضعيفة
        weak_passwords = ['123', 'admin', 'password', '123456']
        for pwd in weak_passwords:
            response = requests.post(f'{base_url}/lawyersameh', data={
                'username': 'admin',
                'password': pwd
            })
            if response.status_code == 200 and 'dashboard' in response.url:
                print(f"❌ كلمة مرور ضعيفة مقبولة: {pwd}")
                return
        print("✅ لا توجد كلمات مرور ضعيفة")
    except Exception as e:
        print(f"⚠️ خطأ في فحص كلمات المرور: {str(e)}")
    
    # 6. فحص الحماية من Brute Force
    print("\n6️⃣ فحص الحماية من Brute Force...")
    try:
        # محاولة تسجيل دخول متكررة
        for i in range(5):
            response = requests.post(f'{base_url}/lawyersameh', data={
                'username': 'test',
                'password': 'wrong'
            })
            time.sleep(0.1)
        print("⚠️ لا توجد حماية من Brute Force")
    except Exception as e:
        print(f"⚠️ خطأ في فحص Brute Force: {str(e)}")
    
    # 7. فحص تشفير الاتصال
    print("\n7️⃣ فحص تشفير الاتصال...")
    if base_url.startswith('https'):
        print("✅ الاتصال مشفر (HTTPS)")
    else:
        print("❌ الاتصال غير مشفر (HTTP)")
    
    print("\n" + "=" * 60)
    print("🔒 انتهى فحص الحماية")

if __name__ == "__main__":
    security_audit()
