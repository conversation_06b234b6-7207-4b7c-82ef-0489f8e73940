{% extends "base.html" %}

{% block title %}مركز الطباعة والتصدير{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-primary mb-1">
                        <i class="fas fa-print me-2"></i>
                        مركز الطباعة والتصدير
                    </h2>
                    <p class="text-muted">طباعة وتصدير العقود والفواتير والتقارير بتصميم احترافي</p>
                </div>
            </div>

            <!-- Print Categories -->
            <div class="row mb-4">
                <!-- العقود والاتفاقيات -->
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-file-contract me-2"></i>
                                العقود والاتفاقيات
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary" onclick="openContractPrint()">
                                    <i class="fas fa-file-alt me-2"></i>
                                    طباعة عقد إيجار
                                </button>
                                <button class="btn btn-outline-primary" onclick="openLeaseAgreement()">
                                    <i class="fas fa-handshake me-2"></i>
                                    اتفاقية تأجير
                                </button>
                                <button class="btn btn-outline-primary" onclick="openTerminationLetter()">
                                    <i class="fas fa-times-circle me-2"></i>
                                    خطاب إنهاء عقد
                                </button>
                                <button class="btn btn-outline-primary" onclick="openRenewalContract()">
                                    <i class="fas fa-redo me-2"></i>
                                    عقد تجديد
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الفواتير والمدفوعات -->
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-receipt me-2"></i>
                                الفواتير والمدفوعات
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-success" onclick="openRentInvoice()">
                                    <i class="fas fa-file-invoice me-2"></i>
                                    فاتورة إيجار
                                </button>
                                <button class="btn btn-outline-success" onclick="openPaymentReceipt()">
                                    <i class="fas fa-receipt me-2"></i>
                                    إيصال دفع
                                </button>
                                <button class="btn btn-outline-success" onclick="openMaintenanceInvoice()">
                                    <i class="fas fa-tools me-2"></i>
                                    فاتورة صيانة
                                </button>
                                <button class="btn btn-outline-success" onclick="openUtilityBill()">
                                    <i class="fas fa-bolt me-2"></i>
                                    فاتورة خدمات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التقارير والإحصائيات -->
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير والإحصائيات
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-info" onclick="openFinancialReport()">
                                    <i class="fas fa-chart-line me-2"></i>
                                    التقرير المالي
                                </button>
                                <button class="btn btn-outline-info" onclick="openPropertyReport()">
                                    <i class="fas fa-building me-2"></i>
                                    تقرير العقارات
                                </button>
                                <button class="btn btn-outline-info" onclick="openTenantReport()">
                                    <i class="fas fa-users me-2"></i>
                                    تقرير المستأجرين
                                </button>
                                <button class="btn btn-outline-info" onclick="openMaintenanceReport()">
                                    <i class="fas fa-wrench me-2"></i>
                                    تقرير الصيانة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                إجراءات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <button class="btn btn-warning w-100 mb-2" onclick="openBulkPrint()">
                                        <i class="fas fa-print me-2"></i>
                                        طباعة مجمعة
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-secondary w-100 mb-2" onclick="openTemplateManager()">
                                        <i class="fas fa-file-code me-2"></i>
                                        إدارة القوالب
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-dark w-100 mb-2" onclick="openPrintHistory()">
                                        <i class="fas fa-history me-2"></i>
                                        سجل الطباعة
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-purple w-100 mb-2" onclick="openPrintSettings()">
                                        <i class="fas fa-cog me-2"></i>
                                        إعدادات الطباعة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Print Jobs -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        آخر عمليات الطباعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>نوع المستند</th>
                                    <th>العنوان</th>
                                    <th>تاريخ الطباعة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="recentPrintJobs">
                                <!-- سيتم ملء البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Print Modal -->
<div class="modal fade" id="printModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="printModalTitle">
                    <i class="fas fa-print me-2"></i>
                    معاينة الطباعة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="printPreview" class="print-preview">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="printDocument()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
                <button type="button" class="btn btn-success" onclick="downloadPDF()">
                    <i class="fas fa-file-pdf me-2"></i>
                    تحميل PDF
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.btn-purple {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
}

.btn-purple:hover {
    background-color: #5a359a;
    border-color: #5a359a;
    color: white;
}

.print-preview {
    background: white;
    padding: 2rem;
    border: 1px solid #ddd;
    border-radius: 0.5rem;
    min-height: 400px;
}

@media print {
    .modal-header,
    .modal-footer,
    .btn {
        display: none !important;
    }

    .modal-body {
        padding: 0 !important;
    }

    .print-preview {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadRecentPrintJobs();
});

// تحميل آخر عمليات الطباعة
function loadRecentPrintJobs() {
    // بيانات تجريبية - سيتم استبدالها بـ API حقيقي
    const recentJobs = [
        {
            type: 'عقد إيجار',
            title: 'عقد إيجار فيلا الورود',
            date: '2025-07-05',
            status: 'مكتمل',
            id: 1
        },
        {
            type: 'فاتورة إيجار',
            title: 'فاتورة إيجار شهر يوليو',
            date: '2025-07-04',
            status: 'مكتمل',
            id: 2
        },
        {
            type: 'تقرير مالي',
            title: 'التقرير المالي الشهري',
            date: '2025-07-03',
            status: 'مكتمل',
            id: 3
        }
    ];

    const tbody = document.getElementById('recentPrintJobs');
    tbody.innerHTML = recentJobs.map(job => `
        <tr>
            <td><span class="badge bg-primary">${job.type}</span></td>
            <td>${job.title}</td>
            <td>${job.date}</td>
            <td><span class="badge bg-success">${job.status}</span></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="reprintDocument(${job.id})">
                    <i class="fas fa-redo"></i>
                </button>
                <button class="btn btn-sm btn-outline-success" onclick="downloadDocument(${job.id})">
                    <i class="fas fa-download"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// فتح نافذة طباعة عقد الإيجار
function openContractPrint() {
    showPrintModal('طباعة عقد إيجار', generateContractTemplate());
}

// فتح نافذة طباعة فاتورة الإيجار
function openRentInvoice() {
    showPrintModal('طباعة فاتورة إيجار', generateInvoiceTemplate());
}

// فتح نافذة طباعة التقرير المالي
function openFinancialReport() {
    showPrintModal('طباعة التقرير المالي', generateFinancialReportTemplate());
}

// عرض نافذة الطباعة
function showPrintModal(title, content) {
    document.getElementById('printModalTitle').innerHTML = `<i class="fas fa-print me-2"></i>${title}`;
    document.getElementById('printPreview').innerHTML = content;

    const modal = new bootstrap.Modal(document.getElementById('printModal'));
    modal.show();
}

// إنشاء قالب عقد الإيجار
function generateContractTemplate() {
    return `
        <div class="contract-template">
            <div class="text-center mb-4">
                <h2 class="text-primary">عقد إيجار عقار</h2>
                <p class="text-muted">رقم العقد: 2025-001</p>
            </div>

            <div class="row mb-4">
                <div class="col-6">
                    <h5>بيانات المؤجر:</h5>
                    <p><strong>الاسم:</strong> شركة العقارات المتميزة</p>
                    <p><strong>رقم الهوية:</strong> 1234567890</p>
                    <p><strong>الهاتف:</strong> 0501234567</p>
                </div>
                <div class="col-6">
                    <h5>بيانات المستأجر:</h5>
                    <p><strong>الاسم:</strong> أحمد محمد علي</p>
                    <p><strong>رقم الهوية:</strong> 0987654321</p>
                    <p><strong>الهاتف:</strong> 0507654321</p>
                </div>
            </div>

            <div class="mb-4">
                <h5>بيانات العقار:</h5>
                <p><strong>نوع العقار:</strong> فيلا</p>
                <p><strong>العنوان:</strong> حي الورود، شارع الملك فهد</p>
                <p><strong>المساحة:</strong> 400 متر مربع</p>
            </div>

            <div class="mb-4">
                <h5>تفاصيل الإيجار:</h5>
                <p><strong>قيمة الإيجار الشهري:</strong> 5,000 ريال سعودي</p>
                <p><strong>مدة العقد:</strong> سنة واحدة</p>
                <p><strong>تاريخ البداية:</strong> 2025-07-01</p>
                <p><strong>تاريخ الانتهاء:</strong> 2026-06-30</p>
            </div>

            <div class="row mt-5">
                <div class="col-6 text-center">
                    <div class="signature-area">
                        <hr style="width: 200px; margin: 0 auto;">
                        <p class="mt-2">توقيع المؤجر</p>
                    </div>
                </div>
                <div class="col-6 text-center">
                    <div class="signature-area">
                        <hr style="width: 200px; margin: 0 auto;">
                        <p class="mt-2">توقيع المستأجر</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إنشاء قالب فاتورة الإيجار
function generateInvoiceTemplate() {
    return `
        <div class="invoice-template">
            <div class="text-center mb-4">
                <h2 class="text-success">فاتورة إيجار</h2>
                <p class="text-muted">رقم الفاتورة: INV-2025-001</p>
            </div>

            <div class="row mb-4">
                <div class="col-6">
                    <h5>من:</h5>
                    <p><strong>شركة العقارات المتميزة</strong></p>
                    <p>الرياض، المملكة العربية السعودية</p>
                    <p>هاتف: 0501234567</p>
                </div>
                <div class="col-6">
                    <h5>إلى:</h5>
                    <p><strong>أحمد محمد علي</strong></p>
                    <p>حي الورود، الرياض</p>
                    <p>هاتف: 0507654321</p>
                </div>
            </div>

            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>الوصف</th>
                        <th>الفترة</th>
                        <th>المبلغ</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>إيجار شهري - فيلا الورود</td>
                        <td>يوليو 2025</td>
                        <td>5,000 ريال</td>
                    </tr>
                    <tr>
                        <td>رسوم صيانة</td>
                        <td>يوليو 2025</td>
                        <td>200 ريال</td>
                    </tr>
                </tbody>
                <tfoot class="table-light">
                    <tr>
                        <th colspan="2">الإجمالي</th>
                        <th>5,200 ريال</th>
                    </tr>
                </tfoot>
            </table>

            <div class="mt-4">
                <p><strong>تاريخ الاستحقاق:</strong> 2025-07-31</p>
                <p><strong>طريقة الدفع:</strong> تحويل بنكي</p>
            </div>
        </div>
    `;
}

// إنشاء قالب التقرير المالي
function generateFinancialReportTemplate() {
    return `
        <div class="financial-report-template">
            <div class="text-center mb-4">
                <h2 class="text-info">التقرير المالي الشهري</h2>
                <p class="text-muted">يوليو 2025</p>
            </div>

            <div class="row mb-4">
                <div class="col-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h4 class="text-success">25,000</h4>
                            <p>إجمالي الإيرادات</p>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h4 class="text-danger">8,000</h4>
                            <p>إجمالي المصروفات</p>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h4 class="text-primary">17,000</h4>
                            <p>صافي الربح</p>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h4 class="text-warning">5</h4>
                            <p>عدد العقارات</p>
                        </div>
                    </div>
                </div>
            </div>

            <h5>تفاصيل الإيرادات:</h5>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>العقار</th>
                        <th>المستأجر</th>
                        <th>المبلغ</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>فيلا الورود</td>
                        <td>أحمد محمد</td>
                        <td>5,000 ريال</td>
                    </tr>
                    <tr>
                        <td>شقة النخيل</td>
                        <td>فاطمة أحمد</td>
                        <td>3,500 ريال</td>
                    </tr>
                    <tr>
                        <td>مكتب التجارة</td>
                        <td>شركة الأعمال</td>
                        <td>8,000 ريال</td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;
}

// طباعة المستند
function printDocument() {
    window.print();
}

// تحميل PDF
function downloadPDF() {
    // سيتم تنفيذ هذه الوظيفة لاحقاً
    alert('سيتم تنفيذ تحميل PDF قريباً');
}

// إعادة طباعة مستند
function reprintDocument(id) {
    alert(`إعادة طباعة المستند رقم ${id}`);
}

// تحميل مستند
function downloadDocument(id) {
    alert(`تحميل المستند رقم ${id}`);
}

// الوظائف الأخرى
function openLeaseAgreement() { alert('اتفاقية تأجير - قريباً'); }
function openTerminationLetter() { alert('خطاب إنهاء عقد - قريباً'); }
function openRenewalContract() { alert('عقد تجديد - قريباً'); }
function openPaymentReceipt() { alert('إيصال دفع - قريباً'); }
function openMaintenanceInvoice() { alert('فاتورة صيانة - قريباً'); }
function openUtilityBill() { alert('فاتورة خدمات - قريباً'); }
function openPropertyReport() { alert('تقرير العقارات - قريباً'); }
function openTenantReport() { alert('تقرير المستأجرين - قريباً'); }
function openMaintenanceReport() { alert('تقرير الصيانة - قريباً'); }
function openBulkPrint() { alert('طباعة مجمعة - قريباً'); }
function openTemplateManager() { alert('إدارة القوالب - قريباً'); }
function openPrintHistory() { alert('سجل الطباعة - قريباً'); }
function openPrintSettings() { alert('إعدادات الطباعة - قريباً'); }
</script>
{% endblock %}