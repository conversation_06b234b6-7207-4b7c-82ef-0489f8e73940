{% extends 'base.html' %}

{% block title %}تقرير الأتعاب الشامل{% endblock %}

{% block extra_css %}
<style>
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stats-card h5 {
    font-size: 1.1rem;
    margin-bottom: 10px;
    opacity: 0.9;
}

.stats-card .value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-card.overdue {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stats-card.paid {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
}

.stats-card.pending {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

.report-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.currency-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    margin: 2px;
}

.currency-ils { background: #e3f2fd; color: #1976d2; }
.currency-usd { background: #e8f5e8; color: #388e3c; }
.currency-eur { background: #fff3e0; color: #f57c00; }

.progress-custom {
    height: 8px;
    border-radius: 10px;
    background: #f1f3f4;
}

.progress-custom .progress-bar {
    border-radius: 10px;
}

.table-fees {
    font-size: 0.9rem;
}

.table-fees th {
    background: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-paid { background: #d4edda; color: #155724; }
.status-pending { background: #fff3cd; color: #856404; }
.status-overdue { background: #f8d7da; color: #721c24; }

.print-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
}

.print-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- رأس التقرير -->
    <div class="row mb-4">
        <div class="col">
            <h2 class="text-primary mb-3">
                <i class="fas fa-chart-bar me-3"></i>تقرير الأتعاب الشامل
            </h2>
            <p class="text-muted">تقرير شامل عن حالة الأتعاب والمدفوعات</p>
        </div>
        <div class="col-auto">
            <button class="btn print-btn" onclick="window.print()">
                <i class="fas fa-print me-2"></i>طباعة التقرير
            </button>
        </div>
    </div>

    <!-- الإحصائيات الرئيسية -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <h5>إجمالي الأتعاب</h5>
                <div class="value">{{ "{:,.0f}".format(total_fees) }}</div>
                <small>شيكل</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card paid">
                <h5>المبلغ المدفوع</h5>
                <div class="value">{{ "{:,.0f}".format(total_paid) }}</div>
                <small>{{ paid_count }} أتعاب مدفوعة</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card pending">
                <h5>المبلغ المتبقي</h5>
                <div class="value">{{ "{:,.0f}".format(total_remaining) }}</div>
                <small>{{ pending_count }} أتعاب معلقة</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card overdue">
                <h5>الأتعاب المتأخرة</h5>
                <div class="value">{{ "{:,.0f}".format(overdue_amount) }}</div>
                <small>{{ overdue_fees|length }} أتعاب متأخرة</small>
            </div>
        </div>
    </div>

    <!-- تحليل العملات -->
    {% if currency_stats %}
    <div class="report-section">
        <h4 class="mb-3"><i class="fas fa-coins me-2"></i>تحليل الأتعاب حسب العملة</h4>
        <div class="row">
            {% for currency, stats in currency_stats.items() %}
            <div class="col-md-4 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h6 class="card-title">
                            <span class="currency-badge currency-{{ 'ils' if currency == 'شيكل' else 'usd' if currency == 'دولار' else 'eur' }}">
                                {{ currency }}
                            </span>
                        </h6>
                        <div class="mb-2">
                            <small class="text-muted">إجمالي:</small>
                            <strong>{{ "{:,.0f}".format(stats.total) }}</strong>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">مدفوع:</small>
                            <span class="text-success">{{ "{:,.0f}".format(stats.paid) }}</span>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">متبقي:</small>
                            <span class="text-warning">{{ "{:,.0f}".format(stats.remaining) }}</span>
                        </div>
                        <div class="progress progress-custom">
                            <div class="progress-bar bg-success" style="width: {{ (stats.paid / stats.total * 100) if stats.total > 0 else 0 }}%"></div>
                        </div>
                        <small class="text-muted">{{ stats.count }} أتعاب</small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- أعلى العملاء -->
    {% if top_clients %}
    <div class="report-section">
        <h4 class="mb-3"><i class="fas fa-users me-2"></i>أعلى العملاء من حيث الأتعاب</h4>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>العميل</th>
                        <th>إجمالي الأتعاب</th>
                        <th>المدفوع</th>
                        <th>المتبقي</th>
                        <th>عدد الأتعاب</th>
                        <th>نسبة السداد</th>
                    </tr>
                </thead>
                <tbody>
                    {% for client_name, stats in top_clients %}
                    <tr>
                        <td><strong>{{ client_name }}</strong></td>
                        <td>{{ "{:,.0f}".format(stats.total) }} شيكل</td>
                        <td class="text-success">{{ "{:,.0f}".format(stats.paid) }} شيكل</td>
                        <td class="text-warning">{{ "{:,.0f}".format(stats.remaining) }} شيكل</td>
                        <td>{{ stats.count }}</td>
                        <td>
                            <div class="progress progress-custom">
                                <div class="progress-bar bg-info" style="width: {{ (stats.paid / stats.total * 100) if stats.total > 0 else 0 }}%"></div>
                            </div>
                            <small>{{ "{:.1f}".format((stats.paid / stats.total * 100) if stats.total > 0 else 0) }}%</small>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- الأتعاب المتأخرة -->
    {% if overdue_fees %}
    <div class="report-section">
        <h4 class="mb-3 text-danger"><i class="fas fa-exclamation-triangle me-2"></i>الأتعاب المتأخرة</h4>
        <div class="table-responsive">
            <table class="table table-fees">
                <thead>
                    <tr>
                        <th>رقم الأتعاب</th>
                        <th>العميل</th>
                        <th>القضية</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>المبلغ المتبقي</th>
                        <th>أيام التأخير</th>
                    </tr>
                </thead>
                <tbody>
                    {% for fee in overdue_fees %}
                    <tr>
                        <td><strong>{{ fee.fee_number }}</strong></td>
                        <td>{{ fee.client.name if fee.client else 'غير محدد' }}</td>
                        <td>{{ fee.case.case_number if fee.case else 'غير محدد' }}</td>
                        <td>{{ fee.due_date.strftime('%Y-%m-%d') if fee.due_date else '-' }}</td>
                        <td class="text-danger">{{ "{:,.0f}".format(fee.remaining_amount) }} {{ fee.currency }}</td>
                        <td>
                            {% if fee.due_date %}
                            <span class="badge bg-danger">{{ (today - fee.due_date).days }} يوم</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- جميع الأتعاب -->
    <div class="report-section">
        <h4 class="mb-3"><i class="fas fa-list me-2"></i>جميع الأتعاب</h4>
        <div class="table-responsive">
            <table class="table table-fees">
                <thead>
                    <tr>
                        <th>رقم الأتعاب</th>
                        <th>العميل</th>
                        <th>صفة الموكل</th>
                        <th>القضية</th>
                        <th>المبلغ الإجمالي</th>
                        <th>عملة الأتعاب</th>
                        <th>المدفوع</th>
                        <th>المتبقي</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for fee in fees %}
                    <tr>
                        <td><strong>{{ fee.fee_number }}</strong></td>
                        <td>{{ fee.client.name if fee.client else 'غير محدد' }}</td>
                        <td>
                            <span class="badge bg-info">{{ fee.case.client_role if fee.case and fee.case.client_role else 'مدعي' }}</span>
                        </td>
                        <td>{{ fee.case.case_number if fee.case else 'غير محدد' }}</td>
                        <td>{{ "{:,.0f}".format(fee.total_amount) }} {{ fee.currency }}</td>
                        <td>
                            <span class="badge bg-secondary">{{ fee.case.fees_currency if fee.case and fee.case.fees_currency else 'شيكل' }}</span>
                        </td>
                        <td class="text-success">{{ "{:,.0f}".format(fee.paid_amount) }} {{ fee.currency }}</td>
                        <td class="text-warning">{{ "{:,.0f}".format(fee.remaining_amount) }} {{ fee.currency }}</td>
                        <td>{{ fee.due_date.strftime('%Y-%m-%d') if fee.due_date else '-' }}</td>
                        <td>
                            {% if fee.remaining_amount <= 0 %}
                                <span class="status-badge status-paid">مدفوعة</span>
                            {% elif fee.due_date and fee.due_date < today %}
                                <span class="status-badge status-overdue">متأخرة</span>
                            {% else %}
                                <span class="status-badge status-pending">معلقة</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// تحسين الطباعة
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});
</script>
{% endblock %}
