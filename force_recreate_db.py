#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime

# إضافة مسار التطبيق
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app import app, db
from app.models import *

def force_recreate_database():
    """إعادة إنشاء قاعدة البيانات بالقوة"""
    
    print("🚀 بدء إعادة إنشاء قاعدة البيانات بالقوة...")
    print("=" * 60)
    
    with app.app_context():
        try:
            # حذف قاعدة البيانات إذا كانت موجودة
            db_path = 'law_office.db'
            if os.path.exists(db_path):
                os.remove(db_path)
                print("🗑️ تم حذف قاعدة البيانات القديمة")
            
            # إنشاء قاعدة البيانات الجديدة
            print("🔄 إنشاء قاعدة البيانات الجديدة...")
            db.create_all()
            print("✅ تم إنشاء هيكل قاعدة البيانات")
            
            # إنشاء المستخدمين الافتراضيين
            print("👤 إنشاء المستخدمين الافتراضيين...")

            # التحقق من وجود مستخدم المكتب
            if not User.query.filter_by(username='office').first():
                office_user = User(
                    username='office',
                    password='66889088',
                    role='office'
                )
                db.session.add(office_user)

            # التحقق من وجود مستخدم الإدارة
            if not User.query.filter_by(username='admin').first():
                admin_user = User(
                    username='admin',
                    password='admin123',
                    role='admin'
                )
                db.session.add(admin_user)

            db.session.commit()
            print("✅ تم إنشاء المستخدمين الافتراضيين")
            
            # إنشاء بيانات تجريبية أساسية
            print("📊 إنشاء بيانات تجريبية أساسية...")
            
            # عميل تجريبي
            client = Client(
                name='عميل تجريبي',
                phone='0501234567',
                email='<EMAIL>',
                address='عنوان تجريبي',
                national_id='123456789',
                birth_date=datetime(1990, 1, 1),
                occupation='مهندس',
                notes='عميل تجريبي للاختبار',
                role='عميل'
            )
            db.session.add(client)
            
            # قضية تجريبية
            case = Case(
                title='قضية تجريبية',
                case_number='2024/001',
                court='محكمة تجريبية',
                type='مدني',
                status='جاري',
                description='قضية تجريبية للاختبار',
                client_id=1
            )
            db.session.add(case)
            
            # عقار تجريبي
            property_test = Property(
                name='عقار تجريبي',
                type='شقة',
                address='عنوان العقار التجريبي',
                description='عقار تجريبي للاختبار',
                area=100.0,
                rooms_count=3,
                bathrooms_count=2,
                monthly_rent=800.0,
                currency='شيكل',
                status='متاح',
                owner_name='مالك تجريبي',
                owner_phone='0501234567',
                notes='عقار تجريبي للاختبار'
            )
            db.session.add(property_test)
            
            # مستأجر تجريبي
            tenant = Tenant(
                name='مستأجر تجريبي',
                phone='0501234567',
                email='<EMAIL>',
                address='عنوان المستأجر التجريبي',
                national_id='987654321',
                occupation='طبيب',
                emergency_contact='جهة طوارئ',
                notes='مستأجر تجريبي للاختبار'
            )
            db.session.add(tenant)
            
            db.session.commit()
            print("✅ تم إنشاء البيانات التجريبية الأساسية")
            
            print("\n" + "=" * 60)
            print("🎉 تم إنشاء قاعدة البيانات بنجاح!")
            print("👤 المستخدمون:")
            print("   - office / 66889088")
            print("   - admin / admin123")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False

if __name__ == "__main__":
    print("✅ تم تهيئة خدمات النظام بنجاح")
    success = force_recreate_database()
    
    if success:
        print("\n🎉 تم إنشاء قاعدة البيانات بنجاح!")
    else:
        print("\n💥 فشل في إنشاء قاعدة البيانات")
