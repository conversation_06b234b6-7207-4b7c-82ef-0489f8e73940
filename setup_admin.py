#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت إعداد المستخدم الافتراضي لنظام إدارة المكتب
"""

import os
import sys
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app, db
    from app.models import User
    from werkzeug.security import generate_password_hash
    
    def setup_admin_user():
        """إعداد المستخدم الافتراضي"""
        with app.app_context():
            try:
                # إنشاء الجداول إذا لم تكن موجودة
                db.create_all()
                
                # التحقق من وجود مستخدمين
                existing_users = User.query.count()
                
                if existing_users == 0:
                    # إنشاء المستخدم الافتراضي
                    admin_user = User(
                        username='admin',
                        password=generate_password_hash('admin123'),
                        email='<EMAIL>',
                        full_name='مدير النظام',
                        role='admin',
                        is_active=True,
                        created_at=datetime.now(),
                        last_login=None
                    )
                    
                    db.session.add(admin_user)
                    db.session.commit()
                    
                    print("=" * 60)
                    print("✅ تم إنشاء المستخدم الافتراضي بنجاح!")
                    print("=" * 60)
                    print("📋 بيانات تسجيل الدخول:")
                    print("   🔑 اسم المستخدم: admin")
                    print("   🔐 كلمة المرور: admin123")
                    print("=" * 60)
                    print("🌐 روابط النظام:")
                    print("   🏠 الصفحة الرئيسية: http://localhost:5000")
                    print("   🔐 تسجيل الدخول: http://localhost:5000/lawyersameh")
                    print("=" * 60)
                    
                else:
                    print("=" * 60)
                    print("ℹ️  المستخدمون الموجودون في النظام:")
                    print("=" * 60)
                    
                    users = User.query.all()
                    for i, user in enumerate(users, 1):
                        status = "نشط" if user.is_active else "غير نشط"
                        last_login = user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else "لم يسجل دخول"
                        
                        print(f"   {i}. {user.username} ({user.full_name})")
                        print(f"      📧 البريد: {user.email}")
                        print(f"      👤 الدور: {user.role}")
                        print(f"      🟢 الحالة: {status}")
                        print(f"      🕐 آخر دخول: {last_login}")
                        print()
                    
                    print("=" * 60)
                    print("🔐 رابط تسجيل الدخول: http://localhost:5000/lawyersameh")
                    print("=" * 60)
                    
            except Exception as e:
                print(f"❌ خطأ في إعداد المستخدم: {str(e)}")
                import traceback
                traceback.print_exc()
                return False
                
        return True
    
    if __name__ == '__main__':
        print("🚀 بدء إعداد نظام إدارة المكتب...")
        print()
        
        success = setup_admin_user()
        
        if success:
            print("✅ تم إعداد النظام بنجاح!")
            print("💡 يمكنك الآن تشغيل النظام باستخدام: python run.py")
        else:
            print("❌ فشل في إعداد النظام!")
            sys.exit(1)

except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    print("💡 تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
