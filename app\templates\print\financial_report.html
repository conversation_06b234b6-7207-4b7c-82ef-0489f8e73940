{% extends "print/base_print.html" %}

{% block title %}التقرير المالي - {{ report_title }}{% endblock %}

{% block content %}
<div class="document-title">
    التقرير المالي الشامل
</div>

<div class="document-info">
    <div class="row">
        <div class="col-md-6">
            <span class="label">نوع التقرير:</span>
            <span class="value">{{ report_type }}</span>
        </div>
        <div class="col-md-6">
            <span class="label">الفترة:</span>
            <span class="value">{{ period }}</span>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <span class="label">تاريخ التقرير:</span>
            <span class="value">{{ report_date }}</span>
        </div>
        <div class="col-md-6">
            <span class="label">رقم التقرير:</span>
            <span class="value">{{ report_number }}</span>
        </div>
    </div>
</div>

<!-- ملخص مالي عام -->
<div class="financial-summary">
    <h4><i class="fa fa-chart-line"></i> الملخص المالي العام</h4>
    <div class="row">
        <div class="col-md-3">
            <div class="text-center">
                <div class="label">إجمالي الإيرادات</div>
                <div class="amount">{{ total_income | number_format }} شيكل</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="text-center">
                <div class="label">إجمالي المصروفات</div>
                <div class="amount negative">{{ total_expenses | number_format }} شيكل</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="text-center">
                <div class="label">صافي الربح</div>
                <div class="amount {% if net_profit < 0 %}negative{% endif %}">{{ net_profit | number_format }} شيكل</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="text-center">
                <div class="label">الديون المستحقة</div>
                <div class="amount negative">{{ total_debts | number_format }} شيكل</div>
            </div>
        </div>
    </div>
</div>

<!-- تفاصيل الإيرادات -->
<div class="content-section">
    <h3><i class="fa fa-arrow-up text-success"></i> تفاصيل الإيرادات</h3>
    
    <!-- إيرادات القضايا -->
    <h5>إيرادات القضايا والأتعاب</h5>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>رقم القضية</th>
                <th>اسم العميل</th>
                <th>نوع القضية</th>
                <th>الأتعاب المتفق عليها</th>
                <th>المبلغ المدفوع</th>
                <th>المبلغ المتبقي</th>
                <th>الحالة</th>
            </tr>
        </thead>
        <tbody>
            {% for case in cases_income %}
            <tr>
                <td>{{ case.case_number }}</td>
                <td>{{ case.client_name }}</td>
                <td>{{ case.case_type }}</td>
                <td class="amount">{{ case.agreed_fees | number_format }}</td>
                <td class="amount">{{ case.paid_amount | number_format }}</td>
                <td class="amount {% if case.remaining_amount > 0 %}negative{% endif %}">{{ case.remaining_amount | number_format }}</td>
                <td>
                    <span class="badge {% if case.payment_status == 'مدفوع' %}bg-success{% elif case.payment_status == 'غير مدفوع' %}bg-danger{% else %}bg-warning{% endif %}">
                        {{ case.payment_status }}
                    </span>
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="table-info">
                <td colspan="3"><strong>المجموع</strong></td>
                <td class="amount"><strong>{{ cases_total_fees | number_format }}</strong></td>
                <td class="amount"><strong>{{ cases_total_paid | number_format }}</strong></td>
                <td class="amount"><strong>{{ cases_total_remaining | number_format }}</strong></td>
                <td></td>
            </tr>
        </tfoot>
    </table>
    
    <!-- الإيرادات الإيجارية -->
    <h5>الإيرادات الإيجارية</h5>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>العقار</th>
                <th>المستأجر</th>
                <th>الشهر/السنة</th>
                <th>مبلغ الإيجار</th>
                <th>الرسوم الإضافية</th>
                <th>الخصومات</th>
                <th>صافي الإيجار</th>
                <th>حالة الدفع</th>
            </tr>
        </thead>
        <tbody>
            {% for rental in rental_income %}
            <tr>
                <td>{{ rental.property_name }}</td>
                <td>{{ rental.tenant_name }}</td>
                <td>{{ rental.month }}/{{ rental.year }}</td>
                <td class="amount">{{ rental.amount | number_format }}</td>
                <td class="amount">{{ rental.additional_fees | number_format }}</td>
                <td class="amount">{{ rental.discount | number_format }}</td>
                <td class="amount">{{ rental.net_amount | number_format }}</td>
                <td>
                    <span class="badge {% if rental.payment_status == 'مدفوع' %}bg-success{% elif rental.payment_status == 'غير مدفوع' %}bg-danger{% else %}bg-warning{% endif %}">
                        {{ rental.payment_status }}
                    </span>
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="table-info">
                <td colspan="6"><strong>المجموع</strong></td>
                <td class="amount"><strong>{{ rental_total | number_format }}</strong></td>
                <td></td>
            </tr>
        </tfoot>
    </table>
</div>

<!-- تفاصيل المصروفات -->
<div class="content-section">
    <h3><i class="fa fa-arrow-down text-danger"></i> تفاصيل المصروفات</h3>
    
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>التاريخ</th>
                <th>الوصف</th>
                <th>الفئة</th>
                <th>المورد</th>
                <th>المبلغ</th>
                <th>الضريبة</th>
                <th>المجموع</th>
                <th>طريقة الدفع</th>
            </tr>
        </thead>
        <tbody>
            {% for expense in expenses %}
            <tr>
                <td>{{ expense.expense_date }}</td>
                <td>{{ expense.description }}</td>
                <td>{{ expense.category }}</td>
                <td>{{ expense.vendor_name or '-' }}</td>
                <td class="amount">{{ expense.amount | number_format }}</td>
                <td class="amount">{{ expense.tax_amount | number_format }}</td>
                <td class="amount">{{ expense.total_amount | number_format }}</td>
                <td>{{ expense.payment_method }}</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="table-info">
                <td colspan="6"><strong>المجموع</strong></td>
                <td class="amount"><strong>{{ expenses_total | number_format }}</strong></td>
                <td></td>
            </tr>
        </tfoot>
    </table>
</div>

<!-- تحليل المصروفات حسب الفئة -->
<div class="content-section">
    <h3><i class="fa fa-chart-pie"></i> تحليل المصروفات حسب الفئة</h3>
    
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>الفئة</th>
                <th>عدد المصروفات</th>
                <th>إجمالي المبلغ</th>
                <th>النسبة من المجموع</th>
            </tr>
        </thead>
        <tbody>
            {% for category in expense_categories %}
            <tr>
                <td>{{ category.name }}</td>
                <td>{{ category.count }}</td>
                <td class="amount">{{ category.total | number_format }}</td>
                <td>{{ category.percentage }}%</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- الديون والمستحقات -->
<div class="content-section">
    <h3><i class="fa fa-exclamation-triangle text-warning"></i> الديون والمستحقات</h3>
    
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>الدائن</th>
                <th>وصف الدين</th>
                <th>تاريخ الاستحقاق</th>
                <th>المبلغ الأصلي</th>
                <th>المبلغ المدفوع</th>
                <th>المبلغ المتبقي</th>
                <th>الحالة</th>
            </tr>
        </thead>
        <tbody>
            {% for debt in debts %}
            <tr>
                <td>{{ debt.creditor_name }}</td>
                <td>{{ debt.description }}</td>
                <td>{{ debt.due_date }}</td>
                <td class="amount">{{ debt.amount | number_format }}</td>
                <td class="amount">{{ debt.paid_amount | number_format }}</td>
                <td class="amount {% if debt.remaining_amount > 0 %}negative{% endif %}">{{ debt.remaining_amount | number_format }}</td>
                <td>
                    <span class="badge {% if debt.status == 'مدفوع' %}bg-success{% elif debt.status == 'متأخر' %}bg-danger{% else %}bg-warning{% endif %}">
                        {{ debt.status }}
                    </span>
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="table-info">
                <td colspan="5"><strong>المجموع</strong></td>
                <td class="amount"><strong>{{ debts_total | number_format }}</strong></td>
                <td></td>
            </tr>
        </tfoot>
    </table>
</div>

<!-- التوصيات والملاحظات -->
<div class="content-section">
    <h3><i class="fa fa-lightbulb"></i> التوصيات والملاحظات</h3>
    
    <div class="alert alert-info">
        <h6><strong>ملاحظات مالية مهمة:</strong></h6>
        <ul>
            {% if net_profit < 0 %}
            <li class="text-danger">تحذير: هناك خسارة صافية قدرها {{ net_profit | abs | number_format }} شيكل</li>
            {% endif %}
            {% if cases_total_remaining > 0 %}
            <li class="text-warning">يوجد مبلغ {{ cases_total_remaining | number_format }} شيكل غير محصل من أتعاب القضايا</li>
            {% endif %}
            {% if debts_total > 0 %}
            <li class="text-danger">يوجد ديون مستحقة بقيمة {{ debts_total | number_format }} شيكل</li>
            {% endif %}
            <li>نسبة المصروفات إلى الإيرادات: {{ expense_ratio }}%</li>
            <li>متوسط الإيرادات الشهرية: {{ monthly_avg_income | number_format }} شيكل</li>
        </ul>
    </div>
</div>

{% endblock %}

{% block signatures %}
<div class="signature-section">
    <div class="signature-box">
        <div>المحاسب</div>
        <div style="margin-top: 40px;">________________</div>
    </div>
    <div class="signature-box">
        <div>المحامي المسؤول</div>
        <div style="margin-top: 40px;">سامح أبو عودة</div>
    </div>
    <div class="signature-box">
        <div>التاريخ</div>
        <div style="margin-top: 40px;">{{ current_date }}</div>
    </div>
</div>
{% endblock %}
