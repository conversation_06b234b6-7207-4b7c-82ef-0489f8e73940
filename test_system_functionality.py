#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت اختبار وظائف النظام الأساسية
يتحقق من أن جميع الصفحات الرئيسية متاحة وتعمل بشكل صحيح
"""

import requests
import sys
from urllib.parse import urljoin

# إعدادات الاختبار
BASE_URL = 'http://localhost:5000'

def test_server_status():
    """اختبار حالة الخادم"""
    try:
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code == 200:
            print("✅ الخادم يعمل بشكل صحيح")
            return True
        else:
            print(f"❌ الخادم يعمل ولكن يعيد كود خطأ: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار الخادم: {e}")
        return False

def test_login_page():
    """اختبار صفحة تسجيل الدخول"""
    try:
        login_url = urljoin(BASE_URL, '/lawyersameh')
        response = requests.get(login_url, timeout=5)
        
        if response.status_code == 200:
            if 'تسجيل الدخول' in response.text or 'login' in response.text.lower():
                print("✅ صفحة تسجيل الدخول متاحة")
                return True
            else:
                print("⚠️ صفحة تسجيل الدخول متاحة ولكن المحتوى غير متوقع")
                return False
        else:
            print(f"❌ خطأ في الوصول لصفحة تسجيل الدخول: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في اختبار صفحة تسجيل الدخول: {e}")
        return False

def test_static_files():
    """اختبار الملفات الثابتة"""
    static_files = [
        '/static/css/style.css',
        '/static/js/script.js',
        '/static/js/theme-toggle.js'
    ]
    
    working_files = 0
    total_files = len(static_files)
    
    for file_path in static_files:
        try:
            file_url = urljoin(BASE_URL, file_path)
            response = requests.get(file_url, timeout=5)
            
            if response.status_code == 200:
                working_files += 1
                print(f"✅ {file_path}")
            else:
                print(f"❌ {file_path} - كود: {response.status_code}")
        except Exception as e:
            print(f"❌ {file_path} - خطأ: {e}")
    
    print(f"📊 الملفات الثابتة: {working_files}/{total_files} تعمل بشكل صحيح")
    return working_files == total_files

def test_add_pages():
    """اختبار صفحات الإضافة (بدون تسجيل دخول)"""
    add_pages = [
        '/clients/add',
        '/cases/add',
        '/properties/add',
        '/tenants/add',
        '/finance/add',
        '/tasks/add',
        '/appointments/add',
        '/debts/add'
    ]
    
    accessible_pages = 0
    total_pages = len(add_pages)
    
    print("\n🔍 اختبار صفحات الإضافة:")
    print("-" * 40)
    
    for page_path in add_pages:
        try:
            page_url = urljoin(BASE_URL, page_path)
            response = requests.get(page_url, timeout=5, allow_redirects=False)
            
            # إذا كانت الصفحة تعيد توجيه لتسجيل الدخول، فهذا طبيعي
            if response.status_code in [200, 302, 401]:
                accessible_pages += 1
                if response.status_code == 200:
                    print(f"✅ {page_path} - متاح مباشرة")
                else:
                    print(f"🔒 {page_path} - يتطلب تسجيل دخول (طبيعي)")
            else:
                print(f"❌ {page_path} - كود خطأ: {response.status_code}")
        except Exception as e:
            print(f"❌ {page_path} - خطأ: {e}")
    
    print(f"📊 صفحات الإضافة: {accessible_pages}/{total_pages} متاحة")
    return accessible_pages >= total_pages * 0.8  # 80% نجاح مقبول

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار وظائف النظام الأساسية")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # اختبار حالة الخادم
    print("\n1️⃣ اختبار حالة الخادم:")
    if test_server_status():
        tests_passed += 1
    
    # اختبار صفحة تسجيل الدخول
    print("\n2️⃣ اختبار صفحة تسجيل الدخول:")
    if test_login_page():
        tests_passed += 1
    
    # اختبار الملفات الثابتة
    print("\n3️⃣ اختبار الملفات الثابتة:")
    if test_static_files():
        tests_passed += 1
    
    # اختبار صفحات الإضافة
    print("\n4️⃣ اختبار صفحات الإضافة:")
    if test_add_pages():
        tests_passed += 1
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج الاختبار:")
    print(f"✅ اختبارات نجحت: {tests_passed}")
    print(f"❌ اختبارات فشلت: {total_tests - tests_passed}")
    print(f"🎯 معدل النجاح: {(tests_passed/total_tests)*100:.1f}%")
    
    if tests_passed == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! النظام يعمل بشكل صحيح.")
        return True
    elif tests_passed >= total_tests * 0.75:
        print("\n⚠️ معظم الاختبارات نجحت. النظام يعمل بشكل جيد مع بعض المشاكل البسيطة.")
        return True
    else:
        print("\n❌ عدة اختبارات فشلت. يحتاج النظام إلى مراجعة.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
