/**
 * نظام الإشعارات والتنبيهات المتقدم
 * Advanced Notifications and Alerts System
 * 
 * يوفر نظام شامل للإشعارات الفورية والتنبيهات المجدولة
 * Provides comprehensive system for real-time notifications and scheduled alerts
 */

class NotificationsManager {
    constructor() {
        this.notifications = [];
        this.alerts = [];
        this.settings = {
            enabled: true,
            sound: true,
            desktop: true,
            email: false,
            autoHide: true,
            hideDelay: 5000
        };
        this.websocket = null;
        this.init();
    }

    init() {
        this.loadSettings();
        this.setupEventListeners();
        this.initializeWebSocket();
        this.requestNotificationPermission();
        this.loadNotifications();
        this.setupPeriodicChecks();
    }

    loadSettings() {
        const savedSettings = localStorage.getItem('notificationSettings');
        if (savedSettings) {
            this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
        }
    }

    saveSettings() {
        localStorage.setItem('notificationSettings', JSON.stringify(this.settings));
    }

    setupEventListeners() {
        // أزرار التحكم في الإشعارات
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-notification-action]')) {
                const action = e.target.dataset.notificationAction;
                const notificationId = e.target.dataset.notificationId;
                this.handleNotificationAction(action, notificationId);
            }
            
            if (e.target.matches('[data-mark-read]')) {
                const notificationId = e.target.dataset.markRead;
                this.markAsRead(notificationId);
            }
            
            if (e.target.matches('[data-mark-all-read]')) {
                this.markAllAsRead();
            }
            
            if (e.target.matches('[data-clear-notifications]')) {
                this.clearAllNotifications();
            }
            
            if (e.target.matches('[data-notification-settings]')) {
                this.showSettingsModal();
            }
        });

        // إعدادات الإشعارات
        document.addEventListener('change', (e) => {
            if (e.target.matches('.notification-setting')) {
                const setting = e.target.name;
                const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
                this.updateSetting(setting, value);
            }
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                this.showNotificationsPanel();
            }
            
            if (e.ctrlKey && e.shiftKey && e.key === 'N') {
                e.preventDefault();
                this.showSettingsModal();
            }
        });

        // إغلاق الإشعارات عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.notification-panel') && !e.target.matches('[data-toggle-notifications]')) {
                this.hideNotificationsPanel();
            }
        });
    }

    async requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            try {
                const permission = await Notification.requestPermission();
                this.settings.desktop = permission === 'granted';
                this.saveSettings();
            } catch (error) {
                console.warn('خطأ في طلب إذن الإشعارات:', error);
            }
        }
    }

    initializeWebSocket() {
        // إعداد WebSocket للإشعارات الفورية
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/notifications`;
            
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('تم الاتصال بخادم الإشعارات');
                this.showToast('تم الاتصال بنظام الإشعارات', 'success');
            };
            
            this.websocket.onmessage = (event) => {
                const notification = JSON.parse(event.data);
                this.addNotification(notification);
            };
            
            this.websocket.onclose = () => {
                console.log('انقطع الاتصال بخادم الإشعارات');
                // إعادة المحاولة بعد 5 ثوان
                setTimeout(() => this.initializeWebSocket(), 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('خطأ في WebSocket:', error);
            };
        } catch (error) {
            console.warn('WebSocket غير متوفر، سيتم استخدام التحديث الدوري');
            this.setupPolling();
        }
    }

    setupPolling() {
        // التحديث الدوري كبديل للWebSocket
        setInterval(() => {
            this.checkForNewNotifications();
        }, 30000); // كل 30 ثانية
    }

    setupPeriodicChecks() {
        // فحص التنبيهات المجدولة
        setInterval(() => {
            this.checkScheduledAlerts();
        }, 60000); // كل دقيقة

        // فحص المواعيد القادمة
        setInterval(() => {
            this.checkUpcomingAppointments();
        }, 300000); // كل 5 دقائق

        // فحص المهام المتأخرة
        setInterval(() => {
            this.checkOverdueTasks();
        }, 600000); // كل 10 دقائق

        // فحص الاستحقاقات المتأخرة
        setInterval(() => {
            this.checkOverduePayments();
        }, 900000); // كل 15 دقيقة

        // فحص فوري عند التحميل
        setTimeout(() => {
            this.checkScheduledAlerts();
            this.checkUpcomingAppointments();
            this.checkOverdueTasks();
            this.checkOverduePayments();
        }, 3000);
    }

    async loadNotifications() {
        try {
            const response = await fetch('/api/notifications');
            const result = await response.json();
            
            if (result.success) {
                this.notifications = result.notifications;
                this.updateNotificationBadge();
                this.updateNotificationsList();
            }
        } catch (error) {
            console.error('خطأ في تحميل الإشعارات:', error);
        }
    }

    async addNotification(notification) {
        // إضافة الإشعار إلى القائمة
        notification.id = notification.id || Date.now().toString();
        notification.timestamp = notification.timestamp || new Date().toISOString();
        notification.read = notification.read || false;
        
        this.notifications.unshift(notification);
        
        // تحديث الواجهة
        this.updateNotificationBadge();
        this.updateNotificationsList();
        
        // عرض الإشعار
        this.displayNotification(notification);
        
        // حفظ في قاعدة البيانات
        await this.saveNotificationToServer(notification);
    }

    displayNotification(notification) {
        if (!this.settings.enabled) return;

        // إشعار سطح المكتب
        if (this.settings.desktop && 'Notification' in window && Notification.permission === 'granted') {
            const desktopNotification = new Notification(notification.title, {
                body: notification.message,
                icon: '/static/images/logo.png',
                tag: notification.id
            });
            
            desktopNotification.onclick = () => {
                window.focus();
                this.handleNotificationClick(notification);
                desktopNotification.close();
            };
            
            setTimeout(() => desktopNotification.close(), this.settings.hideDelay);
        }

        // إشعار داخل التطبيق
        this.showInAppNotification(notification);
        
        // تشغيل الصوت
        if (this.settings.sound) {
            this.playNotificationSound(notification.type);
        }
    }

    showInAppNotification(notification) {
        const notificationElement = document.createElement('div');
        notificationElement.className = `notification-toast ${notification.type || 'info'}`;
        notificationElement.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="${this.getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-text">
                    <h6 class="notification-title">${notification.title}</h6>
                    <p class="notification-message">${notification.message}</p>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            ${notification.actions ? this.renderNotificationActions(notification.actions) : ''}
        `;
        
        // إضافة إلى الحاوية
        const container = this.getNotificationContainer();
        container.appendChild(notificationElement);
        
        // تأثير الظهور
        setTimeout(() => notificationElement.classList.add('show'), 100);
        
        // إخفاء تلقائي
        if (this.settings.autoHide) {
            setTimeout(() => {
                notificationElement.classList.remove('show');
                setTimeout(() => notificationElement.remove(), 300);
            }, this.settings.hideDelay);
        }
    }

    getNotificationContainer() {
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        return container;
    }

    renderNotificationActions(actions) {
        let html = '<div class="notification-actions">';
        actions.forEach(action => {
            html += `
                <button class="btn btn-sm btn-outline-primary notification-action" 
                        data-notification-action="${action.action}" 
                        data-notification-id="${action.id}">
                    ${action.label}
                </button>
            `;
        });
        html += '</div>';
        return html;
    }

    getNotificationIcon(type) {
        const icons = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-circle',
            'warning': 'fas fa-exclamation-triangle',
            'info': 'fas fa-info-circle',
            'appointment': 'fas fa-calendar-check',
            'task': 'fas fa-tasks',
            'case': 'fas fa-gavel',
            'client': 'fas fa-user',
            'payment': 'fas fa-dollar-sign',
            'document': 'fas fa-file-alt',
            'default': 'fas fa-bell'
        };
        return icons[type] || icons.default;
    }

    playNotificationSound(type) {
        try {
            const audio = new Audio();
            const soundFile = this.getNotificationSound(type);
            audio.src = `/static/sounds/${soundFile}`;
            audio.volume = 0.5;
            audio.play().catch(error => {
                console.warn('لا يمكن تشغيل صوت الإشعار:', error);
            });
        } catch (error) {
            console.warn('خطأ في تشغيل الصوت:', error);
        }
    }

    getNotificationSound(type) {
        const sounds = {
            'success': 'success.mp3',
            'error': 'error.mp3',
            'warning': 'warning.mp3',
            'appointment': 'appointment.mp3',
            'task': 'task.mp3',
            'default': 'notification.mp3'
        };
        return sounds[type] || sounds.default;
    }

    updateNotificationBadge() {
        const unreadCount = this.notifications.filter(n => !n.read).length;
        const badge = document.querySelector('.notification-badge');
        
        if (badge) {
            if (unreadCount > 0) {
                badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                badge.style.display = 'inline-block';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    updateNotificationsList() {
        const list = document.getElementById('notifications-list');
        if (!list) return;
        
        if (this.notifications.length === 0) {
            list.innerHTML = `
                <div class="no-notifications">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد إشعارات</p>
                </div>
            `;
            return;
        }
        
        let html = '';
        this.notifications.slice(0, 50).forEach(notification => {
            html += this.renderNotificationItem(notification);
        });
        
        list.innerHTML = html;
    }

    renderNotificationItem(notification) {
        const timeAgo = this.getTimeAgo(notification.timestamp);
        const readClass = notification.read ? 'read' : 'unread';
        
        return `
            <div class="notification-item ${readClass}" data-notification-id="${notification.id}">
                <div class="notification-icon">
                    <i class="${this.getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content">
                    <h6 class="notification-title">${notification.title}</h6>
                    <p class="notification-message">${notification.message}</p>
                    <small class="notification-time text-muted">${timeAgo}</small>
                </div>
                <div class="notification-actions">
                    ${!notification.read ? `
                        <button class="btn btn-sm btn-outline-primary" data-mark-read="${notification.id}">
                            <i class="fas fa-check"></i>
                        </button>
                    ` : ''}
                    <button class="btn btn-sm btn-outline-danger" data-notification-action="delete" data-notification-id="${notification.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    getTimeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffInSeconds = Math.floor((now - time) / 1000);
        
        if (diffInSeconds < 60) return 'الآن';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} دقيقة`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} ساعة`;
        if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} يوم`;
        
        return time.toLocaleDateString('ar-SA');
    }

    async markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
            notification.read = true;
            this.updateNotificationBadge();
            this.updateNotificationsList();
            
            // تحديث في الخادم
            await this.updateNotificationOnServer(notificationId, { read: true });
        }
    }

    async markAllAsRead() {
        this.notifications.forEach(n => n.read = true);
        this.updateNotificationBadge();
        this.updateNotificationsList();
        
        // تحديث في الخادم
        await fetch('/api/notifications/mark-all-read', { method: 'POST' });
    }

    async clearAllNotifications() {
        this.notifications = [];
        this.updateNotificationBadge();
        this.updateNotificationsList();
        
        // حذف من الخادم
        await fetch('/api/notifications/clear-all', { method: 'DELETE' });
    }

    showNotificationsPanel() {
        const panel = document.getElementById('notifications-panel');
        if (panel) {
            panel.classList.toggle('show');
        }
    }

    hideNotificationsPanel() {
        const panel = document.getElementById('notifications-panel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    // فحص التنبيهات المجدولة
    async checkScheduledAlerts() {
        try {
            const response = await fetch('/api/alerts/check');
            const result = await response.json();
            
            if (result.success && result.alerts) {
                result.alerts.forEach(alert => {
                    this.addNotification({
                        title: alert.title,
                        message: alert.message,
                        type: alert.type,
                        priority: alert.priority
                    });
                });
            }
        } catch (error) {
            console.error('خطأ في فحص التنبيهات:', error);
        }
    }

    // فحص المواعيد القادمة
    async checkUpcomingAppointments() {
        try {
            const response = await fetch('/api/appointments/upcoming');
            const result = await response.json();
            
            if (result.success && result.appointments) {
                result.appointments.forEach(appointment => {
                    this.addNotification({
                        title: 'موعد قادم',
                        message: `موعد مع ${appointment.client_name} في ${appointment.time}`,
                        type: 'appointment',
                        priority: 'high'
                    });
                });
            }
        } catch (error) {
            console.error('خطأ في فحص المواعيد:', error);
        }
    }

    // فحص المهام المتأخرة
    async checkOverdueTasks() {
        try {
            const response = await fetch('/api/tasks/overdue');
            const result = await response.json();
            
            if (result.success && result.tasks) {
                result.tasks.forEach(task => {
                    this.addNotification({
                        title: 'مهمة متأخرة',
                        message: `المهمة "${task.title}" متأخرة عن الموعد المحدد`,
                        type: 'warning',
                        priority: 'high'
                    });
                });
            }
        } catch (error) {
            console.error('خطأ في فحص المهام:', error);
        }
    }

    // حفظ الإشعار في الخادم
    async saveNotificationToServer(notification) {
        try {
            await fetch('/api/notifications', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(notification)
            });
        } catch (error) {
            console.error('خطأ في حفظ الإشعار:', error);
        }
    }

    // تحديث الإشعار في الخادم
    async updateNotificationOnServer(id, updates) {
        try {
            await fetch(`/api/notifications/${id}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(updates)
            });
        } catch (error) {
            console.error('خطأ في تحديث الإشعار:', error);
        }
    }

    // وظائف مساعدة
    updateSetting(setting, value) {
        this.settings[setting] = value;
        this.saveSettings();
    }

    handleNotificationAction(action, notificationId) {
        switch (action) {
            case 'delete':
                this.deleteNotification(notificationId);
                break;
            case 'view':
                this.viewNotification(notificationId);
                break;
            default:
                console.log(`إجراء غير معروف: ${action}`);
        }
    }

    deleteNotification(notificationId) {
        this.notifications = this.notifications.filter(n => n.id !== notificationId);
        this.updateNotificationBadge();
        this.updateNotificationsList();
    }

    showToast(message, type = 'info') {
        this.addNotification({
            title: 'إشعار',
            message: message,
            type: type,
            priority: 'low'
        });
    }

    showSettingsModal() {
        // عرض نافذة إعدادات الإشعارات
        const modal = document.getElementById('notificationSettingsModal');
        if (modal) {
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // تحديث القيم الحالية
            this.updateSettingsForm();
        }
    }

    updateSettingsForm() {
        const form = document.getElementById('notificationSettingsForm');
        if (!form) return;

        Object.keys(this.settings).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                if (input.type === 'checkbox') {
                    input.checked = this.settings[key];
                } else {
                    input.value = this.settings[key];
                }
            }
        });
    }

    async checkForNewNotifications() {
        try {
            const lastCheck = localStorage.getItem('lastNotificationCheck') || '0';
            const response = await fetch(`/api/notifications/new?since=${lastCheck}`);
            const result = await response.json();

            if (result.success && result.notifications) {
                result.notifications.forEach(notification => {
                    this.addNotification(notification);
                });

                localStorage.setItem('lastNotificationCheck', Date.now().toString());
            }
        } catch (error) {
            console.error('خطأ في فحص الإشعارات الجديدة:', error);
        }
    }

    handleNotificationClick(notification) {
        // التعامل مع النقر على الإشعار
        if (notification.url) {
            window.location.href = notification.url;
        } else if (notification.action) {
            this.executeNotificationAction(notification.action, notification.data);
        }

        // تمييز كمقروء
        this.markAsRead(notification.id);
    }

    executeNotificationAction(action, data) {
        switch (action) {
            case 'open_case':
                window.location.href = `/cases/${data.case_id}`;
                break;
            case 'open_client':
                window.location.href = `/clients/${data.client_id}`;
                break;
            case 'open_appointment':
                window.location.href = `/calendar?event=${data.event_id}`;
                break;
            case 'open_task':
                window.location.href = `/tasks/${data.task_id}`;
                break;
            default:
                console.log(`إجراء غير معروف: ${action}`);
        }
    }

    // إنشاء إشعارات مخصصة
    createCustomNotification(title, message, type = 'info', options = {}) {
        const notification = {
            title: title,
            message: message,
            type: type,
            priority: options.priority || 'normal',
            url: options.url,
            action: options.action,
            data: options.data,
            persistent: options.persistent || false
        };

        this.addNotification(notification);
        return notification;
    }

    // إنشاء تنبيه مجدول
    async createScheduledAlert(title, message, scheduleTime, options = {}) {
        const alert = {
            title: title,
            message: message,
            schedule_time: scheduleTime,
            type: options.type || 'info',
            priority: options.priority || 'normal',
            repeat: options.repeat || 'none',
            enabled: true
        };

        try {
            const response = await fetch('/api/alerts', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(alert)
            });

            const result = await response.json();
            if (result.success) {
                this.showToast('تم إنشاء التنبيه بنجاح', 'success');
                return result.alert;
            }
        } catch (error) {
            console.error('خطأ في إنشاء التنبيه:', error);
            this.showToast('خطأ في إنشاء التنبيه', 'error');
        }
    }

    // تصدير الإشعارات
    exportNotifications(format = 'json') {
        const data = {
            notifications: this.notifications,
            exported_at: new Date().toISOString(),
            total_count: this.notifications.length
        };

        let content, filename, mimeType;

        if (format === 'json') {
            content = JSON.stringify(data, null, 2);
            filename = `notifications_${new Date().toISOString().split('T')[0]}.json`;
            mimeType = 'application/json';
        } else if (format === 'csv') {
            content = this.convertNotificationsToCSV(this.notifications);
            filename = `notifications_${new Date().toISOString().split('T')[0]}.csv`;
            mimeType = 'text/csv';
        }

        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
    }

    convertNotificationsToCSV(notifications) {
        const headers = ['ID', 'Title', 'Message', 'Type', 'Priority', 'Read', 'Timestamp'];
        const rows = notifications.map(n => [
            n.id,
            `"${n.title}"`,
            `"${n.message}"`,
            n.type,
            n.priority,
            n.read ? 'Yes' : 'No',
            n.timestamp
        ]);

        return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    }

    // إحصائيات الإشعارات
    getNotificationStats() {
        const total = this.notifications.length;
        const unread = this.notifications.filter(n => !n.read).length;
        const byType = {};
        const byPriority = {};

        this.notifications.forEach(n => {
            byType[n.type] = (byType[n.type] || 0) + 1;
            byPriority[n.priority] = (byPriority[n.priority] || 0) + 1;
        });

        return {
            total,
            unread,
            read: total - unread,
            byType,
            byPriority
        };
    }

    // تنظيف الإشعارات القديمة
    cleanupOldNotifications(daysOld = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysOld);

        const initialCount = this.notifications.length;
        this.notifications = this.notifications.filter(n => {
            const notificationDate = new Date(n.timestamp);
            return notificationDate > cutoffDate || !n.read;
        });

        const removedCount = initialCount - this.notifications.length;
        if (removedCount > 0) {
            this.updateNotificationBadge();
            this.updateNotificationsList();
            this.showToast(`تم حذف ${removedCount} إشعار قديم`, 'info');
        }

        return removedCount;
    }

    // فحص المهام المتأخرة
    async checkOverdueTasks() {
        try {
            const response = await fetch('/api/overdue-tasks');
            const result = await response.json();

            if (result.success && result.tasks.length > 0) {
                result.tasks.forEach(task => {
                    this.createNotification({
                        title: '⚠️ مهمة متأخرة',
                        message: `المهمة "${task.title}" متأخرة منذ ${this.formatDate(task.due_date)}`,
                        type: 'warning',
                        priority: 'high',
                        persistent: true,
                        data: { type: 'overdue_task', task_id: task.id }
                    });
                });
            }
        } catch (error) {
            console.error('خطأ في فحص المهام المتأخرة:', error);
        }
    }

    // فحص المواعيد القادمة
    async checkUpcomingAppointments() {
        try {
            const response = await fetch('/api/upcoming-appointments');
            const result = await response.json();

            if (result.success && result.appointments.length > 0) {
                result.appointments.forEach(appointment => {
                    this.createNotification({
                        title: '📅 موعد قادم',
                        message: `موعد "${appointment.subject}" في ${this.formatDateTime(appointment.date)}`,
                        type: 'info',
                        priority: 'medium',
                        data: { type: 'upcoming_appointment', appointment_id: appointment.id }
                    });
                });
            }
        } catch (error) {
            console.error('خطأ في فحص المواعيد القادمة:', error);
        }
    }

    // فحص الاستحقاقات المتأخرة
    async checkOverduePayments() {
        try {
            const response = await fetch('/api/overdue-payments');
            const result = await response.json();

            if (result.success && result.payments.length > 0) {
                result.payments.forEach(payment => {
                    this.createNotification({
                        title: '💰 استحقاق متأخر',
                        message: `استحقاق بقيمة ${payment.amount} ${payment.currency} متأخر منذ ${this.formatDate(payment.due_date)}`,
                        type: 'error',
                        priority: 'high',
                        persistent: true,
                        data: { type: 'overdue_payment', payment_id: payment.id }
                    });
                });
            }
        } catch (error) {
            console.error('خطأ في فحص الاستحقاقات المتأخرة:', error);
        }
    }

    // تنسيق التاريخ
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    // تنسيق التاريخ والوقت
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('ar-SA');
    }
}

// تهيئة نظام الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.notificationsManager = new NotificationsManager();
});
