#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التقرير الشامل النهائي لحالة النظام
Final Comprehensive System Status Report
"""

import sqlite3
import os
import time
from datetime import datetime
import json

class FinalSystemReport:
    def __init__(self):
        self.db_path = 'instance/lawoffice.db'
        self.report_data = {}
    
    def check_database_status(self):
        """فحص حالة قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # فحص الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # فحص المستخدمين
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            # فحص البيانات الرئيسية
            main_tables_data = {}
            main_tables = ['clients', 'cases', 'properties', 'tenants', 'leases', 
                          'financial_transactions', 'fees', 'debts', 'tasks', 'appointments']
            
            for table in main_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    main_tables_data[table] = count
                except:
                    main_tables_data[table] = 0
            
            # فحص النسخ الاحتياطية
            backup_count = 0
            try:
                cursor.execute("SELECT COUNT(*) FROM backup_logs")
                backup_count = cursor.fetchone()[0]
            except:
                pass
            
            # فحص سجلات الاختبارات
            test_logs_count = 0
            try:
                cursor.execute("SELECT COUNT(*) FROM test_logs")
                test_logs_count = cursor.fetchone()[0]
            except:
                pass
            
            # فحص سجلات الأداء
            performance_logs_count = 0
            try:
                cursor.execute("SELECT COUNT(*) FROM performance_logs")
                performance_logs_count = cursor.fetchone()[0]
            except:
                pass
            
            conn.close()
            
            self.report_data['database'] = {
                'status': 'operational',
                'total_tables': len(tables),
                'user_accounts': user_count,
                'main_tables_data': main_tables_data,
                'backup_logs': backup_count,
                'test_logs': test_logs_count,
                'performance_logs': performance_logs_count,
                'db_size_mb': os.path.getsize(self.db_path) / 1024 / 1024
            }
            
            return True
            
        except Exception as e:
            self.report_data['database'] = {
                'status': 'error',
                'error': str(e)
            }
            return False
    
    def check_authentication_status(self):
        """فحص حالة المصادقة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # فحص حسابات المستخدمين
            cursor.execute("SELECT username, role FROM users")
            users = cursor.fetchall()
            
            # التحقق من وجود الحسابات المطلوبة
            required_accounts = ['office', 'admin']
            existing_accounts = [user[0] for user in users]
            
            missing_accounts = list(set(required_accounts) - set(existing_accounts))
            
            conn.close()
            
            self.report_data['authentication'] = {
                'status': 'operational' if len(missing_accounts) == 0 else 'incomplete',
                'total_users': len(users),
                'existing_accounts': existing_accounts,
                'missing_accounts': missing_accounts,
                'user_details': [{'username': u[0], 'role': u[1]} for u in users]
            }
            
            return len(missing_accounts) == 0
            
        except Exception as e:
            self.report_data['authentication'] = {
                'status': 'error',
                'error': str(e)
            }
            return False
    
    def check_file_structure(self):
        """فحص هيكل الملفات"""
        required_files = [
            'app/routes.py',
            'app/models.py',
            'app/__init__.py',
            'run.py',
            'instance/lawoffice.db'
        ]
        
        required_directories = [
            'app/templates',
            'app/static',
            'instance',
            'backups'
        ]
        
        existing_files = []
        missing_files = []
        
        for file_path in required_files:
            if os.path.exists(file_path):
                existing_files.append(file_path)
            else:
                missing_files.append(file_path)
        
        existing_dirs = []
        missing_dirs = []
        
        for dir_path in required_directories:
            if os.path.exists(dir_path):
                existing_dirs.append(dir_path)
            else:
                missing_dirs.append(dir_path)
        
        # فحص ملفات النسخ الاحتياطية
        backup_files = []
        if os.path.exists('backups'):
            backup_files = [f for f in os.listdir('backups') if f.endswith('.zip')]
        
        self.report_data['file_structure'] = {
            'status': 'complete' if len(missing_files) == 0 and len(missing_dirs) == 0 else 'incomplete',
            'existing_files': existing_files,
            'missing_files': missing_files,
            'existing_directories': existing_dirs,
            'missing_directories': missing_dirs,
            'backup_files_count': len(backup_files)
        }
        
        return len(missing_files) == 0 and len(missing_dirs) == 0
    
    def check_system_functionality(self):
        """فحص وظائف النظام"""
        functionality_status = {}
        
        # فحص إمكانية الاتصال بقاعدة البيانات
        try:
            conn = sqlite3.connect(self.db_path)
            conn.close()
            functionality_status['database_connection'] = True
        except:
            functionality_status['database_connection'] = False
        
        # فحص وجود البيانات التجريبية
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM clients")
            clients_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM cases")
            cases_count = cursor.fetchone()[0]
            
            functionality_status['test_data_available'] = clients_count > 0 and cases_count > 0
            
            conn.close()
        except:
            functionality_status['test_data_available'] = False
        
        # فحص أنظمة المراقبة
        monitoring_systems = {
            'backup_system': os.path.exists('backup_system.py'),
            'testing_system': os.path.exists('periodic_testing_system.py'),
            'performance_monitoring': os.path.exists('performance_monitoring_system.py')
        }
        
        functionality_status.update(monitoring_systems)
        
        self.report_data['functionality'] = {
            'status': 'operational' if all(functionality_status.values()) else 'partial',
            'details': functionality_status
        }
        
        return all(functionality_status.values())
    
    def generate_comprehensive_report(self):
        """إنشاء التقرير الشامل"""
        print("📋 إنشاء التقرير الشامل النهائي للنظام")
        print("=" * 70)
        
        # فحص جميع المكونات
        print("🔍 فحص مكونات النظام...")
        
        db_status = self.check_database_status()
        auth_status = self.check_authentication_status()
        file_status = self.check_file_structure()
        func_status = self.check_system_functionality()
        
        # حساب النتيجة الإجمالية
        total_score = 0
        max_score = 100
        
        # نقاط قاعدة البيانات (30 نقطة)
        if db_status:
            db_score = 30
            if self.report_data['database']['user_accounts'] >= 2:
                db_score += 5
            if sum(self.report_data['database']['main_tables_data'].values()) > 20:
                db_score += 5
        else:
            db_score = 0
        total_score += min(db_score, 40)
        
        # نقاط المصادقة (20 نقطة)
        auth_score = 20 if auth_status else 0
        total_score += auth_score
        
        # نقاط هيكل الملفات (20 نقطة)
        file_score = 20 if file_status else 10
        total_score += file_score
        
        # نقاط الوظائف (20 نقطة)
        func_score = 20 if func_status else 10
        total_score += func_score
        
        # تقييم عام
        if total_score >= 90:
            overall_status = "ممتاز"
            status_emoji = "🎉"
        elif total_score >= 70:
            overall_status = "جيد جداً"
            status_emoji = "✅"
        elif total_score >= 50:
            overall_status = "جيد"
            status_emoji = "⚠️"
        else:
            overall_status = "يحتاج تحسين"
            status_emoji = "❌"
        
        # طباعة التقرير
        print(f"\n{status_emoji} التقييم العام: {overall_status}")
        print(f"📊 النتيجة الإجمالية: {total_score}/{max_score}")
        print("=" * 70)
        
        # تفاصيل قاعدة البيانات
        print("\n🗄️ حالة قاعدة البيانات:")
        if db_status:
            db_data = self.report_data['database']
            print(f"   ✅ الحالة: تعمل بشكل طبيعي")
            print(f"   📊 عدد الجداول: {db_data['total_tables']}")
            print(f"   👥 حسابات المستخدمين: {db_data['user_accounts']}")
            print(f"   📁 حجم قاعدة البيانات: {db_data['db_size_mb']:.2f} MB")
            print(f"   📋 إجمالي السجلات: {sum(db_data['main_tables_data'].values())}")
            print("   📈 توزيع البيانات:")
            for table, count in db_data['main_tables_data'].items():
                if count > 0:
                    print(f"      - {table}: {count} سجل")
        else:
            print(f"   ❌ خطأ في قاعدة البيانات: {self.report_data['database']['error']}")
        
        # تفاصيل المصادقة
        print("\n🔐 حالة نظام المصادقة:")
        if auth_status:
            auth_data = self.report_data['authentication']
            print(f"   ✅ الحالة: يعمل بشكل طبيعي")
            print(f"   👤 عدد المستخدمين: {auth_data['total_users']}")
            print("   📝 الحسابات المتاحة:")
            for user in auth_data['user_details']:
                print(f"      - {user['username']} ({user['role']})")
        else:
            auth_data = self.report_data['authentication']
            if 'missing_accounts' in auth_data:
                print(f"   ⚠️ حسابات مفقودة: {', '.join(auth_data['missing_accounts'])}")
            else:
                print(f"   ❌ خطأ في نظام المصادقة: {auth_data.get('error', 'غير محدد')}")
        
        # تفاصيل هيكل الملفات
        print("\n📁 حالة هيكل الملفات:")
        file_data = self.report_data['file_structure']
        if file_status:
            print(f"   ✅ الحالة: مكتمل")
        else:
            print(f"   ⚠️ الحالة: غير مكتمل")
            if file_data['missing_files']:
                print(f"   📄 ملفات مفقودة: {', '.join(file_data['missing_files'])}")
            if file_data['missing_directories']:
                print(f"   📂 مجلدات مفقودة: {', '.join(file_data['missing_directories'])}")
        
        print(f"   💾 ملفات النسخ الاحتياطية: {file_data['backup_files_count']}")
        
        # تفاصيل الوظائف
        print("\n⚙️ حالة وظائف النظام:")
        func_data = self.report_data['functionality']
        if func_status:
            print(f"   ✅ الحالة: تعمل بالكامل")
        else:
            print(f"   ⚠️ الحالة: تعمل جزئياً")
        
        print("   🔧 تفاصيل الوظائف:")
        for func_name, status in func_data['details'].items():
            status_icon = "✅" if status else "❌"
            print(f"      {status_icon} {func_name}")
        
        # التوصيات
        print("\n💡 التوصيات:")
        recommendations = []
        
        if not auth_status:
            recommendations.append("إنشاء حسابات المستخدمين المفقودة")
        
        if not file_status:
            recommendations.append("استكمال هيكل الملفات المطلوب")
        
        if not func_status:
            recommendations.append("إصلاح الوظائف غير العاملة")
        
        if total_score < 90:
            recommendations.append("تشغيل اختبارات شاملة للنظام")
        
        if len(recommendations) == 0:
            print("   🎉 النظام يعمل بشكل مثالي - لا توجد توصيات!")
        else:
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
        
        # معلومات إضافية
        print(f"\n📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🔄 للحصول على تقرير محدث، قم بتشغيل هذا السكريبت مرة أخرى")
        
        # حفظ التقرير
        self.save_report_to_file(total_score, overall_status)
        
        return total_score, overall_status
    
    def save_report_to_file(self, score, status):
        """حفظ التقرير في ملف"""
        try:
            report_filename = f"system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            report_summary = {
                'timestamp': datetime.now().isoformat(),
                'overall_score': score,
                'overall_status': status,
                'detailed_data': self.report_data
            }
            
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(report_summary, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 تم حفظ التقرير في: {report_filename}")
            
        except Exception as e:
            print(f"\n⚠️ تحذير: لم يتم حفظ التقرير: {e}")

def main():
    """الدالة الرئيسية"""
    report_system = FinalSystemReport()
    
    print("📋 نظام التقرير الشامل النهائي")
    print("=" * 50)
    
    score, status = report_system.generate_comprehensive_report()
    
    print(f"\n🎯 خلاصة التقييم: {status} ({score}/100)")

if __name__ == "__main__":
    main()
