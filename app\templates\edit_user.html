{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
    <h2 class="mb-4 text-center">تعديل بيانات المستخدم</h2>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">اسم المستخدم</label>
            <input type="text" name="username" class="form-control" value="{{ user.username }}" required>
        </div>
        <div class="mb-3">
            <label class="form-label">كلمة المرور الجديدة (اتركها فارغة للإبقاء على الحالية)</label>
            <input type="password" name="password" class="form-control">
        </div>
        <div class="mb-3">
            <label class="form-label">الصلاحية</label>
            <select name="role" class="form-select">
                <option value="مدير" {% if user.role == 'مدير' %}selected{% endif %}>مدير</option>
                <option value="موظف" {% if user.role == 'موظف' %}selected{% endif %}>موظف</option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary">حفظ التعديلات</button>
        <a href="/users" class="btn btn-secondary">إلغاء</a>
    </form>
</div>
{% endblock %}
