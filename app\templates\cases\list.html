{% extends "base.html" %}

{% block title %}قائمة القضايا{% endblock %}
{% block page_title %}قائمة القضايا{% endblock %}

{% block content %}
<style>
    /* ===== تصميم صفحة القضايا الاحترافية ===== */
    .page-header {
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-2xl);
        box-shadow: var(--shadow-md);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .page-header h1 {
        font-size: var(--text-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .cases-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: var(--spacing-xl);
    }
    
    .case-card {
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-md);
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
    }
    
    .case-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }
    
    .case-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    }
    
    .case-card.active::before {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
    
    .case-card.pending::before {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }
    
    .case-card.closed::before {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    }
    
    .case-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: var(--spacing-lg);
    }
    
    .case-info h3 {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;
        line-height: 1.4;
    }
    
    .case-number {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }
    
    .case-status {
        padding: var(--spacing-xs) var(--spacing-md);
        border-radius: var(--radius-full);
        font-size: var(--text-xs);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-active {
        background: rgba(16, 185, 129, 0.1);
        color: #059669;
        border: 1px solid rgba(16, 185, 129, 0.2);
    }
    
    .status-pending {
        background: rgba(245, 158, 11, 0.1);
        color: #d97706;
        border: 1px solid rgba(245, 158, 11, 0.2);
    }
    
    .status-closed {
        background: rgba(107, 114, 128, 0.1);
        color: #4b5563;
        border: 1px solid rgba(107, 114, 128, 0.2);
    }
    
    .case-details {
        margin-bottom: var(--spacing-lg);
    }
    
    .detail-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
        font-size: var(--text-sm);
    }
    
    .detail-item i {
        width: 16px;
        color: var(--text-secondary);
    }
    
    .case-description {
        background: var(--bg-tertiary);
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        margin-bottom: var(--spacing-lg);
        font-size: var(--text-sm);
        line-height: 1.6;
        color: var(--text-secondary);
    }
    
    .case-actions {
        display: flex;
        gap: var(--spacing-sm);
        justify-content: flex-end;
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--border-color);
    }
    
    .btn-action {
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-md);
        font-size: var(--text-sm);
        font-weight: 500;
        text-decoration: none;
        transition: var(--transition-normal);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        border: none;
        cursor: pointer;
    }
    
    .btn-view {
        background: var(--bg-tertiary);
        color: var(--text-primary);
        border: 1px solid var(--border-color);
    }
    
    .btn-view:hover {
        background: var(--primary-color);
        color: var(--text-white);
    }
    
    .btn-edit {
        background: var(--warning-color);
        color: var(--text-white);
    }
    
    .btn-edit:hover {
        background: #d97706;
    }
    
    .btn-delete {
        background: var(--danger-color);
        color: var(--text-white);
    }
    
    .btn-delete:hover {
        background: #dc2626;
    }
    
    .empty-state {
        text-align: center;
        padding: var(--spacing-4xl);
        color: var(--text-secondary);
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-md);
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: var(--spacing-lg);
        opacity: 0.5;
    }
    
    /* ===== الرسوم المتحركة ===== */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .case-card {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;
    }
    
    .case-card:nth-child(1) { animation-delay: 0.1s; }
    .case-card:nth-child(2) { animation-delay: 0.2s; }
    .case-card:nth-child(3) { animation-delay: 0.3s; }
    .case-card:nth-child(4) { animation-delay: 0.4s; }
    .case-card:nth-child(5) { animation-delay: 0.5s; }
    .case-card:nth-child(6) { animation-delay: 0.6s; }
    
    /* ===== الاستجابة للشاشات الصغيرة ===== */
    @media (max-width: 768px) {
        .cases-grid {
            grid-template-columns: 1fr;
        }
        
        .page-header {
            flex-direction: column;
            gap: var(--spacing-md);
            align-items: stretch;
        }
    }
</style>

<!-- رأس الصفحة -->
<div class="page-header">
    <h1>
        <i class="fas fa-gavel"></i>
        قائمة القضايا
    </h1>
    <div class="btn-group">
        <a href="{{ url_for('add_case') }}" class="btn btn-success">
            <i class="fas fa-plus"></i>
            إضافة قضية جديدة
        </a>
        <button type="button" class="btn btn-primary"
                onclick="openFormModal('{{ url_for('modal_add_case') }}', 'إضافة قضية جديدة', {
                    size: 'xl',
                    onSuccess: function() { window.location.reload(); }
                })">
            <i class="fas fa-plus-circle"></i>
            إضافة سريعة
        </button>
    </div>
</div>

<!-- قائمة القضايا -->
{% if cases %}
<div class="cases-grid">
    {% for case in cases %}
    <div class="case-card {{ case.status|lower if case.status else 'pending' }}">
        <div class="case-header">
            <div class="case-info">
                <h3>{{ case.title }}</h3>
                <div class="case-number">رقم القضية: {{ case.case_number or case.id }}</div>
            </div>
            <div class="case-status status-{{ case.status|lower if case.status else 'pending' }}">
                {% if case.status == 'active' %}
                    نشطة
                {% elif case.status == 'closed' %}
                    مغلقة
                {% else %}
                    معلقة
                {% endif %}
            </div>
        </div>
        
        <div class="case-details">
            {% if case.client %}
            <div class="detail-item">
                <i class="fas fa-user"></i>
                <span>العميل: {{ case.client.name }}</span>
            </div>
            {% endif %}
            
            {% if case.court %}
            <div class="detail-item">
                <i class="fas fa-university"></i>
                <span>المحكمة: {{ case.court }}</span>
            </div>
            {% endif %}
            
            {% if case.case_type %}
            <div class="detail-item">
                <i class="fas fa-tag"></i>
                <span>نوع القضية: {{ case.case_type }}</span>
            </div>
            {% endif %}
            
            <div class="detail-item">
                <i class="fas fa-calendar"></i>
                <span>تاريخ الإنشاء: {{ case.created_at.strftime('%Y-%m-%d') if case.created_at else 'غير محدد' }}</span>
            </div>
            
            {% if case.next_hearing %}
            <div class="detail-item">
                <i class="fas fa-clock"></i>
                <span>الجلسة القادمة: {{ case.next_hearing.strftime('%Y-%m-%d') }}</span>
            </div>
            {% endif %}
        </div>
        
        {% if case.description %}
        <div class="case-description">
            {{ case.description[:150] }}{% if case.description|length > 150 %}...{% endif %}
        </div>
        {% endif %}
        
        <div class="case-actions">
            <a href="{{ url_for('edit_case', case_id=case.id) }}" class="btn-action btn-edit">
                <i class="fas fa-edit"></i>
                تعديل
            </a>
            <button type="button" class="btn-action btn-delete" onclick="confirmDelete({{ case.id }}, '{{ case.title }}')">
                <i class="fas fa-trash"></i>
                حذف
            </button>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="empty-state">
    <i class="fas fa-gavel"></i>
    <h3>لا توجد قضايا</h3>
    <p>لم يتم العثور على أي قضايا. ابدأ بإضافة قضية جديدة.</p>
    <div class="btn-group">
        <a href="{{ url_for('add_case') }}" class="btn btn-success">
            <i class="fas fa-plus"></i>
            إضافة قضية جديدة
        </a>
        <button type="button" class="btn btn-primary"
                onclick="openFormModal('{{ url_for('modal_add_case') }}', 'إضافة قضية جديدة', {
                    size: 'xl',
                    onSuccess: function() { window.location.reload(); }
                })">
            <i class="fas fa-plus-circle"></i>
            إضافة سريعة
        </button>
    </div>
</div>
{% endif %}



<script>
function confirmDelete(caseId, caseTitle) {
    if (confirm('هل أنت متأكد من حذف القضية "' + caseTitle + '"؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        // إرسال طلب الحذف
        fetch('/cases/' + caseId + '/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف القضية');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف القضية');
        });
    }
}
</script>
{% endblock %}
