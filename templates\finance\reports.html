{% extends "base.html" %}

{% block title %}التقارير المالية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">التقارير المالية</h3>
                </div>
                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="client_id">العميل</label>
                                    <select class="form-control" id="client_id" name="client_id">
                                        <option value="">جميع العملاء</option>
                                        {% for client in clients %}
                                        <option value="{{ client.id }}" {% if request.args.get('client_id') == client.id|string %}selected{% endif %}>{{ client.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="case_id">القضية</label>
                                    <select class="form-control" id="case_id" name="case_id">
                                        <option value="">جميع القضايا</option>
                                        {% for case in cases %}
                                        <option value="{{ case.id }}" {% if request.args.get('case_id') == case.id|string %}selected{% endif %}>{{ case.case_number }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="transaction_type">نوع المعاملة</label>
                                    <select class="form-control" id="transaction_type" name="transaction_type">
                                        <option value="">جميع الأنواع</option>
                                        <option value="قبض" {% if request.args.get('transaction_type') == 'قبض' %}selected{% endif %}>قبض</option>
                                        <option value="دفعة" {% if request.args.get('transaction_type') == 'دفعة' %}selected{% endif %}>دفعة</option>
                                        <option value="أتعاب" {% if request.args.get('transaction_type') == 'أتعاب' %}selected{% endif %}>أتعاب</option>
                                        <option value="صرف" {% if request.args.get('transaction_type') == 'صرف' %}selected{% endif %}>صرف</option>
                                        <option value="مصاريف" {% if request.args.get('transaction_type') == 'مصاريف' %}selected{% endif %}>مصاريف</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">بحث</button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="date_from">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request.args.get('date_from', '') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="date_to">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request.args.get('date_to', '') }}">
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- ملخص مالي -->
                    {% if summary %}
                    <div class="row mb-4">
                        {% for currency, data in summary.items() %}
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">{{ currency }}</h5>
                                    <p class="card-text">
                                        <strong>الإيرادات:</strong> {{ "%.2f"|format(data.income) }}<br>
                                        <strong>المصروفات:</strong> {{ "%.2f"|format(data.expense) }}<br>
                                        <strong>الرصيد:</strong> 
                                        <span class="{% if data.balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                            {{ "%.2f"|format(data.balance) }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- إحصائيات -->
                    {% if stats %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">إحصائيات التقرير</h5>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong>إجمالي المعاملات:</strong> {{ stats.total_transactions }}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>عدد العملاء:</strong> {{ stats.total_clients }}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>عدد القضايا:</strong> {{ stats.total_cases }}
                                        </div>
                                        <div class="col-md-3">
                                            {% if stats.date_range.from and stats.date_range.to %}
                                            <strong>الفترة:</strong> {{ stats.date_range.from.strftime('%Y-%m-%d') }} - {{ stats.date_range.to.strftime('%Y-%m-%d') }}
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- جدول المعاملات -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>العملة</th>
                                    <th>العميل</th>
                                    <th>القضية</th>
                                    <th>الوصف</th>
                                    <th>طريقة الدفع</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions %}
                                <tr>
                                    <td>{{ transaction.date.strftime('%Y-%m-%d') if transaction.date else '' }}</td>
                                    <td>
                                        <span class="badge {% if transaction.type in ['قبض', 'دفعة', 'أتعاب', 'تحصيل إيجار'] %}badge-success{% else %}badge-danger{% endif %}">
                                            {{ transaction.type }}
                                        </span>
                                    </td>
                                    <td>{{ "%.2f"|format(transaction.amount) }}</td>
                                    <td>{{ transaction.currency or 'شيكل' }}</td>
                                    <td>{{ transaction.client.name if transaction.client else '' }}</td>
                                    <td>{{ transaction.case.case_number if transaction.case else '' }}</td>
                                    <td>{{ transaction.description }}</td>
                                    <td>{{ transaction.payment_method or 'نقدي' }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد معاملات مالية</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- أزرار التصدير -->
                    <div class="mt-3">
                        <button class="btn btn-success" onclick="exportToExcel()">تصدير إلى Excel</button>
                        <button class="btn btn-info" onclick="printReport()">طباعة التقرير</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function exportToExcel() {
    // تصدير إلى Excel
    var table = document.querySelector('table');
    var wb = XLSX.utils.table_to_book(table, {sheet: "التقرير المالي"});
    XLSX.writeFile(wb, 'تقرير_مالي_' + new Date().toISOString().split('T')[0] + '.xlsx');
}

function printReport() {
    window.print();
}
</script>
{% endblock %}
