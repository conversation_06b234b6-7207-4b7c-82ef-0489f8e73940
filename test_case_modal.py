#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
from datetime import datetime

BASE_URL = "http://localhost:5000"

def test_case_modal():
    """اختبار modal إضافة القضية"""
    print("🧪 اختبار modal إضافة القضية...")
    
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {'username': 'office', 'password': '66889088'}
    login_response = session.post(f"{BASE_URL}/lawyersameh", data=login_data)
    
    if login_response.status_code != 200:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # الحصول على modal إضافة القضية
    modal_response = session.get(f"{BASE_URL}/modal/add_case")
    
    if modal_response.status_code != 200:
        print(f"❌ فشل في الوصول للـ modal: {modal_response.status_code}")
        return False
    
    print("✅ تم الوصول للـ modal بنجاح")
    
    # البحث عن CSRF token
    soup = BeautifulSoup(modal_response.text, 'html.parser')
    csrf_input = soup.find('input', {'name': 'csrf_token'})
    
    if not csrf_input or not csrf_input.get('value'):
        print("❌ CSRF token مفقود")
        return False
    
    csrf_token = csrf_input.get('value')
    print(f"✅ CSRF token موجود: {csrf_token[:15]}...")
    
    # فحص الحقول المطلوبة
    required_fields = ['title', 'case_number', 'type', 'status', 'court', 'opponent', 'description']
    missing_fields = []
    
    for field in required_fields:
        field_element = soup.find(['input', 'select', 'textarea'], {'name': field})
        if not field_element:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"❌ حقول مفقودة: {missing_fields}")
        return False
    
    print("✅ جميع الحقول المطلوبة موجودة")
    
    # إعداد بيانات القضية
    case_data = {
        'title': f'قضية اختبار {datetime.now().strftime("%H%M%S")}',
        'case_number': f'TEST-{datetime.now().strftime("%H%M%S")}',
        'type': 'مدني',
        'status': 'جديدة',
        'court': 'محكمة اختبار',
        'opponent': 'خصم اختبار',
        'description': 'وصف قضية اختبار',
        'client_id': '1',  # افتراض وجود عميل برقم 1
        'client_role': 'مدعي',
        'csrf_token': csrf_token
    }
    
    print("🔄 محاولة إضافة القضية...")
    
    # إرسال البيانات
    add_response = session.post(f"{BASE_URL}/cases/add", data=case_data)
    
    if add_response.status_code in [200, 302]:
        print("✅ تم إضافة القضية بنجاح!")
        
        # التحقق من وجود القضية في قائمة القضايا
        cases_response = session.get(f"{BASE_URL}/cases")
        if cases_response.status_code == 200:
            if case_data['title'] in cases_response.text:
                print("✅ تم التأكد من حفظ القضية في قاعدة البيانات")
                return True
            else:
                print("⚠️ القضية لم تظهر في قائمة القضايا")
        
        return True
    else:
        print(f"❌ فشل في إضافة القضية: {add_response.status_code}")
        print(f"Response: {add_response.text[:200]}...")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار modal إضافة القضية...")
    print("=" * 60)
    
    success = test_case_modal()
    
    print("=" * 60)
    if success:
        print("🎉 modal إضافة القضية يعمل بشكل ممتاز!")
    else:
        print("💥 هناك مشكلة في modal إضافة القضية")
