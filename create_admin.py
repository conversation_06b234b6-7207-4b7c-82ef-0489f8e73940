from app import db, app
from app.models import User

def create_admin():
    with app.app_context():
        if not User.query.filter_by(username="admin").first():
            user = User(username="admin", password="admin123")
            db.session.add(user)
            db.session.commit()
            print("تم إنشاء المستخدم admin بنجاح.")
        else:
            print("المستخدم admin موجود بالفعل.")

if __name__ == "__main__":
    create_admin()
