{% extends "base.html" %}

{% block title %}تعديل موعد{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-calendar-edit"></i> تعديل موعد</h1>
                    <p>تعديل بيانات الموعد المحدد</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit text-primary me-2"></i>
                        تعديل بيانات الموعد
                    </h5>
                </div>
                <div class="card-body">
        <form method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <div class="mb-3">
                <label class="form-label">الموضوع</label>
                <input type="text" name="subject" class="form-control" required value="{{ appointment.subject }}">
            </div>
            <div class="mb-3">
                <label class="form-label">التاريخ</label>
                <input type="datetime-local" name="date" class="form-control" required value="{{ appointment.date.strftime('%Y-%m-%dT%H:%M') if appointment.date else '' }}">
            </div>
            <div class="mb-3">
                <label class="form-label">المكان</label>
                <input type="text" name="location" class="form-control" value="{{ appointment.location }}">
            </div>
            <div class="mb-3">
                <label class="form-label">العميل</label>
                <select name="client_id" class="form-select">
                    <option value="">بدون</option>
                    {% for client in clients %}
                    <option value="{{ client.id }}" {% if appointment.client_id==client.id %}selected{% endif %}>{{ client.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">القضية</label>
                <select name="case_id" class="form-select">
                    <option value="">بدون</option>
                    {% for case in cases %}
                    <option value="{{ case.id }}" {% if appointment.case_id==case.id %}selected{% endif %}>{{ case.title }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">ملاحظات</label>
                <textarea name="notes" class="form-control">{{ appointment.notes }}</textarea>
            </div>
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                            <a href="{{ url_for('appointments_list') }}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-arrow-right me-2"></i>
                                رجوع
                            </a>
                        </div>
                    </div>
                </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
