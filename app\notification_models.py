"""
نماذج قاعدة البيانات لنظام الإشعارات والتنبيهات
Database Models for Notifications and Alerts System
"""

from datetime import datetime, timezone
from . import db
from sqlalchemy import func

class Notification(db.Model):
    """نموذج الإشعارات"""
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(50), default='info')  # success, error, warning, info, appointment, task, case, client, payment, document
    priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
    
    # معلومات المستخدم
    user_id = db.Column(db.Integer, nullable=False)  # سنضيف المرجع لاحقاً
    
    # حالة الإشعار
    read = db.Column(db.Boolean, default=False)
    read_at = db.Column(db.DateTime)
    
    # معلومات إضافية
    url = db.Column(db.String(500))  # رابط للانتقال إليه عند النقر
    action = db.Column(db.String(100))  # إجراء مخصص
    data = db.Column(db.JSON)  # بيانات إضافية
    
    # إعدادات العرض
    persistent = db.Column(db.Boolean, default=False)  # لا يختفي تلقائياً
    desktop_sent = db.Column(db.Boolean, default=False)  # تم إرسال إشعار سطح المكتب
    email_sent = db.Column(db.Boolean, default=False)  # تم إرسال بريد إلكتروني
    
    # التوقيتات
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    expires_at = db.Column(db.DateTime)  # تاريخ انتهاء الصلاحية
    
    # العلاقات - سيتم إضافتها لاحقاً
    # user = db.relationship('User', backref='notifications')
    
    def __repr__(self):
        return f'<Notification {self.id}: {self.title}>'
    
    def to_dict(self):
        """تحويل الإشعار إلى قاموس"""
        return {
            'id': self.id,
            'title': self.title,
            'message': self.message,
            'type': self.type,
            'priority': self.priority,
            'read': self.read,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'url': self.url,
            'action': self.action,
            'data': self.data,
            'persistent': self.persistent,
            'created_at': self.created_at.isoformat(),
            'expires_at': self.expires_at.isoformat() if self.expires_at else None
        }
    
    def mark_as_read(self):
        """تمييز الإشعار كمقروء"""
        self.read = True
        self.read_at = datetime.now(timezone.utc)
        db.session.commit()
    
    def is_expired(self):
        """فحص انتهاء صلاحية الإشعار"""
        if not self.expires_at:
            return False
        return datetime.now(timezone.utc) > self.expires_at
    
    @classmethod
    def get_unread_count(cls, user_id):
        """عدد الإشعارات غير المقروءة للمستخدم"""
        return cls.query.filter_by(user_id=user_id, read=False).count()
    
    @classmethod
    def get_recent_notifications(cls, user_id, limit=50):
        """الإشعارات الحديثة للمستخدم"""
        return cls.query.filter_by(user_id=user_id)\
                       .order_by(cls.created_at.desc())\
                       .limit(limit).all()
    
    @classmethod
    def cleanup_expired(cls):
        """حذف الإشعارات المنتهية الصلاحية"""
        expired = cls.query.filter(
            cls.expires_at.isnot(None),
            cls.expires_at < datetime.now(timezone.utc)
        ).all()
        
        for notification in expired:
            db.session.delete(notification)
        
        db.session.commit()
        return len(expired)


class Alert(db.Model):
    """نموذج التنبيهات المجدولة"""
    __tablename__ = 'alerts'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(50), default='info')
    priority = db.Column(db.String(20), default='normal')
    
    # معلومات المستخدم
    user_id = db.Column(db.Integer, nullable=False)  # سنضيف المرجع لاحقاً
    
    # جدولة التنبيه
    schedule_time = db.Column(db.DateTime, nullable=False)
    repeat_type = db.Column(db.String(20), default='none')  # none, daily, weekly, monthly, yearly
    repeat_interval = db.Column(db.Integer, default=1)  # كل كم من الوحدة
    repeat_until = db.Column(db.DateTime)  # تاريخ انتهاء التكرار
    
    # حالة التنبيه
    enabled = db.Column(db.Boolean, default=True)
    last_triggered = db.Column(db.DateTime)
    next_trigger = db.Column(db.DateTime)
    trigger_count = db.Column(db.Integer, default=0)
    
    # إعدادات التنبيه
    send_email = db.Column(db.Boolean, default=False)
    send_desktop = db.Column(db.Boolean, default=True)
    send_in_app = db.Column(db.Boolean, default=True)
    
    # معلومات إضافية
    url = db.Column(db.String(500))
    action = db.Column(db.String(100))
    data = db.Column(db.JSON)
    
    # التوقيتات
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # العلاقات - سيتم إضافتها لاحقاً
    # user = db.relationship('User', backref='alerts')
    
    def __repr__(self):
        return f'<Alert {self.id}: {self.title}>'
    
    def to_dict(self):
        """تحويل التنبيه إلى قاموس"""
        return {
            'id': self.id,
            'title': self.title,
            'message': self.message,
            'type': self.type,
            'priority': self.priority,
            'schedule_time': self.schedule_time.isoformat(),
            'repeat_type': self.repeat_type,
            'repeat_interval': self.repeat_interval,
            'repeat_until': self.repeat_until.isoformat() if self.repeat_until else None,
            'enabled': self.enabled,
            'last_triggered': self.last_triggered.isoformat() if self.last_triggered else None,
            'next_trigger': self.next_trigger.isoformat() if self.next_trigger else None,
            'trigger_count': self.trigger_count,
            'send_email': self.send_email,
            'send_desktop': self.send_desktop,
            'send_in_app': self.send_in_app,
            'url': self.url,
            'action': self.action,
            'data': self.data,
            'created_at': self.created_at.isoformat()
        }
    
    def should_trigger(self):
        """فحص ما إذا كان يجب تشغيل التنبيه"""
        if not self.enabled:
            return False
        
        now = datetime.now(timezone.utc)
        
        # فحص الوقت المجدول
        if self.next_trigger and now >= self.next_trigger:
            return True
        
        # فحص الوقت الأول
        if not self.last_triggered and now >= self.schedule_time:
            return True
        
        return False
    
    def trigger(self):
        """تشغيل التنبيه"""
        from .notification_service import NotificationService
        
        # إنشاء إشعار
        notification_data = {
            'title': self.title,
            'message': self.message,
            'type': self.type,
            'priority': self.priority,
            'url': self.url,
            'action': self.action,
            'data': self.data
        }
        
        NotificationService.create_notification(self.user_id, **notification_data)
        
        # تحديث معلومات التشغيل
        self.last_triggered = datetime.now(timezone.utc)
        self.trigger_count += 1
        
        # حساب الوقت التالي للتكرار
        self.calculate_next_trigger()
        
        db.session.commit()
    
    def calculate_next_trigger(self):
        """حساب وقت التشغيل التالي"""
        if self.repeat_type == 'none':
            self.next_trigger = None
            self.enabled = False
            return
        
        from dateutil.relativedelta import relativedelta
        
        base_time = self.last_triggered or self.schedule_time
        
        if self.repeat_type == 'daily':
            self.next_trigger = base_time + relativedelta(days=self.repeat_interval)
        elif self.repeat_type == 'weekly':
            self.next_trigger = base_time + relativedelta(weeks=self.repeat_interval)
        elif self.repeat_type == 'monthly':
            self.next_trigger = base_time + relativedelta(months=self.repeat_interval)
        elif self.repeat_type == 'yearly':
            self.next_trigger = base_time + relativedelta(years=self.repeat_interval)
        
        # فحص انتهاء التكرار
        if self.repeat_until and self.next_trigger > self.repeat_until:
            self.next_trigger = None
            self.enabled = False
    
    @classmethod
    def get_pending_alerts(cls):
        """الحصول على التنبيهات المعلقة"""
        now = datetime.now(timezone.utc)
        return cls.query.filter(
            cls.enabled == True,
            db.or_(
                db.and_(cls.next_trigger.isnot(None), cls.next_trigger <= now),
                db.and_(cls.last_triggered.is_(None), cls.schedule_time <= now)
            )
        ).all()
    
    @classmethod
    def process_pending_alerts(cls):
        """معالجة التنبيهات المعلقة"""
        pending_alerts = cls.get_pending_alerts()
        processed_count = 0
        
        for alert in pending_alerts:
            try:
                alert.trigger()
                processed_count += 1
            except Exception as e:
                print(f"خطأ في تشغيل التنبيه {alert.id}: {e}")
        
        return processed_count


class NotificationSettings(db.Model):
    """إعدادات الإشعارات للمستخدم"""
    __tablename__ = 'notification_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False, unique=True)  # سنضيف المرجع لاحقاً
    
    # إعدادات عامة
    enabled = db.Column(db.Boolean, default=True)
    sound_enabled = db.Column(db.Boolean, default=True)
    desktop_enabled = db.Column(db.Boolean, default=True)
    email_enabled = db.Column(db.Boolean, default=False)
    
    # إعدادات العرض
    auto_hide = db.Column(db.Boolean, default=True)
    hide_delay = db.Column(db.Integer, default=5000)  # بالميلي ثانية
    max_notifications = db.Column(db.Integer, default=50)
    
    # إعدادات أنواع الإشعارات
    case_notifications = db.Column(db.Boolean, default=True)
    client_notifications = db.Column(db.Boolean, default=True)
    appointment_notifications = db.Column(db.Boolean, default=True)
    task_notifications = db.Column(db.Boolean, default=True)
    payment_notifications = db.Column(db.Boolean, default=True)
    document_notifications = db.Column(db.Boolean, default=True)
    
    # إعدادات التوقيت
    quiet_hours_enabled = db.Column(db.Boolean, default=False)
    quiet_hours_start = db.Column(db.Time)
    quiet_hours_end = db.Column(db.Time)
    
    # التوقيتات
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # العلاقات - سيتم إضافتها لاحقاً
    # user = db.relationship('User', backref=db.backref('notification_settings', uselist=False))
    
    def __repr__(self):
        return f'<NotificationSettings for User {self.user_id}>'
    
    def to_dict(self):
        """تحويل الإعدادات إلى قاموس"""
        return {
            'enabled': self.enabled,
            'sound_enabled': self.sound_enabled,
            'desktop_enabled': self.desktop_enabled,
            'email_enabled': self.email_enabled,
            'auto_hide': self.auto_hide,
            'hide_delay': self.hide_delay,
            'max_notifications': self.max_notifications,
            'case_notifications': self.case_notifications,
            'client_notifications': self.client_notifications,
            'appointment_notifications': self.appointment_notifications,
            'task_notifications': self.task_notifications,
            'payment_notifications': self.payment_notifications,
            'document_notifications': self.document_notifications,
            'quiet_hours_enabled': self.quiet_hours_enabled,
            'quiet_hours_start': self.quiet_hours_start.strftime('%H:%M') if self.quiet_hours_start else None,
            'quiet_hours_end': self.quiet_hours_end.strftime('%H:%M') if self.quiet_hours_end else None
        }
    
    def is_quiet_time(self):
        """فحص ما إذا كان الوقت الحالي في ساعات الهدوء"""
        if not self.quiet_hours_enabled or not self.quiet_hours_start or not self.quiet_hours_end:
            return False
        
        now = datetime.now(timezone.utc).time()
        
        if self.quiet_hours_start <= self.quiet_hours_end:
            # نفس اليوم
            return self.quiet_hours_start <= now <= self.quiet_hours_end
        else:
            # عبر منتصف الليل
            return now >= self.quiet_hours_start or now <= self.quiet_hours_end
    
    @classmethod
    def get_or_create_for_user(cls, user_id):
        """الحصول على إعدادات المستخدم أو إنشاؤها"""
        settings = cls.query.filter_by(user_id=user_id).first()
        if not settings:
            settings = cls(user_id=user_id)
            db.session.add(settings)
            db.session.commit()
        return settings


# دوال مساعدة
def create_notification(user_id, title, message, **kwargs):
    """إنشاء إشعار جديد"""
    notification = Notification(
        user_id=user_id,
        title=title,
        message=message,
        **kwargs
    )
    db.session.add(notification)
    db.session.commit()
    return notification


def create_alert(user_id, title, message, schedule_time, **kwargs):
    """إنشاء تنبيه مجدول جديد"""
    alert = Alert(
        user_id=user_id,
        title=title,
        message=message,
        schedule_time=schedule_time,
        **kwargs
    )
    
    # حساب وقت التشغيل التالي
    alert.next_trigger = schedule_time
    
    db.session.add(alert)
    db.session.commit()
    return alert


def get_notification_stats(user_id):
    """إحصائيات الإشعارات للمستخدم"""
    total = Notification.query.filter_by(user_id=user_id).count()
    unread = Notification.query.filter_by(user_id=user_id, read=False).count()
    
    # إحصائيات حسب النوع
    type_stats = db.session.query(
        Notification.type,
        func.count(Notification.id)
    ).filter_by(user_id=user_id).group_by(Notification.type).all()
    
    # إحصائيات حسب الأولوية
    priority_stats = db.session.query(
        Notification.priority,
        func.count(Notification.id)
    ).filter_by(user_id=user_id).group_by(Notification.priority).all()
    
    return {
        'total': total,
        'unread': unread,
        'read': total - unread,
        'by_type': dict(type_stats),
        'by_priority': dict(priority_stats)
    }
