#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys

def test_login():
    """اختبار تسجيل الدخول بعد تحسينات الحماية"""
    base_url = 'http://localhost:5000'
    
    print("🔐 اختبار تسجيل الدخول بعد تحسينات الحماية...")
    print("=" * 60)
    
    # اختبار تسجيل الدخول بحساب office
    print("\n1️⃣ اختبار تسجيل الدخول بحساب office...")
    try:
        session = requests.Session()
        
        # الحصول على صفحة تسجيل الدخول
        login_page = session.get(f'{base_url}/lawyersameh')
        if login_page.status_code == 200:
            print("✅ صفحة تسجيل الدخول متاحة")
        
        # محاولة تسجيل الدخول
        login_data = {
            'username': 'office',
            'password': '66889088'
        }
        
        response = session.post(f'{base_url}/lawyersameh', data=login_data)
        
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ تسجيل الدخول بحساب office نجح")
        elif 'dashboard' in response.text or 'اللوحة الرئيسية' in response.text:
            print("✅ تسجيل الدخول بحساب office نجح")
        else:
            print("❌ فشل تسجيل الدخول بحساب office")
            print(f"Status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار حساب office: {str(e)}")
    
    # اختبار تسجيل الدخول بحساب admin
    print("\n2️⃣ اختبار تسجيل الدخول بحساب admin...")
    try:
        session = requests.Session()
        
        # محاولة تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f'{base_url}/lawyersameh', data=login_data)
        
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ تسجيل الدخول بحساب admin نجح")
        elif 'dashboard' in response.text or 'اللوحة الرئيسية' in response.text:
            print("✅ تسجيل الدخول بحساب admin نجح")
        else:
            print("❌ فشل تسجيل الدخول بحساب admin")
            print(f"Status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار حساب admin: {str(e)}")
    
    # اختبار الحماية من Brute Force
    print("\n3️⃣ اختبار الحماية من Brute Force...")
    try:
        session = requests.Session()
        
        # محاولة تسجيل دخول متكررة بكلمة مرور خاطئة
        for i in range(6):  # أكثر من الحد المسموح (5)
            login_data = {
                'username': 'test',
                'password': 'wrong_password'
            }
            
            response = session.post(f'{base_url}/lawyersameh', data=login_data)
            print(f"محاولة {i+1}: Status {response.status_code}")
            
            if 'حظر' in response.text or 'blocked' in response.text.lower():
                print("✅ تم تفعيل الحماية من Brute Force")
                break
        else:
            print("⚠️ لم يتم اكتشاف حماية Brute Force")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار Brute Force: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🔐 انتهى اختبار تسجيل الدخول")

if __name__ == "__main__":
    test_login()
