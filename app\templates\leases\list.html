{% extends "base.html" %}

{% block title %}إدارة عقود الإيجار{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-primary"><i class="fa fa-file-contract"></i> إدارة عقود الإيجار</h2>
        <div>
            <a href="{{ url_for('add_lease') }}" class="btn btn-success me-2">
                <i class="fa fa-plus"></i> إضافة عقد جديد
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#quickAddModal">
                <i class="fa fa-plus-circle"></i> إضافة سريعة
            </button>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h5><i class="fa fa-file-contract"></i> العقود النشطة</h5>
                    <h3>{{ active_count }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h5><i class="fa fa-clock"></i> تنتهي قريباً</h5>
                    <h3>{{ expiring_count }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h5><i class="fa fa-money-bill"></i> الدخل الشهري</h5>
                    <h3>{{ "%.2f"|format(total_monthly_income) }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h5><i class="fa fa-list"></i> إجمالي العقود</h5>
                    <h3>{{ leases|length }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول العقود -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fa fa-table"></i> قائمة عقود الإيجار</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>العقار</th>
                            <th>المستأجر</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>الإيجار الشهري</th>
                            <th>التأمين</th>
                            <th>الحالة</th>
                            <th>الأيام المتبقية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for lease in leases %}
                        <tr class="{% if lease.is_expiring_soon() %}table-warning{% elif not lease.is_active() %}table-secondary{% endif %}">
                            <td>{{ lease.property.name }}</td>
                            <td>{{ lease.tenant.name }}</td>
                            <td>{{ lease.start_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ lease.end_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ "%.2f"|format(lease.rent_amount) }} {{ lease.currency }}</td>
                            <td>{{ "%.2f"|format(lease.deposit) }} {{ lease.currency }}</td>
                            <td>
                                <span class="badge 
                                    {% if lease.status == 'نشط' %}bg-success
                                    {% elif lease.status == 'منتهي' %}bg-secondary
                                    {% elif lease.status == 'ملغي' %}bg-danger
                                    {% else %}bg-warning{% endif %}">
                                    {{ lease.status }}
                                </span>
                            </td>
                            <td>
                                {% if lease.is_active() %}
                                    {% set days = lease.days_remaining() %}
                                    {% if days > 0 %}
                                        <span class="{% if days <= 30 %}text-warning{% else %}text-success{% endif %}">
                                            {{ days }} يوم
                                        </span>
                                    {% else %}
                                        <span class="text-danger">منتهي</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('view_lease', lease_id=lease.id) }}" 
                                       class="btn btn-info btn-sm" title="عرض">
                                        <i class="fa fa-eye"></i>
                                    </a>
                                    {% if lease.is_active() %}
                                    <a href="{{ url_for('edit_lease', lease_id=lease.id) }}" 
                                       class="btn btn-primary btn-sm" title="تعديل">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('terminate_lease', lease_id=lease.id) }}" 
                                       class="btn btn-warning btn-sm" title="إنهاء">
                                        <i class="fa fa-stop"></i>
                                    </a>
                                    {% endif %}
                                    <a href="{{ url_for('lease_payments', lease_id=lease.id) }}"
                                       class="btn btn-success btn-sm" title="المدفوعات">
                                        <i class="fa fa-money-bill"></i>
                                    </a>
                                    <a href="{{ url_for('print_contract', lease_id=lease.id) }}"
                                       class="btn btn-secondary btn-sm" title="طباعة العقد" target="_blank">
                                        <i class="fa fa-print"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="9" class="text-center text-muted">لا توجد عقود إيجار مسجلة</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- تنبيهات العقود المنتهية قريباً -->
    {% if expiring_count > 0 %}
    <div class="alert alert-warning mt-4">
        <h6><i class="fa fa-exclamation-triangle"></i> تنبيه: عقود تنتهي قريباً</h6>
        <p>يوجد {{ expiring_count }} عقد/عقود ستنتهي خلال الـ 30 يوماً القادمة. يرجى المتابعة مع المستأجرين لتجديد العقود.</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
// تحديث الصفحة كل 10 دقائق لتحديث حالات العقود
setTimeout(function() {
    location.reload();
}, 600000);

// تمييز العقود المنتهية قريباً
$(document).ready(function() {
    $('.table tbody tr').each(function() {
        var daysText = $(this).find('td:nth-child(8)').text().trim();
        if (daysText.includes('يوم')) {
            var days = parseInt(daysText);
            if (days <= 7) {
                $(this).addClass('table-danger');
            } else if (days <= 30) {
                $(this).addClass('table-warning');
            }
        }
    });
});
</script>

<!-- Modal للإضافة السريعة -->
<div class="modal fade" id="quickAddModal" tabindex="-1" aria-labelledby="quickAddModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickAddModalLabel">إضافة عقد إيجار سريع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="modalContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const quickAddModal = document.getElementById('quickAddModal');
    quickAddModal.addEventListener('show.bs.modal', function() {
        fetch('{{ url_for("modal_add_lease") }}')
            .then(response => response.text())
            .then(html => {
                document.getElementById('modalContent').innerHTML = html;
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('modalContent').innerHTML = '<div class="alert alert-danger">حدث خطأ في تحميل النموذج</div>';
            });
    });
});
</script>
{% endblock %}
