{% extends "base.html" %}

{% block title %}إدارة المواعيد المتقدمة{% endblock %}

{% block head %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
<style>
    /* تصميم احترافي مع لمسات سحرية */
    .appointments-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px 0;
        border-radius: 20px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
    }
    
    .appointments-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: magicRotate 20s linear infinite;
    }
    
    @keyframes magicRotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .appointment-card {
        background: var(--theme-bg-primary);
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: var(--theme-shadow-lg);
        border: 1px solid var(--theme-border-color);
        transition: all var(--theme-transition-normal);
        position: relative;
        overflow: hidden;
    }
    
    .appointment-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(102,126,234,0.05) 0%, transparent 100%);
        pointer-events: none;
    }
    
    .appointment-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--theme-shadow-2xl);
    }
    
    .appointment-status {
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.85rem;
        font-weight: 600;
        text-align: center;
        min-width: 100px;
        display: inline-block;
    }
    
    .status-scheduled {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: white;
    }
    
    .status-completed {
        background: linear-gradient(135deg, #00b894, #00a085);
        color: white;
    }
    
    .status-cancelled {
        background: linear-gradient(135deg, #fd79a8, #e84393);
        color: white;
    }
    
    .status-postponed {
        background: linear-gradient(135deg, #fdcb6e, #e17055);
        color: white;
    }
    
    .priority-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-block;
        margin-left: 10px;
    }
    
    .priority-high {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
        animation: magicPulse 2s infinite;
    }
    
    .priority-medium {
        background: linear-gradient(135deg, #ffa726, #ff9800);
        color: white;
    }
    
    .priority-low {
        background: linear-gradient(135deg, #66bb6a, #4caf50);
        color: white;
    }
    
    @keyframes magicPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }
    
    .appointment-type-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-left: 15px;
    }
    
    .type-court {
        background: linear-gradient(135deg, #667eea, #764ba2);
    }
    
    .type-client {
        background: linear-gradient(135deg, #f093fb, #f5576c);
    }
    
    .type-general {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
    }
    
    .type-task {
        background: linear-gradient(135deg, #43e97b, #38f9d7);
    }
    
    .magic-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all var(--theme-transition-normal);
        position: relative;
        overflow: hidden;
    }
    
    .magic-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }
    
    .magic-button:hover::before {
        left: 100%;
    }
    
    .magic-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102,126,234,0.4);
    }
    
    .quick-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: var(--theme-bg-primary);
        padding: 25px;
        border-radius: 20px;
        text-align: center;
        box-shadow: var(--theme-shadow-md);
        border: 1px solid var(--theme-border-color);
        transition: all var(--theme-transition-normal);
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--theme-shadow-xl);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .filters-section {
        background: var(--theme-bg-primary);
        padding: 25px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: var(--theme-shadow-md);
        border: 1px solid var(--theme-border-color);
    }
    
    .appointment-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .action-btn {
        padding: 8px 16px;
        border-radius: 20px;
        border: none;
        font-size: 0.85rem;
        font-weight: 600;
        transition: all var(--theme-transition-fast);
        cursor: pointer;
    }
    
    .btn-edit {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: white;
    }
    
    .btn-delete {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
    }
    
    .btn-complete {
        background: linear-gradient(135deg, #00b894, #00a085);
        color: white;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }
    
    .search-box {
        position: relative;
        margin-bottom: 20px;
    }
    
    .search-input {
        width: 100%;
        padding: 15px 50px 15px 20px;
        border: 2px solid var(--theme-border-color);
        border-radius: 25px;
        font-size: 1rem;
        transition: all var(--theme-transition-normal);
        background: var(--theme-bg-primary);
    }
    
    .search-input:focus {
        outline: none;
        border-color: var(--theme-primary);
        box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
    }
    
    .search-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--theme-text-muted);
        font-size: 1.2rem;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--theme-text-muted);
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
    
    /* تحسينات الاستجابة */
    @media (max-width: 768px) {
        .appointment-card {
            padding: 20px;
        }
        
        .appointments-header {
            padding: 30px 0;
        }
        
        .quick-stats {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .appointment-actions {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="appointments-header text-center">
        <div class="container">
            <h1 class="display-4 fw-bold mb-3">
                <i class="fa fa-calendar-check me-3"></i>
                إدارة المواعيد المتقدمة
            </h1>
            <p class="lead mb-4">نظام شامل لإدارة وتنظيم جميع مواعيدك القانونية والمهنية</p>
            <button class="magic-button" onclick="openAddAppointmentModal()">
                <i class="fa fa-plus me-2"></i>
                إضافة موعد جديد
            </button>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="quick-stats">
        <div class="stat-card">
            <div class="stat-number" id="totalAppointments">{{ appointments|length }}</div>
            <div class="text-muted">إجمالي المواعيد</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="todayAppointments">0</div>
            <div class="text-muted">مواعيد اليوم</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="upcomingAppointments">0</div>
            <div class="text-muted">المواعيد القادمة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="overdueAppointments">0</div>
            <div class="text-muted">المواعيد المتأخرة</div>
        </div>
    </div>

    <!-- قسم البحث والفلاتر -->
    <div class="filters-section">
        <div class="row">
            <div class="col-md-6">
                <div class="search-box">
                    <input type="text" class="search-input" id="searchInput" placeholder="البحث في المواعيد...">
                    <i class="fa fa-search search-icon"></i>
                </div>
            </div>
            <div class="col-md-6">
                <div class="row">
                    <div class="col-md-6">
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="مجدول">مجدول</option>
                            <option value="مكتمل">مكتمل</option>
                            <option value="ملغي">ملغي</option>
                            <option value="مؤجل">مؤجل</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <select class="form-select" id="typeFilter">
                            <option value="">جميع الأنواع</option>
                            <option value="جلسة محكمة">جلسة محكمة</option>
                            <option value="اجتماع عميل">اجتماع عميل</option>
                            <option value="موعد عام">موعد عام</option>
                            <option value="مهمة">مهمة</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم عرض المواعيد -->
    <div class="appointments-list" id="appointmentsList">
        {% if appointments %}
            {% for appointment in appointments %}
            <div class="appointment-card" data-appointment-id="{{ appointment.id }}"
                 data-status="{{ appointment.status }}"
                 data-type="{{ appointment.type }}"
                 data-date="{{ appointment.date.strftime('%Y-%m-%d') if appointment.date }}">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <div class="appointment-type-icon
                            {% if appointment.type == 'جلسة محكمة' %}type-court
                            {% elif appointment.type == 'اجتماع عميل' %}type-client
                            {% elif appointment.type == 'مهمة' %}type-task
                            {% else %}type-general{% endif %}">
                            <i class="fa
                                {% if appointment.type == 'جلسة محكمة' %}fa-gavel
                                {% elif appointment.type == 'اجتماع عميل' %}fa-users
                                {% elif appointment.type == 'مهمة' %}fa-tasks
                                {% else %}fa-calendar{% endif %}"></i>
                        </div>
                    </div>
                    <div class="col-md-7">
                        <h5 class="mb-2 fw-bold">{{ appointment.title }}</h5>
                        <p class="text-muted mb-2">{{ appointment.description or 'لا يوجد وصف' }}</p>
                        <div class="d-flex align-items-center flex-wrap">
                            <span class="me-3">
                                <i class="fa fa-calendar text-primary me-1"></i>
                                {{ appointment.date.strftime('%Y/%m/%d') if appointment.date }}
                            </span>
                            <span class="me-3">
                                <i class="fa fa-clock text-info me-1"></i>
                                {{ appointment.time.strftime('%H:%M') if appointment.time }}
                            </span>
                            {% if appointment.location %}
                            <span class="me-3">
                                <i class="fa fa-map-marker-alt text-warning me-1"></i>
                                {{ appointment.location }}
                            </span>
                            {% endif %}
                            {% if appointment.client %}
                            <span class="me-3">
                                <i class="fa fa-user text-success me-1"></i>
                                {{ appointment.client.name }}
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="appointment-status
                            {% if appointment.status == 'مجدول' %}status-scheduled
                            {% elif appointment.status == 'مكتمل' %}status-completed
                            {% elif appointment.status == 'ملغي' %}status-cancelled
                            {% elif appointment.status == 'مؤجل' %}status-postponed
                            {% endif %}">
                            {{ appointment.status }}
                        </div>
                        {% if appointment.priority %}
                        <div class="priority-badge priority-{{ appointment.priority|lower }}">
                            {{ appointment.priority }}
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-2">
                        <div class="appointment-actions">
                            <button class="action-btn btn-edit" onclick="editAppointment({{ appointment.id }})">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button class="action-btn btn-complete" onclick="completeAppointment({{ appointment.id }})">
                                <i class="fa fa-check"></i>
                            </button>
                            <button class="action-btn btn-delete" onclick="deleteAppointment({{ appointment.id }})">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <i class="fa fa-calendar-times"></i>
                <h3>لا توجد مواعيد</h3>
                <p>لم يتم إنشاء أي مواعيد بعد. ابدأ بإضافة موعد جديد.</p>
                <button class="magic-button" onclick="openAddAppointmentModal()">
                    <i class="fa fa-plus me-2"></i>
                    إضافة أول موعد
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- نافذة إضافة/تعديل موعد -->
<div class="modal fade" id="appointmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-gradient text-white">
                <h5 class="modal-title" id="appointmentModalTitle">
                    <i class="fa fa-calendar-plus me-2"></i>
                    إضافة موعد جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="appointmentForm">
                    <input type="hidden" id="appointmentId" name="appointment_id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="appointmentTitle" class="form-label fw-bold">
                                    <i class="fa fa-heading text-primary me-2"></i>
                                    عنوان الموعد *
                                </label>
                                <input type="text" class="form-control" id="appointmentTitle" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="appointmentType" class="form-label fw-bold">
                                    <i class="fa fa-tag text-info me-2"></i>
                                    نوع الموعد *
                                </label>
                                <select class="form-select" id="appointmentType" name="type" required>
                                    <option value="">اختر نوع الموعد</option>
                                    <option value="جلسة محكمة">جلسة محكمة</option>
                                    <option value="اجتماع عميل">اجتماع عميل</option>
                                    <option value="موعد عام">موعد عام</option>
                                    <option value="مهمة">مهمة</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="appointmentDate" class="form-label fw-bold">
                                    <i class="fa fa-calendar text-success me-2"></i>
                                    التاريخ *
                                </label>
                                <input type="date" class="form-control" id="appointmentDate" name="date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="appointmentTime" class="form-label fw-bold">
                                    <i class="fa fa-clock text-warning me-2"></i>
                                    الوقت *
                                </label>
                                <input type="time" class="form-control" id="appointmentTime" name="time" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="appointmentClient" class="form-label fw-bold">
                                    <i class="fa fa-user text-primary me-2"></i>
                                    العميل
                                </label>
                                <select class="form-select" id="appointmentClient" name="client_id">
                                    <option value="">اختر العميل</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}">{{ client.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="appointmentCase" class="form-label fw-bold">
                                    <i class="fa fa-briefcase text-info me-2"></i>
                                    القضية
                                </label>
                                <select class="form-select" id="appointmentCase" name="case_id">
                                    <option value="">اختر القضية</option>
                                    {% for case in cases %}
                                    <option value="{{ case.id }}" data-client="{{ case.client_id }}">{{ case.title }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="appointmentLocation" class="form-label fw-bold">
                                    <i class="fa fa-map-marker-alt text-danger me-2"></i>
                                    المكان
                                </label>
                                <input type="text" class="form-control" id="appointmentLocation" name="location" placeholder="مكان الموعد">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="appointmentPriority" class="form-label fw-bold">
                                    <i class="fa fa-exclamation-triangle text-warning me-2"></i>
                                    الأولوية
                                </label>
                                <select class="form-select" id="appointmentPriority" name="priority">
                                    <option value="منخفضة">منخفضة</option>
                                    <option value="متوسطة" selected>متوسطة</option>
                                    <option value="عالية">عالية</option>
                                    <option value="عاجلة">عاجلة</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="appointmentDescription" class="form-label fw-bold">
                            <i class="fa fa-file-text text-secondary me-2"></i>
                            الوصف
                        </label>
                        <textarea class="form-control" id="appointmentDescription" name="description" rows="3" placeholder="تفاصيل إضافية عن الموعد"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="appointmentStatus" class="form-label fw-bold">
                                    <i class="fa fa-flag text-info me-2"></i>
                                    الحالة
                                </label>
                                <select class="form-select" id="appointmentStatus" name="status">
                                    <option value="مجدول" selected>مجدول</option>
                                    <option value="مكتمل">مكتمل</option>
                                    <option value="ملغي">ملغي</option>
                                    <option value="مؤجل">مؤجل</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="appointmentReminder" class="form-label fw-bold">
                                    <i class="fa fa-bell text-warning me-2"></i>
                                    تذكير قبل
                                </label>
                                <select class="form-select" id="appointmentReminder" name="reminder_minutes">
                                    <option value="0">بدون تذكير</option>
                                    <option value="15">15 دقيقة</option>
                                    <option value="30" selected>30 دقيقة</option>
                                    <option value="60">ساعة واحدة</option>
                                    <option value="120">ساعتان</option>
                                    <option value="1440">يوم واحد</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fa fa-times me-2"></i>
                    إلغاء
                </button>
                <button type="button" class="magic-button" onclick="saveAppointment()">
                    <i class="fa fa-save me-2"></i>
                    حفظ الموعد
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fa fa-exclamation-triangle me-2"></i>
                    تأكيد الحذف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-0">هل أنت متأكد من حذف هذا الموعد؟ لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                    <i class="fa fa-trash me-2"></i>
                    حذف
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// متغيرات عامة
let currentAppointmentId = null;
let appointments = {{ appointments|tojson if appointments else '[]' }};

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeFilters();
    updateStatistics();
    initializeSelect2();
    setupEventListeners();
});

// تهيئة Select2
function initializeSelect2() {
    $('#appointmentClient, #appointmentCase').select2({
        dropdownParent: $('#appointmentModal'),
        placeholder: 'اختر...',
        allowClear: true,
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث
    document.getElementById('searchInput').addEventListener('input', filterAppointments);

    // الفلاتر
    document.getElementById('statusFilter').addEventListener('change', filterAppointments);
    document.getElementById('typeFilter').addEventListener('change', filterAppointments);

    // ربط العميل بالقضايا
    document.getElementById('appointmentClient').addEventListener('change', function() {
        const clientId = this.value;
        const caseSelect = document.getElementById('appointmentCase');
        const caseOptions = caseSelect.querySelectorAll('option[data-client]');

        caseOptions.forEach(option => {
            if (!clientId || option.dataset.client === clientId) {
                option.style.display = 'block';
            } else {
                option.style.display = 'none';
            }
        });

        // إعادة تهيئة Select2
        $('#appointmentCase').val(null).trigger('change');
    });
}

// تحديث الإحصائيات
function updateStatistics() {
    const today = new Date().toISOString().split('T')[0];
    const now = new Date();

    let todayCount = 0;
    let upcomingCount = 0;
    let overdueCount = 0;

    appointments.forEach(appointment => {
        const appointmentDate = appointment.date;
        const appointmentDateTime = new Date(appointmentDate + ' ' + (appointment.time || '00:00'));

        if (appointmentDate === today) {
            todayCount++;
        }

        if (appointmentDateTime > now && appointment.status === 'مجدول') {
            upcomingCount++;
        }

        if (appointmentDateTime < now && appointment.status === 'مجدول') {
            overdueCount++;
        }
    });

    document.getElementById('todayAppointments').textContent = todayCount;
    document.getElementById('upcomingAppointments').textContent = upcomingCount;
    document.getElementById('overdueAppointments').textContent = overdueCount;
}

// فلترة المواعيد
function filterAppointments() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;

    const appointmentCards = document.querySelectorAll('.appointment-card');

    appointmentCards.forEach(card => {
        const title = card.querySelector('h5').textContent.toLowerCase();
        const description = card.querySelector('p').textContent.toLowerCase();
        const status = card.dataset.status;
        const type = card.dataset.type;

        const matchesSearch = title.includes(searchTerm) || description.includes(searchTerm);
        const matchesStatus = !statusFilter || status === statusFilter;
        const matchesType = !typeFilter || type === typeFilter;

        if (matchesSearch && matchesStatus && matchesType) {
            card.style.display = 'block';
            card.style.animation = 'fadeIn 0.3s ease-in-out';
        } else {
            card.style.display = 'none';
        }
    });
}

// فتح نافذة إضافة موعد
function openAddAppointmentModal() {
    currentAppointmentId = null;
    document.getElementById('appointmentModalTitle').innerHTML = '<i class="fa fa-calendar-plus me-2"></i>إضافة موعد جديد';
    document.getElementById('appointmentForm').reset();
    $('#appointmentClient, #appointmentCase').val(null).trigger('change');

    // تعيين التاريخ الحالي كافتراضي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('appointmentDate').value = today;

    const modal = new bootstrap.Modal(document.getElementById('appointmentModal'));
    modal.show();
}

// تعديل موعد
function editAppointment(appointmentId) {
    currentAppointmentId = appointmentId;

    // جلب بيانات الموعد من الخادم
    fetch(`/api/appointments/${appointmentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const appointment = data.appointment;

                document.getElementById('appointmentModalTitle').innerHTML = '<i class="fa fa-edit me-2"></i>تعديل الموعد';
                document.getElementById('appointmentId').value = appointment.id;
                document.getElementById('appointmentTitle').value = appointment.title;
                document.getElementById('appointmentType').value = appointment.type;
                document.getElementById('appointmentDate').value = appointment.date;
                document.getElementById('appointmentTime').value = appointment.time;
                document.getElementById('appointmentLocation').value = appointment.location || '';
                document.getElementById('appointmentPriority').value = appointment.priority || 'متوسطة';
                document.getElementById('appointmentDescription').value = appointment.description || '';
                document.getElementById('appointmentStatus').value = appointment.status;

                $('#appointmentClient').val(appointment.client_id).trigger('change');
                $('#appointmentCase').val(appointment.case_id).trigger('change');

                const modal = new bootstrap.Modal(document.getElementById('appointmentModal'));
                modal.show();
            } else {
                showNotification('خطأ في جلب بيانات الموعد', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('خطأ في الاتصال بالخادم', 'error');
        });
}

// حفظ الموعد
function saveAppointment() {
    const form = document.getElementById('appointmentForm');
    const formData = new FormData(form);

    // التحقق من صحة البيانات
    if (!formData.get('title') || !formData.get('date') || !formData.get('time') || !formData.get('type')) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    const url = currentAppointmentId ? `/api/appointments/${currentAppointmentId}` : '/api/appointments';
    const method = currentAppointmentId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(currentAppointmentId ? 'تم تحديث الموعد بنجاح' : 'تم إضافة الموعد بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('appointmentModal')).hide();
            location.reload(); // إعادة تحميل الصفحة لعرض التحديثات
        } else {
            showNotification(data.message || 'حدث خطأ أثناء حفظ الموعد', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    });
}

// إكمال موعد
function completeAppointment(appointmentId) {
    fetch(`/api/appointments/${appointmentId}/complete`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إكمال الموعد بنجاح', 'success');
            location.reload();
        } else {
            showNotification(data.message || 'حدث خطأ أثناء إكمال الموعد', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    });
}

// حذف موعد
function deleteAppointment(appointmentId) {
    currentAppointmentId = appointmentId;
    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    modal.show();
}

// تأكيد الحذف
function confirmDelete() {
    if (!currentAppointmentId) return;

    fetch(`/api/appointments/${currentAppointmentId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم حذف الموعد بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal')).hide();
            location.reload();
        } else {
            showNotification(data.message || 'حدث خطأ أثناء حذف الموعد', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    });
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

    notification.innerHTML = `
        <i class="fa fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// تهيئة الفلاتر
function initializeFilters() {
    // إضافة تأثيرات CSS للفلترة
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .appointment-card {
            transition: all 0.3s ease;
        }

        .appointment-card[style*="display: none"] {
            animation: fadeOut 0.3s ease-out;
        }

        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-10px); }
        }
    `;
    document.head.appendChild(style);
}

// تحديث الوقت الحقيقي
setInterval(updateStatistics, 60000); // تحديث كل دقيقة

// إضافة تأثيرات سحرية للأزرار
document.addEventListener('DOMContentLoaded', function() {
    const magicButtons = document.querySelectorAll('.magic-button');
    magicButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
{% endblock %}
}
