from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager
from flask_babel import Babel
from flask_migrate import Migrate
from flask_wtf.csrf import CSRFProtect
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'secret-key')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///lawoffice.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['BABEL_DEFAULT_LOCALE'] = 'ar'
app.config['BABEL_DEFAULT_TIMEZONE'] = 'Asia/Riyadh'

# Extensions
csrf = CSRFProtect(app)
db = SQLAlchemy(app)
migrate = Migrate(app, db)
login_manager = LoginManager(app)
babel = Babel(app)

from .models import User, Notification, NotificationSettings

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Template filters
@app.template_filter('number_format')
def number_format(value):
    """تنسيق الأرقام مع فواصل الآلاف"""
    if value is None:
        return "0"
    try:
        return f"{float(value):,.2f}".replace(',', ' ')
    except (ValueError, TypeError):
        return str(value)

from . import routes

# تهيئة خدمات النظام
def initialize_services():
    """تهيئة خدمات النظام عند بدء التشغيل"""
    try:
        from .settings_service import SettingsService
        from .backup_service import BackupService

        # تهيئة خدمة الإعدادات
        settings_service = SettingsService()
        settings_service.initialize_default_settings()

        # تهيئة خدمة النسخ الاحتياطي
        backup_service = BackupService(app)
        backup_service.init_app(app)

        print("✅ تم تهيئة خدمات النظام بنجاح")

    except Exception as e:
        print(f"❌ خطأ في تهيئة خدمات النظام: {str(e)}")

# تهيئة الخدمات عند بدء التطبيق
with app.app_context():
    try:
        db.create_all()
        initialize_services()
    except Exception as e:
        print(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")

if __name__ == '__main__':
    app.run(debug=True)
