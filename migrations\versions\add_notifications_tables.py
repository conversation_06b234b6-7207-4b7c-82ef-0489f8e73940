"""Add notifications and notification_settings tables

Revision ID: add_notifications_tables
Revises: add_document_fields
Create Date: 2025-07-05 19:15:00.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = 'add_notifications_tables'
down_revision = 'add_document_fields'
branch_labels = None
depends_on = None

def upgrade():
    """إنشاء جداول التنبيهات والإعدادات"""
    
    # إنشاء جدول notifications
    try:
        op.create_table('notifications',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('type', sa.String(length=50), nullable=False),
            sa.Column('title', sa.String(length=200), nullable=False),
            sa.Column('message', sa.Text(), nullable=False),
            sa.Column('priority', sa.String(length=20), nullable=True),
            sa.Column('is_read', sa.<PERSON>(), nullable=True),
            sa.Column('is_active', sa.<PERSON>(), nullable=True),
            sa.Column('property_id', sa.Integer(), nullable=True),
            sa.Column('tenant_id', sa.Integer(), nullable=True),
            sa.Column('lease_id', sa.Integer(), nullable=True),
            sa.Column('user_id', sa.Integer(), nullable=False),
            sa.Column('due_date', sa.DateTime(), nullable=True),
            sa.Column('reminder_date', sa.DateTime(), nullable=True),
            sa.Column('action_url', sa.String(length=500), nullable=True),
            sa.Column('action_data', sa.Text(), nullable=True),
            sa.Column('created_date', sa.DateTime(), nullable=True),
            sa.Column('updated_date', sa.DateTime(), nullable=True),
            sa.Column('read_date', sa.DateTime(), nullable=True),
            sa.ForeignKeyConstraint(['lease_id'], ['leases.id'], ),
            sa.ForeignKeyConstraint(['property_id'], ['properties.id'], ),
            sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
            sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
        print("✓ تم إنشاء جدول notifications")
    except Exception as e:
        print(f"⚠ خطأ في إنشاء جدول notifications: {e}")
    
    # إنشاء جدول notification_settings
    try:
        op.create_table('notification_settings',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('user_id', sa.Integer(), nullable=False),
            sa.Column('lease_expiry_enabled', sa.Boolean(), nullable=True),
            sa.Column('lease_expiry_days', sa.Integer(), nullable=True),
            sa.Column('payment_due_enabled', sa.Boolean(), nullable=True),
            sa.Column('payment_due_days', sa.Integer(), nullable=True),
            sa.Column('maintenance_due_enabled', sa.Boolean(), nullable=True),
            sa.Column('maintenance_due_days', sa.Integer(), nullable=True),
            sa.Column('tenant_review_enabled', sa.Boolean(), nullable=True),
            sa.Column('tenant_review_months', sa.Integer(), nullable=True),
            sa.Column('email_notifications', sa.Boolean(), nullable=True),
            sa.Column('sms_notifications', sa.Boolean(), nullable=True),
            sa.Column('push_notifications', sa.Boolean(), nullable=True),
            sa.Column('created_date', sa.DateTime(), nullable=True),
            sa.Column('updated_date', sa.DateTime(), nullable=True),
            sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
        print("✓ تم إنشاء جدول notification_settings")
    except Exception as e:
        print(f"⚠ خطأ في إنشاء جدول notification_settings: {e}")

def downgrade():
    """حذف جداول التنبيهات والإعدادات"""
    try:
        op.drop_table('notification_settings')
        op.drop_table('notifications')
        print("✓ تم حذف جداول التنبيهات")
    except Exception as e:
        print(f"⚠ خطأ في حذف جداول التنبيهات: {e}")
