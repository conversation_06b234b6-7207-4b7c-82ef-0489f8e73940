{% extends "base.html" %}

{% block title %}الأرشيف والإعدادات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-archive"></i> إدارة الأرشيف</h1>
                    <p>أرشفة البيانات وإدارة النسخ الاحتياطية والإعدادات</p>
                </div>
                <div class="page-actions">
                    <button type="button" class="btn btn-primary" onclick="refreshArchive()">
                        <i class="fas fa-sync me-2"></i>
                        تحديث
                    </button>
                    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#settingsModal">
                        <i class="fas fa-cog me-2"></i>
                        الإعدادات
                    </button>
                    <button type="button" class="btn btn-warning" onclick="createBackup()">
                        <i class="fas fa-download me-2"></i>
                        نسخة احتياطية
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-folder-open text-primary me-2"></i>
                        محتويات الأرشيف
                    </h5>
                </div>
                <div class="card-body">
                    <!-- إحصائيات الأرشيف -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stats-card bg-primary">
                                <h3 id="archived-cases">0</h3>
                                <p>القضايا المؤرشفة</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-info">
                                <h3 id="archived-clients">0</h3>
                                <p>العملاء المؤرشفون</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-success">
                                <h3 id="archived-documents">0</h3>
                                <p>المستندات المؤرشفة</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-warning">
                                <h3 id="total-size">0 MB</h3>
                                <p>حجم الأرشيف</p>
                            </div>
                        </div>
                    </div>

                    <!-- تبويبات الأرشيف -->
                    <ul class="nav nav-tabs" id="archiveTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="cases-tab" data-bs-toggle="tab" data-bs-target="#cases" type="button" role="tab">
                                <i class="fas fa-gavel me-2"></i>القضايا المؤرشفة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="clients-tab" data-bs-toggle="tab" data-bs-target="#clients" type="button" role="tab">
                                <i class="fas fa-users me-2"></i>العملاء المؤرشفون
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents" type="button" role="tab">
                                <i class="fas fa-file-alt me-2"></i>المستندات المؤرشفة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                                <i class="fas fa-server me-2"></i>معلومات النظام
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="archiveTabContent">
                        <!-- تبويب القضايا المؤرشفة -->
                        <div class="tab-pane fade show active" id="cases" role="tabpanel">
                            <div class="mt-3">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>القضايا المؤرشفة</h6>
                                    <div class="input-group" style="width: 300px;">
                                        <input type="text" class="form-control" id="search-cases" placeholder="البحث في القضايا...">
                                        <button class="btn btn-outline-secondary" type="button" onclick="searchArchivedCases()">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div id="archived-cases-container">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p class="mt-2">جاري تحميل القضايا المؤرشفة...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب العملاء المؤرشفون -->
                        <div class="tab-pane fade" id="clients" role="tabpanel">
                            <div class="mt-3">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>العملاء المؤرشفون</h6>
                                    <div class="input-group" style="width: 300px;">
                                        <input type="text" class="form-control" id="search-clients" placeholder="البحث في العملاء...">
                                        <button class="btn btn-outline-secondary" type="button" onclick="searchArchivedClients()">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div id="archived-clients-container">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p class="mt-2">جاري تحميل العملاء المؤرشفين...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب المستندات المؤرشفة -->
                        <div class="tab-pane fade" id="documents" role="tabpanel">
                            <div class="mt-3">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>المستندات المؤرشفة</h6>
                                    <div class="input-group" style="width: 300px;">
                                        <input type="text" class="form-control" id="search-documents" placeholder="البحث في المستندات...">
                                        <button class="btn btn-outline-secondary" type="button" onclick="searchArchivedDocuments()">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div id="archived-documents-container">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p class="mt-2">جاري تحميل المستندات المؤرشفة...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب معلومات النظام -->
                        <div class="tab-pane fade" id="system" role="tabpanel">
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">معلومات النظام</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="system-info">
                                                    <div class="text-center py-3">
                                                        <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                                        <p class="mt-2 small">جاري التحميل...</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">النسخ الاحتياطية</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="backup-logs">
                                                    <div class="text-center py-3">
                                                        <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                                        <p class="mt-2 small">جاري التحميل...</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة الإعدادات المتقدمة -->
<div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-gradient-primary text-white">
                <h5 class="modal-title" id="settingsModalLabel">
                    <i class="fas fa-cogs me-2"></i>إعدادات النظام المتقدمة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div class="row g-0">
                    <!-- الشريط الجانبي للفئات -->
                    <div class="col-md-3 bg-light border-end">
                        <div class="list-group list-group-flush" id="settingsCategories">
                            <a class="list-group-item list-group-item-action active" data-category="general">
                                <i class="fas fa-building me-2"></i>الإعدادات العامة
                            </a>
                            <a class="list-group-item list-group-item-action" data-category="archive">
                                <i class="fas fa-archive me-2"></i>إعدادات الأرشيف
                            </a>
                            <a class="list-group-item list-group-item-action" data-category="backup">
                                <i class="fas fa-database me-2"></i>النسخ الاحتياطي
                            </a>
                            <a class="list-group-item list-group-item-action" data-category="security">
                                <i class="fas fa-shield-alt me-2"></i>الأمان والحماية
                            </a>
                            <a class="list-group-item list-group-item-action" data-category="ui">
                                <i class="fas fa-palette me-2"></i>واجهة المستخدم
                            </a>
                            <a class="list-group-item list-group-item-action" data-category="notifications">
                                <i class="fas fa-bell me-2"></i>التنبيهات
                            </a>
                            <a class="list-group-item list-group-item-action" data-category="reports">
                                <i class="fas fa-file-alt me-2"></i>التقارير
                            </a>
                        </div>
                    </div>

                    <!-- محتوى الإعدادات -->
                    <div class="col-md-9">
                        <div class="p-4">
                            <!-- الإعدادات العامة -->
                            <div id="general-settings" class="settings-category">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-building me-2"></i>الإعدادات العامة
                                </h6>
                                <form id="generalSettingsForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="office_name" class="form-label">اسم المكتب</label>
                                                <input type="text" class="form-control" id="office_name" name="office_name">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="office_address" class="form-label">عنوان المكتب</label>
                                                <input type="text" class="form-control" id="office_address" name="office_address">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="office_phone" class="form-label">رقم الهاتف</label>
                                                <input type="text" class="form-control" id="office_phone" name="office_phone">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="office_email" class="form-label">البريد الإلكتروني</label>
                                                <input type="email" class="form-control" id="office_email" name="office_email">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="default_currency" class="form-label">العملة الافتراضية</label>
                                                <select class="form-select" id="default_currency" name="default_currency">
                                                    <option value="ريال سعودي">ريال سعودي</option>
                                                    <option value="درهم إماراتي">درهم إماراتي</option>
                                                    <option value="دولار أمريكي">دولار أمريكي</option>
                                                    <option value="يورو">يورو</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                                <select class="form-select" id="timezone" name="timezone">
                                                    <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                                                    <option value="Asia/Dubai">دبي (GMT+4)</option>
                                                    <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                                                    <option value="Asia/Qatar">الدوحة (GMT+3)</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- إعدادات الأرشيف -->
                            <div id="archive-settings" class="settings-category d-none">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-archive me-2"></i>إعدادات الأرشيف
                                </h6>
                                <form id="archiveSettingsForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="auto_archive_enabled" name="auto_archive_enabled">
                                                    <label class="form-check-label" for="auto_archive_enabled">
                                                        تفعيل الأرشفة التلقائية
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="auto_archive_days" class="form-label">أرشفة تلقائية بعد (أيام)</label>
                                                <input type="number" class="form-control" id="auto_archive_days" name="auto_archive_days" min="1" max="3650">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="archive_closed_cases" name="archive_closed_cases">
                                                    <label class="form-check-label" for="archive_closed_cases">
                                                        أرشفة القضايا المغلقة تلقائياً
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="archive_inactive_clients" name="archive_inactive_clients">
                                                    <label class="form-check-label" for="archive_inactive_clients">
                                                        أرشفة العملاء غير النشطين
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="archive_old_documents" name="archive_old_documents">
                                                    <label class="form-check-label" for="archive_old_documents">
                                                        أرشفة المستندات القديمة
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- إعدادات النسخ الاحتياطي -->
                            <div id="backup-settings" class="settings-category d-none">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-database me-2"></i>إعدادات النسخ الاحتياطي
                                </h6>
                                <form id="backupSettingsForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="auto_backup_enabled" name="auto_backup_enabled">
                                                    <label class="form-check-label" for="auto_backup_enabled">
                                                        تفعيل النسخ الاحتياطي التلقائي
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="backup_frequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                                <select class="form-select" id="backup_frequency" name="backup_frequency">
                                                    <option value="daily">يومي</option>
                                                    <option value="weekly">أسبوعي</option>
                                                    <option value="monthly">شهري</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="backup_time" class="form-label">وقت النسخ الاحتياطي</label>
                                                <input type="time" class="form-control" id="backup_time" name="backup_time">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="backup_retention_days" class="form-label">مدة الاحتفاظ (أيام)</label>
                                                <input type="number" class="form-control" id="backup_retention_days" name="backup_retention_days" min="1" max="365">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="backup_compression" name="backup_compression">
                                                    <label class="form-check-label" for="backup_compression">
                                                        ضغط النسخ الاحتياطية
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="backup_encryption" name="backup_encryption">
                                                    <label class="form-check-label" for="backup_encryption">
                                                        تشفير النسخ الاحتياطية
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>

                                <!-- إحصائيات النسخ الاحتياطي -->
                                <div class="mt-4">
                                    <h6 class="text-secondary mb-3">إحصائيات النسخ الاحتياطي</h6>
                                    <div class="row" id="backupStats">
                                        <div class="col-md-3">
                                            <div class="card bg-primary text-white">
                                                <div class="card-body text-center">
                                                    <h5 class="card-title" id="totalBackups">0</h5>
                                                    <p class="card-text">إجمالي النسخ</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-success text-white">
                                                <div class="card-body text-center">
                                                    <h5 class="card-title" id="successfulBackups">0</h5>
                                                    <p class="card-text">نسخ ناجحة</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-danger text-white">
                                                <div class="card-body text-center">
                                                    <h5 class="card-title" id="failedBackups">0</h5>
                                                    <p class="card-text">نسخ فاشلة</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-info text-white">
                                                <div class="card-body text-center">
                                                    <h5 class="card-title" id="totalSize">0 MB</h5>
                                                    <p class="card-text">الحجم الإجمالي</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- أزرار النسخ الاحتياطي -->
                                <div class="mt-4">
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-success" onclick="createManualBackup()">
                                            <i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية الآن
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="viewBackupHistory()">
                                            <i class="fas fa-history me-2"></i>سجل النسخ الاحتياطية
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="restoreBackup()">
                                            <i class="fas fa-upload me-2"></i>استعادة نسخة احتياطية
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- إعدادات الأمان والحماية -->
                            <div id="security-settings" class="settings-category d-none">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-shield-alt me-2"></i>إعدادات الأمان والحماية
                                </h6>
                                <form id="securitySettingsForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="enable_2fa" name="enable_2fa">
                                                    <label class="form-check-label" for="enable_2fa">
                                                        تفعيل المصادقة الثنائية
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="session_timeout" class="form-label">انتهاء الجلسة (دقائق)</label>
                                                <input type="number" class="form-control" id="session_timeout" name="session_timeout" min="5" max="480">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="password_min_length" class="form-label">الحد الأدنى لطول كلمة المرور</label>
                                                <input type="number" class="form-control" id="password_min_length" name="password_min_length" min="6" max="20">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="max_login_attempts" class="form-label">عدد محاولات تسجيل الدخول</label>
                                                <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts" min="3" max="10">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="enable_audit_log" name="enable_audit_log">
                                                    <label class="form-check-label" for="enable_audit_log">
                                                        تفعيل سجل المراجعة
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="enable_ip_whitelist" name="enable_ip_whitelist">
                                                    <label class="form-check-label" for="enable_ip_whitelist">
                                                        تفعيل القائمة البيضاء للـ IP
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- إعدادات واجهة المستخدم -->
                            <div id="ui-settings" class="settings-category d-none">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-palette me-2"></i>إعدادات واجهة المستخدم
                                </h6>
                                <form id="uiSettingsForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="default_theme" class="form-label">المظهر الافتراضي</label>
                                                <select class="form-select" id="default_theme" name="default_theme">
                                                    <option value="light">فاتح</option>
                                                    <option value="dark">داكن</option>
                                                    <option value="auto">تلقائي</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="default_language" class="form-label">اللغة الافتراضية</label>
                                                <select class="form-select" id="default_language" name="default_language">
                                                    <option value="ar">العربية</option>
                                                    <option value="en">English</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="items_per_page" class="form-label">عدد العناصر في الصفحة</label>
                                                <select class="form-select" id="items_per_page" name="items_per_page">
                                                    <option value="10">10</option>
                                                    <option value="25">25</option>
                                                    <option value="50">50</option>
                                                    <option value="100">100</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="show_sidebar_icons" name="show_sidebar_icons">
                                                    <label class="form-check-label" for="show_sidebar_icons">
                                                        إظهار أيقونات الشريط الجانبي
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="enable_animations" name="enable_animations">
                                                    <label class="form-check-label" for="enable_animations">
                                                        تفعيل الحركات والانتقالات
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="enable_tooltips" name="enable_tooltips">
                                                    <label class="form-check-label" for="enable_tooltips">
                                                        تفعيل التلميحات
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- إعدادات التنبيهات -->
                            <div id="notifications-settings" class="settings-category d-none">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-bell me-2"></i>إعدادات التنبيهات
                                </h6>
                                <form id="notificationsSettingsForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="enable_email_notifications" name="enable_email_notifications">
                                                    <label class="form-check-label" for="enable_email_notifications">
                                                        تفعيل التنبيهات عبر البريد الإلكتروني
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="enable_sms_notifications" name="enable_sms_notifications">
                                                    <label class="form-check-label" for="enable_sms_notifications">
                                                        تفعيل التنبيهات عبر الرسائل النصية
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="notify_case_updates" name="notify_case_updates">
                                                    <label class="form-check-label" for="notify_case_updates">
                                                        تنبيهات تحديثات القضايا
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="notify_payment_due" name="notify_payment_due">
                                                    <label class="form-check-label" for="notify_payment_due">
                                                        تنبيهات استحقاق المدفوعات
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="notify_appointments" name="notify_appointments">
                                                    <label class="form-check-label" for="notify_appointments">
                                                        تنبيهات المواعيد
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="notification_advance_days" class="form-label">التنبيه قبل (أيام)</label>
                                                <input type="number" class="form-control" id="notification_advance_days" name="notification_advance_days" min="1" max="30">
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- إعدادات التقارير -->
                            <div id="reports-settings" class="settings-category d-none">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-file-alt me-2"></i>إعدادات التقارير
                                </h6>
                                <form id="reportsSettingsForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="default_report_format" class="form-label">تنسيق التقرير الافتراضي</label>
                                                <select class="form-select" id="default_report_format" name="default_report_format">
                                                    <option value="pdf">PDF</option>
                                                    <option value="excel">Excel</option>
                                                    <option value="word">Word</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="auto_generate_reports" name="auto_generate_reports">
                                                    <label class="form-check-label" for="auto_generate_reports">
                                                        إنشاء التقارير تلقائياً
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="report_generation_frequency" class="form-label">تكرار إنشاء التقارير</label>
                                                <select class="form-select" id="report_generation_frequency" name="report_generation_frequency">
                                                    <option value="daily">يومي</option>
                                                    <option value="weekly">أسبوعي</option>
                                                    <option value="monthly">شهري</option>
                                                    <option value="quarterly">ربع سنوي</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="include_charts" name="include_charts">
                                                    <label class="form-check-label" for="include_charts">
                                                        تضمين الرسوم البيانية
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="watermark_reports" name="watermark_reports">
                                                    <label class="form-check-label" for="watermark_reports">
                                                        إضافة علامة مائية للتقارير
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="report_retention_days" class="form-label">مدة الاحتفاظ بالتقارير (أيام)</label>
                                                <input type="number" class="form-control" id="report_retention_days" name="report_retention_days" min="30" max="3650">
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-warning" onclick="resetSettings()">
                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                </button>
                <button type="button" class="btn btn-success" onclick="exportSettings()">
                    <i class="fas fa-download me-2"></i>تصدير الإعدادات
                </button>
                <button type="button" class="btn btn-info" onclick="importSettings()">
                    <i class="fas fa-upload me-2"></i>استيراد الإعدادات
                </button>
                <button type="button" class="btn btn-primary" onclick="saveAllSettings()">
                    <i class="fas fa-save me-2"></i>حفظ جميع الإعدادات
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadArchiveStatistics();
    loadArchivedCases();
    loadSystemInfo();
});

// تحميل إحصائيات الأرشيف
async function loadArchiveStatistics() {
    try {
        const response = await fetch('/api/archive/statistics');
        const data = await response.json();

        if (data.success) {
            document.getElementById('archived-cases').textContent = data.statistics.archived_cases || 0;
            document.getElementById('archived-clients').textContent = data.statistics.archived_clients || 0;
            document.getElementById('archived-documents').textContent = data.statistics.archived_documents || 0;
            document.getElementById('total-size').textContent = (data.statistics.total_size || 0) + ' MB';
        }
    } catch (error) {
        console.error('خطأ في تحميل الإحصائيات:', error);
    }
}

// تحميل القضايا المؤرشفة
async function loadArchivedCases() {
    try {
        const response = await fetch('/api/archive/cases');
        const data = await response.json();

        if (data.success) {
            displayArchivedCases(data.cases);
        } else {
            showError('فشل في تحميل القضايا المؤرشفة');
        }
    } catch (error) {
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض القضايا المؤرشفة
function displayArchivedCases(cases) {
    const container = document.getElementById('archived-cases-container');

    if (cases.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-archive fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد قضايا مؤرشفة</h5>
                <p class="text-muted">ستظهر القضايا المؤرشفة هنا</p>
            </div>
        `;
        return;
    }

    container.innerHTML = `
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>رقم القضية</th>
                        <th>العنوان</th>
                        <th>العميل</th>
                        <th>تاريخ الأرشفة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${cases.map(case_ => `
                        <tr>
                            <td>${case_.case_number}</td>
                            <td>${case_.title}</td>
                            <td>${case_.client_name || 'غير محدد'}</td>
                            <td>${formatDate(case_.archived_date)}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="restoreCase(${case_.id})" title="استعادة">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteCase(${case_.id})" title="حذف نهائي">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// تحميل معلومات النظام
async function loadSystemInfo() {
    try {
        const response = await fetch('/api/system/info');
        const data = await response.json();

        if (data.success) {
            displaySystemInfo(data);
        }
    } catch (error) {
        console.error('خطأ في تحميل معلومات النظام:', error);
    }
}

// عرض معلومات النظام
function displaySystemInfo(info) {
    const container = document.getElementById('system-info');

    container.innerHTML = `
        <div class="row">
            <div class="col-12">
                <p><strong>إصدار النظام:</strong> ${info.version || '1.0.0'}</p>
                <p><strong>تاريخ آخر تحديث:</strong> ${formatDate(info.last_update || new Date())}</p>
                <p><strong>حجم قاعدة البيانات:</strong> ${info.database_size || 'غير محدد'}</p>
                <p><strong>عدد المستخدمين:</strong> ${info.total_users || 0}</p>
                <p><strong>حالة النظام:</strong> <span class="badge bg-success">يعمل بشكل طبيعي</span></p>
            </div>
        </div>
    `;

    // تحميل سجلات النسخ الاحتياطية
    const backupContainer = document.getElementById('backup-logs');
    backupContainer.innerHTML = `
        <div class="text-center">
            <p class="text-muted">لا توجد نسخ احتياطية حالياً</p>
            <button class="btn btn-primary btn-sm" onclick="createBackup()">
                <i class="fas fa-download me-2"></i>
                إنشاء نسخة احتياطية
            </button>
        </div>
    `;
}

// استعادة قضية
async function restoreCase(caseId) {
    if (!confirm('هل أنت متأكد من استعادة هذه القضية؟')) return;

    try {
        const response = await fetch(`/api/archive/cases/${caseId}/restore`, {
            method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
            showSuccess('تم استعادة القضية بنجاح');
            loadArchivedCases();
            loadArchiveStatistics();
        } else {
            showError(data.error || 'فشل في استعادة القضية');
        }
    } catch (error) {
        showError('خطأ في الاتصال بالخادم');
    }
}

// حذف قضية نهائياً
async function deleteCase(caseId) {
    if (!confirm('هل أنت متأكد من حذف هذه القضية نهائياً؟ هذا الإجراء لا يمكن التراجع عنه.')) return;

    try {
        const response = await fetch(`/api/archive/cases/${caseId}/delete`, {
            method: 'DELETE'
        });
        const data = await response.json();

        if (data.success) {
            showSuccess('تم حذف القضية نهائياً');
            loadArchivedCases();
            loadArchiveStatistics();
        } else {
            showError(data.error || 'فشل في حذف القضية');
        }
    } catch (error) {
        showError('خطأ في الاتصال بالخادم');
    }
}

// إنشاء نسخة احتياطية
async function createBackup() {
    try {
        showSuccess('جاري إنشاء النسخة الاحتياطية...');

        const response = await fetch('/api/backup/create', {
            method: 'POST'
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `backup_${new Date().toISOString().split('T')[0]}.zip`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            showSuccess('تم إنشاء النسخة الاحتياطية وتحميلها بنجاح');
        } else {
            showError('فشل في إنشاء النسخة الاحتياطية');
        }
    } catch (error) {
        showError('خطأ في إنشاء النسخة الاحتياطية');
    }
}

// حفظ الإعدادات
async function saveSettings() {
    try {
        const generalSettings = {
            office_name: document.getElementById('office-name').value,
            office_address: document.getElementById('office-address').value,
            office_phone: document.getElementById('office-phone').value,
            office_email: document.getElementById('office-email').value,
            auto_backup: document.getElementById('auto-backup').checked
        };

        const archiveSettings = {
            auto_archive_days: document.getElementById('auto-archive-days').value,
            backup_frequency: document.getElementById('backup-frequency').value,
            max_backup_files: document.getElementById('max-backup-files').value,
            compress_backups: document.getElementById('compress-backups').checked
        };

        // حفظ الإعدادات العامة
        const generalResponse = await fetch('/api/settings/general', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(generalSettings)
        });

        // حفظ إعدادات الأرشفة
        const archiveResponse = await fetch('/api/settings/archive', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(archiveSettings)
        });

        if (generalResponse.ok && archiveResponse.ok) {
            bootstrap.Modal.getInstance(document.getElementById('settingsModal')).hide();
            showSuccess('تم حفظ الإعدادات بنجاح');
        } else {
            showError('فشل في حفظ بعض الإعدادات');
        }
    } catch (error) {
        showError('خطأ في حفظ الإعدادات');
    }
}

// تحديث الأرشيف
function refreshArchive() {
    loadArchiveStatistics();
    loadArchivedCases();
    loadSystemInfo();
    showSuccess('تم تحديث الأرشيف');
}

// دوال البحث
function searchArchivedCases() {
    // تنفيذ البحث في القضايا المؤرشفة
}

function searchArchivedClients() {
    // تنفيذ البحث في العملاء المؤرشفين
}

function searchArchivedDocuments() {
    // تنفيذ البحث في المستندات المؤرشفة
}

// دوال مساعدة
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function showSuccess(message) {
    alert(message);
}

function showError(message) {
    alert('خطأ: ' + message);
}

// تحميل البيانات عند تغيير التبويبات
document.getElementById('clients-tab').addEventListener('click', function() {
    loadArchivedClients();
});

document.getElementById('documents-tab').addEventListener('click', function() {
    loadArchivedDocuments();
});

// تحميل العملاء المؤرشفين
async function loadArchivedClients() {
    try {
        const response = await fetch('/api/archive/clients');
        const data = await response.json();

        if (data.success) {
            displayArchivedClients(data.clients);
        }
    } catch (error) {
        console.error('خطأ في تحميل العملاء المؤرشفين:', error);
    }
}

// عرض العملاء المؤرشفين
function displayArchivedClients(clients) {
    const container = document.getElementById('archived-clients-container');

    if (clients.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد عملاء مؤرشفون</h5>
            </div>
        `;
        return;
    }

    container.innerHTML = `
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>تاريخ الأرشفة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${clients.map(client => `
                        <tr>
                            <td>${client.name}</td>
                            <td>${client.phone || 'غير محدد'}</td>
                            <td>${client.email || 'غير محدد'}</td>
                            <td>${formatDate(client.archived_date)}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="restoreClient(${client.id})" title="استعادة">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteClient(${client.id})" title="حذف نهائي">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// تحميل المستندات المؤرشفة
async function loadArchivedDocuments() {
    try {
        const response = await fetch('/api/archive/documents');
        const data = await response.json();

        if (data.success) {
            displayArchivedDocuments(data.documents);
        }
    } catch (error) {
        console.error('خطأ في تحميل المستندات المؤرشفة:', error);
    }
}

// عرض المستندات المؤرشفة
function displayArchivedDocuments(documents) {
    const container = document.getElementById('archived-documents-container');

    if (documents.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مستندات مؤرشفة</h5>
            </div>
        `;
        return;
    }

    container.innerHTML = `
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>اسم المستند</th>
                        <th>النوع</th>
                        <th>الحجم</th>
                        <th>تاريخ الأرشفة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${documents.map(doc => `
                        <tr>
                            <td>${doc.filename}</td>
                            <td>${doc.file_type || 'غير محدد'}</td>
                            <td>${doc.file_size || 'غير محدد'}</td>
                            <td>${formatDate(doc.archived_date)}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="restoreDocument(${doc.id})" title="استعادة">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteDocument(${doc.id})" title="حذف نهائي">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// استعادة عميل
async function restoreClient(clientId) {
    if (!confirm('هل أنت متأكد من استعادة هذا العميل؟')) return;

    try {
        const response = await fetch(`/api/archive/clients/${clientId}/restore`, {
            method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
            showSuccess('تم استعادة العميل بنجاح');
            loadArchivedClients();
            loadArchiveStatistics();
        } else {
            showError(data.error || 'فشل في استعادة العميل');
        }
    } catch (error) {
        showError('خطأ في الاتصال بالخادم');
    }
}

// حذف عميل نهائياً
async function deleteClient(clientId) {
    if (!confirm('هل أنت متأكد من حذف هذا العميل نهائياً؟')) return;

    try {
        const response = await fetch(`/api/archive/clients/${clientId}/delete`, {
            method: 'DELETE'
        });
        const data = await response.json();

        if (data.success) {
            showSuccess('تم حذف العميل نهائياً');
            loadArchivedClients();
            loadArchiveStatistics();
        } else {
            showError(data.error || 'فشل في حذف العميل');
        }
    } catch (error) {
        showError('خطأ في الاتصال بالخادم');
    }
}

// استعادة مستند
async function restoreDocument(docId) {
    if (!confirm('هل أنت متأكد من استعادة هذا المستند؟')) return;

    try {
        const response = await fetch(`/api/archive/documents/${docId}/restore`, {
            method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
            showSuccess('تم استعادة المستند بنجاح');
            loadArchivedDocuments();
            loadArchiveStatistics();
        } else {
            showError(data.error || 'فشل في استعادة المستند');
        }
    } catch (error) {
        showError('خطأ في الاتصال بالخادم');
    }
}

// حذف مستند نهائياً
async function deleteDocument(docId) {
    if (!confirm('هل أنت متأكد من حذف هذا المستند نهائياً؟')) return;

    try {
        const response = await fetch(`/api/archive/documents/${docId}/delete`, {
            method: 'DELETE'
        });
        const data = await response.json();

        if (data.success) {
            showSuccess('تم حذف المستند نهائياً');
            loadArchivedDocuments();
            loadArchiveStatistics();
        } else {
            showError(data.error || 'فشل في حذف المستند');
        }
    } catch (error) {
        showError('خطأ في الاتصال بالخادم');
    }
}

// إدارة الإعدادات المتقدمة
let currentSettings = {};

// تبديل الفئات في نافذة الإعدادات المتقدمة
document.addEventListener('DOMContentLoaded', function() {
    // إعداد تبديل الفئات
    const categoryLinks = document.querySelectorAll('#settingsCategories .list-group-item');
    const settingsCategories = document.querySelectorAll('.settings-category');

    categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // إزالة الفئة النشطة
            categoryLinks.forEach(l => l.classList.remove('active'));
            settingsCategories.forEach(c => c.classList.add('d-none'));

            // تفعيل الفئة المحددة
            this.classList.add('active');
            const category = this.getAttribute('data-category');
            document.getElementById(category + '-settings').classList.remove('d-none');
        });
    });

    // تحميل الإعدادات عند فتح النافذة
    const settingsModal = document.getElementById('settingsModal');
    if (settingsModal) {
        settingsModal.addEventListener('show.bs.modal', function() {
            loadAllSettings();
            loadBackupStats();
        });
    }
});

// تحميل جميع الإعدادات
async function loadAllSettings() {
    try {
        const response = await fetch('/api/settings/all');
        const data = await response.json();

        if (data.success) {
            currentSettings = data.settings;
            populateSettingsForm();
        }
    } catch (error) {
        console.error('Error loading settings:', error);
        showError('حدث خطأ أثناء تحميل الإعدادات');
    }
}

// ملء النماذج بالإعدادات
function populateSettingsForm() {
    Object.keys(currentSettings).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = currentSettings[key] === 'true' || currentSettings[key] === true;
            } else {
                element.value = currentSettings[key] || '';
            }
        }
    });
}

// حفظ جميع الإعدادات
async function saveAllSettings() {
    const settingsData = {};

    // جمع البيانات من جميع النماذج
    const forms = ['generalSettingsForm', 'archiveSettingsForm', 'backupSettingsForm',
                   'securitySettingsForm', 'uiSettingsForm', 'notificationsSettingsForm', 'reportsSettingsForm'];

    forms.forEach(formId => {
        const form = document.getElementById(formId);
        if (form) {
            const formData = new FormData(form);
            for (let [key, value] of formData.entries()) {
                const element = document.getElementById(key);
                if (element && element.type === 'checkbox') {
                    settingsData[key] = element.checked;
                } else {
                    settingsData[key] = value;
                }
            }
        }
    });

    try {
        const response = await fetch('/api/settings/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(settingsData)
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم حفظ جميع الإعدادات بنجاح');
            currentSettings = settingsData;

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
            modal.hide();
        } else {
            showError('حدث خطأ أثناء حفظ الإعدادات: ' + data.message);
        }
    } catch (error) {
        console.error('Error saving settings:', error);
        showError('حدث خطأ أثناء حفظ الإعدادات');
    }
}

// إعادة تعيين الإعدادات
async function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
        try {
            const response = await fetch('/api/settings/reset', {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                showSuccess('تم إعادة تعيين الإعدادات بنجاح');
                loadAllSettings();
            } else {
                showError('حدث خطأ أثناء إعادة تعيين الإعدادات');
            }
        } catch (error) {
            console.error('Error resetting settings:', error);
            showError('حدث خطأ أثناء إعادة تعيين الإعدادات');
        }
    }
}

// تصدير الإعدادات
async function exportSettings() {
    try {
        const response = await fetch('/api/settings/export');
        const blob = await response.blob();

        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'settings_backup_' + new Date().toISOString().split('T')[0] + '.json';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);

        showSuccess('تم تصدير الإعدادات بنجاح');
    } catch (error) {
        console.error('Error exporting settings:', error);
        showError('حدث خطأ أثناء تصدير الإعدادات');
    }
}

// استيراد الإعدادات
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async function(e) {
        const file = e.target.files[0];
        if (file) {
            try {
                const text = await file.text();
                const settings = JSON.parse(text);

                const response = await fetch('/api/settings/import', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(settings)
                });

                const data = await response.json();

                if (data.success) {
                    showSuccess('تم استيراد الإعدادات بنجاح');
                    loadAllSettings();
                } else {
                    showError('حدث خطأ أثناء استيراد الإعدادات');
                }
            } catch (error) {
                console.error('Error importing settings:', error);
                showError('ملف الإعدادات غير صالح');
            }
        }
    };
    input.click();
}

// إنشاء نسخة احتياطية يدوية
async function createManualBackup() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';
    button.disabled = true;

    try {
        const response = await fetch('/api/backup/create', {
            method: 'POST'
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم إنشاء النسخة الاحتياطية بنجاح');
            loadBackupStats();
        } else {
            showError('حدث خطأ أثناء إنشاء النسخة الاحتياطية: ' + data.message);
        }
    } catch (error) {
        console.error('Error creating backup:', error);
        showError('حدث خطأ أثناء إنشاء النسخة الاحتياطية');
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// تحميل إحصائيات النسخ الاحتياطي
async function loadBackupStats() {
    try {
        const response = await fetch('/api/backup/stats');
        const data = await response.json();

        if (data.success) {
            document.getElementById('totalBackups').textContent = data.stats.total_backups || 0;
            document.getElementById('successfulBackups').textContent = data.stats.successful_backups || 0;
            document.getElementById('failedBackups').textContent = data.stats.failed_backups || 0;
            document.getElementById('totalSize').textContent = data.stats.total_size || '0 MB';
        }
    } catch (error) {
        console.error('Error loading backup stats:', error);
    }
}

// عرض سجل النسخ الاحتياطية
async function viewBackupHistory() {
    try {
        const response = await fetch('/api/backup/history');
        const data = await response.json();

        if (data.success) {
            showSuccess('سيتم تطوير عرض السجل قريباً');
        }
    } catch (error) {
        console.error('Error loading backup history:', error);
        showError('حدث خطأ أثناء تحميل سجل النسخ الاحتياطية');
    }
}

// استعادة نسخة احتياطية
function restoreBackup() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.zip';
    input.onchange = async function(e) {
        const file = e.target.files[0];
        if (file) {
            if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
                const formData = new FormData();
                formData.append('backup_file', file);

                try {
                    const response = await fetch('/api/backup/restore', {
                        method: 'POST',
                        body: formData
                    });

                    const data = await response.json();

                    if (data.success) {
                        showSuccess('تم استعادة النسخة الاحتياطية بنجاح');
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } else {
                        showError('حدث خطأ أثناء استعادة النسخة الاحتياطية: ' + data.message);
                    }
                } catch (error) {
                    console.error('Error restoring backup:', error);
                    showError('حدث خطأ أثناء استعادة النسخة الاحتياطية');
                }
            }
        }
    };
    input.click();
}
</script>

<style>
.stats-card {
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    color: white;
    margin-bottom: 10px;
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-card p {
    margin: 0;
    opacity: 0.9;
}

.nav-tabs .nav-link {
    color: #495057;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    border-color: #007bff #007bff #fff;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>
{% endblock %}