/**
 * نظام التقارير الشامل
 * Comprehensive Reports System
 * 
 * يوفر واجهة شاملة لإنشاء وإدارة التقارير المختلفة
 * Provides comprehensive interface for creating and managing various reports
 */

class ReportsManager {
    constructor() {
        this.currentReport = null;
        this.reportData = null;
        this.chartInstances = {};
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadReportTemplates();
        this.initializeCharts();
    }

    setupEventListeners() {
        // أزرار التقارير السريعة
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-quick-report]')) {
                const reportType = e.target.dataset.quickReport;
                this.generateQuickReport(reportType);
            }
            
            if (e.target.matches('[data-custom-report]')) {
                this.showCustomReportBuilder();
            }
            
            if (e.target.matches('[data-export-report]')) {
                const format = e.target.dataset.exportFormat;
                this.exportReport(format);
            }
            
            if (e.target.matches('[data-print-report]')) {
                this.printReport();
            }
            
            if (e.target.matches('[data-schedule-report]')) {
                this.showScheduleReportModal();
            }
        });

        // فلاتر التقارير
        document.addEventListener('change', (e) => {
            if (e.target.matches('.report-filter')) {
                this.applyReportFilters();
            }
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                this.showQuickReportMenu();
            }
            
            if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                e.preventDefault();
                this.showCustomReportBuilder();
            }
            
            if (e.ctrlKey && e.key === 'p' && this.currentReport) {
                e.preventDefault();
                this.printReport();
            }
        });
    }

    async generateQuickReport(reportType) {
        try {
            this.showLoadingIndicator('جاري إنشاء التقرير...');
            
            const response = await fetch('/api/reports/quick', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: reportType,
                    filters: this.getActiveFilters()
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.currentReport = {
                    type: reportType,
                    data: result.data,
                    metadata: result.metadata
                };
                
                this.displayReport(result.data, result.metadata);
                this.updateReportCharts(result.charts);
                this.saveReportToHistory(reportType, result.metadata);
            } else {
                this.showError('خطأ في إنشاء التقرير: ' + result.error);
            }
        } catch (error) {
            this.showError('خطأ في الاتصال بالخادم: ' + error.message);
        } finally {
            this.hideLoadingIndicator();
        }
    }

    async generateCustomReport(criteria) {
        try {
            this.showLoadingIndicator('جاري إنشاء التقرير المخصص...');
            
            const response = await fetch('/api/reports/custom', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(criteria)
            });

            const result = await response.json();
            
            if (result.success) {
                this.currentReport = {
                    type: 'custom',
                    data: result.data,
                    metadata: result.metadata,
                    criteria: criteria
                };
                
                this.displayReport(result.data, result.metadata);
                this.updateReportCharts(result.charts);
                
                // حفظ التقرير المخصص
                this.saveCustomReport(criteria, result.metadata);
            } else {
                this.showError('خطأ في إنشاء التقرير المخصص: ' + result.error);
            }
        } catch (error) {
            this.showError('خطأ في الاتصال بالخادم: ' + error.message);
        } finally {
            this.hideLoadingIndicator();
        }
    }

    displayReport(data, metadata) {
        const reportContainer = document.getElementById('report-display-area');
        
        let html = `
            <div class="report-header">
                <div class="row align-items-center mb-4">
                    <div class="col">
                        <h2 class="report-title">
                            <i class="fas fa-chart-bar me-2"></i>${metadata.title}
                        </h2>
                        <p class="report-subtitle text-muted">${metadata.description}</p>
                    </div>
                    <div class="col-auto">
                        <div class="report-actions">
                            <button class="btn btn-outline-primary btn-sm" data-export-report data-export-format="pdf">
                                <i class="fas fa-file-pdf me-1"></i>PDF
                            </button>
                            <button class="btn btn-outline-success btn-sm" data-export-report data-export-format="excel">
                                <i class="fas fa-file-excel me-1"></i>Excel
                            </button>
                            <button class="btn btn-outline-info btn-sm" data-print-report>
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                            <button class="btn btn-outline-warning btn-sm" data-schedule-report>
                                <i class="fas fa-clock me-1"></i>جدولة
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="report-metadata">
                    <div class="row">
                        <div class="col-md-3">
                            <small class="text-muted">تاريخ الإنشاء:</small>
                            <div>${new Date().toLocaleString('ar-SA')}</div>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">الفترة:</small>
                            <div>${metadata.period || 'جميع الفترات'}</div>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">عدد السجلات:</small>
                            <div>${metadata.recordCount || 0}</div>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">نوع التقرير:</small>
                            <div>${metadata.type || 'عام'}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="report-content">
        `;

        // إضافة الملخص التنفيذي
        if (data.summary) {
            html += this.generateSummarySection(data.summary);
        }

        // إضافة الرسوم البيانية
        if (data.charts) {
            html += this.generateChartsSection(data.charts);
        }

        // إضافة الجداول
        if (data.tables) {
            html += this.generateTablesSection(data.tables);
        }

        // إضافة التحليلات
        if (data.analysis) {
            html += this.generateAnalysisSection(data.analysis);
        }

        html += '</div>';
        
        reportContainer.innerHTML = html;
        
        // تحديث الرسوم البيانية
        setTimeout(() => {
            this.renderCharts(data.charts);
        }, 100);
    }

    generateSummarySection(summary) {
        let html = `
            <div class="report-section summary-section">
                <h3 class="section-title">
                    <i class="fas fa-chart-pie me-2"></i>الملخص التنفيذي
                </h3>
                <div class="row">
        `;

        summary.forEach(item => {
            const iconClass = this.getIconForMetric(item.type);
            const colorClass = this.getColorForMetric(item.type);
            
            html += `
                <div class="col-md-3 mb-3">
                    <div class="summary-card ${colorClass}">
                        <div class="summary-icon">
                            <i class="${iconClass}"></i>
                        </div>
                        <div class="summary-content">
                            <h4>${item.value}</h4>
                            <p>${item.label}</p>
                            ${item.change ? `<small class="change ${item.change > 0 ? 'positive' : 'negative'}">
                                ${item.change > 0 ? '+' : ''}${item.change}%
                            </small>` : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div></div>';
        return html;
    }

    generateChartsSection(charts) {
        let html = `
            <div class="report-section charts-section">
                <h3 class="section-title">
                    <i class="fas fa-chart-line me-2"></i>الرسوم البيانية
                </h3>
                <div class="row">
        `;

        charts.forEach((chart, index) => {
            html += `
                <div class="col-md-6 mb-4">
                    <div class="chart-container">
                        <h5 class="chart-title">${chart.title}</h5>
                        <canvas id="chart-${index}" class="report-chart"></canvas>
                    </div>
                </div>
            `;
        });

        html += '</div></div>';
        return html;
    }

    generateTablesSection(tables) {
        let html = `
            <div class="report-section tables-section">
                <h3 class="section-title">
                    <i class="fas fa-table me-2"></i>البيانات التفصيلية
                </h3>
        `;

        tables.forEach(table => {
            html += `
                <div class="table-container mb-4">
                    <h5 class="table-title">${table.title}</h5>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
            `;
            
            table.headers.forEach(header => {
                html += `<th>${header}</th>`;
            });
            
            html += `
                                </tr>
                            </thead>
                            <tbody>
            `;
            
            table.rows.forEach(row => {
                html += '<tr>';
                row.forEach(cell => {
                    html += `<td>${cell}</td>`;
                });
                html += '</tr>';
            });
            
            html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    generateAnalysisSection(analysis) {
        let html = `
            <div class="report-section analysis-section">
                <h3 class="section-title">
                    <i class="fas fa-brain me-2"></i>التحليل والتوصيات
                </h3>
        `;

        analysis.forEach(item => {
            html += `
                <div class="analysis-item mb-3">
                    <h5 class="analysis-title">
                        <i class="fas fa-lightbulb me-2"></i>${item.title}
                    </h5>
                    <p class="analysis-content">${item.content}</p>
                    ${item.recommendations ? `
                        <div class="recommendations">
                            <h6>التوصيات:</h6>
                            <ul>
                                ${item.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    renderCharts(charts) {
        if (!charts) return;
        
        charts.forEach((chartData, index) => {
            const canvas = document.getElementById(`chart-${index}`);
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            
            // تدمير الرسم البياني السابق إن وجد
            if (this.chartInstances[`chart-${index}`]) {
                this.chartInstances[`chart-${index}`].destroy();
            }
            
            this.chartInstances[`chart-${index}`] = new Chart(ctx, {
                type: chartData.type,
                data: chartData.data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        title: {
                            display: true,
                            text: chartData.title
                        }
                    },
                    ...chartData.options
                }
            });
        });
    }

    async exportReport(format) {
        if (!this.currentReport) {
            this.showError('لا يوجد تقرير لتصديره');
            return;
        }

        try {
            this.showLoadingIndicator(`جاري تصدير التقرير بصيغة ${format.toUpperCase()}...`);
            
            const response = await fetch('/api/reports/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    report: this.currentReport,
                    format: format
                })
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `report_${new Date().getTime()}.${format}`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                this.showSuccess(`تم تصدير التقرير بصيغة ${format.toUpperCase()} بنجاح`);
            } else {
                this.showError('خطأ في تصدير التقرير');
            }
        } catch (error) {
            this.showError('خطأ في تصدير التقرير: ' + error.message);
        } finally {
            this.hideLoadingIndicator();
        }
    }

    printReport() {
        if (!this.currentReport) {
            this.showError('لا يوجد تقرير للطباعة');
            return;
        }

        const printWindow = window.open('', '_blank');
        const reportContent = document.getElementById('report-display-area').innerHTML;
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>طباعة التقرير</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
                    .report-actions { display: none !important; }
                    .chart-container { page-break-inside: avoid; }
                    .table-container { page-break-inside: avoid; }
                    @media print {
                        .no-print { display: none !important; }
                        .page-break { page-break-before: always; }
                    }
                </style>
            </head>
            <body>
                <div class="container-fluid">
                    ${reportContent}
                </div>
            </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.focus();
        
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 1000);
    }

    // وظائف مساعدة
    getIconForMetric(type) {
        const icons = {
            'cases': 'fas fa-gavel',
            'clients': 'fas fa-users',
            'revenue': 'fas fa-dollar-sign',
            'expenses': 'fas fa-credit-card',
            'properties': 'fas fa-building',
            'tasks': 'fas fa-tasks',
            'default': 'fas fa-chart-bar'
        };
        return icons[type] || icons.default;
    }

    getColorForMetric(type) {
        const colors = {
            'cases': 'bg-primary',
            'clients': 'bg-success',
            'revenue': 'bg-info',
            'expenses': 'bg-warning',
            'properties': 'bg-secondary',
            'tasks': 'bg-dark',
            'default': 'bg-light'
        };
        return colors[type] || colors.default;
    }

    getActiveFilters() {
        const filters = {};
        document.querySelectorAll('.report-filter').forEach(filter => {
            if (filter.value) {
                filters[filter.name] = filter.value;
            }
        });
        return filters;
    }

    showLoadingIndicator(message) {
        const indicator = document.getElementById('loading-indicator');
        if (indicator) {
            indicator.querySelector('.loading-message').textContent = message;
            indicator.style.display = 'block';
        }
    }

    hideLoadingIndicator() {
        const indicator = document.getElementById('loading-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    showSuccess(message) {
        // إظهار رسالة نجاح
        this.showToast(message, 'success');
    }

    showError(message) {
        // إظهار رسالة خطأ
        this.showToast(message, 'error');
    }

    showToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 5000);
    }

    saveReportToHistory(type, metadata) {
        const history = JSON.parse(localStorage.getItem('reportHistory') || '[]');
        history.unshift({
            type: type,
            metadata: metadata,
            timestamp: new Date().toISOString()
        });
        
        // الاحتفاظ بآخر 50 تقرير فقط
        if (history.length > 50) {
            history.splice(50);
        }
        
        localStorage.setItem('reportHistory', JSON.stringify(history));
    }

    saveCustomReport(criteria, metadata) {
        const customReports = JSON.parse(localStorage.getItem('customReports') || '[]');
        customReports.unshift({
            criteria: criteria,
            metadata: metadata,
            timestamp: new Date().toISOString()
        });
        
        localStorage.setItem('customReports', JSON.stringify(customReports));
    }
}

// تهيئة نظام التقارير عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.reportsManager = new ReportsManager();
});
