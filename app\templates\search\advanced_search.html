{% extends "base.html" %}

{% block title %}البحث المتقدم{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/advanced-search.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-search-plus me-3"></i>البحث المتقدم
                </h1>
                <p class="page-description text-muted">
                    ابحث في جميع أقسام النظام باستخدام الفلاتر المتقدمة والبحث الذكي
                </p>
            </div>
            <div class="col-auto">
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="advancedSearch.showSearchHistory()">
                        <i class="fas fa-history me-1"></i>تاريخ البحث
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="advancedSearch.showSavedSearches()">
                        <i class="fas fa-bookmark me-1"></i>البحث المحفوظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-gavel"></i>
                </div>
                <div class="stat-content">
                    <h3 id="total-cases">{{ total_cases or 0 }}</h3>
                    <p>إجمالي القضايا</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3 id="total-clients">{{ total_clients or 0 }}</h3>
                    <p>إجمالي العملاء</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-building"></i>
                </div>
                <div class="stat-content">
                    <h3 id="total-properties">{{ total_properties or 0 }}</h3>
                    <p>إجمالي العقارات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-money-bill"></i>
                </div>
                <div class="stat-content">
                    <h3 id="total-financial">{{ total_financial or 0 }}</h3>
                    <p>المعاملات المالية</p>
                </div>
            </div>
        </div>
    </div>

    <!-- نصائح البحث -->
    <div class="search-tips mb-4">
        <div class="alert alert-info">
            <h5><i class="fas fa-lightbulb me-2"></i>نصائح للبحث الفعال:</h5>
            <ul class="mb-0">
                <li><strong>البحث السريع:</strong> استخدم Ctrl+K للوصول السريع لشريط البحث</li>
                <li><strong>الفلاتر المتقدمة:</strong> استخدم Ctrl+Shift+F لفتح الفلاتر المتقدمة</li>
                <li><strong>البحث الذكي:</strong> يمكنك البحث بأرقام القضايا، أسماء العملاء، أو أي نص</li>
                <li><strong>التصدير:</strong> يمكنك تصدير نتائج البحث إلى Excel أو PDF</li>
            </ul>
        </div>
    </div>

    <!-- البحث السريع -->
    <div class="quick-search-section mb-4">
        <h4><i class="fas fa-bolt me-2"></i>البحث السريع</h4>
        <div class="row">
            <div class="col-md-3">
                <button class="btn btn-outline-primary w-100 mb-2" onclick="advancedSearch.quickSearch('cases', 'active')">
                    <i class="fas fa-gavel me-1"></i>القضايا النشطة
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-outline-success w-100 mb-2" onclick="advancedSearch.quickSearch('clients', 'recent')">
                    <i class="fas fa-user-plus me-1"></i>العملاء الجدد
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-outline-info w-100 mb-2" onclick="advancedSearch.quickSearch('properties', 'available')">
                    <i class="fas fa-building me-1"></i>العقارات المتاحة
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-outline-warning w-100 mb-2" onclick="advancedSearch.quickSearch('financial', 'pending')">
                    <i class="fas fa-clock me-1"></i>المعاملات المعلقة
                </button>
            </div>
        </div>
    </div>

    <!-- البحث المتقدم سيتم إدراجه هنا بواسطة JavaScript -->
    <!-- سيتم إنشاء واجهة البحث المتقدم تلقائياً -->

</div>

<!-- مودال البحث المحفوظ -->
<div class="modal fade" id="savedSearchesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-bookmark me-2"></i>البحث المحفوظ
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="saved-searches-list">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال تفاصيل النتيجة -->
<div class="modal fade" id="resultDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>تفاصيل النتيجة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="result-details-content">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="advancedSearch.openResultInNewTab()">
                    <i class="fas fa-external-link-alt me-1"></i>فتح في صفحة جديدة
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/advanced-search.js') }}"></script>
<script>
// إضافة وظائف إضافية للبحث السريع
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الإحصائيات
    updateSearchStats();
    
    // إضافة وظائف البحث السريع
    window.advancedSearch.quickSearch = function(section, filter) {
        document.getElementById('global-search-input').value = '';
        document.getElementById('search-type').value = section;
        
        // تطبيق الفلتر المناسب
        switch(filter) {
            case 'active':
                document.getElementById('status-filter').value = 'active';
                break;
            case 'recent':
                document.getElementById('date-range').value = 'week';
                break;
            case 'available':
                document.getElementById('status-filter').value = 'available';
                break;
            case 'pending':
                document.getElementById('status-filter').value = 'pending';
                break;
        }
        
        this.performGlobalSearch();
    };
    
    // إضافة وظيفة البحث المحفوظ
    window.advancedSearch.showSavedSearches = function() {
        const modal = new bootstrap.Modal(document.getElementById('savedSearchesModal'));
        
        // تحميل البحث المحفوظ
        const savedSearches = JSON.parse(localStorage.getItem('savedSearches') || '[]');
        const listContainer = document.getElementById('saved-searches-list');
        
        if (savedSearches.length === 0) {
            listContainer.innerHTML = '<p class="text-muted text-center">لا يوجد بحث محفوظ</p>';
        } else {
            let html = '<div class="list-group">';
            savedSearches.forEach((search, index) => {
                html += `
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${search.name}</h6>
                                <p class="mb-1 text-muted">${search.term}</p>
                                <small class="text-muted">${new Date(search.timestamp).toLocaleString('ar-SA')}</small>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="advancedSearch.loadSavedSearch(${index})">
                                    <i class="fas fa-play"></i> تشغيل
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="advancedSearch.deleteSavedSearch(${index})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            listContainer.innerHTML = html;
        }
        
        modal.show();
    };
    
    // تحميل البحث المحفوظ
    window.advancedSearch.loadSavedSearch = function(index) {
        const savedSearches = JSON.parse(localStorage.getItem('savedSearches') || '[]');
        const search = savedSearches[index];
        
        if (search) {
            document.getElementById('global-search-input').value = search.term;
            
            // تطبيق الفلاتر
            Object.keys(search.filters).forEach(key => {
                const element = document.getElementById(key.replace(/([A-Z])/g, '-$1').toLowerCase());
                if (element) {
                    element.value = search.filters[key] || '';
                }
            });
            
            bootstrap.Modal.getInstance(document.getElementById('savedSearchesModal')).hide();
            this.performGlobalSearch();
        }
    };
    
    // حذف البحث المحفوظ
    window.advancedSearch.deleteSavedSearch = function(index) {
        if (confirm('هل أنت متأكد من حذف هذا البحث المحفوظ؟')) {
            const savedSearches = JSON.parse(localStorage.getItem('savedSearches') || '[]');
            savedSearches.splice(index, 1);
            localStorage.setItem('savedSearches', JSON.stringify(savedSearches));
            this.showSavedSearches(); // إعادة تحديث القائمة
        }
    };
});

// تحديث الإحصائيات
async function updateSearchStats() {
    try {
        const response = await fetch('/api/search/stats');
        const stats = await response.json();
        
        if (stats.success) {
            document.getElementById('total-cases').textContent = stats.cases || 0;
            document.getElementById('total-clients').textContent = stats.clients || 0;
            document.getElementById('total-properties').textContent = stats.properties || 0;
            document.getElementById('total-financial').textContent = stats.financial || 0;
        }
    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);
    }
}
</script>
{% endblock %}
