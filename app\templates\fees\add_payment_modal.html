<!-- نموذج إضافة دفعة للأتعاب -->
<form id="addPaymentForm" method="POST" action="{{ url_for('modal_add_fee_payment', fee_id=fee.id) }}">
    <div class="modal-body" dir="rtl">
        <!-- معلومات الأتعاب -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="fa fa-info-circle"></i> معلومات الأتعاب</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-2">
                            <strong>رقم الأتعاب:</strong> {{ fee.fee_number }}
                        </div>
                        <div class="mb-2">
                            <strong>العميل:</strong> {{ fee.client.name }}
                        </div>
                        <div class="mb-2">
                            <strong>نوع الأتعاب:</strong> {{ fee.fee_type }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-2">
                            <strong>المبلغ الإجمالي:</strong> 
                            <span class="text-primary fw-bold">{{ fee.total_amount | number_format }} {{ fee.currency }}</span>
                        </div>
                        <div class="mb-2">
                            <strong>المبلغ المدفوع:</strong> 
                            <span class="text-success">{{ fee.paid_amount | number_format }} {{ fee.currency }}</span>
                        </div>
                        <div class="mb-2">
                            <strong>المبلغ المتبقي:</strong> 
                            <span class="text-danger fw-bold">{{ fee.remaining_amount | number_format }} {{ fee.currency }}</span>
                        </div>
                    </div>
                </div>

                <!-- شريط التقدم -->
                <div class="mt-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">نسبة السداد</span>
                        <span class="small">{{ ((fee.paid_amount / fee.total_amount) * 100) | round(1) }}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar 
                                    {% if fee.payment_status == 'مدفوع كاملاً' %}bg-success
                                    {% elif fee.payment_status == 'مدفوع جزئياً' %}bg-warning
                                    {% else %}bg-danger
                                    {% endif %}" 
                             style="width: {{ ((fee.paid_amount / fee.total_amount) * 100) | round(1) }}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الدفعة الجديدة -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0"><i class="fa fa-money-bill-wave"></i> معلومات الدفعة الجديدة</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">مبلغ الدفعة <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" name="amount" class="form-control" 
                                       step="0.01" min="0.01" max="{{ fee.remaining_amount }}" 
                                       required id="paymentAmount">
                                <span class="input-group-text">{{ fee.currency }}</span>
                            </div>
                            <div class="form-text">
                                الحد الأقصى: {{ fee.remaining_amount | number_format }} {{ fee.currency }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">تاريخ الدفع <span class="text-danger">*</span></label>
                            <input type="date" name="payment_date" class="form-control" 
                                   value="{{ today }}" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                            <select name="payment_method" class="form-select" required id="paymentMethod">
                                <option value="">اختر طريقة الدفع</option>
                                <option value="نقداً">نقداً</option>
                                <option value="شيك">شيك</option>
                                <option value="تحويل بنكي">تحويل بنكي</option>
                                <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                <option value="محفظة إلكترونية">محفظة إلكترونية</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">رقم المرجع</label>
                            <input type="text" name="reference" class="form-control" 
                                   placeholder="رقم الشيك، التحويل، أو المرجع" id="paymentReference">
                            <div class="form-text" id="referenceHelp"></div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">ملاحظات الدفعة</label>
                    <textarea name="notes" class="form-control" rows="3" 
                              placeholder="أي ملاحظات حول هذه الدفعة..."></textarea>
                </div>

                <!-- معاينة النتيجة -->
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">معاينة بعد الدفع:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="text-muted small">المبلغ المدفوع الجديد</div>
                                    <div class="fw-bold text-success" id="newPaidAmount">
                                        {{ fee.paid_amount | number_format }} {{ fee.currency }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="text-muted small">المبلغ المتبقي الجديد</div>
                                    <div class="fw-bold text-danger" id="newRemainingAmount">
                                        {{ fee.remaining_amount | number_format }} {{ fee.currency }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="text-muted small">الحالة الجديدة</div>
                                    <div class="fw-bold" id="newStatus">
                                        <span class="badge bg-{{ 'danger' if fee.payment_status == 'غير مدفوع' else ('warning' if fee.payment_status == 'مدفوع جزئياً' else 'success') }}">
                                            {{ fee.payment_status }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="progress">
                                <div class="progress-bar bg-success" id="newProgressBar"
                                     style="width: {{ ((fee.paid_amount / fee.total_amount) * 100) | round(1) }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار سريعة للمبالغ -->
        <div class="card mt-3">
            <div class="card-body">
                <h6 class="card-title">مبالغ سريعة:</h6>
                <div class="btn-group flex-wrap" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm quick-amount" 
                            data-amount="{{ fee.remaining_amount }}">
                        المبلغ كاملاً ({{ fee.remaining_amount | number_format }})
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm quick-amount" 
                            data-amount="{{ (fee.remaining_amount / 2) | round(2) }}">
                        نصف المبلغ ({{ (fee.remaining_amount / 2) | round(2) | number_format }})
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm quick-amount" 
                            data-amount="{{ (fee.remaining_amount / 4) | round(2) }}">
                        ربع المبلغ ({{ (fee.remaining_amount / 4) | round(2) | number_format }})
                    </button>
                    {% if fee.remaining_amount >= 1000 %}
                    <button type="button" class="btn btn-outline-primary btn-sm quick-amount" 
                            data-amount="1000">
                        1,000 {{ fee.currency }}
                    </button>
                    {% endif %}
                    {% if fee.remaining_amount >= 500 %}
                    <button type="button" class="btn btn-outline-primary btn-sm quick-amount" 
                            data-amount="500">
                        500 {{ fee.currency }}
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="fa fa-times"></i> إلغاء
        </button>
        <button type="submit" class="btn btn-success">
            <i class="fa fa-save"></i> تسجيل الدفعة
        </button>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentAmountInput = document.getElementById('paymentAmount');
    const paymentMethodSelect = document.getElementById('paymentMethod');
    const referenceInput = document.getElementById('paymentReference');
    const referenceHelp = document.getElementById('referenceHelp');
    
    const totalAmount = {{ fee.total_amount }};
    const currentPaid = {{ fee.paid_amount }};
    const remaining = {{ fee.remaining_amount }};

    // تحديث المعاينة عند تغيير المبلغ
    function updatePreview() {
        const paymentAmount = parseFloat(paymentAmountInput.value) || 0;
        const newPaidAmount = currentPaid + paymentAmount;
        const newRemainingAmount = totalAmount - newPaidAmount;
        const newPercentage = (newPaidAmount / totalAmount) * 100;

        // تحديث المبالغ
        document.getElementById('newPaidAmount').textContent = 
            newPaidAmount.toLocaleString('ar-SA', {minimumFractionDigits: 2}) + ' {{ fee.currency }}';
        document.getElementById('newRemainingAmount').textContent = 
            newRemainingAmount.toLocaleString('ar-SA', {minimumFractionDigits: 2}) + ' {{ fee.currency }}';

        // تحديث الحالة
        let newStatus, badgeClass;
        if (newRemainingAmount <= 0) {
            newStatus = 'مدفوع كاملاً';
            badgeClass = 'bg-success';
        } else if (newPaidAmount > 0) {
            newStatus = 'مدفوع جزئياً';
            badgeClass = 'bg-warning';
        } else {
            newStatus = 'غير مدفوع';
            badgeClass = 'bg-danger';
        }

        document.getElementById('newStatus').innerHTML = 
            `<span class="badge ${badgeClass}">${newStatus}</span>`;

        // تحديث شريط التقدم
        document.getElementById('newProgressBar').style.width = `${Math.min(newPercentage, 100)}%`;
    }

    // تحديث نص المساعدة لرقم المرجع
    function updateReferenceHelp() {
        const method = paymentMethodSelect.value;
        let helpText = '';
        
        switch(method) {
            case 'شيك':
                helpText = 'أدخل رقم الشيك';
                referenceInput.placeholder = 'رقم الشيك';
                break;
            case 'تحويل بنكي':
                helpText = 'أدخل رقم التحويل أو المرجع البنكي';
                referenceInput.placeholder = 'رقم التحويل';
                break;
            case 'بطاقة ائتمان':
                helpText = 'أدخل آخر 4 أرقام من البطاقة';
                referenceInput.placeholder = 'آخر 4 أرقام';
                break;
            case 'محفظة إلكترونية':
                helpText = 'أدخل رقم المعاملة';
                referenceInput.placeholder = 'رقم المعاملة';
                break;
            default:
                helpText = 'رقم مرجعي اختياري';
                referenceInput.placeholder = 'رقم المرجع';
        }
        
        referenceHelp.textContent = helpText;
    }

    // ربط الأحداث
    paymentAmountInput.addEventListener('input', updatePreview);
    paymentMethodSelect.addEventListener('change', updateReferenceHelp);

    // أزرار المبالغ السريعة
    document.querySelectorAll('.quick-amount').forEach(button => {
        button.addEventListener('click', function() {
            const amount = this.getAttribute('data-amount');
            paymentAmountInput.value = amount;
            updatePreview();
        });
    });

    // التحقق من صحة المبلغ
    paymentAmountInput.addEventListener('blur', function() {
        const amount = parseFloat(this.value);
        if (amount > remaining) {
            alert(`المبلغ المدخل أكبر من المبلغ المتبقي (${remaining.toLocaleString('ar-SA', {minimumFractionDigits: 2})} {{ fee.currency }})`);
            this.value = remaining;
            updatePreview();
        }
    });

    // تحديث أولي
    updatePreview();
    updateReferenceHelp();
});
</script>
