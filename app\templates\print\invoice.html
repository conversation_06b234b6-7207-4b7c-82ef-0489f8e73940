{% extends "print/base_print.html" %}

{% block title %}فاتورة رقم {{ invoice.number }}{% endblock %}

{% block content %}
<div class="document-title">
    فاتورة {{ invoice.type }}
</div>

<div class="document-info">
    <div class="row">
        <div class="col-md-6">
            <span class="label">رقم الفاتورة:</span>
            <span class="value">{{ invoice.number }}</span>
        </div>
        <div class="col-md-6">
            <span class="label">تاريخ الإصدار:</span>
            <span class="value">{{ invoice.issue_date }}</span>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <span class="label">تاريخ الاستحقاق:</span>
            <span class="value">{{ invoice.due_date }}</span>
        </div>
        <div class="col-md-6">
            <span class="label">حالة الفاتورة:</span>
            <span class="value">
                <span class="badge {% if invoice.status == 'مدفوعة' %}bg-success{% elif invoice.status == 'متأخرة' %}bg-danger{% else %}bg-warning{% endif %}">
                    {{ invoice.status }}
                </span>
            </span>
        </div>
    </div>
</div>

<!-- معلومات العميل -->
<div class="content-section">
    <h3><i class="fa fa-user"></i> معلومات العميل</h3>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5>بيانات العميل</h5>
                </div>
                <div class="card-body">
                    <p><strong>الاسم:</strong> {{ client.name }}</p>
                    <p><strong>رقم الهوية:</strong> {{ client.id_number }}</p>
                    <p><strong>العنوان:</strong> {{ client.address }}</p>
                    <p><strong>الهاتف:</strong> {{ client.phone }}</p>
                    {% if client.email %}
                    <p><strong>البريد الإلكتروني:</strong> {{ client.email }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5>بيانات الفوترة</h5>
                </div>
                <div class="card-body">
                    <p><strong>طريقة الدفع:</strong> {{ invoice.payment_method }}</p>
                    <p><strong>شروط الدفع:</strong> {{ invoice.payment_terms }}</p>
                    <p><strong>العملة:</strong> {{ invoice.currency }}</p>
                    {% if invoice.reference_number %}
                    <p><strong>رقم المرجع:</strong> {{ invoice.reference_number }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تفاصيل الخدمات -->
<div class="content-section">
    <h3><i class="fa fa-list"></i> تفاصيل الخدمات</h3>
    
    <table class="table table-bordered">
        <thead>
            <tr>
                <th style="width: 5%;">#</th>
                <th style="width: 40%;">الوصف</th>
                <th style="width: 10%;">الكمية</th>
                <th style="width: 15%;">السعر الوحدة</th>
                <th style="width: 15%;">الخصم</th>
                <th style="width: 15%;">المجموع</th>
            </tr>
        </thead>
        <tbody>
            {% for item in invoice.items %}
            <tr>
                <td>{{ loop.index }}</td>
                <td>
                    <strong>{{ item.description }}</strong>
                    {% if item.details %}
                    <br><small class="text-muted">{{ item.details }}</small>
                    {% endif %}
                </td>
                <td class="text-center">{{ item.quantity }}</td>
                <td class="amount">{{ item.unit_price | number_format }}</td>
                <td class="amount">{{ item.discount | number_format }}</td>
                <td class="amount">{{ item.total | number_format }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- ملخص الفاتورة -->
<div class="content-section">
    <div class="row">
        <div class="col-md-6">
            <!-- ملاحظات -->
            {% if invoice.notes %}
            <div class="card">
                <div class="card-header">
                    <h6><i class="fa fa-sticky-note"></i> ملاحظات</h6>
                </div>
                <div class="card-body">
                    {{ invoice.notes | nl2br }}
                </div>
            </div>
            {% endif %}
            
            <!-- شروط وأحكام -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fa fa-info-circle"></i> شروط وأحكام</h6>
                </div>
                <div class="card-body">
                    <small>
                        <ul>
                            <li>يجب سداد الفاتورة خلال {{ invoice.payment_days | default(30) }} يوماً من تاريخ الإصدار</li>
                            <li>في حالة التأخير في السداد، سيتم احتساب غرامة تأخير {{ invoice.late_fee_rate | default(2) }}% شهرياً</li>
                            <li>جميع المبالغ بالشيكل الإسرائيلي ما لم يُذكر خلاف ذلك</li>
                            <li>هذه الفاتورة صالحة لمدة {{ invoice.validity_days | default(90) }} يوماً من تاريخ الإصدار</li>
                        </ul>
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="financial-summary">
                <h4><i class="fa fa-calculator"></i> ملخص الفاتورة</h4>
                
                <table class="table table-borderless">
                    <tr>
                        <td><strong>المجموع الفرعي:</strong></td>
                        <td class="amount text-end"><strong>{{ invoice.subtotal | number_format }} {{ invoice.currency }}</strong></td>
                    </tr>
                    {% if invoice.discount_amount > 0 %}
                    <tr>
                        <td>الخصم ({{ invoice.discount_percentage }}%):</td>
                        <td class="amount text-end text-success">-{{ invoice.discount_amount | number_format }} {{ invoice.currency }}</td>
                    </tr>
                    {% endif %}
                    {% if invoice.tax_amount > 0 %}
                    <tr>
                        <td>ضريبة القيمة المضافة ({{ invoice.tax_rate }}%):</td>
                        <td class="amount text-end">{{ invoice.tax_amount | number_format }} {{ invoice.currency }}</td>
                    </tr>
                    {% endif %}
                    {% if invoice.additional_fees > 0 %}
                    <tr>
                        <td>رسوم إضافية:</td>
                        <td class="amount text-end">{{ invoice.additional_fees | number_format }} {{ invoice.currency }}</td>
                    </tr>
                    {% endif %}
                    <tr class="table-primary">
                        <td><strong>المجموع الإجمالي:</strong></td>
                        <td class="amount text-end"><strong>{{ invoice.total_amount | number_format }} {{ invoice.currency }}</strong></td>
                    </tr>
                    {% if invoice.paid_amount > 0 %}
                    <tr>
                        <td>المبلغ المدفوع:</td>
                        <td class="amount text-end text-success">{{ invoice.paid_amount | number_format }} {{ invoice.currency }}</td>
                    </tr>
                    <tr class="table-warning">
                        <td><strong>المبلغ المتبقي:</strong></td>
                        <td class="amount text-end"><strong>{{ invoice.remaining_amount | number_format }} {{ invoice.currency }}</strong></td>
                    </tr>
                    {% endif %}
                </table>
                
                <!-- المبلغ بالكلمات -->
                <div class="alert alert-light mt-3">
                    <small><strong>المبلغ بالكلمات:</strong></small><br>
                    <em>{{ invoice.amount_in_words }}</em>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- سجل المدفوعات -->
{% if invoice.payments %}
<div class="content-section">
    <h3><i class="fa fa-money-bill"></i> سجل المدفوعات</h3>
    
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>التاريخ</th>
                <th>المبلغ</th>
                <th>طريقة الدفع</th>
                <th>رقم المرجع</th>
                <th>ملاحظات</th>
            </tr>
        </thead>
        <tbody>
            {% for payment in invoice.payments %}
            <tr>
                <td>{{ payment.date }}</td>
                <td class="amount">{{ payment.amount | number_format }} {{ invoice.currency }}</td>
                <td>{{ payment.method }}</td>
                <td>{{ payment.reference or '-' }}</td>
                <td>{{ payment.notes or '-' }}</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="table-info">
                <td><strong>المجموع المدفوع</strong></td>
                <td class="amount"><strong>{{ invoice.paid_amount | number_format }} {{ invoice.currency }}</strong></td>
                <td colspan="3"></td>
            </tr>
        </tfoot>
    </table>
</div>
{% endif %}

<!-- رمز QR للدفع الإلكتروني -->
{% if invoice.qr_code %}
<div class="content-section text-center">
    <h5><i class="fa fa-qrcode"></i> الدفع الإلكتروني</h5>
    <p>امسح الرمز أدناه للدفع إلكترونياً</p>
    <img src="{{ invoice.qr_code }}" alt="QR Code للدفع" style="width: 150px; height: 150px;">
</div>
{% endif %}

<!-- معلومات الحساب البنكي -->
<div class="content-section">
    <h3><i class="fa fa-university"></i> معلومات الحساب البنكي</h3>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h6>البنك الأول</h6>
                    <p><strong>اسم البنك:</strong> بنك فلسطين</p>
                    <p><strong>رقم الحساب:</strong> 123456789</p>
                    <p><strong>IBAN:</strong> ***********************</p>
                    <p><strong>اسم الحساب:</strong> مكتب المحامي سامح</p>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h6>البنك الثاني</h6>
                    <p><strong>اسم البنك:</strong> البنك الإسلامي الفلسطيني</p>
                    <p><strong>رقم الحساب:</strong> 987654321</p>
                    <p><strong>IBAN:</strong> ***********************</p>
                    <p><strong>اسم الحساب:</strong> مكتب المحامي سامح</p>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block signatures %}
<div class="signature-section">
    <div class="signature-box">
        <div>المحاسب</div>
        <div style="margin-top: 40px;">________________</div>
    </div>
    <div class="signature-box">
        <div>المحامي</div>
        <div style="margin-top: 40px;">سامح أبو عودة</div>
    </div>
    <div class="signature-box">
        <div>ختم المكتب</div>
        <div style="margin-top: 40px; border: 2px dashed #ccc; height: 60px; border-radius: 50%;"></div>
    </div>
</div>
{% endblock %}
