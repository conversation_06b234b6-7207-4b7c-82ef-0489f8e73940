<!-- عرض تفاصيل الأتعاب -->
<div class="modal-body" dir="rtl">
    <!-- معلومات الأتعاب الأساسية -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fa fa-info-circle"></i> معلومات الأتعاب</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-4"><strong>رقم الأتعاب:</strong></div>
                        <div class="col-8">{{ fee.fee_number }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>نوع الأتعاب:</strong></div>
                        <div class="col-8">{{ fee.fee_type }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>فئة الخدمة:</strong></div>
                        <div class="col-8">{{ fee.service_category }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>تاريخ الخدمة:</strong></div>
                        <div class="col-8">{{ fee.service_date.strftime('%Y-%m-%d') }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>تاريخ الاستحقاق:</strong></div>
                        <div class="col-8 {% if fee.is_overdue() %}text-danger{% endif %}">
                            {{ fee.due_date.strftime('%Y-%m-%d') }}
                            {% if fee.is_overdue() %}
                                <span class="badge bg-danger ms-2">متأخر {{ fee.days_overdue() }} يوم</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>الأولوية:</strong></div>
                        <div class="col-8">
                            <span class="badge 
                                        {% if fee.priority == 'عاجلة' %}bg-danger
                                        {% elif fee.priority == 'عالية' %}bg-warning
                                        {% elif fee.priority == 'متوسطة' %}bg-info
                                        {% else %}bg-secondary
                                        {% endif %}">
                                {{ fee.priority }}
                            </span>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>شروط الدفع:</strong></div>
                        <div class="col-8">{{ fee.payment_terms }}</div>
                    </div>
                    {% if fee.is_recurring %}
                    <div class="row mb-2">
                        <div class="col-4"><strong>أتعاب دورية:</strong></div>
                        <div class="col-8">
                            <span class="badge bg-info">{{ fee.recurring_period }}</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0"><i class="fa fa-user"></i> معلومات العميل والقضية</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-4"><strong>العميل:</strong></div>
                        <div class="col-8">{{ fee.client.name }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>هاتف العميل:</strong></div>
                        <div class="col-8">{{ fee.client.phone or 'غير محدد' }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>بريد العميل:</strong></div>
                        <div class="col-8">{{ fee.client.email or 'غير محدد' }}</div>
                    </div>
                    {% if fee.case %}
                    <div class="row mb-2">
                        <div class="col-4"><strong>رقم القضية:</strong></div>
                        <div class="col-8">{{ fee.case.case_number }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>نوع القضية:</strong></div>
                        <div class="col-8">{{ fee.case.case_type }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>حالة القضية:</strong></div>
                        <div class="col-8">{{ fee.case.status }}</div>
                    </div>
                    {% else %}
                    <div class="row mb-2">
                        <div class="col-4"><strong>القضية:</strong></div>
                        <div class="col-8 text-muted">غير مرتبطة بقضية</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- وصف الخدمة -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h6 class="mb-0"><i class="fa fa-file-text"></i> وصف الخدمة</h6>
        </div>
        <div class="card-body">
            <p class="mb-0">{{ fee.description }}</p>
            {% if fee.notes %}
            <hr>
            <h6>ملاحظات:</h6>
            <p class="mb-0 text-muted">{{ fee.notes }}</p>
            {% endif %}
        </div>
    </div>

    <!-- المعلومات المالية -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h6 class="mb-0"><i class="fa fa-money-bill-wave"></i> المعلومات المالية</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>المبلغ الأساسي:</strong></td>
                            <td class="text-end">{{ fee.base_amount | number_format }} {{ fee.currency }}</td>
                        </tr>
                        <tr>
                            <td><strong>رسوم إضافية:</strong></td>
                            <td class="text-end">{{ fee.additional_fees | number_format }} {{ fee.currency }}</td>
                        </tr>
                        <tr>
                            <td><strong>خصم:</strong></td>
                            <td class="text-end text-success">-{{ fee.discount_amount | number_format }} {{ fee.currency }}</td>
                        </tr>
                        <tr>
                            <td><strong>ضريبة:</strong></td>
                            <td class="text-end">{{ fee.tax_amount | number_format }} {{ fee.currency }}</td>
                        </tr>
                        <tr class="table-primary">
                            <td><strong>المجموع الإجمالي:</strong></td>
                            <td class="text-end"><strong>{{ fee.total_amount | number_format }} {{ fee.currency }}</strong></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>المبلغ المدفوع:</strong></td>
                            <td class="text-end text-success">{{ fee.paid_amount | number_format }} {{ fee.currency }}</td>
                        </tr>
                        <tr>
                            <td><strong>المبلغ المتبقي:</strong></td>
                            <td class="text-end text-danger">{{ fee.remaining_amount | number_format }} {{ fee.currency }}</td>
                        </tr>
                        <tr>
                            <td><strong>حالة الدفع:</strong></td>
                            <td class="text-end">
                                <span class="badge 
                                            {% if fee.payment_status == 'غير مدفوع' %}bg-danger
                                            {% elif fee.payment_status == 'مدفوع جزئياً' %}bg-warning
                                            {% else %}bg-success
                                            {% endif %}">
                                    {{ fee.payment_status }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>نسبة السداد:</strong></td>
                            <td class="text-end">{{ ((fee.paid_amount / fee.total_amount) * 100) | round(1) }}%</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- شريط التقدم -->
            <div class="mt-3">
                <div class="d-flex justify-content-between mb-1">
                    <span class="small">تقدم السداد</span>
                    <span class="small">{{ ((fee.paid_amount / fee.total_amount) * 100) | round(1) }}%</span>
                </div>
                <div class="progress">
                    <div class="progress-bar 
                                {% if fee.payment_status == 'مدفوع كاملاً' %}bg-success
                                {% elif fee.payment_status == 'مدفوع جزئياً' %}bg-warning
                                {% else %}bg-danger
                                {% endif %}" 
                         style="width: {{ ((fee.paid_amount / fee.total_amount) * 100) | round(1) }}%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- سجل الدفعات -->
    <div class="card">
        <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
            <h6 class="mb-0"><i class="fa fa-history"></i> سجل الدفعات ({{ payments|length }})</h6>
            {% if fee.remaining_amount > 0 %}
            <button type="button" class="btn btn-success btn-sm"
                    onclick="openFormModal('{{ url_for('modal_add_fee_payment', fee_id=fee.id) }}', 'إضافة دفعة', {
                        onSuccess: function() { window.location.reload(); }
                    })">
                <i class="fa fa-plus"></i> إضافة دفعة
            </button>
            {% endif %}
        </div>
        <div class="card-body">
            {% if payments %}
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>رقم الإيصال</th>
                            <th>تاريخ الدفع</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>رقم المرجع</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                        <tr>
                            <td>{{ payment.receipt_number }}</td>
                            <td>{{ payment.payment_date.strftime('%Y-%m-%d') }}</td>
                            <td class="text-success fw-bold">{{ payment.amount | number_format }} {{ fee.currency }}</td>
                            <td>{{ payment.payment_method }}</td>
                            <td>{{ payment.reference or '-' }}</td>
                            <td>{{ payment.notes or '-' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="table-success">
                            <th colspan="2">إجمالي المدفوع:</th>
                            <th class="text-success">{{ fee.paid_amount | number_format }} {{ fee.currency }}</th>
                            <th colspan="3"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fa fa-money-bill-wave fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">لا توجد دفعات مسجلة</h6>
                <p class="text-muted">لم يتم تسجيل أي دفعات لهذه الأتعاب بعد</p>
                {% if fee.remaining_amount > 0 %}
                <button type="button" class="btn btn-success"
                        onclick="openFormModal('{{ url_for('modal_add_fee_payment', fee_id=fee.id) }}', 'إضافة دفعة', {
                            onSuccess: function() { window.location.reload(); }
                        })">
                    <i class="fa fa-plus"></i> تسجيل أول دفعة
                </button>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- معلومات النظام -->
    <div class="card mt-4">
        <div class="card-header bg-light">
            <h6 class="mb-0 text-muted"><i class="fa fa-cog"></i> معلومات النظام</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">تاريخ الإنشاء: {{ fee.created_date.strftime('%Y-%m-%d %H:%M') }}</small>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">آخر تحديث: {{ fee.updated_date.strftime('%Y-%m-%d %H:%M') }}</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
        <i class="fa fa-times"></i> إغلاق
    </button>
    <button type="button" class="btn btn-primary"
            onclick="openFormModal('{{ url_for('modal_edit_fee', fee_id=fee.id) }}', 'تعديل الأتعاب', {
                size: 'xl',
                onSuccess: function() { window.location.reload(); }
            })">
        <i class="fa fa-edit"></i> تعديل
    </button>
    {% if fee.remaining_amount > 0 %}
    <button type="button" class="btn btn-success"
            onclick="openFormModal('{{ url_for('modal_add_fee_payment', fee_id=fee.id) }}', 'إضافة دفعة', {
                onSuccess: function() { window.location.reload(); }
            })">
        <i class="fa fa-plus"></i> إضافة دفعة
    </button>
    {% endif %}
    <a href="{{ url_for('print_invoice', case_id=fee.case.id) if fee.case else '#' }}" 
       class="btn btn-info" target="_blank" 
       {% if not fee.case %}disabled{% endif %}>
        <i class="fa fa-print"></i> طباعة فاتورة
    </a>
</div>
