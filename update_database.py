#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db
from app.models import Case
import sqlite3

def update_database():
    with app.app_context():
        try:
            # إضافة الحقول الجديدة مباشرة إلى قاعدة البيانات
            conn = sqlite3.connect('instance/lawoffice.db')
            cursor = conn.cursor()
            
            # إضافة حقل صفة الموكل
            try:
                cursor.execute('ALTER TABLE cases ADD COLUMN client_role VARCHAR(50)')
                print('✅ تم إضافة حقل client_role')
            except sqlite3.OperationalError as e:
                if 'duplicate column name' in str(e):
                    print('⚠️ حقل client_role موجود مسبقاً')
                else:
                    print(f'❌ خطأ في إضافة client_role: {e}')
            
            # إضافة حقل عملة الأتعاب
            try:
                cursor.execute('ALTER TABLE cases ADD COLUMN fees_currency VARCHAR(20)')
                print('✅ تم إضافة حقل fees_currency')
            except sqlite3.OperationalError as e:
                if 'duplicate column name' in str(e):
                    print('⚠️ حقل fees_currency موجود مسبقاً')
                else:
                    print(f'❌ خطأ في إضافة fees_currency: {e}')
            
            # تحديث القيم الافتراضية
            cursor.execute('UPDATE cases SET client_role = ? WHERE client_role IS NULL OR client_role = ""', ('مدعي',))
            cursor.execute('UPDATE cases SET fees_currency = ? WHERE fees_currency IS NULL OR fees_currency = ""', ('شيكل',))
            
            conn.commit()
            conn.close()
            print('✅ تم تحديث قاعدة البيانات بنجاح')
            
        except Exception as e:
            print(f'❌ خطأ: {e}')

if __name__ == '__main__':
    update_database()
