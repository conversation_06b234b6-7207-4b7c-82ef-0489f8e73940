#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للوظائف
"""

import requests
import re

BASE_URL = "http://localhost:5000"

def test_pages():
    """اختبار الوصول للصفحات"""
    session = requests.Session()
    
    # تسجيل الدخول
    print("🔐 تسجيل الدخول...")
    login_page = session.get(f"{BASE_URL}/lawyersameh")
    csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', login_page.text)
    
    if not csrf_match:
        print("❌ لم يتم العثور على CSRF token")
        return
    
    login_data = {
        'username': 'office',
        'password': '66889088',
        'csrf_token': csrf_match.group(1)
    }
    
    response = session.post(f"{BASE_URL}/lawyersameh", data=login_data)
    if "dashboard" not in response.url:
        print("❌ فشل في تسجيل الدخول")
        return
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # اختبار الصفحات
    pages = [
        ('/clients', 'قائمة العملاء'),
        ('/clients/add', 'إضافة عميل'),
        ('/cases', 'قائمة القضايا'),
        ('/cases/add', 'إضافة قضية'),
        ('/properties', 'قائمة العقارات'),
        ('/properties/add', 'إضافة عقار'),
        ('/tenants', 'قائمة المستأجرين'),
        ('/tenants/add', 'إضافة مستأجر'),
        ('/appointments', 'قائمة المواعيد'),
        ('/appointments/add', 'إضافة موعد'),
        ('/tasks', 'قائمة المهام'),
        ('/tasks/add', 'إضافة مهمة'),
        ('/finance', 'المعاملات المالية'),
        ('/finance/add', 'إضافة معاملة مالية')
    ]
    
    print("\n📋 اختبار الوصول للصفحات...")
    for url, name in pages:
        response = session.get(f"{BASE_URL}{url}")
        if response.status_code == 200:
            print(f"  ✅ {name}: يعمل")
        else:
            print(f"  ❌ {name}: خطأ {response.status_code}")
    
    # اختبار وجود أزرار التعديل في صفحة العملاء
    print("\n🔍 فحص أزرار التعديل في صفحة العملاء...")
    clients_page = session.get(f"{BASE_URL}/clients")
    if "تعديل" in clients_page.text:
        print("  ✅ أزرار التعديل موجودة")
        
        # البحث عن رابط تعديل
        edit_match = re.search(r'/clients/(\d+)/edit', clients_page.text)
        if edit_match:
            client_id = edit_match.group(1)
            print(f"  🔍 اختبار صفحة تعديل العميل {client_id}...")
            
            edit_page = session.get(f"{BASE_URL}/clients/{client_id}/edit")
            if edit_page.status_code == 200:
                print("    ✅ صفحة التعديل تعمل")
            else:
                print(f"    ❌ صفحة التعديل لا تعمل: {edit_page.status_code}")
        else:
            print("  ❌ لم يتم العثور على رابط تعديل")
    else:
        print("  ❌ أزرار التعديل غير موجودة")
    
    # اختبار وجود أزرار الحذف
    print("\n🗑️ فحص أزرار الحذف...")
    if "حذف" in clients_page.text:
        print("  ✅ أزرار الحذف موجودة في صفحة العملاء")
    else:
        print("  ❌ أزرار الحذف غير موجودة في صفحة العملاء")

if __name__ == "__main__":
    test_pages()
