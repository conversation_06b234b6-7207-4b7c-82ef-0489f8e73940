/**
 * نظام إدارة الثيمات (الوضع المظلم/الفاتح)
 * Theme Manager System (Dark/Light Mode)
 */

class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        // تطبيق الثيم المحفوظ
        this.applyTheme(this.currentTheme);
        
        // إضافة مستمع للأزرار
        this.setupEventListeners();
        
        // تحديث واجهة المستخدم
        this.updateUI();
    }

    setupEventListeners() {
        // البحث عن أزرار تبديل الثيم
        const themeToggleButtons = document.querySelectorAll('[data-theme-toggle]');
        
        themeToggleButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.toggleTheme();
            });
        });

        // مستمع لتغيير النظام
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (this.currentTheme === 'auto') {
                    this.applyTheme('auto');
                }
            });
        }
    }

    toggleTheme() {
        const themes = ['light', 'dark', 'auto'];
        const currentIndex = themes.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themes.length;
        
        this.setTheme(themes[nextIndex]);
    }

    setTheme(theme) {
        this.currentTheme = theme;
        localStorage.setItem('theme', theme);
        this.applyTheme(theme);
        this.updateUI();
        
        // إرسال حدث مخصص
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: theme }
        }));
    }

    applyTheme(theme) {
        const html = document.documentElement;
        
        // إزالة جميع فئات الثيم
        html.classList.remove('theme-light', 'theme-dark', 'theme-auto');
        
        if (theme === 'auto') {
            // استخدام تفضيل النظام
            const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
            html.classList.add(prefersDark ? 'theme-dark' : 'theme-light');
            html.classList.add('theme-auto');
        } else {
            html.classList.add(`theme-${theme}`);
        }

        // تحديث متغيرات CSS
        this.updateCSSVariables(theme);
    }

    updateCSSVariables(theme) {
        const root = document.documentElement;
        
        if (theme === 'dark' || (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            // الوضع المظلم
            root.style.setProperty('--primary-color', '#1a252f');
            root.style.setProperty('--secondary-color', '#2980b9');
            root.style.setProperty('--bg-color', '#2c3e50');
            root.style.setProperty('--text-color', '#ecf0f1');
            root.style.setProperty('--border-color', '#34495e');
            root.style.setProperty('--light-color', '#34495e');
            root.style.setProperty('--dark-color', '#ecf0f1');
            root.style.setProperty('--shadow', '0 2px 4px rgba(0,0,0,0.3)');
        } else {
            // الوضع الفاتح
            root.style.setProperty('--primary-color', '#2c3e50');
            root.style.setProperty('--secondary-color', '#3498db');
            root.style.setProperty('--bg-color', '#ffffff');
            root.style.setProperty('--text-color', '#495057');
            root.style.setProperty('--border-color', '#dee2e6');
            root.style.setProperty('--light-color', '#f8f9fa');
            root.style.setProperty('--dark-color', '#343a40');
            root.style.setProperty('--shadow', '0 2px 4px rgba(0,0,0,0.1)');
        }
    }

    updateUI() {
        // تحديث نص الأزرار
        const themeButtons = document.querySelectorAll('[data-theme-toggle]');
        const themeIcons = document.querySelectorAll('[data-theme-icon]');
        
        themeButtons.forEach(button => {
            const text = this.getThemeText(this.currentTheme);
            if (button.querySelector('.theme-text')) {
                button.querySelector('.theme-text').textContent = text;
            }
        });

        themeIcons.forEach(icon => {
            const iconClass = this.getThemeIcon(this.currentTheme);
            icon.className = `fas ${iconClass}`;
        });

        // تحديث body class
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${this.currentTheme}`);
    }

    getThemeText(theme) {
        const texts = {
            'light': 'الوضع الفاتح',
            'dark': 'الوضع المظلم',
            'auto': 'تلقائي'
        };
        return texts[theme] || 'غير محدد';
    }

    getThemeIcon(theme) {
        const icons = {
            'light': 'fa-sun',
            'dark': 'fa-moon',
            'auto': 'fa-adjust'
        };
        return icons[theme] || 'fa-adjust';
    }

    getCurrentTheme() {
        return this.currentTheme;
    }

    getEffectiveTheme() {
        if (this.currentTheme === 'auto') {
            return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
        return this.currentTheme;
    }

    // دالة لإضافة زر تبديل الثيم
    createThemeToggleButton(container) {
        const button = document.createElement('button');
        button.className = 'btn btn-outline-secondary btn-sm';
        button.setAttribute('data-theme-toggle', '');
        button.innerHTML = `
            <i class="fas fa-adjust" data-theme-icon></i>
            <span class="theme-text ms-1">${this.getThemeText(this.currentTheme)}</span>
        `;
        
        if (container) {
            container.appendChild(button);
        }
        
        this.setupEventListeners();
        return button;
    }

    // دالة لحفظ تفضيلات المستخدم
    saveUserPreference(userId) {
        if (userId) {
            fetch('/api/user/theme', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    theme: this.currentTheme
                })
            }).catch(error => {
                console.warn('فشل في حفظ تفضيل الثيم:', error);
            });
        }
    }

    // دالة لتحميل تفضيلات المستخدم
    async loadUserPreference(userId) {
        if (userId) {
            try {
                const response = await fetch(`/api/user/theme/${userId}`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.theme) {
                        this.setTheme(data.theme);
                    }
                }
            } catch (error) {
                console.warn('فشل في تحميل تفضيل الثيم:', error);
            }
        }
    }
}

// إنشاء مثيل عام
window.themeManager = new ThemeManager();

// تصدير للاستخدام في وحدات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}

// إضافة CSS للثيمات
const themeStyles = `
    .theme-dark {
        background-color: var(--bg-color) !important;
        color: var(--text-color) !important;
    }
    
    .theme-dark .card {
        background-color: var(--light-color) !important;
        color: var(--text-color) !important;
    }
    
    .theme-dark .navbar {
        background-color: var(--light-color) !important;
        border-color: var(--border-color) !important;
    }
    
    .theme-dark .table {
        background-color: var(--bg-color) !important;
        color: var(--text-color) !important;
    }
    
    .theme-dark .table thead th {
        background-color: var(--primary-color) !important;
    }
    
    .theme-dark .form-control {
        background-color: var(--light-color) !important;
        border-color: var(--border-color) !important;
        color: var(--text-color) !important;
    }
    
    .theme-dark .form-control:focus {
        background-color: var(--light-color) !important;
        border-color: var(--secondary-color) !important;
        color: var(--text-color) !important;
    }
    
    .theme-dark .sidebar {
        background: linear-gradient(180deg, #1a252f, #2c3e50) !important;
    }
    
    .theme-dark .modal-content {
        background-color: var(--light-color) !important;
        color: var(--text-color) !important;
    }
    
    .theme-dark .dropdown-menu {
        background-color: var(--light-color) !important;
        border-color: var(--border-color) !important;
    }
    
    .theme-dark .dropdown-item {
        color: var(--text-color) !important;
    }
    
    .theme-dark .dropdown-item:hover {
        background-color: var(--border-color) !important;
    }
`;

// إضافة الأنماط إلى الصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = themeStyles;
document.head.appendChild(styleSheet);

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // البحث عن حاوي لزر الثيم وإضافته
    const themeContainer = document.querySelector('[data-theme-container]');
    if (themeContainer) {
        window.themeManager.createThemeToggleButton(themeContainer);
    }
    
    // تحميل تفضيلات المستخدم إذا كان مسجل الدخول
    const userId = document.body.getAttribute('data-user-id');
    if (userId) {
        window.themeManager.loadUserPreference(userId);
    }
});

console.log('تم تحميل نظام إدارة الثيمات بنجاح');
