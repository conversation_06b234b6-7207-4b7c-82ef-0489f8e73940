<form method="POST" action="{{ url_for('add_expense') }}" class="modal-form" id="addExpenseForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-receipt"></i> معلومات المصروف الأساسية
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">وصف المصروف <span class="text-danger">*</span></label>
                <input type="text" name="description" class="form-control" required 
                       placeholder="وصف المصروف">
            </div>
            <div class="col-md-6">
                <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                <input type="number" name="amount" class="form-control" required 
                       step="0.01" min="0" placeholder="مبلغ المصروف">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">فئة المصروف</label>
                <select name="category" class="form-control">
                    <option value="مكتبية">مصروفات مكتبية</option>
                    <option value="إيجار">إيجار</option>
                    <option value="كهرباء">كهرباء</option>
                    <option value="ماء">ماء</option>
                    <option value="هاتف">هاتف وإنترنت</option>
                    <option value="صيانة">صيانة</option>
                    <option value="وقود">وقود ومواصلات</option>
                    <option value="قرطاسية">قرطاسية</option>
                    <option value="ضرائب">ضرائب ورسوم</option>
                    <option value="تأمين">تأمين</option>
                    <option value="تسويق">تسويق وإعلان</option>
                    <option value="تدريب">تدريب وتطوير</option>
                    <option value="أخرى">أخرى</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">العملة</label>
                <select name="currency" class="form-control">
                    <option value="شيكل">شيكل</option>
                    <option value="دولار">دولار أمريكي</option>
                    <option value="دينار">دينار أردني</option>
                    <option value="يورو">يورو</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-calendar"></i> التواريخ والتكرار
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">تاريخ المصروف</label>
                <input type="date" name="expense_date" class="form-control" 
                       value="{{ today }}">
            </div>
            <div class="col-md-6">
                <label class="form-label">نوع المصروف</label>
                <select name="expense_type" class="form-control">
                    <option value="مرة واحدة">مرة واحدة</option>
                    <option value="شهري">شهري</option>
                    <option value="ربع سنوي">ربع سنوي</option>
                    <option value="نصف سنوي">نصف سنوي</option>
                    <option value="سنوي">سنوي</option>
                </select>
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">تاريخ الاستحقاق التالي</label>
                <input type="date" name="next_due_date" class="form-control">
            </div>
            <div class="col-md-6">
                <label class="form-label">حالة الدفع</label>
                <select name="payment_status" class="form-control">
                    <option value="مدفوع">مدفوع</option>
                    <option value="غير مدفوع">غير مدفوع</option>
                    <option value="مدفوع جزئياً">مدفوع جزئياً</option>
                    <option value="متأخر">متأخر</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-building"></i> معلومات المورد
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">اسم المورد/الجهة</label>
                <input type="text" name="vendor_name" class="form-control" 
                       placeholder="اسم المورد أو الجهة">
            </div>
            <div class="col-md-6">
                <label class="form-label">هاتف المورد</label>
                <input type="tel" name="vendor_phone" class="form-control" 
                       placeholder="رقم هاتف المورد">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-12">
                <label class="form-label">عنوان المورد</label>
                <textarea name="vendor_address" class="form-control" rows="2" 
                          placeholder="عنوان المورد"></textarea>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-credit-card"></i> تفاصيل الدفع
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">طريقة الدفع</label>
                <select name="payment_method" class="form-control">
                    <option value="نقداً">نقداً</option>
                    <option value="شيك">شيك</option>
                    <option value="تحويل بنكي">تحويل بنكي</option>
                    <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                    <option value="بطاقة خصم">بطاقة خصم</option>
                    <option value="أخرى">أخرى</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">رقم المرجع/الفاتورة</label>
                <input type="text" name="reference_number" class="form-control" 
                       placeholder="رقم الفاتورة أو المرجع">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">المبلغ المدفوع</label>
                <input type="number" name="paid_amount" class="form-control" 
                       step="0.01" min="0" value="0" placeholder="المبلغ المدفوع">
            </div>
            <div class="col-md-6">
                <label class="form-label">أولوية الدفع</label>
                <select name="priority" class="form-control">
                    <option value="عادية">عادية</option>
                    <option value="متوسطة">متوسطة</option>
                    <option value="عالية">عالية</option>
                    <option value="عاجلة">عاجلة</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-tags"></i> التصنيف والضرائب
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">قابل للخصم ضريبياً؟</label>
                <select name="tax_deductible" class="form-control">
                    <option value="نعم">نعم</option>
                    <option value="لا">لا</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">نسبة الضريبة (%)</label>
                <input type="number" name="tax_rate" class="form-control" 
                       step="0.01" min="0" max="100" value="0" placeholder="نسبة الضريبة">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">مبلغ الضريبة</label>
                <input type="number" name="tax_amount" class="form-control" 
                       step="0.01" min="0" value="0" placeholder="مبلغ الضريبة" readonly>
            </div>
            <div class="col-md-6">
                <label class="form-label">المبلغ الإجمالي</label>
                <input type="number" name="total_amount" class="form-control" 
                       step="0.01" min="0" value="0" placeholder="المبلغ الإجمالي" readonly>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-sticky-note"></i> ملاحظات
        </div>
        <div class="mb-3">
            <label class="form-label">تفاصيل إضافية</label>
            <textarea name="details" class="form-control" rows="3" 
                      placeholder="تفاصيل إضافية حول المصروف..."></textarea>
        </div>
        <div class="mb-3">
            <label class="form-label">ملاحظات</label>
            <textarea name="notes" class="form-control" rows="2" 
                      placeholder="ملاحظات إضافية..."></textarea>
        </div>
    </div>

    <!-- ملخص مالي -->
    <div class="alert alert-info">
        <h6><strong>ملخص مالي:</strong></h6>
        <p><strong>المبلغ الأساسي:</strong> <span id="baseAmount">0.00</span> شيكل</p>
        <p><strong>مبلغ الضريبة:</strong> <span id="taxAmountDisplay">0.00</span> شيكل</p>
        <p><strong>المبلغ الإجمالي:</strong> <span id="totalAmountDisplay">0.00</span> شيكل</p>
        <p><strong>المبلغ المتبقي:</strong> <span id="remainingAmount">0.00</span> شيكل</p>
    </div>

    <div class="text-center mt-4">
        <button type="submit" class="btn btn-success px-4">
            <i class="fa fa-save"></i> حفظ المصروف
        </button>
        <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
            إلغاء
        </button>
    </div>
</form>

<script>
document.getElementById('addExpenseForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            if (modal) modal.hide();
            showAlert('تم إضافة المصروف بنجاح', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(data.message || 'حدث خطأ أثناء إضافة المصروف', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
});

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
    document.body.appendChild(alertDiv);
    setTimeout(() => alertDiv.remove(), 5000);
}
</script>

<style>
.modal-form .form-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.modal-form .form-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
}

.modal-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.modal-form .text-danger {
    color: #dc3545 !important;
}

.modal-form .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.modal-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.alert {
    border-radius: 10px;
}
</style>

<script>
// حساب الضريبة والمبلغ الإجمالي تلقائياً
function calculateTotals() {
    var baseAmount = parseFloat(document.querySelector('input[name="amount"]').value) || 0;
    var taxRate = parseFloat(document.querySelector('input[name="tax_rate"]').value) || 0;
    var paidAmount = parseFloat(document.querySelector('input[name="paid_amount"]').value) || 0;
    
    var taxAmount = (baseAmount * taxRate) / 100;
    var totalAmount = baseAmount + taxAmount;
    var remainingAmount = totalAmount - paidAmount;
    
    // تحديث الحقول
    document.querySelector('input[name="tax_amount"]').value = taxAmount.toFixed(2);
    document.querySelector('input[name="total_amount"]').value = totalAmount.toFixed(2);
    
    // تحديث العرض
    document.getElementById('baseAmount').textContent = baseAmount.toFixed(2);
    document.getElementById('taxAmountDisplay').textContent = taxAmount.toFixed(2);
    document.getElementById('totalAmountDisplay').textContent = totalAmount.toFixed(2);
    document.getElementById('remainingAmount').textContent = remainingAmount.toFixed(2);
}

// ربط الأحداث
document.querySelector('input[name="amount"]').addEventListener('input', calculateTotals);
document.querySelector('input[name="tax_rate"]').addEventListener('input', calculateTotals);
document.querySelector('input[name="paid_amount"]').addEventListener('input', calculateTotals);

// حساب أولي
calculateTotals();
</script>
