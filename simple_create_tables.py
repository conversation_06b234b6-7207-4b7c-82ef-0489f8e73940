import sqlite3

conn = sqlite3.connect('lawoffice.db')
cursor = conn.cursor()

# إنشاء جدول notifications
cursor.execute('''
CREATE TABLE IF NOT EXISTS notifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    priority VARCHAR(20) DEFAULT 'medium',
    is_read BOOLEAN DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    property_id INTEGER,
    tenant_id INTEGER,
    lease_id INTEGER,
    user_id INTEGER NOT NULL,
    due_date DATETIME,
    reminder_date DATETIME,
    action_url VARCHAR(500),
    action_data TEXT,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    read_date DATETIME
)
''')

# إنشاء جدول notification_settings
cursor.execute('''
CREATE TABLE IF NOT EXISTS notification_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    lease_expiry_enabled BOOLEAN DEFAULT 1,
    lease_expiry_days INTEGER DEFAULT 30,
    payment_due_enabled BOOLEAN DEFAULT 1,
    payment_due_days INTEGER DEFAULT 5,
    maintenance_due_enabled BOOLEAN DEFAULT 1,
    maintenance_due_days INTEGER DEFAULT 7,
    tenant_review_enabled BOOLEAN DEFAULT 1,
    tenant_review_months INTEGER DEFAULT 6,
    email_notifications BOOLEAN DEFAULT 1,
    sms_notifications BOOLEAN DEFAULT 0,
    push_notifications BOOLEAN DEFAULT 1,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP
)
''')

conn.commit()
conn.close()
print('تم إنشاء الجداول بنجاح')
