from app import db, app
from app.models import User
import sys

def reset_admin(new_username="admin", new_password="admin123"):
    with app.app_context():
        user = User.query.filter_by(username=new_username).first()
        if user:
            user.password = new_password
            db.session.commit()
            print(f"تمت إعادة تعيين كلمة مرور {new_username} إلى {new_password}")
        else:
            # حذف أي مستخدم admin قديم إذا كان موجوداً
            old_admin = User.query.filter_by(username="admin").first()
            if old_admin:
                db.session.delete(old_admin)
                db.session.commit()
            user = User(username=new_username, password=new_password)
            db.session.add(user)
            db.session.commit()
            print(f"تم إنشاء مستخدم {new_username} بكلمة مرور {new_password}")

if __name__ == "__main__":
    if len(sys.argv) == 3:
        reset_admin(sys.argv[1], sys.argv[2])
    else:
        reset_admin()
