"""Add file_url and uploaded_date fields to document tables

Revision ID: add_document_fields
Revises: fix_properties_table
Create Date: 2025-07-05 18:59:00.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = 'add_document_fields'
down_revision = 'fix_properties_table'
branch_labels = None
depends_on = None

def upgrade():
    """إضافة الحقول الجديدة لجداول المستندات"""
    
    # إضافة حقول لجدول property_documents
    try:
        op.add_column('property_documents', sa.Column('file_url', sa.String(500), nullable=True))
        print("✓ تم إضافة file_url إلى property_documents")
    except Exception as e:
        print(f"⚠ file_url موجود بالفعل في property_documents: {e}")
    
    try:
        op.add_column('property_documents', sa.Column('uploaded_date', sa.DateTime(), nullable=True, default=datetime.now))
        print("✓ تم إضافة uploaded_date إلى property_documents")
    except Exception as e:
        print(f"⚠ uploaded_date موجود بالفعل في property_documents: {e}")
    
    # إضافة حقول لجدول tenant_documents
    try:
        op.add_column('tenant_documents', sa.Column('file_url', sa.String(500), nullable=True))
        print("✓ تم إضافة file_url إلى tenant_documents")
    except Exception as e:
        print(f"⚠ file_url موجود بالفعل في tenant_documents: {e}")
    
    try:
        op.add_column('tenant_documents', sa.Column('uploaded_date', sa.DateTime(), nullable=True, default=datetime.now))
        print("✓ تم إضافة uploaded_date إلى tenant_documents")
    except Exception as e:
        print(f"⚠ uploaded_date موجود بالفعل في tenant_documents: {e}")
    
    # إضافة حقول لجدول lease_documents
    try:
        op.add_column('lease_documents', sa.Column('file_url', sa.String(500), nullable=True))
        print("✓ تم إضافة file_url إلى lease_documents")
    except Exception as e:
        print(f"⚠ file_url موجود بالفعل في lease_documents: {e}")
    
    try:
        op.add_column('lease_documents', sa.Column('uploaded_date', sa.DateTime(), nullable=True, default=datetime.now))
        print("✓ تم إضافة uploaded_date إلى lease_documents")
    except Exception as e:
        print(f"⚠ uploaded_date موجود بالفعل في lease_documents: {e}")

def downgrade():
    """إزالة الحقول المضافة"""
    
    # إزالة حقول من جدول lease_documents
    try:
        op.drop_column('lease_documents', 'uploaded_date')
        op.drop_column('lease_documents', 'file_url')
    except Exception as e:
        print(f"خطأ في إزالة حقول lease_documents: {e}")
    
    # إزالة حقول من جدول tenant_documents
    try:
        op.drop_column('tenant_documents', 'uploaded_date')
        op.drop_column('tenant_documents', 'file_url')
    except Exception as e:
        print(f"خطأ في إزالة حقول tenant_documents: {e}")
    
    # إزالة حقول من جدول property_documents
    try:
        op.drop_column('property_documents', 'uploaded_date')
        op.drop_column('property_documents', 'file_url')
    except Exception as e:
        print(f"خطأ في إزالة حقول property_documents: {e}")
