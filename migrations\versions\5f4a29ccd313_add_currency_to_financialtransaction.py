"""add currency to financialtransaction

Revision ID: 5f4a29ccd313
Revises: b1824b851f71
Create Date: 2025-07-01 22:50:47.321532

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5f4a29ccd313'
down_revision = 'b1824b851f71'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('financial_transaction', schema=None) as batch_op:
        batch_op.add_column(sa.Column('currency', sa.String(length=20), nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('financial_transaction', schema=None) as batch_op:
        batch_op.drop_column('currency')

    # ### end Alembic commands ###
