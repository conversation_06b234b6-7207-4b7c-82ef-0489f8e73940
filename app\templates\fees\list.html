{% extends 'base.html' %}
{% block title %}إدارة الأتعاب{% endblock %}

{% block head %}
<style>
    .fee-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .fee-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .fee-card.overdue {
        border-left-color: #dc3545;
    }
    .fee-card.paid {
        border-left-color: #28a745;
    }
    .fee-card.partial {
        border-left-color: #ffc107;
    }
    .status-badge {
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
    }
    .amount-display {
        font-size: 1.2rem;
        font-weight: bold;
    }
    .progress-thin {
        height: 8px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4" dir="rtl">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1"><i class="fa fa-money-bill-wave text-success"></i> إدارة الأتعاب</h2>
                    <p class="text-muted mb-0">إدارة شاملة للأتعاب والرسوم القانونية</p>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-success" 
                            onclick="openFormModal('{{ url_for('modal_add_fee') }}', 'إضافة أتعاب جديدة', {
                                size: 'xl',
                                onSuccess: function() { window.location.reload(); }
                            })">
                        <i class="fa fa-plus"></i> إضافة أتعاب
                    </button>
                    <a href="{{ url_for('print_financial_report') }}" class="btn btn-info" target="_blank">
                        <i class="fa fa-print"></i> تقرير الأتعاب
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي الأتعاب</h6>
                            <h4 class="mb-0">{{ total_fees | number_format }} ₪</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fa fa-money-bill-wave fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المحصل</h6>
                            <h4 class="mb-0">{{ total_paid | number_format }} ₪</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fa fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المتبقي</h6>
                            <h4 class="mb-0">{{ total_remaining | number_format }} ₪</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fa fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">متأخرة</h6>
                            <h4 class="mb-0">{{ overdue_count }}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fa fa-exclamation-triangle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">البحث</label>
                            <input type="text" name="search" class="form-control" 
                                   placeholder="رقم الأتعاب، العميل، أو الوصف..." 
                                   value="{{ request.args.get('search', '') }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">نوع الأتعاب</label>
                            <select name="fee_type" class="form-select">
                                <option value="">جميع الأنواع</option>
                                <option value="استشارة" {{ 'selected' if request.args.get('fee_type') == 'استشارة' }}>استشارة</option>
                                <option value="ترافع" {{ 'selected' if request.args.get('fee_type') == 'ترافع' }}>ترافع</option>
                                <option value="صياغة عقد" {{ 'selected' if request.args.get('fee_type') == 'صياغة عقد' }}>صياغة عقد</option>
                                <option value="تحصيل" {{ 'selected' if request.args.get('fee_type') == 'تحصيل' }}>تحصيل</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">حالة الدفع</label>
                            <select name="payment_status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="غير مدفوع" {{ 'selected' if request.args.get('payment_status') == 'غير مدفوع' }}>غير مدفوع</option>
                                <option value="مدفوع جزئياً" {{ 'selected' if request.args.get('payment_status') == 'مدفوع جزئياً' }}>مدفوع جزئياً</option>
                                <option value="مدفوع كاملاً" {{ 'selected' if request.args.get('payment_status') == 'مدفوع كاملاً' }}>مدفوع كاملاً</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" name="date_from" class="form-control" 
                                   value="{{ request.args.get('date_from', '') }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" name="date_to" class="form-control" 
                                   value="{{ request.args.get('date_to', '') }}">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block w-100">
                                <i class="fa fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Fees List -->
    <div class="row">
        {% for fee in fees %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card fee-card h-100 
                        {% if fee.is_overdue() %}overdue
                        {% elif fee.payment_status == 'مدفوع كاملاً' %}paid
                        {% elif fee.payment_status == 'مدفوع جزئياً' %}partial
                        {% endif %}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">{{ fee.fee_number }}</h6>
                        <small class="text-muted">{{ fee.service_date.strftime('%Y-%m-%d') }}</small>
                    </div>
                    <span class="badge status-badge 
                                {% if fee.payment_status == 'غير مدفوع' %}bg-danger
                                {% elif fee.payment_status == 'مدفوع جزئياً' %}bg-warning
                                {% else %}bg-success
                                {% endif %}">
                        {{ fee.payment_status }}
                    </span>
                </div>
                <div class="card-body">
                    <h6 class="card-title">{{ fee.fee_type }}</h6>
                    <p class="card-text text-muted small">{{ fee.description[:100] }}...</p>
                    
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted">العميل:</small>
                            <div class="fw-bold">{{ fee.client.name }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">الاستحقاق:</small>
                            <div class="fw-bold {% if fee.is_overdue() %}text-danger{% endif %}">
                                {{ fee.due_date.strftime('%Y-%m-%d') }}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="amount-display text-primary">{{ fee.total_amount | number_format }} ₪</span>
                            <small class="text-muted">{{ ((fee.paid_amount / fee.total_amount) * 100) | round(1) }}%</small>
                        </div>
                        <div class="progress progress-thin">
                            <div class="progress-bar 
                                        {% if fee.payment_status == 'مدفوع كاملاً' %}bg-success
                                        {% elif fee.payment_status == 'مدفوع جزئياً' %}bg-warning
                                        {% else %}bg-danger
                                        {% endif %}" 
                                 style="width: {{ ((fee.paid_amount / fee.total_amount) * 100) | round(1) }}%"></div>
                        </div>
                        <div class="d-flex justify-content-between mt-1">
                            <small class="text-success">مدفوع: {{ fee.paid_amount | number_format }} ₪</small>
                            <small class="text-danger">متبقي: {{ fee.remaining_amount | number_format }} ₪</small>
                        </div>
                    </div>

                    {% if fee.is_overdue() %}
                    <div class="alert alert-danger py-2 mb-3">
                        <i class="fa fa-exclamation-triangle"></i>
                        متأخر {{ fee.days_overdue() }} يوم
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm"
                                onclick="openFormModal('{{ url_for('modal_view_fee', fee_id=fee.id) }}', 'تفاصيل الأتعاب', {size: 'xl'})">
                            <i class="fa fa-eye"></i> عرض
                        </button>
                        {% if fee.remaining_amount > 0 %}
                        <button type="button" class="btn btn-outline-success btn-sm"
                                onclick="openFormModal('{{ url_for('modal_add_fee_payment', fee_id=fee.id) }}', 'إضافة دفعة', {
                                    onSuccess: function() { window.location.reload(); }
                                })">
                            <i class="fa fa-plus"></i> دفعة
                        </button>
                        {% endif %}
                        <button type="button" class="btn btn-outline-secondary btn-sm"
                                onclick="openFormModal('{{ url_for('modal_edit_fee', fee_id=fee.id) }}', 'تعديل الأتعاب', {
                                    size: 'xl',
                                    onSuccess: function() { window.location.reload(); }
                                })">
                            <i class="fa fa-edit"></i> تعديل
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fa fa-money-bill-wave fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أتعاب مسجلة</h5>
                    <p class="text-muted">ابدأ بإضافة أول أتعاب للنظام</p>
                    <button type="button" class="btn btn-success"
                            onclick="openFormModal('{{ url_for('modal_add_fee') }}', 'إضافة أتعاب جديدة', {
                                size: 'xl',
                                onSuccess: function() { window.location.reload(); }
                            })">
                        <i class="fa fa-plus"></i> إضافة أتعاب
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if fees.pages > 1 %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="تصفح الأتعاب">
                <ul class="pagination justify-content-center">
                    {% if fees.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('fees_list', page=fees.prev_num, **request.args) }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in fees.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != fees.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('fees_list', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if fees.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('fees_list', page=fees.next_num, **request.args) }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
// تحديث الصفحة كل 5 دقائق لعرض أحدث البيانات
setTimeout(function() {
    window.location.reload();
}, 300000);

// تنسيق الأرقام
document.addEventListener('DOMContentLoaded', function() {
    // إضافة أي سكريبت إضافي هنا
});
</script>
{% endblock %}
