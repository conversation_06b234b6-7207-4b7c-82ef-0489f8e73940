#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات للجداول الفارغة
Populate Empty Tables
"""

import sqlite3
from datetime import datetime, timedelta
import random

def populate_fees_table():
    """إضافة بيانات للأتعاب"""
    print("💰 إضافة بيانات الأتعاب...")

    db_path = 'instance/lawoffice.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # حذف البيانات الموجودة
    cursor.execute("DELETE FROM fees")

    # جلب معرفات القضايا والعملاء
    cursor.execute("SELECT id FROM cases LIMIT 5")
    case_ids = [row[0] for row in cursor.fetchall()]

    cursor.execute("SELECT id FROM clients LIMIT 5")
    client_ids = [row[0] for row in cursor.fetchall()]
    
    fees_data = [
        {
            'fee_number': 'FEE-2024-001',
            'case_id': case_ids[0] if case_ids else None,
            'client_id': client_ids[0] if client_ids else None,
            'fee_type': 'أتعاب قضية',
            'service_category': 'قضايا تجارية',
            'description': 'أتعاب قضية تجارية',
            'service_date': datetime.now().isoformat(),
            'due_date': (datetime.now() + timedelta(days=30)).isoformat(),
            'base_amount': 5000.00,
            'additional_fees': 0.00,
            'discount_amount': 0.00,
            'tax_amount': 750.00,
            'total_amount': 5750.00,
            'payment_status': 'مستحق',
            'paid_amount': 0.00,
            'remaining_amount': 5750.00,
            'currency': 'ريال سعودي',
            'priority': 'عالية',
            'created_date': datetime.now().isoformat()
        },
        {
            'fee_number': 'FEE-2024-002',
            'case_id': case_ids[1] if len(case_ids) > 1 else None,
            'client_id': client_ids[1] if len(client_ids) > 1 else None,
            'fee_type': 'استشارة قانونية',
            'service_category': 'استشارات',
            'description': 'أتعاب استشارة قانونية',
            'service_date': datetime.now().isoformat(),
            'due_date': (datetime.now() + timedelta(days=15)).isoformat(),
            'base_amount': 3000.00,
            'additional_fees': 0.00,
            'discount_amount': 300.00,
            'tax_amount': 405.00,
            'total_amount': 3105.00,
            'payment_status': 'مدفوع',
            'paid_amount': 3105.00,
            'remaining_amount': 0.00,
            'currency': 'ريال سعودي',
            'priority': 'متوسطة',
            'created_date': datetime.now().isoformat()
        },
        {
            'fee_number': 'FEE-2024-003',
            'case_id': case_ids[2] if len(case_ids) > 2 else None,
            'client_id': client_ids[2] if len(client_ids) > 2 else None,
            'fee_type': 'أتعاب قضية',
            'service_category': 'قضايا عقارية',
            'description': 'أتعاب قضية عقارية',
            'service_date': datetime.now().isoformat(),
            'due_date': (datetime.now() + timedelta(days=45)).isoformat(),
            'base_amount': 7500.00,
            'additional_fees': 500.00,
            'discount_amount': 0.00,
            'tax_amount': 1200.00,
            'total_amount': 9200.00,
            'payment_status': 'مستحق',
            'paid_amount': 0.00,
            'remaining_amount': 9200.00,
            'currency': 'ريال سعودي',
            'priority': 'عالية',
            'created_date': datetime.now().isoformat()
        }
    ]

    for fee in fees_data:
        cursor.execute("""
            INSERT INTO fees (fee_number, case_id, client_id, fee_type, service_category, description,
                            service_date, due_date, base_amount, additional_fees, discount_amount,
                            tax_amount, total_amount, payment_status, paid_amount, remaining_amount,
                            currency, priority, created_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            fee['fee_number'], fee['case_id'], fee['client_id'], fee['fee_type'],
            fee['service_category'], fee['description'], fee['service_date'], fee['due_date'],
            fee['base_amount'], fee['additional_fees'], fee['discount_amount'], fee['tax_amount'],
            fee['total_amount'], fee['payment_status'], fee['paid_amount'], fee['remaining_amount'],
            fee['currency'], fee['priority'], fee['created_date']
        ))
    
    conn.commit()
    print(f"   ✅ تم إضافة {len(fees_data)} سجل أتعاب")
    
    conn.close()

def populate_debts_table():
    """إضافة بيانات الديون"""
    print("💳 إضافة بيانات الديون...")

    db_path = 'instance/lawoffice.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # حذف البيانات الموجودة
    cursor.execute("DELETE FROM debts")

    # جلب معرفات القضايا والعملاء
    cursor.execute("SELECT id FROM cases LIMIT 5")
    case_ids = [row[0] for row in cursor.fetchall()]

    cursor.execute("SELECT id FROM clients LIMIT 5")
    client_ids = [row[0] for row in cursor.fetchall()]
    
    debts_data = [
        {
            'creditor_name': 'شركة التجارة المتقدمة',
            'amount': 15000.00,
            'paid_amount': 0.00,
            'remaining_amount': 15000.00,
            'debt_type': 'دين تجاري',
            'description': 'دين تجاري مستحق للشركة',
            'due_date': (datetime.now() + timedelta(days=90)).isoformat(),
            'status': 'مستحق',
            'priority': 'عالية',
            'currency': 'ريال سعودي',
            'created_date': datetime.now().isoformat(),
            'case_id': case_ids[0] if case_ids else None,
            'client_id': client_ids[0] if client_ids else None
        },
        {
            'creditor_name': 'أحمد محمد العلي',
            'amount': 8500.00,
            'paid_amount': 3000.00,
            'remaining_amount': 5500.00,
            'debt_type': 'دين شخصي',
            'description': 'دين شخصي مع سداد جزئي',
            'due_date': (datetime.now() + timedelta(days=60)).isoformat(),
            'status': 'مدفوع جزئياً',
            'priority': 'متوسطة',
            'currency': 'ريال سعودي',
            'created_date': datetime.now().isoformat(),
            'case_id': case_ids[1] if len(case_ids) > 1 else None,
            'client_id': client_ids[1] if len(client_ids) > 1 else None
        },
        {
            'creditor_name': 'البنك الأهلي التجاري',
            'amount': 25000.00,
            'paid_amount': 0.00,
            'remaining_amount': 25000.00,
            'debt_type': 'دين مصرفي',
            'description': 'دين عقاري مع البنك',
            'due_date': (datetime.now() + timedelta(days=120)).isoformat(),
            'status': 'مستحق',
            'priority': 'عالية',
            'currency': 'ريال سعودي',
            'created_date': datetime.now().isoformat(),
            'case_id': case_ids[2] if len(case_ids) > 2 else None,
            'client_id': client_ids[2] if len(client_ids) > 2 else None
        },
        {
            'creditor_name': 'مؤسسة الخدمات التجارية',
            'amount': 5000.00,
            'paid_amount': 5000.00,
            'remaining_amount': 0.00,
            'debt_type': 'دين تجاري',
            'description': 'دين تجاري مسدد بالكامل',
            'due_date': (datetime.now() + timedelta(days=30)).isoformat(),
            'status': 'مدفوع',
            'priority': 'منخفضة',
            'currency': 'ريال سعودي',
            'created_date': datetime.now().isoformat(),
            'case_id': case_ids[3] if len(case_ids) > 3 else None,
            'client_id': client_ids[3] if len(client_ids) > 3 else None
        }
    ]

    for debt in debts_data:
        cursor.execute("""
            INSERT INTO debts (creditor_name, amount, paid_amount, remaining_amount, debt_type,
                             description, due_date, status, priority, currency, created_date,
                             case_id, client_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            debt['creditor_name'], debt['amount'], debt['paid_amount'], debt['remaining_amount'],
            debt['debt_type'], debt['description'], debt['due_date'], debt['status'],
            debt['priority'], debt['currency'], debt['created_date'], debt['case_id'], debt['client_id']
        ))
    
    conn.commit()
    print(f"   ✅ تم إضافة {len(debts_data)} سجل دين")
    
    conn.close()

def populate_tasks_table():
    """إضافة بيانات المهام"""
    print("📋 إضافة بيانات المهام...")

    db_path = 'instance/lawoffice.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # حذف البيانات الموجودة
    cursor.execute("DELETE FROM tasks")

    # جلب معرفات القضايا والعملاء
    cursor.execute("SELECT id FROM cases LIMIT 5")
    case_ids = [row[0] for row in cursor.fetchall()]

    cursor.execute("SELECT id FROM clients LIMIT 5")
    client_ids = [row[0] for row in cursor.fetchall()]
    
    tasks_data = [
        {
            'title': 'مراجعة ملف القضية التجارية',
            'description': 'مراجعة شاملة لجميع الوثائق والمستندات المتعلقة بالقضية التجارية',
            'case_id': case_ids[0] if case_ids else None,
            'client_id': client_ids[0] if client_ids else None,
            'due_date': (datetime.now() + timedelta(days=7)).isoformat(),
            'priority': 'عالية',
            'status': 'قيد التنفيذ',
            'created_at': datetime.now().isoformat()
        },
        {
            'title': 'إعداد مذكرة دفاع',
            'description': 'إعداد مذكرة دفاع شاملة للقضية العقارية',
            'case_id': case_ids[1] if len(case_ids) > 1 else None,
            'client_id': client_ids[1] if len(client_ids) > 1 else None,
            'due_date': (datetime.now() + timedelta(days=14)).isoformat(),
            'priority': 'عالية',
            'status': 'لم تبدأ',
            'created_at': datetime.now().isoformat()
        },
        {
            'title': 'متابعة إجراءات المحكمة',
            'description': 'متابعة سير الإجراءات في المحكمة والحصول على التحديثات',
            'case_id': case_ids[2] if len(case_ids) > 2 else None,
            'client_id': client_ids[2] if len(client_ids) > 2 else None,
            'due_date': (datetime.now() + timedelta(days=3)).isoformat(),
            'priority': 'متوسطة',
            'status': 'مكتملة',
            'created_at': datetime.now().isoformat()
        },
        {
            'title': 'جمع الأدلة والشهادات',
            'description': 'جمع جميع الأدلة والشهادات اللازمة لدعم القضية',
            'case_id': case_ids[3] if len(case_ids) > 3 else None,
            'client_id': client_ids[3] if len(client_ids) > 3 else None,
            'due_date': (datetime.now() + timedelta(days=21)).isoformat(),
            'priority': 'عالية',
            'status': 'قيد التنفيذ',
            'created_at': datetime.now().isoformat()
        },
        {
            'title': 'استشارة قانونية للعميل',
            'description': 'تقديم استشارة قانونية شاملة للعميل حول خياراته القانونية',
            'case_id': case_ids[4] if len(case_ids) > 4 else None,
            'client_id': client_ids[4] if len(client_ids) > 4 else None,
            'due_date': (datetime.now() + timedelta(days=5)).isoformat(),
            'priority': 'متوسطة',
            'status': 'لم تبدأ',
            'created_at': datetime.now().isoformat()
        },
        {
            'title': 'مراجعة العقود والاتفاقيات',
            'description': 'مراجعة قانونية شاملة للعقود والاتفاقيات',
            'case_id': None,
            'client_id': client_ids[0] if client_ids else None,
            'due_date': (datetime.now() + timedelta(days=10)).isoformat(),
            'priority': 'منخفضة',
            'status': 'لم تبدأ',
            'created_at': datetime.now().isoformat()
        }
    ]
    
    for task in tasks_data:
        cursor.execute("""
            INSERT INTO tasks (title, description, case_id, client_id, due_date, priority, status,
                             category, progress, estimated_hours, actual_hours, created_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            task['title'], task['description'], task['case_id'], task['client_id'],
            task['due_date'], task['priority'], task['status'], 'قانونية',
            50 if task['status'] == 'قيد التنفيذ' else (100 if task['status'] == 'مكتملة' else 0),
            8.0, 4.0 if task['status'] == 'قيد التنفيذ' else (8.0 if task['status'] == 'مكتملة' else 0.0),
            task['created_at']
        ))
    
    conn.commit()
    print(f"   ✅ تم إضافة {len(tasks_data)} مهمة")
    
    conn.close()

def populate_appointments_table():
    """إضافة بيانات المواعيد"""
    print("📅 إضافة بيانات المواعيد...")

    db_path = 'instance/lawoffice.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # حذف البيانات الموجودة
    cursor.execute("DELETE FROM appointments")

    # جلب معرفات القضايا والعملاء
    cursor.execute("SELECT id FROM cases LIMIT 5")
    case_ids = [row[0] for row in cursor.fetchall()]

    cursor.execute("SELECT id FROM clients LIMIT 5")
    client_ids = [row[0] for row in cursor.fetchall()]
    
    appointments_data = [
        {
            'subject': 'جلسة محكمة - القضية التجارية',
            'date': (datetime.now() + timedelta(days=5)).isoformat(),
            'location': 'المحكمة التجارية - الرياض',
            'notes': 'حضور جلسة المحكمة للقضية التجارية رقم 2024/123',
            'case_id': case_ids[0] if case_ids else None,
            'client_id': client_ids[0] if client_ids else None
        },
        {
            'subject': 'اجتماع مع العميل',
            'date': (datetime.now() + timedelta(days=2)).isoformat(),
            'location': 'مكتب المحاماة',
            'notes': 'اجتماع لمناقشة تطورات القضية العقارية',
            'case_id': case_ids[1] if len(case_ids) > 1 else None,
            'client_id': client_ids[1] if len(client_ids) > 1 else None
        },
        {
            'subject': 'استشارة قانونية',
            'date': (datetime.now() + timedelta(days=1)).isoformat(),
            'location': 'مكتب المحاماة',
            'notes': 'جلسة استشارة قانونية للعميل الجديد',
            'case_id': None,
            'client_id': client_ids[2] if len(client_ids) > 2 else None
        },
        {
            'subject': 'جلسة تحكيم',
            'date': (datetime.now() + timedelta(days=8)).isoformat(),
            'location': 'مركز الرياض للتحكيم التجاري',
            'notes': 'حضور جلسة التحكيم في النزاع التجاري',
            'case_id': case_ids[2] if len(case_ids) > 2 else None,
            'client_id': client_ids[2] if len(client_ids) > 2 else None
        },
        {
            'subject': 'اجتماع فريق العمل',
            'date': (datetime.now() + timedelta(days=7)).isoformat(),
            'location': 'قاعة الاجتماعات - مكتب المحاماة',
            'notes': 'اجتماع دوري لفريق العمل لمراجعة القضايا الجارية',
            'case_id': None,
            'client_id': None
        },
        {
            'subject': 'موعد مع الخبير القانوني',
            'date': (datetime.now() + timedelta(days=12)).isoformat(),
            'location': 'مكتب الخبير القانوني',
            'notes': 'استشارة مع خبير قانوني متخصص في القضايا العقارية',
            'case_id': case_ids[3] if len(case_ids) > 3 else None,
            'client_id': client_ids[3] if len(client_ids) > 3 else None
        }
    ]

    for appointment in appointments_data:
        cursor.execute("""
            INSERT INTO appointments (subject, date, location, notes, case_id, client_id)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            appointment['subject'], appointment['date'], appointment['location'],
            appointment['notes'], appointment['case_id'], appointment['client_id']
        ))
    
    conn.commit()
    print(f"   ✅ تم إضافة {len(appointments_data)} موعد")
    
    conn.close()

def verify_data_insertion():
    """التحقق من إدراج البيانات"""
    print("\n📊 التحقق من إدراج البيانات...")
    print("=" * 50)
    
    db_path = 'instance/lawoffice.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    tables = [
        ('fees', 'الأتعاب'),
        ('debts', 'الديون'),
        ('tasks', 'المهام'),
        ('appointments', 'المواعيد')
    ]
    
    for table, description in tables:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   ✅ {description}: {count} سجل")
        except Exception as e:
            print(f"   ❌ {description}: خطأ - {e}")
    
    conn.close()

if __name__ == "__main__":
    print("📝 بدء إضافة بيانات للجداول الفارغة...")
    print("=" * 60)
    
    try:
        # إضافة بيانات الأتعاب
        populate_fees_table()
        
        # إضافة بيانات الديون
        populate_debts_table()
        
        # إضافة بيانات المهام
        populate_tasks_table()
        
        # إضافة بيانات المواعيد
        populate_appointments_table()
        
        # التحقق من النتائج
        verify_data_insertion()
        
        print("\n🎉 تم إضافة جميع البيانات بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ في إضافة البيانات: {e}")
        import traceback
        traceback.print_exc()
