// نظام النوافذ المنبثقة الاحترافي
class ModalManager {
    constructor() {
        this.currentModal = null;
        this.initializeModalSystem();
    }

    initializeModalSystem() {
        // إنشاء container للنوافذ المنبثقة
        if (!document.getElementById('modal-container')) {
            const container = document.createElement('div');
            container.id = 'modal-container';
            document.body.appendChild(container);
        }

        // إضافة أنماط CSS
        this.addModalStyles();
        
        // ربط الأحداث
        this.bindEvents();
    }

    addModalStyles() {
        if (document.getElementById('modal-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'modal-styles';
        styles.textContent = `
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1050;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .modal-overlay.show {
                opacity: 1;
            }

            .modal-dialog {
                background: white;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                max-width: 90vw;
                max-height: 90vh;
                overflow: hidden;
                transform: scale(0.7);
                transition: transform 0.3s ease;
                margin: 20px;
            }

            .modal-overlay.show .modal-dialog {
                transform: scale(1);
            }

            .modal-header {
                padding: 20px;
                border-bottom: 1px solid #dee2e6;
                display: flex;
                justify-content: between;
                align-items: center;
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border-radius: 15px 15px 0 0;
            }

            .modal-title {
                margin: 0;
                font-size: 1.25rem;
                font-weight: 600;
            }

            .modal-close {
                background: none;
                border: none;
                color: white;
                font-size: 1.5rem;
                cursor: pointer;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background-color 0.2s;
            }

            .modal-close:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }

            .modal-body {
                padding: 20px;
                max-height: 60vh;
                overflow-y: auto;
            }

            .modal-footer {
                padding: 15px 20px;
                border-top: 1px solid #dee2e6;
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                background-color: #f8f9fa;
                border-radius: 0 0 15px 15px;
            }

            .modal-sm .modal-dialog { max-width: 400px; }
            .modal-md .modal-dialog { max-width: 600px; }
            .modal-lg .modal-dialog { max-width: 800px; }
            .modal-xl .modal-dialog { max-width: 1200px; }

            @media (max-width: 768px) {
                .modal-dialog {
                    max-width: 95vw;
                    margin: 10px;
                }
                
                .modal-header, .modal-body, .modal-footer {
                    padding: 15px;
                }
            }

            .form-section {
                margin-bottom: 25px;
                padding: 20px;
                border: 1px solid #e9ecef;
                border-radius: 10px;
                background-color: #f8f9fa;
            }

            .form-section-title {
                font-size: 1.1rem;
                font-weight: 600;
                color: #495057;
                margin-bottom: 15px;
                padding-bottom: 8px;
                border-bottom: 2px solid #007bff;
            }

            .loading-spinner {
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 3px solid #f3f3f3;
                border-top: 3px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(styles);
    }

    bindEvents() {
        // إغلاق النافذة بالضغط على ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.currentModal) {
                this.closeModal();
            }
        });

        // إغلاق النافذة بالضغط خارجها
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal();
            }
        });
    }

    openModal(options) {
        const {
            title = 'نافذة جديدة',
            content = '',
            size = 'md',
            buttons = [],
            onOpen = null,
            onClose = null
        } = options;

        // إغلاق النافذة الحالية إن وجدت
        if (this.currentModal) {
            this.closeModal();
        }

        // إنشاء النافذة
        const modalHtml = `
            <div class="modal-overlay modal-${size}" id="current-modal">
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="modal-close" onclick="modalManager.closeModal()">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    ${buttons.length > 0 ? `
                        <div class="modal-footer">
                            ${buttons.map(btn => `
                                <button type="button" class="btn ${btn.class || 'btn-secondary'}" 
                                        onclick="${btn.onclick || ''}">${btn.text}</button>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        // إضافة النافذة للصفحة
        const container = document.getElementById('modal-container');
        container.innerHTML = modalHtml;

        this.currentModal = document.getElementById('current-modal');

        // إظهار النافذة مع تأثير
        setTimeout(() => {
            this.currentModal.classList.add('show');
        }, 10);

        // تنفيذ callback عند الفتح
        if (onOpen) onOpen();

        // حفظ callback الإغلاق
        this.onCloseCallback = onClose;

        return this.currentModal;
    }

    closeModal() {
        if (!this.currentModal) return;

        // إخفاء النافذة مع تأثير
        this.currentModal.classList.remove('show');

        setTimeout(() => {
            if (this.currentModal) {
                this.currentModal.remove();
                this.currentModal = null;
            }
        }, 300);

        // تنفيذ callback عند الإغلاق
        if (this.onCloseCallback) {
            this.onCloseCallback();
            this.onCloseCallback = null;
        }
    }

    loadFormModal(url, title, options = {}) {
        const {
            size = 'lg',
            onSuccess = null,
            onError = null
        } = options;

        // إظهار نافذة تحميل
        this.openModal({
            title: title,
            content: '<div class="text-center"><div class="loading-spinner"></div><p class="mt-2">جاري التحميل...</p></div>',
            size: size
        });

        // تحميل المحتوى
        fetch(url)
            .then(response => response.text())
            .then(html => {
                // استخراج محتوى النموذج
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const form = doc.querySelector('form');
                
                if (form) {
                    // تحديث محتوى النافذة
                    const modalBody = this.currentModal.querySelector('.modal-body');
                    modalBody.innerHTML = form.outerHTML;

                    // ربط النموذج بـ AJAX
                    this.bindFormSubmission(form, onSuccess, onError);
                } else {
                    throw new Error('لم يتم العثور على نموذج في الصفحة');
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل النموذج:', error);
                this.currentModal.querySelector('.modal-body').innerHTML = 
                    '<div class="alert alert-danger">حدث خطأ في تحميل النموذج</div>';
                
                if (onError) onError(error);
            });
    }

    bindFormSubmission(form, onSuccess, onError) {
        form.addEventListener('submit', (e) => {
            e.preventDefault();

            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // إظهار حالة التحميل
            submitBtn.innerHTML = '<div class="loading-spinner"></div> جاري الحفظ...';
            submitBtn.disabled = true;

            // إضافة CSRF token إذا لم يكن موجوداً
            const csrfToken = form.querySelector('input[name="csrf_token"]')?.value ||
                             document.querySelector('meta[name="csrf-token"]')?.content;

            const headers = {};
            if (csrfToken) {
                headers['X-CSRFToken'] = csrfToken;
            }

            fetch(form.action || window.location.href, {
                method: 'POST',
                body: formData,
                headers: headers
            })
            .then(response => {
                if (response.ok) {
                    if (onSuccess) {
                        onSuccess(response);
                    } else {
                        // إعادة تحميل الصفحة افتراضياً
                        window.location.reload();
                    }
                    this.closeModal();
                } else {
                    throw new Error('فشل في حفظ البيانات');
                }
            })
            .catch(error => {
                console.error('خطأ في إرسال النموذج:', error);
                
                // إظهار رسالة خطأ
                let errorDiv = form.querySelector('.form-error');
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.className = 'alert alert-danger form-error';
                    form.insertBefore(errorDiv, form.firstChild);
                }
                errorDiv.textContent = 'حدث خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى.';

                if (onError) onError(error);
            })
            .finally(() => {
                // إعادة تعيين الزر
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }
}

// إنشاء مثيل عام
const modalManager = new ModalManager();

// دوال مساعدة سريعة
function openQuickModal(title, content, size = 'md') {
    return modalManager.openModal({ title, content, size });
}

function openFormModal(url, title, options = {}) {
    return modalManager.loadFormModal(url, title, options);
}

function closeModal() {
    modalManager.closeModal();
}
