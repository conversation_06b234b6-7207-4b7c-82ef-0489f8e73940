{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
    <h2 class="mb-4 text-warning">تعديل الجلسة</h2>
    <form method="post">
        <div class="mb-3">
            <label for="case_id" class="form-label">القضية</label>
            <select name="case_id" id="case_id" class="form-select" required>
                {% for case in cases %}
                <option value="{{ case.id }}" {% if case.id == session.case_id %}selected{% endif %}>{{ case.title }} ({{ case.client.name }})</option>
                {% endfor %}
            </select>
        </div>
        <div class="mb-3">
            <label for="date" class="form-label">تاريخ ووقت الجلسة</label>
            <input type="datetime-local" name="date" id="date" class="form-control" value="{{ session.date.strftime('%Y-%m-%dT%H:%M') }}" required>
        </div>
        <div class="mb-3">
            <label for="location" class="form-label">المكان</label>
            <input type="text" name="location" id="location" class="form-control" value="{{ session.location }}">
        </div>
        <div class="mb-3">
            <label for="notes" class="form-label">ملاحظات</label>
            <textarea name="notes" id="notes" class="form-control">{{ session.notes }}</textarea>
        </div>
        <button type="submit" class="btn btn-primary">تحديث الجلسة</button>
        <a href="{{ url_for('sessions_list') }}" class="btn btn-secondary">إلغاء</a>
    </form>
</div>
{% endblock %}
