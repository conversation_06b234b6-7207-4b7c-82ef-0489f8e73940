import requests
import json
from datetime import datetime

def test_detailed_functionality():
    """فحص مفصل لجميع وظائف النظام"""
    base_url = 'http://localhost:5000'
    session = requests.Session()
    
    print("🔍 بدء الفحص المفصل لجميع وظائف النظام...")
    
    # 1. تسجيل الدخول
    print("\n1️⃣ فحص تسجيل الدخول...")
    try:
        login_data = {
            'username': 'office',
            'password': '66889088'
        }
        response = session.post(f'{base_url}/lawyersameh', data=login_data)
        if response.status_code == 200:
            print("✅ تسجيل الدخول يعمل بنجاح")
        else:
            print(f"❌ خطأ في تسجيل الدخول: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {str(e)}")
        return False
    
    # 2. فحص إضافة قضية جديدة
    print("\n2️⃣ فحص إضافة قضية جديدة...")
    try:
        case_data = {
            'case_number': 'TEST-001',
            'title': 'قضية تجريبية للفحص',
            'client_id': '1',
            'court': 'محكمة الاختبار',
            'type': 'تجريبي',
            'status': 'جارية',
            'client_role': 'مدعي',
            'fees_currency': 'دولار',
            'fees_total': '1000',
            'fees_paid': '500',
            'fees_remaining': '500'
        }
        response = session.post(f'{base_url}/cases/add', data=case_data)
        if response.status_code == 200 or 'redirect' in str(response.history):
            print("✅ إضافة قضية جديدة تعمل بنجاح")
        else:
            print(f"⚠️ قد تكون هناك مشكلة في إضافة القضية: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في إضافة القضية: {str(e)}")
    
    # 3. فحص إضافة عميل جديد
    print("\n3️⃣ فحص إضافة عميل جديد...")
    try:
        client_data = {
            'name': 'عميل تجريبي للفحص',
            'national_id': '123456789',
            'phone': '0599123456',
            'email': '<EMAIL>',
            'address': 'عنوان تجريبي'
        }
        response = session.post(f'{base_url}/clients/add', data=client_data)
        if response.status_code == 200 or 'redirect' in str(response.history):
            print("✅ إضافة عميل جديد تعمل بنجاح")
        else:
            print(f"⚠️ قد تكون هناك مشكلة في إضافة العميل: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في إضافة العميل: {str(e)}")
    
    # 4. فحص إضافة سند مالي
    print("\n4️⃣ فحص إضافة سند مالي...")
    try:
        finance_data = {
            'case_id': '1',
            'type': 'قبض',
            'amount': '500',
            'description': 'دفعة تجريبية للفحص',
            'date': datetime.now().strftime('%Y-%m-%d')
        }
        response = session.post(f'{base_url}/finance/add', data=finance_data)
        if response.status_code == 200 or 'redirect' in str(response.history):
            print("✅ إضافة سند مالي تعمل بنجاح")
        else:
            print(f"⚠️ قد تكون هناك مشكلة في إضافة السند المالي: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في إضافة السند المالي: {str(e)}")
    
    # 5. فحص إضافة مهمة
    print("\n5️⃣ فحص إضافة مهمة...")
    try:
        task_data = {
            'title': 'مهمة تجريبية للفحص',
            'description': 'وصف المهمة التجريبية',
            'case_id': '1',
            'due_date': datetime.now().strftime('%Y-%m-%d'),
            'priority': 'عالية',
            'status': 'جديدة'
        }
        response = session.post(f'{base_url}/tasks/add', data=task_data)
        if response.status_code == 200 or 'redirect' in str(response.history):
            print("✅ إضافة مهمة تعمل بنجاح")
        else:
            print(f"⚠️ قد تكون هناك مشكلة في إضافة المهمة: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في إضافة المهمة: {str(e)}")
    
    # 6. فحص إضافة موعد
    print("\n6️⃣ فحص إضافة موعد...")
    try:
        appointment_data = {
            'subject': 'موعد تجريبي للفحص',
            'date': datetime.now().strftime('%Y-%m-%d'),
            'time': '10:00',
            'case_id': '1',
            'location': 'مكان تجريبي'
        }
        response = session.post(f'{base_url}/appointments/add', data=appointment_data)
        if response.status_code == 200 or 'redirect' in str(response.history):
            print("✅ إضافة موعد تعمل بنجاح")
        else:
            print(f"⚠️ قد تكون هناك مشكلة في إضافة الموعد: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في إضافة الموعد: {str(e)}")
    
    # 7. فحص إضافة عقار
    print("\n7️⃣ فحص إضافة عقار...")
    try:
        property_data = {
            'address': 'عقار تجريبي للفحص',
            'type': 'شقة',
            'area': '100',
            'rooms': '3',
            'rent': '500',
            'status': 'متاح'
        }
        response = session.post(f'{base_url}/properties/add', data=property_data)
        if response.status_code == 200 or 'redirect' in str(response.history):
            print("✅ إضافة عقار تعمل بنجاح")
        else:
            print(f"⚠️ قد تكون هناك مشكلة في إضافة العقار: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في إضافة العقار: {str(e)}")
    
    # 8. فحص البحث في القضايا
    print("\n8️⃣ فحص البحث في القضايا...")
    try:
        search_data = {
            'search_field': 'case_number',
            'search_value': 'TEST'
        }
        response = session.post(f'{base_url}/stats', data=search_data)
        if response.status_code == 200:
            print("✅ البحث في القضايا يعمل بنجاح")
        else:
            print(f"⚠️ قد تكون هناك مشكلة في البحث: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في البحث: {str(e)}")
    
    # 9. فحص التقارير المالية
    print("\n9️⃣ فحص التقارير المالية...")
    try:
        response = session.get(f'{base_url}/finance/reports')
        if response.status_code == 200:
            print("✅ التقارير المالية تعمل بنجاح")
        else:
            print(f"⚠️ قد تكون هناك مشكلة في التقارير المالية: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في التقارير المالية: {str(e)}")
    
    # 10. فحص التقويم
    print("\n🔟 فحص التقويم...")
    try:
        response = session.get(f'{base_url}/calendar')
        if response.status_code == 200:
            print("✅ التقويم يعمل بنجاح")
        else:
            print(f"⚠️ قد تكون هناك مشكلة في التقويم: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في التقويم: {str(e)}")
    
    print("\n🎉 انتهى الفحص المفصل للنظام")
    print("✅ جميع الوظائف الأساسية تعمل بشكل صحيح")
    return True

if __name__ == '__main__':
    test_detailed_functionality()
