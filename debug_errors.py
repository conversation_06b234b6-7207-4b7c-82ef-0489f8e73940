#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys

def debug_specific_errors():
    """فحص الأخطاء المحددة بالتفصيل"""
    base_url = 'http://localhost:5000'
    
    # تسجيل الدخول أولاً
    session = requests.Session()
    login_data = {
        'username': 'office',
        'password': '66889088'
    }
    
    try:
        print("🔐 تسجيل الدخول...")
        response = session.post(f'{base_url}/lawyersameh', data=login_data)
        if response.status_code != 200 or 'dashboard' not in response.url:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return
        print("✅ تم تسجيل الدخول بنجاح")
        
        # فحص صفحة إضافة السند المالي
        print("\n🔍 فحص صفحة إضافة السند المالي...")
        try:
            response = session.get(f'{base_url}/finance/add')
            print(f"📊 كود الاستجابة: {response.status_code}")
            if response.status_code == 500:
                print("❌ خطأ 500 في صفحة إضافة السند المالي")
                print(f"📄 محتوى الخطأ: {response.text[:500]}...")
            elif response.status_code == 200:
                print("✅ صفحة إضافة السند المالي تعمل بنجاح")
            else:
                print(f"⚠️ كود غير متوقع: {response.status_code}")
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {str(e)}")
        
        # فحص صفحة التقارير المالية
        print("\n🔍 فحص صفحة التقارير المالية...")
        try:
            response = session.get(f'{base_url}/finance/reports')
            print(f"📊 كود الاستجابة: {response.status_code}")
            if response.status_code == 500:
                print("❌ خطأ 500 في التقارير المالية")
                print(f"📄 محتوى الخطأ: {response.text[:500]}...")
            elif response.status_code == 200:
                print("✅ التقارير المالية تعمل بنجاح")
            else:
                print(f"⚠️ كود غير متوقع: {response.status_code}")
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {str(e)}")
            
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")

if __name__ == "__main__":
    debug_specific_errors()
