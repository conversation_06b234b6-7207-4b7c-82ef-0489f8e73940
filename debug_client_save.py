#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup

BASE_URL = "http://localhost:5000"

def debug_client_save():
    """تشخيص مشكلة حفظ العميل"""
    print("🔍 تشخيص مشكلة حفظ العميل...")
    
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {'username': 'office', 'password': '66889088'}
    login_response = session.post(f"{BASE_URL}/lawyersameh", data=login_data)
    
    if login_response.status_code != 200:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # الحصول على عدد العملاء قبل الإضافة
    clients_before = session.get(f"{BASE_URL}/clients")
    soup_before = BeautifulSoup(clients_before.text, 'html.parser')
    clients_count_before = len(soup_before.find_all('tr')) - 1  # -1 للهيدر
    print(f"📊 عدد العملاء قبل الإضافة: {clients_count_before}")
    
    # الحصول على CSRF token
    modal_response = session.get(f"{BASE_URL}/modal/add_client")
    soup = BeautifulSoup(modal_response.text, 'html.parser')
    csrf_input = soup.find('input', {'name': 'csrf_token'})
    csrf_token = csrf_input.get('value')
    
    # إضافة عميل جديد
    client_data = {
        'name': 'عميل تشخيص المشكلة',
        'phone': '0501234571',
        'email': '<EMAIL>',
        'address': 'عنوان تشخيص',
        'role': 'موكل',
        'csrf_token': csrf_token
    }
    
    print("🔄 محاولة إضافة عميل...")
    add_response = session.post(f"{BASE_URL}/clients/add", data=client_data)
    
    print(f"📊 رمز الاستجابة: {add_response.status_code}")
    print(f"📄 محتوى الاستجابة: {add_response.text[:300]}...")
    
    # فحص العملاء بعد الإضافة
    clients_after = session.get(f"{BASE_URL}/clients")
    soup_after = BeautifulSoup(clients_after.text, 'html.parser')
    clients_count_after = len(soup_after.find_all('tr')) - 1
    print(f"📊 عدد العملاء بعد الإضافة: {clients_count_after}")
    
    if clients_count_after > clients_count_before:
        print("✅ تم إضافة العميل بنجاح!")
        return True
    else:
        print("❌ لم يتم إضافة العميل")
        
        # البحث عن رسائل الخطأ
        if 'error' in add_response.text.lower() or 'خطأ' in add_response.text:
            print("⚠️ يوجد رسالة خطأ في الاستجابة")
        
        return False

if __name__ == "__main__":
    print("🚀 بدء تشخيص مشكلة حفظ العميل...")
    print("=" * 50)
    
    success = debug_client_save()
    
    print("=" * 50)
    if success:
        print("🎉 العميل تم حفظه بنجاح!")
    else:
        print("💥 هناك مشكلة في حفظ العميل")
