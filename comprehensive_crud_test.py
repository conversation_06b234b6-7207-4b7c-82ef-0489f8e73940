#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع عمليات CRUD
"""

import requests
import re
from datetime import datetime

BASE_URL = "http://localhost:5000"
LOGIN_DATA = {
    'username': 'office',
    'password': '66889088'
}

class ComprehensiveCRUDTester:
    def __init__(self):
        self.session = requests.Session()
        
    def login(self):
        """تسجيل الدخول"""
        print("🔐 تسجيل الدخول...")
        login_page = self.session.get(f"{BASE_URL}/lawyersameh")
        csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', login_page.text)
        csrf_token = csrf_match.group(1) if csrf_match else None
        
        if not csrf_token:
            print("❌ لم يتم العثور على CSRF token في صفحة تسجيل الدخول")
            return False
        
        login_data = LOGIN_DATA.copy()
        login_data['csrf_token'] = csrf_token
        
        response = self.session.post(f"{BASE_URL}/lawyersameh", data=login_data)
        if response.status_code != 200 or "dashboard" not in response.url:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        return True
    
    def test_client_crud(self):
        """اختبار عمليات CRUD للعملاء"""
        print("\n👤 اختبار عمليات CRUD للعملاء...")
        
        # 1. اختبار الإضافة (Create)
        print("  ➕ اختبار إضافة عميل جديد...")
        add_page = self.session.get(f"{BASE_URL}/clients/add")
        csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', add_page.text)
        
        if csrf_match:
            client_data = {
                'name': f'عميل اختبار {datetime.now().strftime("%H%M%S")}',
                'phone': '0123456789',
                'email': '<EMAIL>',
                'address': 'عنوان اختبار',
                'role': 'موكل',
                'csrf_token': csrf_match.group(1)
            }
            
            response = self.session.post(f"{BASE_URL}/clients/add", data=client_data)
            if response.status_code == 200 and "clients" in response.url:
                print("    ✅ إضافة العميل تعمل بنجاح")
            else:
                print(f"    ❌ فشل في إضافة العميل: {response.status_code}")
                return False
        else:
            print("    ❌ لم يتم العثور على CSRF token")
            return False
        
        # 2. اختبار القراءة (Read)
        print("  📖 اختبار قراءة قائمة العملاء...")
        clients_page = self.session.get(f"{BASE_URL}/clients")
        if clients_page.status_code == 200:
            print("    ✅ قراءة قائمة العملاء تعمل بنجاح")
        else:
            print(f"    ❌ فشل في قراءة قائمة العملاء: {clients_page.status_code}")
            return False
        
        # 3. اختبار التعديل (Update)
        print("  ✏️ اختبار تعديل العميل...")
        client_id_match = re.search(r'/clients/(\d+)/edit', clients_page.text)
        if client_id_match:
            client_id = client_id_match.group(1)
            edit_page = self.session.get(f"{BASE_URL}/clients/{client_id}/edit")
            
            if edit_page.status_code == 200:
                csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', edit_page.text)
                if csrf_match:
                    edit_data = {
                        'name': 'عميل محدث',
                        'phone': '0987654321',
                        'email': '<EMAIL>',
                        'address': 'عنوان محدث',
                        'role': 'موكل',
                        'csrf_token': csrf_match.group(1)
                    }
                    
                    response = self.session.post(f"{BASE_URL}/clients/{client_id}/edit", data=edit_data)
                    if response.status_code == 200 and "clients" in response.url:
                        print("    ✅ تعديل العميل يعمل بنجاح")
                    else:
                        print(f"    ❌ فشل في تعديل العميل: {response.status_code}")
                        return False
                else:
                    print("    ❌ لم يتم العثور على CSRF token في صفحة التعديل")
                    return False
            else:
                print(f"    ❌ فشل في الوصول لصفحة التعديل: {edit_page.status_code}")
                return False
        else:
            print("    ❌ لم يتم العثور على عميل للتعديل")
            return False
        
        # 4. اختبار الحذف (Delete)
        print("  🗑️ اختبار حذف العميل...")
        # الحصول على CSRF token من meta tag
        csrf_match = re.search(r'name="csrf-token" content="([^"]+)"', clients_page.text)
        headers = {}
        if csrf_match:
            headers['X-CSRFToken'] = csrf_match.group(1)
        
        response = self.session.post(f"{BASE_URL}/clients/{client_id}/delete", headers=headers)
        if response.status_code == 200:
            print("    ✅ حذف العميل يعمل بنجاح")
        else:
            print(f"    ❌ فشل في حذف العميل: {response.status_code}")
            return False
        
        return True
    
    def test_case_crud(self):
        """اختبار عمليات CRUD للقضايا"""
        print("\n⚖️ اختبار عمليات CRUD للقضايا...")
        
        # 1. اختبار الإضافة
        print("  ➕ اختبار إضافة قضية جديدة...")
        add_page = self.session.get(f"{BASE_URL}/cases/add")
        csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', add_page.text)
        
        if csrf_match:
            case_data = {
                'title': f'قضية اختبار {datetime.now().strftime("%H%M%S")}',
                'case_number': f'TEST{datetime.now().strftime("%H%M%S")}',
                'court': 'محكمة اختبار',
                'case_type': 'مدني',
                'status': 'جديدة',
                'client_id': '1',  # افتراض وجود عميل برقم 1
                'csrf_token': csrf_match.group(1)
            }
            
            response = self.session.post(f"{BASE_URL}/cases/add", data=case_data)
            if response.status_code == 200 and "cases" in response.url:
                print("    ✅ إضافة القضية تعمل بنجاح")
            else:
                print(f"    ❌ فشل في إضافة القضية: {response.status_code}")
                return False
        else:
            print("    ❌ لم يتم العثور على CSRF token")
            return False
        
        # 2. اختبار القراءة
        print("  📖 اختبار قراءة قائمة القضايا...")
        cases_page = self.session.get(f"{BASE_URL}/cases")
        if cases_page.status_code == 200:
            print("    ✅ قراءة قائمة القضايا تعمل بنجاح")
        else:
            print("    ❌ فشل في قراءة قائمة القضايا")
            return False
        
        # 3. اختبار التعديل
        print("  ✏️ اختبار تعديل القضية...")
        case_id_match = re.search(r'/cases/(\d+)/edit', cases_page.text)
        if case_id_match:
            case_id = case_id_match.group(1)
            edit_page = self.session.get(f"{BASE_URL}/cases/{case_id}/edit")
            
            if edit_page.status_code == 200:
                print("    ✅ صفحة تعديل القضية تعمل بنجاح")
            else:
                print(f"    ❌ فشل في الوصول لصفحة تعديل القضية: {edit_page.status_code}")
                return False
        else:
            print("    ❌ لم يتم العثور على قضية للتعديل")
            return False
        
        return True
    
    def test_property_crud(self):
        """اختبار عمليات CRUD للعقارات"""
        print("\n🏢 اختبار عمليات CRUD للعقارات...")
        
        # اختبار القراءة
        print("  📖 اختبار قراءة قائمة العقارات...")
        properties_page = self.session.get(f"{BASE_URL}/properties")
        if properties_page.status_code == 200:
            print("    ✅ قراءة قائمة العقارات تعمل بنجاح")
        else:
            print("    ❌ فشل في قراءة قائمة العقارات")
            return False
        
        # اختبار صفحة الإضافة
        print("  ➕ اختبار صفحة إضافة عقار...")
        add_page = self.session.get(f"{BASE_URL}/properties/add")
        if add_page.status_code == 200:
            print("    ✅ صفحة إضافة العقار تعمل بنجاح")
        else:
            print("    ❌ فشل في الوصول لصفحة إضافة العقار")
            return False
        
        return True
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء الاختبار الشامل لعمليات CRUD...")
        print("=" * 60)
        
        if not self.login():
            return False
        
        results = []
        results.append(self.test_client_crud())
        results.append(self.test_case_crud())
        results.append(self.test_property_crud())
        
        print("\n" + "=" * 60)
        print("📊 النتائج النهائية:")
        
        success_count = sum(results)
        total_count = len(results)
        
        print(f"✅ الاختبارات الناجحة: {success_count}/{total_count}")
        print(f"❌ الاختبارات الفاشلة: {total_count - success_count}/{total_count}")
        
        if success_count == total_count:
            print("🎉 جميع عمليات CRUD تعمل بشكل صحيح!")
            return True
        else:
            print("⚠️ هناك مشاكل تحتاج إلى إصلاح")
            return False

if __name__ == "__main__":
    tester = ComprehensiveCRUDTester()
    success = tester.run_all_tests()
    exit(0 if success else 1)
