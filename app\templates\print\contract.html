{% extends "print/base_print.html" %}

{% block title %}عقد {{ contract_type }} - {{ contract_number }}{% endblock %}

{% block content %}
<div class="document-title">
    عقد {{ contract_type }}
</div>

<div class="document-info">
    <div class="row">
        <div class="col-md-6">
            <span class="label">رقم العقد:</span>
            <span class="value">{{ contract_number }}</span>
        </div>
        <div class="col-md-6">
            <span class="label">تاريخ العقد:</span>
            <span class="value">{{ contract_date }}</span>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <span class="label">مدة العقد:</span>
            <span class="value">{{ contract_duration }}</span>
        </div>
        <div class="col-md-6">
            <span class="label">حالة العقد:</span>
            <span class="value">{{ contract_status }}</span>
        </div>
    </div>
</div>

<!-- أطراف العقد -->
<div class="content-section">
    <h3><i class="fa fa-users"></i> أطراف العقد</h3>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fa fa-user"></i> الطرف الأول</h5>
                </div>
                <div class="card-body">
                    <p><strong>الاسم:</strong> {{ first_party.name }}</p>
                    <p><strong>رقم الهوية:</strong> {{ first_party.id_number }}</p>
                    <p><strong>العنوان:</strong> {{ first_party.address }}</p>
                    <p><strong>الهاتف:</strong> {{ first_party.phone }}</p>
                    {% if first_party.email %}
                    <p><strong>البريد الإلكتروني:</strong> {{ first_party.email }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fa fa-user"></i> الطرف الثاني</h5>
                </div>
                <div class="card-body">
                    <p><strong>الاسم:</strong> {{ second_party.name }}</p>
                    <p><strong>رقم الهوية:</strong> {{ second_party.id_number }}</p>
                    <p><strong>العنوان:</strong> {{ second_party.address }}</p>
                    <p><strong>الهاتف:</strong> {{ second_party.phone }}</p>
                    {% if second_party.email %}
                    <p><strong>البريد الإلكتروني:</strong> {{ second_party.email }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تفاصيل العقد -->
{% if contract_type == 'إيجار' %}
<div class="content-section">
    <h3><i class="fa fa-home"></i> تفاصيل العقار المؤجر</h3>
    
    <div class="document-info">
        <div class="row">
            <div class="col-md-6">
                <span class="label">اسم العقار:</span>
                <span class="value">{{ property.name }}</span>
            </div>
            <div class="col-md-6">
                <span class="label">نوع العقار:</span>
                <span class="value">{{ property.type }}</span>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <span class="label">العنوان الكامل:</span>
                <span class="value">{{ property.address }}</span>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <span class="label">المساحة:</span>
                <span class="value">{{ property.area }} متر مربع</span>
            </div>
            <div class="col-md-4">
                <span class="label">عدد الغرف:</span>
                <span class="value">{{ property.rooms }}</span>
            </div>
            <div class="col-md-4">
                <span class="label">عدد الحمامات:</span>
                <span class="value">{{ property.bathrooms }}</span>
            </div>
        </div>
    </div>
</div>

<div class="content-section">
    <h3><i class="fa fa-money-bill"></i> الشروط المالية</h3>
    
    <div class="financial-summary">
        <div class="row">
            <div class="col-md-3">
                <div class="text-center">
                    <div class="label">قيمة الإيجار الشهري</div>
                    <div class="amount">{{ lease.monthly_rent | number_format }} {{ lease.currency }}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <div class="label">مبلغ التأمين</div>
                    <div class="amount">{{ lease.security_deposit | number_format }} {{ lease.currency }}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <div class="label">رسوم الوساطة</div>
                    <div class="amount">{{ lease.broker_fee | number_format }} {{ lease.currency }}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <div class="label">إجمالي المبلغ المستحق</div>
                    <div class="amount">{{ lease.total_amount | number_format }} {{ lease.currency }}</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- بنود العقد -->
<div class="content-section">
    <h3><i class="fa fa-list"></i> بنود وشروط العقد</h3>
    
    <div class="contract-terms">
        <h5>البند الأول: موضوع العقد</h5>
        <p>{{ contract.clause_1 | default('يتفق الطرفان على ' + contract_type + ' وفقاً للشروط والأحكام المنصوص عليها في هذا العقد.') }}</p>
        
        <h5>البند الثاني: مدة العقد</h5>
        <p>{{ contract.clause_2 | default('تبدأ مدة العقد من تاريخ ' + contract.start_date + ' وتنتهي في تاريخ ' + contract.end_date + '.') }}</p>
        
        <h5>البند الثالث: الالتزامات المالية</h5>
        <p>{{ contract.clause_3 | default('يلتزم الطرف الثاني بدفع المبالغ المستحقة في المواعيد المحددة دون تأخير.') }}</p>
        
        <h5>البند الرابع: التزامات الأطراف</h5>
        <p>{{ contract.clause_4 | default('يلتزم كل طرف بتنفيذ التزاماته وفقاً لما هو منصوص عليه في هذا العقد.') }}</p>
        
        <h5>البند الخامس: إنهاء العقد</h5>
        <p>{{ contract.clause_5 | default('يمكن إنهاء العقد بموافقة الطرفين أو وفقاً للشروط المنصوص عليها.') }}</p>
        
        <h5>البند السادس: فض النزاعات</h5>
        <p>{{ contract.clause_6 | default('في حالة نشوء أي نزاع، يتم حله ودياً أو عن طريق المحاكم المختصة.') }}</p>
        
        {% if contract.additional_terms %}
        <h5>بنود إضافية:</h5>
        <div>{{ contract.additional_terms | nl2br }}</div>
        {% endif %}
    </div>
</div>

<!-- جدول الدفعات (للعقود الإيجارية) -->
{% if contract_type == 'إيجار' and payment_schedule %}
<div class="content-section page-break-inside-avoid">
    <h3><i class="fa fa-calendar"></i> جدول الدفعات</h3>
    
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>الشهر</th>
                <th>تاريخ الاستحقاق</th>
                <th>المبلغ المستحق</th>
                <th>حالة الدفع</th>
                <th>تاريخ الدفع الفعلي</th>
                <th>ملاحظات</th>
            </tr>
        </thead>
        <tbody>
            {% for payment in payment_schedule %}
            <tr>
                <td>{{ payment.month }}</td>
                <td>{{ payment.due_date }}</td>
                <td class="amount">{{ payment.amount | number_format }}</td>
                <td>
                    <span class="badge {% if payment.status == 'مدفوع' %}bg-success{% elif payment.status == 'متأخر' %}bg-danger{% else %}bg-warning{% endif %}">
                        {{ payment.status }}
                    </span>
                </td>
                <td>{{ payment.paid_date or '-' }}</td>
                <td>{{ payment.notes or '-' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- الشهود -->
{% if witnesses %}
<div class="content-section">
    <h3><i class="fa fa-eye"></i> الشهود</h3>
    
    <div class="row">
        {% for witness in witnesses %}
        <div class="col-md-6">
            <div class="card mb-3">
                <div class="card-body">
                    <h6>الشاهد {{ loop.index }}</h6>
                    <p><strong>الاسم:</strong> {{ witness.name }}</p>
                    <p><strong>رقم الهوية:</strong> {{ witness.id_number }}</p>
                    <p><strong>الهاتف:</strong> {{ witness.phone }}</p>
                    <div style="margin-top: 30px; border-top: 1px solid #000; text-align: center; padding-top: 10px;">
                        التوقيع
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<!-- إقرار وموافقة -->
<div class="content-section">
    <h3><i class="fa fa-check-circle"></i> إقرار وموافقة</h3>
    
    <div class="alert alert-info">
        <p><strong>إقرار الطرف الأول:</strong></p>
        <p>أقر أنا الموقع أدناه بأنني قد قرأت جميع بنود هذا العقد وفهمتها جيداً، وأوافق على جميع الشروط والأحكام المذكورة فيه، وأتعهد بالالتزام بها.</p>
        
        <p><strong>إقرار الطرف الثاني:</strong></p>
        <p>أقر أنا الموقع أدناه بأنني قد قرأت جميع بنود هذا العقد وفهمتها جيداً، وأوافق على جميع الشروط والأحكام المذكورة فيه، وأتعهد بالالتزام بها.</p>
    </div>
</div>

{% endblock %}

{% block signatures %}
<div class="signature-section">
    <div class="signature-box">
        <div>الطرف الأول</div>
        <div style="margin-top: 40px;">{{ first_party.name }}</div>
        <div style="margin-top: 10px;">________________</div>
    </div>
    <div class="signature-box">
        <div>الطرف الثاني</div>
        <div style="margin-top: 40px;">{{ second_party.name }}</div>
        <div style="margin-top: 10px;">________________</div>
    </div>
    <div class="signature-box">
        <div>المحامي المشرف</div>
        <div style="margin-top: 40px;">سامح أبو عودة</div>
        <div style="margin-top: 10px;">________________</div>
    </div>
</div>
{% endblock %}
