/**
 * نظام التقارير الشامل - التنسيقات
 * Comprehensive Reports System - Styles
 */

/* ==================== متغيرات CSS ==================== */
:root {
    --report-primary: #2c3e50;
    --report-secondary: #34495e;
    --report-success: #27ae60;
    --report-info: #3498db;
    --report-warning: #f39c12;
    --report-danger: #e74c3c;
    --report-light: #ecf0f1;
    --report-dark: #2c3e50;
    
    --report-border-radius: 8px;
    --report-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --report-transition: all 0.3s ease;
}

/* ==================== تخطيط التقارير الرئيسي ==================== */
.reports-container {
    padding: 20px;
    background: var(--bs-body-bg);
    min-height: 100vh;
}

.reports-header {
    background: linear-gradient(135deg, var(--report-primary), var(--report-secondary));
    color: white;
    padding: 30px;
    border-radius: var(--report-border-radius);
    margin-bottom: 30px;
    box-shadow: var(--report-shadow);
}

.reports-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.reports-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* ==================== أزرار التقارير السريعة ==================== */
.quick-reports-section {
    margin-bottom: 30px;
}

.quick-report-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--report-border-radius);
    padding: 20px;
    text-align: center;
    transition: var(--report-transition);
    cursor: pointer;
    height: 100%;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.quick-report-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--report-shadow);
    border-color: var(--report-primary);
}

.quick-report-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--report-primary);
}

.quick-report-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--report-dark);
}

.quick-report-description {
    color: #6c757d;
    font-size: 0.9rem;
}

/* ==================== فلاتر التقارير ==================== */
.report-filters {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--report-border-radius);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.filter-group {
    margin-bottom: 20px;
}

.filter-label {
    font-weight: 600;
    color: var(--report-dark);
    margin-bottom: 8px;
    display: block;
}

.filter-input {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px 15px;
    font-size: 0.95rem;
    transition: var(--report-transition);
}

.filter-input:focus {
    border-color: var(--report-primary);
    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
}

/* ==================== منطقة عرض التقارير ==================== */
.report-display-area {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--report-border-radius);
    padding: 30px;
    box-shadow: var(--report-shadow);
    min-height: 500px;
}

.report-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.report-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--report-dark);
    margin-bottom: 10px;
}

.report-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
}

.report-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.report-metadata {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-top: 20px;
}

/* ==================== أقسام التقرير ==================== */
.report-section {
    margin-bottom: 40px;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--report-dark);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--report-primary);
}

/* ==================== بطاقات الملخص ==================== */
.summary-section {
    margin-bottom: 40px;
}

.summary-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--report-border-radius);
    padding: 20px;
    text-align: center;
    transition: var(--report-transition);
    height: 100%;
    position: relative;
    overflow: hidden;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--report-primary);
}

.summary-card.bg-primary::before { background: var(--report-primary); }
.summary-card.bg-success::before { background: var(--report-success); }
.summary-card.bg-info::before { background: var(--report-info); }
.summary-card.bg-warning::before { background: var(--report-warning); }
.summary-card.bg-danger::before { background: var(--report-danger); }

.summary-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--report-primary);
}

.summary-card.bg-primary .summary-icon { color: var(--report-primary); }
.summary-card.bg-success .summary-icon { color: var(--report-success); }
.summary-card.bg-info .summary-icon { color: var(--report-info); }
.summary-card.bg-warning .summary-icon { color: var(--report-warning); }
.summary-card.bg-danger .summary-icon { color: var(--report-danger); }

.summary-content h4 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: var(--report-dark);
}

.summary-content p {
    color: #6c757d;
    margin-bottom: 10px;
    font-weight: 500;
}

.change {
    font-size: 0.85rem;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
}

.change.positive {
    background: #d4edda;
    color: #155724;
}

.change.negative {
    background: #f8d7da;
    color: #721c24;
}

/* ==================== الرسوم البيانية ==================== */
.charts-section {
    margin-bottom: 40px;
}

.chart-container {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--report-border-radius);
    padding: 20px;
    height: 400px;
    position: relative;
}

.chart-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--report-dark);
    margin-bottom: 15px;
    text-align: center;
}

.report-chart {
    max-height: 300px;
}

/* ==================== الجداول ==================== */
.tables-section {
    margin-bottom: 40px;
}

.table-container {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--report-border-radius);
    overflow: hidden;
}

.table-title {
    background: #f8f9fa;
    padding: 15px 20px;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--report-dark);
    border-bottom: 1px solid #e9ecef;
}

.table-responsive {
    max-height: 500px;
    overflow-y: auto;
}

.table {
    margin-bottom: 0;
}

.table th {
    background: var(--report-dark);
    color: white;
    font-weight: 600;
    border: none;
    padding: 12px 15px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* ==================== التحليل والتوصيات ==================== */
.analysis-section {
    margin-bottom: 40px;
}

.analysis-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: var(--report-border-radius);
    padding: 20px;
    margin-bottom: 20px;
}

.analysis-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--report-dark);
    margin-bottom: 15px;
}

.analysis-content {
    color: #495057;
    line-height: 1.6;
    margin-bottom: 15px;
}

.recommendations {
    background: white;
    border: 1px solid #d1ecf1;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
}

.recommendations h6 {
    color: var(--report-info);
    font-weight: 600;
    margin-bottom: 10px;
}

.recommendations ul {
    margin-bottom: 0;
    padding-right: 20px;
}

.recommendations li {
    margin-bottom: 5px;
    color: #495057;
}

/* ==================== مؤشر التحميل ==================== */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 30px;
    border-radius: var(--report-border-radius);
    text-align: center;
    box-shadow: var(--report-shadow);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--report-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-message {
    font-size: 1.1rem;
    color: var(--report-dark);
    margin: 0;
}

/* ==================== التصميم المتجاوب ==================== */
@media (max-width: 768px) {
    .reports-container {
        padding: 15px;
    }
    
    .reports-header {
        padding: 20px;
        text-align: center;
    }
    
    .reports-title {
        font-size: 2rem;
    }
    
    .report-actions {
        justify-content: center;
        margin-top: 15px;
    }
    
    .chart-container {
        height: 300px;
    }
    
    .summary-card {
        margin-bottom: 15px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* ==================== الطباعة ==================== */
@media print {
    .reports-container {
        padding: 0;
    }
    
    .report-actions,
    .no-print {
        display: none !important;
    }
    
    .report-section {
        page-break-inside: avoid;
        margin-bottom: 30px;
    }
    
    .chart-container,
    .table-container {
        page-break-inside: avoid;
    }
    
    .summary-card {
        border: 2px solid #ddd;
        margin-bottom: 15px;
    }
    
    .table {
        font-size: 0.85rem;
    }
    
    .analysis-item {
        border: 2px solid #ddd;
    }
}

/* ==================== الوضع المظلم ==================== */
[data-theme="dark"] {
    --report-primary: #3498db;
    --report-secondary: #2980b9;
    --report-light: #34495e;
    --report-dark: #ecf0f1;
}

[data-theme="dark"] .quick-report-card,
[data-theme="dark"] .report-filters,
[data-theme="dark"] .report-display-area,
[data-theme="dark"] .chart-container,
[data-theme="dark"] .table-container,
[data-theme="dark"] .summary-card {
    background: #2c3e50;
    border-color: #34495e;
    color: #ecf0f1;
}

[data-theme="dark"] .table th {
    background: #34495e;
}

[data-theme="dark"] .table tbody tr:hover {
    background: #34495e;
}

[data-theme="dark"] .analysis-item {
    background: #34495e;
    border-color: #2c3e50;
}

[data-theme="dark"] .recommendations {
    background: #2c3e50;
    border-color: #34495e;
}
