#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت اختبار شامل لوظائف الحفظ في النظام
يختبر جميع نماذج الإضافة للتأكد من عملها بشكل صحيح
"""

import requests
import json
import time
from datetime import datetime, timedelta

# إعدادات الاختبار
BASE_URL = "http://localhost:5000"
LOGIN_DATA = {
    'username': 'office',
    'password': '66889088'
}

class SaveFunctionalityTester:
    def __init__(self):
        self.session = requests.Session()
        self.csrf_token = None
        self.test_results = []
        
    def login(self):
        """تسجيل الدخول والحصول على CSRF token"""
        print("🔐 تسجيل الدخول...")
        
        # الحصول على صفحة تسجيل الدخول
        login_page = self.session.get(f"{BASE_URL}/lawyersameh")
        if login_page.status_code != 200:
            raise Exception(f"فشل في الوصول لصفحة تسجيل الدخول: {login_page.status_code}")
        
        # استخراج CSRF token
        import re
        csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', login_page.text)
        if csrf_match:
            self.csrf_token = csrf_match.group(1)
            print(f"✅ تم الحصول على CSRF token: {self.csrf_token[:20]}...")
        else:
            raise Exception("لم يتم العثور على CSRF token")
        
        # تسجيل الدخول
        login_data = LOGIN_DATA.copy()
        login_data['csrf_token'] = self.csrf_token
        
        response = self.session.post(f"{BASE_URL}/lawyersameh", data=login_data)
        if response.status_code == 200 and "dashboard" in response.url:
            print("✅ تم تسجيل الدخول بنجاح")
            return True
        else:
            raise Exception(f"فشل في تسجيل الدخول: {response.status_code}")
    
    def get_csrf_token_from_page(self, url):
        """الحصول على CSRF token من صفحة معينة"""
        page = self.session.get(url)
        if page.status_code != 200:
            return None
            
        import re
        csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', page.text)
        return csrf_match.group(1) if csrf_match else None
    
    def test_client_save(self):
        """اختبار حفظ عميل جديد"""
        print("\n👤 اختبار حفظ عميل جديد...")
        
        # الحصول على CSRF token من صفحة إضافة العميل
        csrf_token = self.get_csrf_token_from_page(f"{BASE_URL}/clients/add")
        if not csrf_token:
            self.test_results.append({"test": "client_save", "status": "failed", "error": "لم يتم العثور على CSRF token"})
            return False
        
        client_data = {
            'csrf_token': csrf_token,
            'name': f'عميل اختبار {datetime.now().strftime("%H:%M:%S")}',
            'id_number': f'123456{datetime.now().strftime("%H%M%S")}',
            'phone': '0599123456',
            'email': '<EMAIL>',
            'address': 'عنوان اختبار',
            'notes': 'ملاحظات اختبار'
        }
        
        response = self.session.post(f"{BASE_URL}/clients/add", data=client_data)
        
        if response.status_code == 200 and ("تمت إضافة العميل بنجاح" in response.text or response.url.endswith("/clients")):
            print("✅ تم حفظ العميل بنجاح")
            self.test_results.append({"test": "client_save", "status": "success"})
            return True
        else:
            print(f"❌ فشل في حفظ العميل: {response.status_code}")
            self.test_results.append({"test": "client_save", "status": "failed", "error": f"Status: {response.status_code}"})
            return False
    
    def test_case_save(self):
        """اختبار حفظ قضية جديدة"""
        print("\n⚖️ اختبار حفظ قضية جديدة...")
        
        # الحصول على CSRF token من صفحة إضافة القضية
        csrf_token = self.get_csrf_token_from_page(f"{BASE_URL}/cases/add")
        if not csrf_token:
            self.test_results.append({"test": "case_save", "status": "failed", "error": "لم يتم العثور على CSRF token"})
            return False
        
        case_data = {
            'csrf_token': csrf_token,
            'title': f'قضية اختبار {datetime.now().strftime("%H:%M:%S")}',
            'client_id': '1',  # افتراض وجود عميل برقم 1
            'client_role': 'مدعي',
            'type': 'مدني',
            'status': 'جارية',
            'description': 'وصف قضية اختبار',
            'fees_total': '1000',
            'fees_currency': 'شيكل',
            'fees_paid': '500',
            'court_fees_total': '200',
            'court_fees_paid': '100'
        }
        
        response = self.session.post(f"{BASE_URL}/cases/add", data=case_data)
        
        if response.status_code == 200 and ("تمت إضافة القضية بنجاح" in response.text or response.url.endswith("/cases")):
            print("✅ تم حفظ القضية بنجاح")
            self.test_results.append({"test": "case_save", "status": "success"})
            return True
        else:
            print(f"❌ فشل في حفظ القضية: {response.status_code}")
            self.test_results.append({"test": "case_save", "status": "failed", "error": f"Status: {response.status_code}"})
            return False
    
    def test_property_save(self):
        """اختبار حفظ عقار جديد"""
        print("\n🏢 اختبار حفظ عقار جديد...")
        
        csrf_token = self.get_csrf_token_from_page(f"{BASE_URL}/properties/add")
        if not csrf_token:
            self.test_results.append({"test": "property_save", "status": "failed", "error": "لم يتم العثور على CSRF token"})
            return False
        
        property_data = {
            'csrf_token': csrf_token,
            'name': f'عقار اختبار {datetime.now().strftime("%H:%M:%S")}',
            'property_type': 'مكتب',
            'address': 'عنوان العقار',
            'area': '100',
            'monthly_rent': '1500',
            'description': 'وصف العقار'
        }
        
        response = self.session.post(f"{BASE_URL}/properties/add", data=property_data)
        
        if response.status_code == 200 and ("تمت إضافة العقار بنجاح" in response.text or response.url.endswith("/properties")):
            print("✅ تم حفظ العقار بنجاح")
            self.test_results.append({"test": "property_save", "status": "success"})
            return True
        else:
            print(f"❌ فشل في حفظ العقار: {response.status_code}")
            self.test_results.append({"test": "property_save", "status": "failed", "error": f"Status: {response.status_code}"})
            return False
    
    def test_tenant_save(self):
        """اختبار حفظ مستأجر جديد"""
        print("\n👨‍💼 اختبار حفظ مستأجر جديد...")
        
        csrf_token = self.get_csrf_token_from_page(f"{BASE_URL}/tenants/add")
        if not csrf_token:
            self.test_results.append({"test": "tenant_save", "status": "failed", "error": "لم يتم العثور على CSRF token"})
            return False
        
        tenant_data = {
            'csrf_token': csrf_token,
            'name': f'مستأجر اختبار {datetime.now().strftime("%H:%M:%S")}',
            'phone': '0599654321',
            'email': '<EMAIL>',
            'address': 'عنوان المستأجر',
            'notes': 'ملاحظات المستأجر'
        }
        
        response = self.session.post(f"{BASE_URL}/tenants/add", data=tenant_data)
        
        if response.status_code == 200 and ("تمت إضافة المستأجر بنجاح" in response.text or response.url.endswith("/tenants")):
            print("✅ تم حفظ المستأجر بنجاح")
            self.test_results.append({"test": "tenant_save", "status": "success"})
            return True
        else:
            print(f"❌ فشل في حفظ المستأجر: {response.status_code}")
            self.test_results.append({"test": "tenant_save", "status": "failed", "error": f"Status: {response.status_code}"})
            return False
    
    def test_financial_transaction_save(self):
        """اختبار حفظ معاملة مالية جديدة"""
        print("\n💰 اختبار حفظ معاملة مالية جديدة...")
        
        csrf_token = self.get_csrf_token_from_page(f"{BASE_URL}/finance/add")
        if not csrf_token:
            self.test_results.append({"test": "financial_save", "status": "failed", "error": "لم يتم العثور على CSRF token"})
            return False
        
        transaction_data = {
            'csrf_token': csrf_token,
            'type': 'قبض',
            'amount': '500',
            'currency': 'شيكل',
            'description': f'معاملة اختبار {datetime.now().strftime("%H:%M:%S")}',
            'date': datetime.now().strftime('%Y-%m-%d'),
            'client_id': '1',  # افتراض وجود عميل برقم 1
            'case_id': '1',
            'payment_method': 'نقدي',
            'reference_number': '',
            'notes': ''
        }
        
        response = self.session.post(f"{BASE_URL}/finance/add", data=transaction_data)
        
        if response.status_code == 200 and ("تمت إضافة" in response.text or "تم حفظ" in response.text or "نجح" in response.text or "finance" in response.url):
            print("✅ تم حفظ المعاملة المالية بنجاح")
            self.test_results.append({"test": "financial_save", "status": "success"})
            return True
        else:
            print(f"❌ فشل في حفظ المعاملة المالية: {response.status_code}")
            self.test_results.append({"test": "financial_save", "status": "failed", "error": f"Status: {response.status_code}"})
            return False
    
    def test_appointment_save(self):
        """اختبار حفظ موعد جديد"""
        print("\n📅 اختبار حفظ موعد جديد...")
        
        csrf_token = self.get_csrf_token_from_page(f"{BASE_URL}/appointments/add")
        if not csrf_token:
            self.test_results.append({"test": "appointment_save", "status": "failed", "error": "لم يتم العثور على CSRF token"})
            return False
        
        appointment_data = {
            'csrf_token': csrf_token,
            'subject': f'موعد اختبار {datetime.now().strftime("%H:%M:%S")}',
            'date': (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%dT%H:%M'),
            'location': 'مكان الموعد',
            'client_id': '1',  # افتراض وجود عميل برقم 1
            'notes': 'ملاحظات الموعد'
        }
        
        response = self.session.post(f"{BASE_URL}/appointments/add", data=appointment_data)
        
        if response.status_code == 200 and ("تمت إضافة الموعد بنجاح" in response.text or response.url.endswith("/appointments")):
            print("✅ تم حفظ الموعد بنجاح")
            self.test_results.append({"test": "appointment_save", "status": "success"})
            return True
        else:
            print(f"❌ فشل في حفظ الموعد: {response.status_code}")
            self.test_results.append({"test": "appointment_save", "status": "failed", "error": f"Status: {response.status_code}"})
            return False
    
    def test_task_save(self):
        """اختبار حفظ مهمة جديدة"""
        print("\n✅ اختبار حفظ مهمة جديدة...")
        
        csrf_token = self.get_csrf_token_from_page(f"{BASE_URL}/tasks/add")
        if not csrf_token:
            self.test_results.append({"test": "task_save", "status": "failed", "error": "لم يتم العثور على CSRF token"})
            return False
        
        task_data = {
            'csrf_token': csrf_token,
            'title': f'مهمة اختبار {datetime.now().strftime("%H:%M:%S")}',
            'description': 'وصف المهمة',
            'due_date': (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%dT%H:%M'),
            'priority': 'متوسطة',
            'status': 'جديدة'
        }
        
        response = self.session.post(f"{BASE_URL}/tasks/add", data=task_data)
        
        if response.status_code == 200 and ("تمت إضافة المهمة بنجاح" in response.text or "tasks" in response.url):
            print("✅ تم حفظ المهمة بنجاح")
            self.test_results.append({"test": "task_save", "status": "success"})
            return True
        else:
            print(f"❌ فشل في حفظ المهمة: {response.status_code}")
            self.test_results.append({"test": "task_save", "status": "failed", "error": f"Status: {response.status_code}"})
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار وظائف الحفظ الشامل...")
        print("=" * 50)
        
        try:
            # تسجيل الدخول
            self.login()
            
            # تشغيل جميع الاختبارات
            tests = [
                self.test_client_save,
                self.test_case_save,
                self.test_property_save,
                self.test_tenant_save,
                self.test_financial_transaction_save,
                self.test_appointment_save,
                self.test_task_save
            ]
            
            for test in tests:
                try:
                    test()
                    time.sleep(1)  # انتظار قصير بين الاختبارات
                except Exception as e:
                    print(f"❌ خطأ في الاختبار: {e}")
                    self.test_results.append({"test": test.__name__, "status": "error", "error": str(e)})
            
            # عرض النتائج النهائية
            self.show_results()
            
        except Exception as e:
            print(f"❌ خطأ عام في الاختبار: {e}")
    
    def show_results(self):
        """عرض نتائج الاختبارات"""
        print("\n" + "=" * 50)
        print("📊 نتائج اختبار وظائف الحفظ")
        print("=" * 50)
        
        success_count = sum(1 for result in self.test_results if result['status'] == 'success')
        total_count = len(self.test_results)
        
        print(f"✅ نجح: {success_count}/{total_count}")
        print(f"❌ فشل: {total_count - success_count}/{total_count}")
        
        if total_count - success_count > 0:
            print("\n🔍 تفاصيل الأخطاء:")
            for result in self.test_results:
                if result['status'] != 'success':
                    print(f"  - {result['test']}: {result.get('error', 'خطأ غير محدد')}")
        
        print("\n" + "=" * 50)

if __name__ == "__main__":
    tester = SaveFunctionalityTester()
    tester.run_all_tests()
