/**
 * Advanced Search and Filtering System
 * نظام البحث والتصفية المتقدم
 *
 * Features:
 * - Multi-field search across all entities
 * - Advanced filtering with multiple criteria
 * - Real-time search suggestions
 * - Search history and saved searches
 * - Export search results
 * - Global search across all modules
 */

class AdvancedSearchManager {
    constructor() {
        this.searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
        this.savedSearches = JSON.parse(localStorage.getItem('savedSearches') || '[]');
        this.currentFilters = {};
        this.searchResults = [];
        this.searchTimeout = null;
        this.init();
    }

    init() {
        this.createSearchInterface();
        this.bindEvents();
        this.loadSearchHistory();
        this.initializeFilters();
    }

    createSearchInterface() {
        // إنشاء واجهة البحث المتقدم
        const searchContainer = document.createElement('div');
        searchContainer.id = 'advanced-search-container';
        searchContainer.className = 'advanced-search-container';
        searchContainer.innerHTML = `
            <!-- شريط البحث الرئيسي -->
            <div class="main-search-bar">
                <div class="search-input-group">
                    <input type="text" id="global-search-input" class="form-control search-input"
                           placeholder="البحث في جميع أقسام النظام..." autocomplete="off">
                    <div class="search-buttons">
                        <button type="button" class="btn btn-primary search-btn" onclick="advancedSearch.performGlobalSearch()">
                            <i class="fas fa-search"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary filter-btn" onclick="advancedSearch.toggleAdvancedFilters()">
                            <i class="fas fa-filter"></i>
                        </button>
                        <button type="button" class="btn btn-outline-info history-btn" onclick="advancedSearch.showSearchHistory()">
                            <i class="fas fa-history"></i>
                        </button>
                    </div>
                </div>

                <!-- اقتراحات البحث -->
                <div id="search-suggestions" class="search-suggestions"></div>
            </div>

            <!-- الفلاتر المتقدمة -->
            <div id="advanced-filters" class="advanced-filters" style="display: none;">
                <div class="filters-header">
                    <h5><i class="fas fa-sliders-h me-2"></i>الفلاتر المتقدمة</h5>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="advancedSearch.clearAllFilters()">
                        <i class="fas fa-times me-1"></i>مسح الكل
                    </button>
                </div>

                <div class="filters-content">
                    <div class="row">
                        <!-- فلتر نوع البحث -->
                        <div class="col-md-3">
                            <label class="form-label">نوع البحث</label>
                            <select id="search-type" class="form-select">
                                <option value="all">جميع الأقسام</option>
                                <option value="cases">القضايا</option>
                                <option value="clients">العملاء</option>
                                <option value="properties">العقارات</option>
                                <option value="tenants">المستأجرين</option>
                                <option value="financial">المالية</option>
                                <option value="documents">المستندات</option>
                                <option value="calendar">التقويم</option>
                            </select>
                        </div>

                        <!-- فلتر التاريخ -->
                        <div class="col-md-3">
                            <label class="form-label">فترة التاريخ</label>
                            <select id="date-range" class="form-select">
                                <option value="">جميع التواريخ</option>
                                <option value="today">اليوم</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                                <option value="quarter">هذا الربع</option>
                                <option value="year">هذا العام</option>
                                <option value="custom">فترة مخصصة</option>
                            </select>
                        </div>

                        <!-- فلتر الحالة -->
                        <div class="col-md-3">
                            <label class="form-label">الحالة</label>
                            <select id="status-filter" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="pending">معلق</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>

                        <!-- فلتر المبلغ -->
                        <div class="col-md-3">
                            <label class="form-label">نطاق المبلغ</label>
                            <div class="input-group">
                                <input type="number" id="amount-min" class="form-control" placeholder="من">
                                <input type="number" id="amount-max" class="form-control" placeholder="إلى">
                            </div>
                        </div>
                    </div>

                    <!-- فلاتر التاريخ المخصصة -->
                    <div id="custom-date-range" class="row mt-3" style="display: none;">
                        <div class="col-md-6">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" id="date-from" class="form-control">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" id="date-to" class="form-control">
                        </div>
                    </div>

                    <!-- فلاتر إضافية -->
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <label class="form-label">العميل</label>
                            <select id="client-filter" class="form-select">
                                <option value="">جميع العملاء</option>
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label class="form-label">نوع القضية</label>
                            <select id="case-type-filter" class="form-select">
                                <option value="">جميع الأنواع</option>
                                <option value="civil">مدني</option>
                                <option value="criminal">جنائي</option>
                                <option value="commercial">تجاري</option>
                                <option value="family">أحوال شخصية</option>
                                <option value="administrative">إداري</option>
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label class="form-label">الأولوية</label>
                            <select id="priority-filter" class="form-select">
                                <option value="">جميع الأولويات</option>
                                <option value="high">عالية</option>
                                <option value="medium">متوسطة</option>
                                <option value="low">منخفضة</option>
                            </select>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="filters-actions mt-3">
                        <button type="button" class="btn btn-primary" onclick="advancedSearch.applyFilters()">
                            <i class="fas fa-filter me-1"></i>تطبيق الفلاتر
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="advancedSearch.saveCurrentSearch()">
                            <i class="fas fa-save me-1"></i>حفظ البحث
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="advancedSearch.exportResults()">
                            <i class="fas fa-download me-1"></i>تصدير النتائج
                        </button>
                    </div>
                </div>
            </div>

            <!-- نتائج البحث -->
            <div id="search-results" class="search-results"></div>
        `;

        // إضافة الواجهة إلى الصفحة
        const targetContainer = document.querySelector('.container-fluid') || document.body;
        targetContainer.insertBefore(searchContainer, targetContainer.firstChild);
    }

    bindEvents() {
        // ربط أحداث البحث
        const searchInput = document.getElementById('global-search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.showSearchSuggestions(e.target.value);
                }, 300);
            });

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performGlobalSearch();
                }
            });
        }

        // ربط أحداث الفلاتر
        const dateRangeSelect = document.getElementById('date-range');
        if (dateRangeSelect) {
            dateRangeSelect.addEventListener('change', (e) => {
                const customDateRange = document.getElementById('custom-date-range');
                if (e.target.value === 'custom') {
                    customDateRange.style.display = 'block';
                } else {
                    customDateRange.style.display = 'none';
                }
            });
        }

        // ربط أحداث اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            // Ctrl+K للبحث السريع
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                this.focusSearchInput();
            }

            // Ctrl+Shift+F للفلاتر المتقدمة
            if (e.ctrlKey && e.shiftKey && e.key === 'F') {
                e.preventDefault();
                this.toggleAdvancedFilters();
            }
        });
    }

    async performGlobalSearch() {
        const searchTerm = document.getElementById('global-search-input').value.trim();
        if (!searchTerm) {
            this.showAlert('يرجى إدخال كلمة البحث', 'warning');
            return;
        }

        // إضافة إلى تاريخ البحث
        this.addToSearchHistory(searchTerm);

        // إظهار مؤشر التحميل
        this.showLoadingIndicator();

        try {
            // البحث في جميع الأقسام
            const results = await this.searchAllSections(searchTerm);
            this.displaySearchResults(results);
        } catch (error) {
            console.error('خطأ في البحث:', error);
            this.showAlert('حدث خطأ أثناء البحث', 'error');
        } finally {
            this.hideLoadingIndicator();
        }
    }

    async searchAllSections(searchTerm) {
        const searchType = document.getElementById('search-type').value;
        const results = {
            cases: [],
            clients: [],
            properties: [],
            tenants: [],
            financial: [],
            documents: [],
            calendar: [],
            total: 0
        };

        // تحديد الأقسام للبحث فيها
        const sectionsToSearch = searchType === 'all' ?
            ['cases', 'clients', 'properties', 'tenants', 'financial', 'documents', 'calendar'] :
            [searchType];

        // البحث في كل قسم
        for (const section of sectionsToSearch) {
            try {
                const sectionResults = await this.searchInSection(section, searchTerm);
                results[section] = sectionResults;
                results.total += sectionResults.length;
            } catch (error) {
                console.error(`خطأ في البحث في قسم ${section}:`, error);
            }
        }

        return results;
    }

    async searchInSection(section, searchTerm) {
        const filters = this.getCurrentFilters();
        const response = await fetch('/api/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                section: section,
                term: searchTerm,
                filters: filters
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data.results || [];
    }

    getCurrentFilters() {
        return {
            dateRange: document.getElementById('date-range').value,
            dateFrom: document.getElementById('date-from').value,
            dateTo: document.getElementById('date-to').value,
            status: document.getElementById('status-filter').value,
            amountMin: document.getElementById('amount-min').value,
            amountMax: document.getElementById('amount-max').value,
            client: document.getElementById('client-filter').value,
            caseType: document.getElementById('case-type-filter').value,
            priority: document.getElementById('priority-filter').value
        };
    }

    displaySearchResults(results) {
        const resultsContainer = document.getElementById('search-results');
        if (!resultsContainer) return;

        if (results.total === 0) {
            resultsContainer.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>لا توجد نتائج</h4>
                    <p class="text-muted">لم يتم العثور على نتائج مطابقة لبحثك</p>
                </div>
            `;
            return;
        }

        let html = `
            <div class="search-results-header">
                <h4><i class="fas fa-search-plus me-2"></i>نتائج البحث (${results.total})</h4>
                <div class="results-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="advancedSearch.exportResults()">
                        <i class="fas fa-download me-1"></i>تصدير
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="advancedSearch.printResults()">
                        <i class="fas fa-print me-1"></i>طباعة
                    </button>
                </div>
            </div>

            <div class="search-results-tabs">
                <ul class="nav nav-tabs" role="tablist">
        `;

        // إنشاء تبويبات النتائج
        const sections = ['cases', 'clients', 'properties', 'tenants', 'financial', 'documents', 'calendar'];
        const sectionNames = {
            cases: 'القضايا',
            clients: 'العملاء',
            properties: 'العقارات',
            tenants: 'المستأجرين',
            financial: 'المالية',
            documents: 'المستندات',
            calendar: 'التقويم'
        };

        let firstTab = true;
        sections.forEach(section => {
            if (results[section].length > 0) {
                html += `
                    <li class="nav-item">
                        <a class="nav-link ${firstTab ? 'active' : ''}" data-bs-toggle="tab" href="#${section}-results">
                            ${sectionNames[section]} (${results[section].length})
                        </a>
                    </li>
                `;
                firstTab = false;
            }
        });

        html += `
                </ul>
                <div class="tab-content">
        `;

        // إنشاء محتوى التبويبات
        firstTab = true;
        sections.forEach(section => {
            if (results[section].length > 0) {
                html += `
                    <div class="tab-pane fade ${firstTab ? 'show active' : ''}" id="${section}-results">
                        ${this.renderSectionResults(section, results[section])}
                    </div>
                `;
                firstTab = false;
            }
        });

        html += `
                </div>
            </div>
        `;

        resultsContainer.innerHTML = html;
        resultsContainer.style.display = 'block';
    }

    renderSectionResults(section, results) {
        let html = '<div class="section-results">';

        switch(section) {
            case 'cases':
                html += this.renderCasesResults(results);
                break;
            case 'clients':
                html += this.renderClientsResults(results);
                break;
            case 'properties':
                html += this.renderPropertiesResults(results);
                break;
            case 'tenants':
                html += this.renderTenantsResults(results);
                break;
            case 'financial':
                html += this.renderFinancialResults(results);
                break;
            case 'documents':
                html += this.renderDocumentsResults(results);
                break;
            case 'calendar':
                html += this.renderCalendarResults(results);
                break;
        }

        html += '</div>';
        return html;
    }

    renderCasesResults(cases) {
        let html = '<div class="table-responsive"><table class="table table-hover">';
        html += `
            <thead>
                <tr>
                    <th>رقم القضية</th>
                    <th>العنوان</th>
                    <th>العميل</th>
                    <th>النوع</th>
                    <th>الحالة</th>
                    <th>التاريخ</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
        `;

        cases.forEach(caseItem => {
            html += `
                <tr>
                    <td><strong>${caseItem.case_number || 'غير محدد'}</strong></td>
                    <td>${caseItem.title}</td>
                    <td>${caseItem.client_name || 'غير محدد'}</td>
                    <td><span class="badge bg-info">${caseItem.case_type || 'غير محدد'}</span></td>
                    <td><span class="badge bg-${this.getStatusColor(caseItem.status)}">${caseItem.status || 'غير محدد'}</span></td>
                    <td>${this.formatDate(caseItem.created_date)}</td>
                    <td>
                        <a href="/case/${caseItem.id}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i>
                        </a>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        return html;
    }

    renderClientsResults(clients) {
        let html = '<div class="table-responsive"><table class="table table-hover">';
        html += `
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>الهاتف</th>
                    <th>البريد الإلكتروني</th>
                    <th>عدد القضايا</th>
                    <th>التاريخ</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
        `;

        clients.forEach(client => {
            html += `
                <tr>
                    <td><strong>${client.name}</strong></td>
                    <td>${client.phone || 'غير محدد'}</td>
                    <td>${client.email || 'غير محدد'}</td>
                    <td><span class="badge bg-primary">${client.cases_count || 0}</span></td>
                    <td>${this.formatDate(client.created_date)}</td>
                    <td>
                        <a href="/client/${client.id}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i>
                        </a>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        return html;
    }

    renderPropertiesResults(properties) {
        let html = '<div class="table-responsive"><table class="table table-hover">';
        html += `
            <thead>
                <tr>
                    <th>اسم العقار</th>
                    <th>العنوان</th>
                    <th>النوع</th>
                    <th>الحالة</th>
                    <th>القيمة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
        `;

        properties.forEach(property => {
            html += `
                <tr>
                    <td><strong>${property.name}</strong></td>
                    <td>${property.address || 'غير محدد'}</td>
                    <td><span class="badge bg-info">${property.property_type || 'غير محدد'}</span></td>
                    <td><span class="badge bg-${this.getStatusColor(property.status)}">${property.status || 'غير محدد'}</span></td>
                    <td>${this.formatCurrency(property.value)}</td>
                    <td>
                        <a href="/property/${property.id}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i>
                        </a>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        return html;
    }

    renderFinancialResults(financial) {
        let html = '<div class="table-responsive"><table class="table table-hover">';
        html += `
            <thead>
                <tr>
                    <th>النوع</th>
                    <th>الوصف</th>
                    <th>المبلغ</th>
                    <th>التاريخ</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
        `;

        financial.forEach(item => {
            html += `
                <tr>
                    <td><span class="badge bg-${this.getFinancialTypeColor(item.type)}">${item.type}</span></td>
                    <td>${item.description}</td>
                    <td><strong>${this.formatCurrency(item.amount)}</strong></td>
                    <td>${this.formatDate(item.date)}</td>
                    <td><span class="badge bg-${this.getStatusColor(item.status)}">${item.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="advancedSearch.viewFinancialDetails(${item.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        return html;
    }

    // وظائف مساعدة للتنسيق
    getStatusColor(status) {
        const colors = {
            'active': 'success',
            'inactive': 'secondary',
            'pending': 'warning',
            'completed': 'success',
            'cancelled': 'danger'
        };
        return colors[status] || 'secondary';
    }

    getFinancialTypeColor(type) {
        const colors = {
            'income': 'success',
            'expense': 'danger',
            'fee': 'primary',
            'debt': 'warning'
        };
        return colors[type] || 'secondary';
    }

    formatDate(dateString) {
        if (!dateString) return 'غير محدد';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    formatCurrency(amount) {
        if (!amount) return '0 ريال';
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(amount);
    }

    // وظائف البحث والتصفية
    async showSearchSuggestions(searchTerm) {
        if (!searchTerm || searchTerm.length < 2) {
            this.hideSuggestions();
            return;
        }

        try {
            const response = await fetch('/api/search/suggestions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ term: searchTerm })
            });

            const suggestions = await response.json();
            this.displaySuggestions(suggestions);
        } catch (error) {
            console.error('خطأ في جلب الاقتراحات:', error);
        }
    }

    displaySuggestions(suggestions) {
        const suggestionsContainer = document.getElementById('search-suggestions');
        if (!suggestionsContainer || !suggestions.length) {
            this.hideSuggestions();
            return;
        }

        let html = '<div class="suggestions-list">';
        suggestions.forEach(suggestion => {
            html += `
                <div class="suggestion-item" onclick="advancedSearch.selectSuggestion('${suggestion.text}', '${suggestion.type}')">
                    <i class="fas fa-${this.getSuggestionIcon(suggestion.type)} me-2"></i>
                    <span class="suggestion-text">${suggestion.text}</span>
                    <span class="suggestion-type">${suggestion.type_name}</span>
                </div>
            `;
        });
        html += '</div>';

        suggestionsContainer.innerHTML = html;
        suggestionsContainer.style.display = 'block';
    }

    getSuggestionIcon(type) {
        const icons = {
            'case': 'gavel',
            'client': 'user',
            'property': 'building',
            'tenant': 'users',
            'financial': 'money-bill',
            'document': 'file'
        };
        return icons[type] || 'search';
    }

    selectSuggestion(text, type) {
        document.getElementById('global-search-input').value = text;
        document.getElementById('search-type').value = type;
        this.hideSuggestions();
        this.performGlobalSearch();
    }

    hideSuggestions() {
        const suggestionsContainer = document.getElementById('search-suggestions');
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
    }

    // وظائف إدارة الفلاتر
    toggleAdvancedFilters() {
        const filtersContainer = document.getElementById('advanced-filters');
        if (filtersContainer) {
            const isVisible = filtersContainer.style.display !== 'none';
            filtersContainer.style.display = isVisible ? 'none' : 'block';

            // تحديث أيقونة الزر
            const filterBtn = document.querySelector('.filter-btn i');
            if (filterBtn) {
                filterBtn.className = isVisible ? 'fas fa-filter' : 'fas fa-filter-circle-xmark';
            }
        }
    }

    applyFilters() {
        this.performGlobalSearch();
    }

    clearAllFilters() {
        // مسح جميع الفلاتر
        document.getElementById('search-type').value = 'all';
        document.getElementById('date-range').value = '';
        document.getElementById('date-from').value = '';
        document.getElementById('date-to').value = '';
        document.getElementById('status-filter').value = '';
        document.getElementById('amount-min').value = '';
        document.getElementById('amount-max').value = '';
        document.getElementById('client-filter').value = '';
        document.getElementById('case-type-filter').value = '';
        document.getElementById('priority-filter').value = '';

        // إخفاء التاريخ المخصص
        document.getElementById('custom-date-range').style.display = 'none';

        // تطبيق البحث بدون فلاتر
        this.performGlobalSearch();
    }

    // وظائف تاريخ البحث
    addToSearchHistory(searchTerm) {
        // إزالة البحث إذا كان موجوداً مسبقاً
        this.searchHistory = this.searchHistory.filter(item => item.term !== searchTerm);

        // إضافة البحث الجديد في المقدمة
        this.searchHistory.unshift({
            term: searchTerm,
            timestamp: new Date().toISOString(),
            filters: this.getCurrentFilters()
        });

        // الاحتفاظ بآخر 50 بحث فقط
        if (this.searchHistory.length > 50) {
            this.searchHistory = this.searchHistory.slice(0, 50);
        }

        // حفظ في localStorage
        localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
    }

    showSearchHistory() {
        if (this.searchHistory.length === 0) {
            this.showAlert('لا يوجد تاريخ بحث', 'info');
            return;
        }

        let html = `
            <div class="modal fade" id="searchHistoryModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-history me-2"></i>تاريخ البحث
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="search-history-list">
        `;

        this.searchHistory.forEach((item, index) => {
            const date = new Date(item.timestamp).toLocaleString('ar-SA');
            html += `
                <div class="history-item">
                    <div class="history-content">
                        <div class="history-term">${item.term}</div>
                        <div class="history-date">${date}</div>
                    </div>
                    <div class="history-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="advancedSearch.repeatSearch(${index})">
                            <i class="fas fa-redo"></i> إعادة البحث
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="advancedSearch.removeFromHistory(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        html += `
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-danger" onclick="advancedSearch.clearSearchHistory()">
                                <i class="fas fa-trash-alt me-1"></i>مسح التاريخ
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة المودال إلى الصفحة وإظهاره
        document.body.insertAdjacentHTML('beforeend', html);
        const modal = new bootstrap.Modal(document.getElementById('searchHistoryModal'));
        modal.show();

        // إزالة المودال عند الإغلاق
        document.getElementById('searchHistoryModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    repeatSearch(index) {
        const searchItem = this.searchHistory[index];
        if (searchItem) {
            // تعيين كلمة البحث
            document.getElementById('global-search-input').value = searchItem.term;

            // تطبيق الفلاتر المحفوظة
            if (searchItem.filters) {
                Object.keys(searchItem.filters).forEach(key => {
                    const element = document.getElementById(key.replace(/([A-Z])/g, '-$1').toLowerCase());
                    if (element) {
                        element.value = searchItem.filters[key] || '';
                    }
                });
            }

            // إغلاق المودال وتنفيذ البحث
            bootstrap.Modal.getInstance(document.getElementById('searchHistoryModal')).hide();
            this.performGlobalSearch();
        }
    }

    removeFromHistory(index) {
        this.searchHistory.splice(index, 1);
        localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));

        // إعادة تحديث المودال
        bootstrap.Modal.getInstance(document.getElementById('searchHistoryModal')).hide();
        setTimeout(() => this.showSearchHistory(), 300);
    }

    clearSearchHistory() {
        if (confirm('هل أنت متأكد من مسح تاريخ البحث بالكامل؟')) {
            this.searchHistory = [];
            localStorage.removeItem('searchHistory');
            bootstrap.Modal.getInstance(document.getElementById('searchHistoryModal')).hide();
            this.showAlert('تم مسح تاريخ البحث', 'success');
        }
    }

    // وظائف البحث المحفوظ
    saveCurrentSearch() {
        const searchTerm = document.getElementById('global-search-input').value.trim();
        if (!searchTerm) {
            this.showAlert('يرجى إدخال كلمة البحث أولاً', 'warning');
            return;
        }

        const searchName = prompt('أدخل اسم البحث المحفوظ:');
        if (!searchName) return;

        const savedSearch = {
            id: Date.now(),
            name: searchName,
            term: searchTerm,
            filters: this.getCurrentFilters(),
            timestamp: new Date().toISOString()
        };

        this.savedSearches.push(savedSearch);
        localStorage.setItem('savedSearches', JSON.stringify(this.savedSearches));

        this.showAlert('تم حفظ البحث بنجاح', 'success');
    }

    // وظائف التصدير والطباعة
    async exportResults() {
        if (!this.searchResults || this.searchResults.length === 0) {
            this.showAlert('لا توجد نتائج للتصدير', 'warning');
            return;
        }

        try {
            const response = await fetch('/api/search/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    results: this.searchResults,
                    format: 'excel'
                })
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `search_results_${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                this.showAlert('تم تصدير النتائج بنجاح', 'success');
            } else {
                throw new Error('فشل في تصدير النتائج');
            }
        } catch (error) {
            console.error('خطأ في التصدير:', error);
            this.showAlert('حدث خطأ أثناء التصدير', 'error');
        }
    }

    printResults() {
        const resultsContainer = document.getElementById('search-results');
        if (!resultsContainer) {
            this.showAlert('لا توجد نتائج للطباعة', 'warning');
            return;
        }

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>نتائج البحث</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f5f5f5; }
                        .no-print { display: none; }
                        @media print {
                            .no-print { display: none !important; }
                        }
                    </style>
                </head>
                <body>
                    <h1>نتائج البحث</h1>
                    <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>
                    ${resultsContainer.innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    // وظائف مساعدة
    focusSearchInput() {
        const searchInput = document.getElementById('global-search-input');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    showLoadingIndicator() {
        const resultsContainer = document.getElementById('search-results');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="loading-indicator text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري البحث...</span>
                    </div>
                    <p class="mt-3">جاري البحث...</p>
                </div>
            `;
            resultsContainer.style.display = 'block';
        }
    }

    hideLoadingIndicator() {
        // سيتم استبدال مؤشر التحميل بالنتائج
    }

    showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 1060; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // تهيئة الفلاتر
    async initializeFilters() {
        await this.loadClients();
    }

    async loadClients() {
        try {
            const response = await fetch('/api/clients');
            const clients = await response.json();

            const clientSelect = document.getElementById('client-filter');
            if (clientSelect && clients.length > 0) {
                clients.forEach(client => {
                    const option = document.createElement('option');
                    option.value = client.id;
                    option.textContent = client.name;
                    clientSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('خطأ في تحميل العملاء:', error);
        }
    }

    loadSearchHistory() {
        // تم تحميل التاريخ في المنشئ
    }
}

// تهيئة نظام البحث المتقدم
let advancedSearch;
document.addEventListener('DOMContentLoaded', function() {
    advancedSearch = new AdvancedSearchManager();
});