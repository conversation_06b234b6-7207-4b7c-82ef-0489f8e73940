{% extends "base.html" %}

{% block title %}تعديل دين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-credit-card"></i> تعديل دين</h1>
                    <p>تعديل بيانات الدين المحدد</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit text-primary me-2"></i>
                        تعديل بيانات الدين
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <!-- معلومات الدائن -->
                        <div class="section-divider">
                            <h5 class="text-primary"><i class="fa fa-user"></i> معلومات الدائن</h5>
                        </div>
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <label class="form-label">اسم الدائن <span class="text-danger">*</span></label>
                                <input type="text" name="creditor_name" class="form-control" 
                                       value="{{ debt.creditor_name }}" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">نوع الدين <span class="text-danger">*</span></label>
                                <select name="debt_type" class="form-control" required>
                                    <option value="">اختر نوع الدين</option>
                                    <option value="قرض شخصي" {% if debt.debt_type == 'قرض شخصي' %}selected{% endif %}>قرض شخصي</option>
                                    <option value="قرض بنكي" {% if debt.debt_type == 'قرض بنكي' %}selected{% endif %}>قرض بنكي</option>
                                    <option value="مستحقات موردين" {% if debt.debt_type == 'مستحقات موردين' %}selected{% endif %}>مستحقات موردين</option>
                                    <option value="رواتب ومكافآت" {% if debt.debt_type == 'رواتب ومكافآت' %}selected{% endif %}>رواتب ومكافآت</option>
                                    <option value="إيجارات" {% if debt.debt_type == 'إيجارات' %}selected{% endif %}>إيجارات</option>
                                    <option value="ضرائب ورسوم" {% if debt.debt_type == 'ضرائب ورسوم' %}selected{% endif %}>ضرائب ورسوم</option>
                                    <option value="خدمات مهنية" {% if debt.debt_type == 'خدمات مهنية' %}selected{% endif %}>خدمات مهنية</option>
                                    <option value="أخرى" {% if debt.debt_type == 'أخرى' %}selected{% endif %}>أخرى</option>
                                </select>
                            </div>
                        </div>

                        <!-- تفاصيل الدين -->
                        <div class="section-divider">
                            <h5 class="text-info"><i class="fa fa-info-circle"></i> تفاصيل الدين</h5>
                        </div>
                        <div class="row g-3 mb-3">
                            <div class="col-md-4">
                                <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                                <input type="number" name="amount" class="form-control" 
                                       step="0.01" min="0" value="{{ debt.amount }}" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">العملة</label>
                                <select name="currency" class="form-control">
                                    <option value="شيكل" {% if debt.currency == 'شيكل' %}selected{% endif %}>شيكل</option>
                                    <option value="دولار" {% if debt.currency == 'دولار' %}selected{% endif %}>دولار أمريكي</option>
                                    <option value="دينار" {% if debt.currency == 'دينار' %}selected{% endif %}>دينار أردني</option>
                                    <option value="يورو" {% if debt.currency == 'يورو' %}selected{% endif %}>يورو</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">الأولوية</label>
                                <select name="priority" class="form-control">
                                    <option value="منخفضة" {% if debt.priority == 'منخفضة' %}selected{% endif %}>منخفضة</option>
                                    <option value="متوسطة" {% if debt.priority == 'متوسطة' %}selected{% endif %}>متوسطة</option>
                                    <option value="عالية" {% if debt.priority == 'عالية' %}selected{% endif %}>عالية</option>
                                    <option value="عاجلة" {% if debt.priority == 'عاجلة' %}selected{% endif %}>عاجلة</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">تاريخ الاستحقاق</label>
                            <input type="date" name="due_date" class="form-control" 
                                   value="{% if debt.due_date %}{{ debt.due_date.strftime('%Y-%m-%d') }}{% endif %}">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وصف الدين</label>
                            <textarea name="description" class="form-control" rows="3" 
                                      placeholder="تفاصيل إضافية عن الدين...">{{ debt.description or '' }}</textarea>
                        </div>

                        <!-- ربط اختياري -->
                        <div class="section-divider">
                            <h5 class="text-warning"><i class="fa fa-link"></i> ربط اختياري</h5>
                        </div>
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <label class="form-label">ربط بقضية (اختياري)</label>
                                <select name="case_id" class="form-control">
                                    <option value="">لا يوجد ربط</option>
                                    {% for case in cases %}
                                    <option value="{{ case.id }}" {% if debt.case_id == case.id %}selected{% endif %}>
                                        {{ case.case_number }} - {{ case.title }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">ربط بعميل (اختياري)</label>
                                <select name="client_id" class="form-control">
                                    <option value="">لا يوجد ربط</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}" {% if debt.client_id == client.id %}selected{% endif %}>
                                        {{ client.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <!-- معلومات الدفع -->
                        <div class="alert alert-info">
                            <h6><strong>معلومات الدفع الحالية:</strong></h6>
                            <p><strong>المدفوع:</strong> {{ "%.2f"|format(debt.paid_amount) }} {{ debt.currency }}</p>
                            <p><strong>المتبقي:</strong> {{ "%.2f"|format(debt.remaining_amount) }} {{ debt.currency }}</p>
                            <p><strong>الحالة:</strong> 
                                <span class="badge 
                                    {% if debt.status == 'مسدد' %}bg-success
                                    {% elif debt.status == 'متأخر' %}bg-danger
                                    {% else %}bg-warning{% endif %}">
                                    {{ debt.status }}
                                </span>
                            </p>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary px-5 py-2">
                                <i class="fa fa-save"></i> حفظ التعديلات
                            </button>
                            <a href="{{ url_for('debts_list') }}" class="btn btn-secondary px-4 py-2">
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.section-divider {
    border-bottom: 2px solid #e9ecef;
    margin: 20px 0 15px 0;
    padding-bottom: 10px;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.text-danger {
    color: #dc3545 !important;
}

.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.alert {
    border-radius: 10px;
}
</style>
{% endblock %}
