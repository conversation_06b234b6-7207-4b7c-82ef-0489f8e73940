<form method="POST" action="{{ url_for('add_client') }}" class="modal-form" id="addClientForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-user"></i> المعلومات الأساسية
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                <input type="text" name="name" class="form-control" required 
                       placeholder="أدخل الاسم الكامل">
            </div>
            <div class="col-md-6">
                <label class="form-label">رقم الهوية</label>
                <input type="text" name="id_number" class="form-control" 
                       placeholder="رقم الهوية أو جواز السفر">
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-phone"></i> معلومات الاتصال
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                <input type="tel" name="phone" class="form-control" required 
                       placeholder="رقم الهاتف">
            </div>
            <div class="col-md-6">
                <label class="form-label">البريد الإلكتروني</label>
                <input type="email" name="email" class="form-control" 
                       placeholder="البريد الإلكتروني">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-12">
                <label class="form-label">العنوان</label>
                <textarea name="address" class="form-control" rows="2" 
                          placeholder="العنوان الكامل"></textarea>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-info-circle"></i> معلومات إضافية
        </div>
        <div class="row g-3">
            <div class="col-md-4">
                <label class="form-label">تاريخ الميلاد</label>
                <input type="date" name="birth_date" class="form-control">
            </div>
            <div class="col-md-4">
                <label class="form-label">المهنة</label>
                <input type="text" name="occupation" class="form-control"
                       placeholder="المهنة">
            </div>
            <div class="col-md-4">
                <label class="form-label">نوع العميل</label>
                <select name="role" class="form-control">
                    <option value="موكل">موكل</option>
                    <option value="خصم">خصم</option>
                    <option value="شاهد">شاهد</option>
                    <option value="محامي">محامي</option>
                    <option value="قاضي">قاضي</option>
                    <option value="أخرى">أخرى</option>
                </select>
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-12">
                <label class="form-label">ملاحظات</label>
                <textarea name="notes" class="form-control" rows="2" 
                          placeholder="ملاحظات إضافية"></textarea>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <button type="submit" class="btn btn-success px-4">
            <i class="fa fa-save"></i> حفظ العميل
        </button>
        <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
            إلغاء
        </button>
    </div>
</form>

<script>
document.getElementById('addClientForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            if (modal) modal.hide();

            // إظهار رسالة نجاح
            showAlert('تم إضافة العميل بنجاح', 'success');

            // إعادة تحميل الصفحة أو تحديث القائمة
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert(data.message || 'حدث خطأ أثناء إضافة العميل', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
});

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>

<style>
.modal-form .form-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.modal-form .form-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
}

.modal-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.modal-form .text-danger {
    color: #dc3545 !important;
}

.modal-form .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.modal-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
</style>
