{% extends 'base.html' %}
{% block title %}الإحصائيات والتقارير{% endblock %}
{% block head %}
<style>
    body { background: #f8fafc; }
    .stats-title { margin-top: 30px; margin-bottom: 28px; font-size: 2.1rem; font-weight: bold; color: #0d6efd; text-align: center; }
    .dashboard-stats-section { max-width:1200px; margin: 0 auto 40px auto; border-radius:18px; }
    .stats-table th, .stats-table td { text-align: center; }
</style>
{% endblock %}
{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-chart-bar"></i> الإحصائيات والتقارير</h1>
                    <p>عرض الإحصائيات التفصيلية والتقارير التحليلية للنظام</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-analytics text-primary me-2"></i>
                        لوحة الإحصائيات
                    </h5>
                </div>
                <div class="card-body">
    <section class="dashboard-stats-section card shadow-sm p-4 mb-5">
        <div class="row g-4 mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">عدد القضايا حسب نوع القضية</div>
                    <div class="card-body">
                        <canvas id="casesByTypeChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">عدد القضايا حسب المحكمة</div>
                    <div class="card-body">
                        <canvas id="casesByCourtChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">عدد القضايا حسب الموكل/العميل</div>
                    <div class="card-body">
                        <canvas id="casesByClientChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="card mb-4">
            <div class="card-header">بحث متقدم في القضايا والتقارير المالية</div>
            <div class="card-body">
                <form method="POST" class="row g-3 align-items-center">
                    <div class="col-auto">
                        <select name="search_field" class="form-select">
                            <option value="client_name" {% if search_field=='client_name' %}selected{% endif %}>اسم الموكل/العميل</option>
                            <option value="national_id" {% if search_field=='national_id' %}selected{% endif %}>رقم الهوية</option>
                            <option value="court" {% if search_field=='court' %}selected{% endif %}>اسم المحكمة</option>
                            <option value="case_number" {% if search_field=='case_number' %}selected{% endif %}>رقم القضية</option>
                            <option value="case_status" {% if search_field=='case_status' %}selected{% endif %}>حالة القضية (مفصولة/متروكة/جارية...)</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <input type="text" name="search_value" class="form-control" placeholder="كلمة البحث" value="{{ search_value or '' }}">
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-primary">بحث</button>
                    </div>
                </form>
                {% if search_results is not none %}
                <div class="table-responsive mt-4">
                    <table class="table table-bordered stats-table">
                        <thead>
                            <tr>
                                <th>رقم القضية</th>
                                <th>العنوان</th>
                                <th>اسم الموكل/العميل</th>
                                <th>رقم الهوية</th>
                                <th>اسم المحكمة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for case in search_results %}
                            <tr>
                                <td>{{ case.case_number }}</td>
                                <td>{{ case.title }}</td>
                                <td>{{ case.client.name }}</td>
                                <td>{{ case.client.national_id }}</td>
                                <td>{{ case.court }}</td>
                                <td>{{ case.status }}</td>
                            </tr>
                            {% endfor %}
                            {% if search_results|length == 0 %}
                            <tr><td colspan="6" class="text-center">لا توجد نتائج</td></tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                {% if extra_report %}
                <div class="alert alert-info mt-3 text-center">
                    <b>إجمالي المبالغ المدفوعة:</b> {{ extra_report.paid }}<br>
                    <b>إجمالي المصروفات:</b> {{ extra_report.expense }}<br>
                    <b>الرصيد المتبقي:</b> {{ extra_report.balance }}
                </div>
                {% endif %}
                {% endif %}
            </div>
        </div>
        <!-- إحصائيات إضافية متقدمة -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">إحصائيات حالات القضايا</div>
                    <div class="card-body">
                        <ul class="list-group">
                            {% for status, count in status_stats.items() %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {{ status }}
                                <span class="badge bg-primary rounded-pill">{{ count }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">إحصائيات صفة الموكل</div>
                    <div class="card-body">
                        <ul class="list-group">
                            {% for role, count in client_role_stats.items() %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {{ role }}
                                <span class="badge bg-info rounded-pill">{{ count }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- تقرير مالي إجمالي -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">تقرير مالي إجمالي للنظام</div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <span class="badge bg-success" style="font-size:1.2rem;">{{ total_paid }}</span>
                                    <p class="mt-2">إجمالي المبالغ المدفوعة</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <span class="badge bg-danger" style="font-size:1.2rem;">{{ total_expense }}</span>
                                    <p class="mt-2">إجمالي المصروفات</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <span class="badge bg-info" style="font-size:1.2rem;">{{ total_balance }}</span>
                                    <p class="mt-2">الرصيد الكلي المتبقي</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- بنود إحصائية ومالية وتحليلية إضافية -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card mb-3">
                    <div class="card-header">متوسط مدة القضايا المنتهية</div>
                    <div class="card-body text-center">
                        {% if avg_duration %}
                        <span class="badge bg-info" style="font-size:1.2rem;">{{ avg_duration }} يوم</span>
                        {% else %}
                        <span class="text-muted">لا يوجد بيانات كافية</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card mb-3">
                    <div class="card-header">القضايا الجديدة هذا الشهر</div>
                    <div class="card-body text-center">
                        <span class="badge bg-success" style="font-size:1.2rem;">{{ new_cases_month }}</span>
                    </div>
                </div>
                <div class="card mb-3">
                    <div class="card-header">القضايا الجديدة هذه السنة</div>
                    <div class="card-body text-center">
                        <span class="badge bg-primary" style="font-size:1.2rem;">{{ new_cases_year }}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-3">
                    <div class="card-header">أكثر العملاء نشاطاً</div>
                    <div class="card-body">
                        <ul class="list-group">
                            {% for name, count in top_clients %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {{ name }}
                                <span class="badge bg-primary rounded-pill">{{ count }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                <div class="card mb-3">
                    <div class="card-header">أكثر المحاكم نشاطاً</div>
                    <div class="card-body">
                        <ul class="list-group">
                            {% for name, count in top_courts %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {{ name }}
                                <span class="badge bg-info rounded-pill">{{ count }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-3">
                    <div class="card-header">نسبة القضايا المنجزة</div>
                    <div class="card-body text-center">
                        <span class="badge bg-success" style="font-size:1.2rem;">{{ closed_ratio|round(1) }}%</span>
                        <div class="text-muted mt-2">({{ closed_count }} من {{ closed_count + open_count }} قضية)</div>
                    </div>
                </div>
                <div class="card mb-3">
                    <div class="card-header">القضايا المنتهية مقابل الجارية</div>
                    <div class="card-body text-center">
                        <span class="badge bg-success">منتهية: {{ closed_count }}</span>
                        <span class="badge bg-warning text-dark">جارية: {{ open_count }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">أعلى القضايا من حيث المبالغ المدفوعة</div>
                    <div class="card-body">
                        <ul class="list-group">
                            {% for case_number, amount in top_paid_cases %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                رقم {{ case_number }}
                                <span class="badge bg-success">{{ amount }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                <div class="card mb-3">
                    <div class="card-header">القضايا التي لم يُدفع كامل مستحقاتها</div>
                    <div class="card-body">
                        {% if unpaid_cases and unpaid_cases|length > 0 %}
                        <ul class="list-group">
                            {% for c in unpaid_cases %}
                            <li class="list-group-item">
                                <b>رقم:</b> {{ c.case_number }} - <b>العميل:</b> {{ c.client }}<br>
                                <span class="text-danger">المتبقي: {{ c.remaining }} / المدفوع: {{ c.paid }} / المطلوب: {{ c.expected }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                        {% else %}
                        <span class="text-muted">لا يوجد أو لم يتم تعريف المبالغ المتوقعة</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">القضايا التي لم يتم تحديثها منذ أكثر من 6 أشهر</div>
                    <div class="card-body">
                        {% if old_cases and old_cases|length > 0 %}
                        <ul class="list-group">
                            {% for c in old_cases %}
                            <li class="list-group-item">
                                <b>رقم:</b> {{ c.case_number }} - <b>العنوان:</b> {{ c.title }} - <b>الحالة:</b> {{ c.status }}
                            </li>
                            {% endfor %}
                        </ul>
                        {% else %}
                        <span class="text-muted">لا يوجد قضايا قديمة غير محدثة</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card mb-3">
                    <div class="card-header">توزيع القضايا حسب الشهور (آخر سنة)</div>
                    <div class="card-body">
                        <canvas id="monthlyCasesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}
{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// بيانات الرسوم البيانية من السيرفر
const typesStats = JSON.parse('{{ types_stats|tojson|safe }}');
const courtsStats = JSON.parse('{{ courts_stats|tojson|safe }}');
const clientsStats = JSON.parse('{{ clients_stats|tojson|safe }}');

// رسم القضايا حسب النوع
const ctxType = document.getElementById('casesByTypeChart').getContext('2d');
new Chart(ctxType, {
    type: 'bar',
    data: {
        labels: Object.keys(typesStats),
        datasets: [{
            label: 'عدد القضايا',
            data: Object.values(typesStats),
            backgroundColor: '#0d6efd',
        }]
    },
    options: {responsive:true, plugins:{legend:{display:false}}, scales:{y:{beginAtZero:true}}}
});

// رسم القضايا حسب المحكمة
const ctxCourt = document.getElementById('casesByCourtChart').getContext('2d');
new Chart(ctxCourt, {
    type: 'bar',
    data: {
        labels: Object.keys(courtsStats),
        datasets: [{
            label: 'عدد القضايا',
            data: Object.values(courtsStats),
            backgroundColor: '#6610f2',
        }]
    },
    options: {responsive:true, plugins:{legend:{display:false}}, scales:{y:{beginAtZero:true}}}
});

// رسم القضايا حسب العميل
const ctxClient = document.getElementById('casesByClientChart').getContext('2d');
new Chart(ctxClient, {
    type: 'bar',
    data: {
        labels: Object.keys(clientsStats),
        datasets: [{
            label: 'عدد القضايا',
            data: Object.values(clientsStats),
            backgroundColor: '#20c997',
        }]
    },
    options: {responsive:true, plugins:{legend:{display:false}}, scales:{y:{beginAtZero:true}}}
});

// رسم توزيع القضايا حسب الشهور
const monthlyCases = JSON.parse('{{ monthly_cases|tojson|safe }}');
const monthsLabels = monthlyCases.map(x => x[0] + '-' + (x[1]<10?'0':'') + x[1]);
const monthsData = monthlyCases.map(x => x[2]);
const ctxMonthly = document.getElementById('monthlyCasesChart').getContext('2d');
new Chart(ctxMonthly, {
    type: 'line',
    data: {
        labels: monthsLabels,
        datasets: [{
            label: 'عدد القضايا',
            data: monthsData,
            backgroundColor: '#0d6efd44',
            borderColor: '#0d6efd',
            fill: true,
            tension: 0.3
        }]
    },
    options: {responsive:true, plugins:{legend:{display:true}}, scales:{y:{beginAtZero:true}}}
});
</script>
{% endblock %}
