<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}نظام إدارة المكتب{% endblock %}</title>
    <meta name="description" content="نظام إدارة مكتب المحاماة - إدارة شاملة للقضايا والعملاء والمالية">

    <!-- الخطوط والأيقونات -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- نظام التصميم الأساسي -->
    <link href="{{ url_for('static', filename='css/design-system.css') }}" rel="stylesheet">

    <!-- ملفات CSS المخصصة -->
    <link href="{{ url_for('static', filename='css/components.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/themes.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/advanced-search.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/reports-system.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/notifications-system.css') }}" rel="stylesheet">

    <!-- Performance Optimizations -->
    <style>
        /* تحسينات الأداء والاستجابة */
        * {
            box-sizing: border-box;
        }

        img {
            max-width: 100%;
            height: auto;
        }

        /* تحسين الخطوط */
        body {
            font-display: swap;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* تحسين الانتقالات */
        .btn, .card, .modal {
            will-change: transform;
        }

        /* تحسين التمرير */
        .sidebar {
            -webkit-overflow-scrolling: touch;
        }

        /* تحسين الاستجابة للشاشات الصغيرة */
        @media (max-width: 576px) {
            .container-fluid {
                padding-left: 10px;
                padding-right: 10px;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
            }

            .page-actions {
                width: 100%;
                justify-content: center;
            }

            .btn {
                font-size: 0.875rem;
                padding: 0.5rem 1rem;
            }

            .card {
                margin-bottom: 1rem;
            }

            .table-responsive {
                font-size: 0.875rem;
            }
        }

        /* تحسين الطباعة */
        @media print {
            .sidebar, .navbar, .page-actions, .btn {
                display: none !important;
            }

            .main-content {
                margin-left: 0 !important;
                padding: 0 !important;
            }

            .card {
                border: 1px solid #000 !important;
                box-shadow: none !important;
            }
        }

        /* تحسين الوضع الداكن */
        [data-theme="dark"] {
            color-scheme: dark;
        }

        [data-theme="dark"] img {
            filter: brightness(0.8);
        }

        /* تحسين إمكانية الوصول */
        .btn:focus,
        .form-control:focus,
        .form-select:focus {
            outline: 2px solid var(--bs-primary);
            outline-offset: 2px;
        }

        /* تحسين الحركة للمستخدمين الذين يفضلون تقليل الحركة */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>

    {% if request.endpoint == 'calendar' %}
    <link href="{{ url_for('static', filename='css/calendar.css') }}" rel="stylesheet">
    {% endif %}
    {% if request.endpoint == 'archive' %}
    <link href="{{ url_for('static', filename='css/archive.css') }}" rel="stylesheet">
    {% endif %}
    <style>
        /* ===== التخطيط الأساسي ===== */
        .app-container {
            display: flex;
            min-height: 100vh;
            background: var(--bg-secondary);
        }

        /* ===== الشريط الجانبي ===== */
        .sidebar {
            width: 280px;
            background: var(--bg-card);
            border-right: 1px solid var(--border-color);
            box-shadow: var(--shadow-lg);
            display: flex;
            flex-direction: column;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: all var(--transition-normal);
        }

        .sidebar-header {
            padding: var(--spacing-xl);
            border-bottom: 1px solid var(--border-color);
            background: var(--primary-gradient);
            color: var(--text-white);
            text-align: center;
            position: relative;
        }

        .sidebar-logo {
            font-size: var(--text-3xl);
            margin-bottom: var(--spacing-sm);
        }

        .sidebar-title {
            font-size: var(--text-xl);
            font-weight: 700;
            margin: 0;
            line-height: 1.3;
        }

        .sidebar-subtitle {
            font-size: var(--text-sm);
            opacity: 0.9;
            margin-top: var(--spacing-xs);
        }

        .sidebar-nav {
            flex: 1;
            padding: var(--spacing-lg) 0;
        }

        .nav-section {
            margin-bottom: var(--spacing-lg);
        }

        .nav-section-title {
            font-size: var(--text-xs);
            font-weight: 600;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0 var(--spacing-lg);
            margin-bottom: var(--spacing-sm);
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md) var(--spacing-lg);
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: var(--text-sm);
            transition: all var(--transition-fast);
            border-right: 3px solid transparent;
            position: relative;
        }

        .nav-link:hover {
            background: var(--bg-tertiary);
            color: var(--primary-color);
            border-right-color: var(--primary-light);
            transform: translateX(-2px);
        }

        .nav-link.active {
            background: rgba(59, 130, 246, 0.1);
            color: var(--primary-color);
            border-right-color: var(--primary-color);
            font-weight: 600;
        }

        .nav-link i {
            font-size: var(--text-lg);
            width: 20px;
            text-align: center;
        }

        .nav-badge {
            background: var(--danger-color);
            color: var(--text-white);
            font-size: var(--text-xs);
            padding: 2px 6px;
            border-radius: 10px;
            margin-right: auto;
            min-width: 18px;
            text-align: center;
        }

        /* ===== المحتوى الرئيسي ===== */
        .main-content {
            flex: 1;
            margin-right: 280px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .top-bar {
            background: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
            padding: var(--spacing-md) var(--spacing-xl);
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            font-size: var(--text-2xl);
            font-weight: 700;
            color: var(--text-primary);
            margin: 0;
        }

        .top-bar-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .content-area {
            flex: 1;
            padding: var(--spacing-xl);
            overflow-y: auto;
        }

        /* ===== الإشعارات والأدوات ===== */
        .notification-btn {
            position: relative;
            background: none;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm) var(--spacing-md);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .notification-btn:hover {
            background: var(--bg-tertiary);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .theme-toggle {
            background: none;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm) var(--spacing-md);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .theme-toggle:hover {
            background: var(--bg-tertiary);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--bg-tertiary);
            border-radius: var(--radius-lg);
            color: var(--text-primary);
            font-weight: 500;
        }

        /* ===== الاستجابة للشاشات الصغيرة ===== */
        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(100%);
                width: 300px;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .mobile-menu-btn {
                display: block;
            }
        }

        @media (max-width: 768px) {
            .top-bar {
                padding: var(--spacing-md);
            }

            .content-area {
                padding: var(--spacing-lg);
            }

            .sidebar {
                width: 100%;
            }
        }

        /* ===== نظام الإشعارات المحدث ===== */
        .notification-dropdown {
            position: absolute;
            top: calc(100% + 8px);
            left: 0;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            width: 380px;
            max-height: 450px;
            overflow: hidden;
            z-index: 1050;
            animation: slideDown 0.3s ease-out;
            display: none;
        }

        .notification-header {
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-tertiary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-header h6 {
            margin: 0;
            font-weight: 600;
            color: var(--text-primary);
        }

        .notification-list {
            max-height: 350px;
            overflow-y: auto;
        }

        .notification-item {
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--border-light);
            cursor: pointer;
            transition: all var(--transition-fast);
            position: relative;
        }

        .notification-item:hover {
            background: var(--bg-tertiary);
        }

        .notification-item.unread {
            background: rgba(59, 130, 246, 0.05);
            border-right: 3px solid var(--primary-color);
        }

        .notification-item.overdue {
            background: rgba(220, 38, 38, 0.05);
            border-right: 3px solid var(--danger-color);
        }

        .notification-badge {
            position: absolute;
            top: -6px;
            left: -6px;
            background: var(--danger-color);
            color: var(--text-white);
            border-radius: 50%;
            padding: 2px 6px;
            font-size: var(--text-xs);
            min-width: 18px;
            text-align: center;
            font-weight: 600;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
    {% block head %}{% endblock %}
</head>
<body>
    <div class="app-container">
        <!-- الشريط الجانبي -->
        <aside class="sidebar" id="sidebar">
            <!-- رأس الشريط الجانبي -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <h1 class="sidebar-title">مكتب المحامي</h1>
                <p class="sidebar-subtitle">سامح قعدان</p>
            </div>

            <!-- قائمة التنقل -->
            <nav class="sidebar-nav">
                <!-- القسم الرئيسي -->
                <div class="nav-section">
                    <div class="nav-section-title">الرئيسية</div>
                    <a class="nav-link {% if request.endpoint=='dashboard' %}active{% endif %}" href="{{ url_for('dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </div>

                <!-- إدارة العملاء والقضايا -->
                <div class="nav-section">
                    <div class="nav-section-title">العملاء والقضايا</div>
                    <a class="nav-link {% if request.endpoint=='clients_list' %}active{% endif %}" href="{{ url_for('clients_list') }}">
                        <i class="fas fa-users"></i>
                        <span>إدارة العملاء</span>
                    </a>
                    <a class="nav-link {% if request.endpoint=='cases_list' %}active{% endif %}" href="{{ url_for('cases_list') }}">
                        <i class="fas fa-gavel"></i>
                        <span>إدارة القضايا</span>
                    </a>
                </div>

                <!-- إدارة العقارات المحسنة -->
                <div class="nav-section">
                    <div class="nav-section-title">إدارة العقارات</div>
                    <a class="nav-link {% if request.endpoint in ['properties', 'properties_enhanced'] %}active{% endif %}" href="{{ url_for('properties_enhanced') }}">
                        <i class="fas fa-building"></i>
                        <span>العقارات المحسنة</span>
                    </a>
                    <a class="nav-link {% if request.endpoint in ['tenants', 'tenants_enhanced'] %}active{% endif %}" href="{{ url_for('tenants_enhanced') }}">
                        <i class="fas fa-users"></i>
                        <span>المستأجرين المحسن</span>
                    </a>
                    <a class="nav-link {% if request.endpoint in ['leases', 'leases_enhanced'] %}active{% endif %}" href="{{ url_for('leases_enhanced') }}">
                        <i class="fas fa-file-contract"></i>
                        <span>عقود الإيجار المحسنة</span>
                    </a>
                    <a class="nav-link {% if request.endpoint=='documents_manager' %}active{% endif %}" href="{{ url_for('documents_manager') }}">
                        <i class="fas fa-folder-open"></i>
                        <span>إدارة المستندات</span>
                    </a>
                    <a class="nav-link {% if request.endpoint=='reports_dashboard' %}active{% endif %}" href="{{ url_for('reports_dashboard') }}">
                        <i class="fas fa-chart-bar"></i>
                        <span>التقارير والإحصائيات</span>
                    </a>
                </div>

                <!-- الإدارة المالية -->
                <div class="nav-section">
                    <div class="nav-section-title">الإدارة المالية</div>
                    <a class="nav-link {% if request.endpoint=='finance_list' %}active{% endif %}" href="{{ url_for('finance_list') }}">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>السندات المالية</span>
                    </a>
                    <a class="nav-link {% if request.endpoint=='fees_list' %}active{% endif %}" href="{{ url_for('fees_list') }}">
                        <i class="fas fa-receipt"></i>
                        <span>إدارة الأتعاب</span>
                    </a>
                    <a class="nav-link {% if request.endpoint=='debts_list' %}active{% endif %}" href="{{ url_for('debts_list') }}">
                        <i class="fas fa-credit-card"></i>
                        <span>إدارة الديون</span>
                    </a>
                </div>

                <!-- المهام والمواعيد -->
                <div class="nav-section">
                    <div class="nav-section-title">المهام والمواعيد</div>
                    <a class="nav-link {% if request.endpoint=='manage_tasks' %}active{% endif %}" href="{{ url_for('manage_tasks') }}">
                        <i class="fas fa-tasks"></i>
                        <span>إدارة المهام</span>
                    </a>
                    <a class="nav-link {% if request.endpoint=='appointments_list' %}active{% endif %}" href="{{ url_for('appointments_list') }}">
                        <i class="fas fa-calendar-check"></i>
                        <span>إدارة المواعيد</span>
                    </a>
                    <a class="nav-link {% if request.endpoint=='calendar' %}active{% endif %}" href="{{ url_for('calendar') }}">
                        <i class="fas fa-calendar-alt"></i>
                        <span>التقويم</span>
                    </a>
                </div>

                <!-- التقارير -->
                <div class="nav-section">
                    <div class="nav-section-title">التقارير والإحصائيات</div>
                    <a class="nav-link {% if request.endpoint=='reports_dashboard' %}active{% endif %}" href="{{ url_for('reports_dashboard') }}">
                        <i class="fas fa-chart-line"></i>
                        <span>التقارير الشاملة</span>
                    </a>
                    <a class="nav-link {% if request.endpoint=='stats' %}active{% endif %}" href="{{ url_for('stats') }}">
                        <i class="fas fa-chart-bar"></i>
                        <span>الإحصائيات</span>
                    </a>
                </div>

                <!-- الأدوات -->
                <div class="nav-section">
                    <div class="nav-section-title">الأدوات والإعدادات</div>
                    <a class="nav-link {% if request.endpoint=='advanced_search_page' %}active{% endif %}" href="{{ url_for('advanced_search_page') }}">
                        <i class="fas fa-search-plus"></i>
                        <span>البحث المتقدم</span>
                    </a>
                    <a class="nav-link {% if request.endpoint=='notifications' %}active{% endif %}" href="{{ url_for('notifications') }}">
                        <i class="fas fa-bell"></i>
                        <span>الإشعارات</span>
                        <span class="nav-badge" id="notificationCount" style="display: none;">0</span>
                    </a>
                    <a class="nav-link {% if request.endpoint=='archive' %}active{% endif %}" href="{{ url_for('archive') }}">
                        <i class="fas fa-archive"></i>
                        <span>الأرشيف</span>
                    </a>
                    <a class="nav-link {% if request.endpoint=='settings' %}active{% endif %}" href="{{ url_for('settings') }}">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                    <a class="nav-link {% if request.endpoint=='backup' %}active{% endif %}" href="{{ url_for('backup') }}">
                        <i class="fas fa-database"></i>
                        <span>النسخ الاحتياطي</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- الشريط العلوي -->
            <header class="top-bar">
                <div class="d-flex align-items-center">
                    <button class="mobile-menu-btn d-lg-none me-3" id="mobileMenuBtn" style="display: none;">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title">{% block page_title %}{% endblock %}</h1>
                </div>

                <div class="top-bar-actions">
                    <!-- زر الإشعارات -->
                    <div class="position-relative">
                        <button class="notification-btn" id="notificationToggle">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
                        </button>

                        <!-- قائمة الإشعارات المنسدلة -->
                        <div class="notification-dropdown" id="notificationDropdown">
                            <div class="notification-header">
                                <h6>الإشعارات</h6>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="markAllRead">
                                    <i class="fas fa-check-double"></i>
                                </button>
                            </div>
                            <div class="notification-list" id="notificationList">
                                <div class="text-center p-3 text-muted">لا توجد إشعارات جديدة</div>
                            </div>
                        </div>
                    </div>

                    <!-- زر تبديل الوضع -->
                    <button class="theme-toggle" id="themeToggle">
                        <i class="fas fa-moon"></i>
                    </button>

                    <!-- معلومات المستخدم -->
                    <div class="user-menu">
                        <i class="fas fa-user-circle"></i>
                        <span>{{ session.username if session.username else 'مستخدم' }}</span>
                    </div>

                    <!-- زر تسجيل الخروج -->
                    <a href="{{ url_for('logout') }}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-sign-out-alt"></i>
                        <span class="d-none d-md-inline">خروج</span>
                    </a>
                </div>
            </header>

            <!-- منطقة المحتوى -->
                <!-- رسائل النظام -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                  {% if messages %}
                    {% for category, message in messages %}
                      <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                      </div>
                    {% endfor %}
                  {% endif %}
                {% endwith %}

                <!-- محتوى الصفحة -->
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>

    <!-- حاوية الإشعارات المنبثقة -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- تضمين لوحة الإشعارات -->
    {% include 'notifications/notification_panel.html' %}

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/modals.js') }}"></script>
    <script src="{{ url_for('static', filename='js/theme-toggle.js') }}"></script>
    <script src="{{ url_for('static', filename='js/notification-bell.js') }}"></script>
    <script src="{{ url_for('static', filename='js/notifications-system.js') }}"></script>

    {% if request.endpoint == 'archive' %}
    <script src="{{ url_for('static', filename='js/archive.js') }}"></script>
    {% endif %}

    <script>
        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة نظام الإشعارات
            if (typeof NotificationSystem !== 'undefined') {
                window.notificationSystem = new NotificationSystem();
            }

            // تهيئة نظام تبديل الوضع
            if (typeof ThemeToggle !== 'undefined') {
                window.themeToggle = new ThemeToggle();
            }

            // تهيئة القائمة الجانبية للهواتف
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const sidebar = document.getElementById('sidebar');

            if (mobileMenuBtn && sidebar) {
                mobileMenuBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('open');
                });

                // إغلاق القائمة عند النقر خارجها
                document.addEventListener('click', function(e) {
                    if (!sidebar.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                        sidebar.classList.remove('open');
                    }
                });
            }

            // تهيئة نظام الإشعارات
            const notificationToggle = document.getElementById('notificationToggle');
            const notificationDropdown = document.getElementById('notificationDropdown');

            if (notificationToggle && notificationDropdown) {
                notificationToggle.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const isVisible = notificationDropdown.style.display === 'block';
                    notificationDropdown.style.display = isVisible ? 'none' : 'block';
                });

                // إغلاق قائمة الإشعارات عند النقر خارجها
                document.addEventListener('click', function() {
                    notificationDropdown.style.display = 'none';
                });

                notificationDropdown.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }

            // تحديث عداد الإشعارات
            function updateNotificationCount() {
                // هذه الدالة ستتم إضافتها لاحقاً للتكامل مع النظام
                console.log('تحديث عداد الإشعارات');
            }

            // تحديث الإشعارات كل 30 ثانية
            setInterval(updateNotificationCount, 30000);

            // تهيئة أولية
            updateNotificationCount();

            // تحسينات الأداء الإضافية
            performanceOptimizations();
        });

        // دالة تحسينات الأداء
        function performanceOptimizations() {
            // تحسين التمرير السلس
            if ('scrollBehavior' in document.documentElement.style) {
                document.documentElement.style.scrollBehavior = 'smooth';
            }

            // تحسين تحميل الصور المؤجل
            const images = document.querySelectorAll('img[data-src]');
            if (images.length > 0 && 'IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            imageObserver.unobserve(img);
                        }
                    });
                });

                images.forEach(img => imageObserver.observe(img));
            }

            // تحسين الأداء للجداول الكبيرة
            const tables = document.querySelectorAll('.table-responsive');
            tables.forEach(table => {
                if (table.scrollWidth > table.clientWidth) {
                    table.style.overflowX = 'auto';
                    table.style.webkitOverflowScrolling = 'touch';
                }
            });

            // تحسين النوافذ المنبثقة
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.addEventListener('shown.bs.modal', function() {
                    const firstInput = modal.querySelector('input:not([type="hidden"]), select, textarea');
                    if (firstInput && !firstInput.disabled) {
                        setTimeout(() => firstInput.focus(), 100);
                    }
                });
            });

            // تحسين الأزرار مع تأثير بصري
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    if (!this.disabled && !this.classList.contains('processing')) {
                        // تأثير الضغط
                        this.style.transform = 'scale(0.98)';
                        setTimeout(() => {
                            this.style.transform = '';
                        }, 100);

                        // إذا كان زر إرسال نموذج
                        if (this.type === 'submit' || this.classList.contains('submit-btn')) {
                            this.classList.add('processing');
                            const originalText = this.innerHTML;
                            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                            this.disabled = true;

                            // إعادة تعيين الزر بعد 10 ثوان كحد أقصى
                            setTimeout(() => {
                                this.innerHTML = originalText;
                                this.disabled = false;
                                this.classList.remove('processing');
                            }, 10000);
                        }
                    }
                });
            });

            // تحسين النماذج
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
                    if (submitBtn && !submitBtn.disabled) {
                        submitBtn.disabled = true;
                        const originalText = submitBtn.innerHTML || submitBtn.value;
                        if (submitBtn.tagName === 'BUTTON') {
                            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
                        }

                        // إعادة تفعيل الزر في حالة فشل الإرسال
                        setTimeout(() => {
                            submitBtn.disabled = false;
                            if (submitBtn.tagName === 'BUTTON') {
                                submitBtn.innerHTML = originalText;
                            }
                        }, 15000);
                    }
                });
            });

            // تحسين الإشعارات التلقائية
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                if (alert.classList.contains('alert-dismissible')) {
                    setTimeout(() => {
                        if (alert.parentNode) {
                            alert.style.transition = 'opacity 0.3s ease';
                            alert.style.opacity = '0';
                            setTimeout(() => {
                                if (alert.parentNode) {
                                    alert.remove();
                                }
                            }, 300);
                        }
                    }, 5000);
                }
            });

            // تحسين البحث المباشر
            const searchInputs = document.querySelectorAll('input[type="search"], .search-input, input[placeholder*="بحث"]');
            searchInputs.forEach(input => {
                let timeout;
                input.addEventListener('input', function() {
                    clearTimeout(timeout);
                    const query = this.value.trim();

                    if (query.length >= 2) {
                        timeout = setTimeout(() => {
                            performLiveSearch(query, this);
                        }, 300);
                    } else if (query.length === 0) {
                        clearSearchResults(this);
                    }
                });
            });

            // تحسين التنقل مع تأثيرات انتقال
            const navLinks = document.querySelectorAll('.nav-link, .sidebar a');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.href && !this.href.includes('#') && !this.target) {
                        e.preventDefault();
                        const url = this.href;

                        // إضافة تأثير التحميل
                        document.body.style.transition = 'opacity 0.2s ease';
                        document.body.style.opacity = '0.8';

                        setTimeout(() => {
                            window.location.href = url;
                        }, 100);
                    }
                });
            });

            // تحسين الذاكرة والتنظيف
            window.addEventListener('beforeunload', function() {
                // تنظيف المتغيرات العامة
                if (window.chartInstances) {
                    window.chartInstances.forEach(chart => {
                        if (chart && typeof chart.destroy === 'function') {
                            chart.destroy();
                        }
                    });
                }

                // تنظيف المؤقتات
                if (window.notificationInterval) {
                    clearInterval(window.notificationInterval);
                }
            });
        }

        // دالة البحث المباشر المحسنة
        function performLiveSearch(query, inputElement) {
            // يمكن تخصيص هذه الدالة حسب نوع الصفحة
            console.log('البحث المباشر عن:', query);

            // إضافة مؤشر التحميل
            const loadingIcon = inputElement.parentNode.querySelector('.search-loading');
            if (loadingIcon) {
                loadingIcon.style.display = 'inline-block';
            }

            // محاكاة البحث (يمكن استبدالها بطلب AJAX حقيقي)
            setTimeout(() => {
                if (loadingIcon) {
                    loadingIcon.style.display = 'none';
                }
            }, 500);
        }

        // دالة مسح نتائج البحث
        function clearSearchResults(inputElement) {
            const resultsContainer = inputElement.parentNode.querySelector('.search-results');
            if (resultsContainer) {
                resultsContainer.innerHTML = '';
                resultsContainer.style.display = 'none';
            }
        }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
