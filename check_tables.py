#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_tables():
    try:
        conn = sqlite3.connect('instance/lawoffice.db')
        cursor = conn.cursor()
        cursor.execute('SELECT name FROM sqlite_master WHERE type="table"')
        tables = cursor.fetchall()
        print('الجداول الموجودة:')
        for table in tables:
            print(f'  - {table[0]}')
        
        # فحص بنية جدول case
        for table_name in ['case', 'cases']:
            try:
                cursor.execute(f'PRAGMA table_info({table_name})')
                columns = cursor.fetchall()
                if columns:
                    print(f'\nأعمدة جدول {table_name}:')
                    for col in columns:
                        print(f'  - {col[1]} ({col[2]})')
                    break
            except:
                continue
        
        conn.close()
        
    except Exception as e:
        print(f'❌ خطأ: {e}')

if __name__ == '__main__':
    check_tables()
