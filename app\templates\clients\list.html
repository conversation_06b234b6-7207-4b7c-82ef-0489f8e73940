{% extends "base.html" %}

{% block title %}قائمة العملاء{% endblock %}
{% block page_title %}قائمة العملاء{% endblock %}

{% block content %}
<style>
    /* ===== تصميم صفحة العملاء الاحترافية ===== */
    .page-header {
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-2xl);
        box-shadow: var(--shadow-md);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .page-header h1 {
        font-size: var(--text-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .search-section {
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-2xl);
        box-shadow: var(--shadow-md);
    }
    
    .search-form {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: var(--spacing-md);
        align-items: end;
    }
    
    .search-input {
        position: relative;
    }
    
    .search-input input {
        width: 100%;
        padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md) 3rem;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        font-size: var(--text-base);
        transition: var(--transition-normal);
    }
    
    .search-input input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .search-input i {
        position: absolute;
        left: var(--spacing-md);
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
    }
    
    .clients-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: var(--spacing-xl);
    }
    
    .client-card {
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-md);
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
    }
    
    .client-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }
    
    .client-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
        border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    }
    
    .client-header {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }
    
    .client-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-white);
        font-size: var(--text-xl);
        font-weight: 700;
        box-shadow: var(--shadow-md);
    }
    
    .client-info h3 {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;
    }
    
    .client-info .client-id {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }
    
    .client-details {
        margin-bottom: var(--spacing-lg);
    }
    
    .detail-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
        font-size: var(--text-sm);
    }
    
    .detail-item i {
        width: 16px;
        color: var(--text-secondary);
    }
    
    .client-actions {
        display: flex;
        gap: var(--spacing-sm);
        justify-content: flex-end;
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--border-color);
    }
    
    .btn-action {
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-md);
        font-size: var(--text-sm);
        font-weight: 500;
        text-decoration: none;
        transition: var(--transition-normal);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        border: none;
        cursor: pointer;
    }
    
    .btn-view {
        background: var(--bg-tertiary);
        color: var(--text-primary);
        border: 1px solid var(--border-color);
    }
    
    .btn-view:hover {
        background: var(--primary-color);
        color: var(--text-white);
    }
    
    .btn-edit {
        background: var(--warning-color);
        color: var(--text-white);
    }
    
    .btn-edit:hover {
        background: #d97706;
    }
    
    .btn-delete {
        background: var(--danger-color);
        color: var(--text-white);
    }
    
    .btn-delete:hover {
        background: #dc2626;
    }
    
    .empty-state {
        text-align: center;
        padding: var(--spacing-4xl);
        color: var(--text-secondary);
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-md);
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: var(--spacing-lg);
        opacity: 0.5;
    }
    
    /* ===== الرسوم المتحركة ===== */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .client-card {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;
    }
    
    .client-card:nth-child(1) { animation-delay: 0.1s; }
    .client-card:nth-child(2) { animation-delay: 0.2s; }
    .client-card:nth-child(3) { animation-delay: 0.3s; }
    .client-card:nth-child(4) { animation-delay: 0.4s; }
    .client-card:nth-child(5) { animation-delay: 0.5s; }
    .client-card:nth-child(6) { animation-delay: 0.6s; }
    
    /* ===== الاستجابة للشاشات الصغيرة ===== */
    @media (max-width: 768px) {
        .clients-grid {
            grid-template-columns: 1fr;
        }
        
        .search-form {
            grid-template-columns: 1fr;
        }
        
        .page-header {
            flex-direction: column;
            gap: var(--spacing-md);
            align-items: stretch;
        }
    }
</style>

<!-- رأس الصفحة -->
<div class="page-header">
    <h1>
        <i class="fas fa-users"></i>
        قائمة العملاء
    </h1>
    <div class="btn-group">
        <a href="{{ url_for('add_client') }}" class="btn btn-success">
            <i class="fas fa-plus"></i>
            إضافة عميل جديد
        </a>
        <button type="button" class="btn btn-primary"
                onclick="openFormModal('{{ url_for('modal_add_client') }}', 'إضافة عميل جديد', {
                    size: 'xl',
                    onSuccess: function() { window.location.reload(); }
                })">
            <i class="fas fa-plus-circle"></i>
            إضافة سريعة
        </button>
    </div>
</div>

<!-- قسم البحث -->
<div class="search-section">
    <form class="search-form" method="GET">
        <div class="search-input">
            <i class="fas fa-search"></i>
            <input type="text" name="search" placeholder="البحث عن عميل..." value="{{ request.args.get('search', '') }}">
        </div>
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-search"></i>
            بحث
        </button>
    </form>
</div>

<!-- قائمة العملاء -->
{% if clients %}
<div class="clients-grid">
    {% for client in clients %}
    <div class="client-card">
        <div class="client-header">
            <div class="client-avatar">
                {{ client.name[0] if client.name else 'ع' }}
            </div>
            <div class="client-info">
                <h3>{{ client.name }}</h3>
                <div class="client-id">رقم العميل: {{ client.id }}</div>
            </div>
        </div>
        
        <div class="client-details">
            {% if client.phone %}
            <div class="detail-item">
                <i class="fas fa-phone"></i>
                <span>{{ client.phone }}</span>
            </div>
            {% endif %}
            
            {% if client.email %}
            <div class="detail-item">
                <i class="fas fa-envelope"></i>
                <span>{{ client.email }}</span>
            </div>
            {% endif %}
            
            {% if client.address %}
            <div class="detail-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>{{ client.address }}</span>
            </div>
            {% endif %}
            
            <div class="detail-item">
                <i class="fas fa-calendar"></i>
                <span>تاريخ التسجيل: {{ client.created_at.strftime('%Y-%m-%d') if client.created_at else 'غير محدد' }}</span>
            </div>
        </div>
        
        <div class="client-actions">
            <a href="{{ url_for('edit_client', client_id=client.id) }}" class="btn-action btn-edit">
                <i class="fas fa-edit"></i>
                تعديل
            </a>
            <button type="button" class="btn-action btn-delete" onclick="confirmDelete({{ client.id }}, '{{ client.name }}')">
                <i class="fas fa-trash"></i>
                حذف
            </button>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="empty-state">
    <i class="fas fa-users"></i>
    <h3>لا توجد عملاء</h3>
    <p>لم يتم العثور على أي عملاء. ابدأ بإضافة عميل جديد.</p>
    <div class="btn-group">
        <a href="{{ url_for('add_client') }}" class="btn btn-success">
            <i class="fas fa-plus"></i>
            إضافة عميل جديد
        </a>
        <button type="button" class="btn btn-primary"
                onclick="openFormModal('{{ url_for('modal_add_client') }}', 'إضافة عميل جديد', {
                    size: 'xl',
                    onSuccess: function() { window.location.reload(); }
                })">
            <i class="fas fa-plus-circle"></i>
            إضافة سريعة
        </button>
    </div>
</div>
{% endif %}



<script>
function confirmDelete(clientId, clientName) {
    if (confirm('هل أنت متأكد من حذف العميل "' + clientName + '"؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        // إرسال طلب الحذف
        fetch('/clients/' + clientId + '/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف العميل');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف العميل');
        });
    }
}
</script>
{% endblock %}
