{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
    <h2 class="mb-4 text-success">إضافة جلسة جديدة</h2>
    <form method="post">
        {% if courts %}
        <div class="mb-3">
            <label for="court_select" class="form-label">المحكمة</label>
            <select id="court_select" class="form-select" onchange="document.getElementById('court').value=this.value">
                <option value="">اختر المحكمة</option>
                {% for court in courts %}
                <option value="{{ court }}">{{ court }}</option>
                {% endfor %}
            </select>
            <input type="hidden" name="court" id="court">
        </div>
        {% endif %}
        <div class="mb-3">
            <label for="client_id" class="form-label">العميل</label>
            <select name="client_id" id="client_id" class="form-select" required onchange="filterCasesByClient()">
                <option value="">اختر العميل</option>
                {% for client in clients %}
                <option value="{{ client.id }}">{{ client.name }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="mb-3">
            <label for="case_id" class="form-label">القضية</label>
            <select name="case_id" id="case_id" class="form-select" required onchange="updateCourtFromCase()">
                <option value="">اختر القضية</option>
                {% for case in cases %}
                <option value="{{ case.id }}" data-client="{{ case.client_id }}" data-court="{{ case.court }}">{{ case.title }} ({{ case.client.name }})</option>
                {% endfor %}
            </select>
        </div>
        <div class="mb-3">
            <label for="date" class="form-label">تاريخ ووقت الجلسة</label>
            <input type="datetime-local" name="date" id="date" class="form-control" required>
        </div>
        <div class="mb-3">
            <label for="notes" class="form-label">ملاحظات</label>
            <textarea name="notes" id="notes" class="form-control"></textarea>
        </div>
        <button type="submit" class="btn btn-primary">حفظ الجلسة</button>
        <a href="{{ url_for('sessions_list') }}" class="btn btn-secondary">إلغاء</a>
    </form>
</div>
<script>
function filterCasesByClient() {
    var clientId = document.getElementById('client_id').value;
    var caseSelect = document.getElementById('case_id');
    Array.from(caseSelect.options).forEach(function(opt) {
        if (!opt.value) return;
        opt.style.display = (opt.getAttribute('data-client') === clientId) ? '' : 'none';
    });
    caseSelect.value = '';
    updateCourtFromCase();
}
function updateCourtFromCase() {
    var select = document.getElementById('case_id');
    var courtInput = document.getElementById('court');
    var selected = select.options[select.selectedIndex];
    if(selected && selected.getAttribute('data-court')) {
        courtInput.value = selected.getAttribute('data-court');
        if(document.getElementById('court_select'))
            document.getElementById('court_select').value = selected.getAttribute('data-court');
    }
}
document.addEventListener('DOMContentLoaded', function() {
    filterCasesByClient();
});
</script>
{% endblock %}
