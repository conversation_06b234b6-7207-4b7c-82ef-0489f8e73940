{% extends "base.html" %}

{% block title %}إدارة العقارات{% endblock %}

{% block extra_css %}
<style>
    .property-card {
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        transition: all 0.3s ease;
        background: white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .property-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .property-image {
        height: 200px;
        object-fit: cover;
        border-radius: 12px 12px 0 0;
    }
    
    .property-status {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
    }
    
    .status-available { background: #d4edda; color: #155724; }
    .status-rented { background: #f8d7da; color: #721c24; }
    .status-maintenance { background: #fff3cd; color: #856404; }
    
    .property-price {
        font-size: 1.5rem;
        font-weight: bold;
        color: #28a745;
    }
    
    .property-details {
        display: flex;
        justify-content: space-between;
        margin: 10px 0;
    }
    
    .detail-item {
        text-align: center;
        flex: 1;
    }
    
    .detail-icon {
        font-size: 1.2rem;
        color: #6c757d;
    }
    
    .filter-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .btn-add-property {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-add-property:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        color: white;
    }
    
    .search-box {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 12px 20px;
        transition: all 0.3s ease;
    }
    
    .search-box:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-0">
                <i class="fas fa-building text-primary"></i>
                إدارة العقارات
            </h2>
            <p class="text-muted">إدارة شاملة ومتطورة لجميع العقارات</p>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-add-property" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
                <i class="fas fa-plus"></i>
                إضافة عقار جديد
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">{{ total_properties or 0 }}</div>
                <div>إجمالي العقارات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="stats-number">{{ available_properties or 0 }}</div>
                <div>عقارات متاحة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);">
                <div class="stats-number">{{ rented_properties or 0 }}</div>
                <div>عقارات مؤجرة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <div class="stats-number">{{ total_income or 0 }} ₪</div>
                <div>الدخل الشهري</div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <form method="GET" id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <input type="text" class="form-control search-box" name="search" 
                           placeholder="البحث في العقارات..." value="{{ request.args.get('search', '') }}">
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="متاح" {{ 'selected' if request.args.get('status') == 'متاح' }}>متاح</option>
                        <option value="مؤجر" {{ 'selected' if request.args.get('status') == 'مؤجر' }}>مؤجر</option>
                        <option value="صيانة" {{ 'selected' if request.args.get('status') == 'صيانة' }}>صيانة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="property_type">
                        <option value="">جميع الأنواع</option>
                        <option value="شقة" {{ 'selected' if request.args.get('property_type') == 'شقة' }}>شقة</option>
                        <option value="فيلا" {{ 'selected' if request.args.get('property_type') == 'فيلا' }}>فيلا</option>
                        <option value="مكتب" {{ 'selected' if request.args.get('property_type') == 'مكتب' }}>مكتب</option>
                        <option value="محل تجاري" {{ 'selected' if request.args.get('property_type') == 'محل تجاري' }}>محل تجاري</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="city">
                        <option value="">جميع المدن</option>
                        {% for city in cities %}
                        <option value="{{ city }}" {{ 'selected' if request.args.get('city') == city }}>{{ city }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
                <div class="col-md-1">
                    <a href="{{ url_for('properties') }}" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Properties Grid -->
    <div class="row">
        {% if properties %}
            {% for property in properties %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="property-card">
                    <div class="position-relative">
                        {% if property.main_image %}
                        <img src="{{ property.main_image }}" class="card-img-top property-image" alt="{{ property.name }}">
                        {% else %}
                        <img src="{{ url_for('static', filename='images/default-property.jpg') }}" 
                             class="card-img-top property-image" alt="صورة افتراضية">
                        {% endif %}
                        
                        <span class="property-status status-{{ property.status|replace('متاح', 'available')|replace('مؤجر', 'rented')|replace('صيانة', 'maintenance') }}">
                            {{ property.status }}
                        </span>
                    </div>
                    
                    <div class="card-body">
                        <h5 class="card-title">{{ property.name }}</h5>
                        <p class="text-muted mb-2">
                            <i class="fas fa-map-marker-alt"></i>
                            {{ property.city }}, {{ property.district }}
                        </p>
                        
                        <div class="property-price mb-3">
                            {{ property.monthly_rent or 0 }} ₪/شهر
                        </div>
                        
                        <div class="property-details">
                            <div class="detail-item">
                                <div class="detail-icon">
                                    <i class="fas fa-bed"></i>
                                </div>
                                <small>{{ property.bedrooms or 0 }} غرفة</small>
                            </div>
                            <div class="detail-item">
                                <div class="detail-icon">
                                    <i class="fas fa-bath"></i>
                                </div>
                                <small>{{ property.bathrooms or 0 }} حمام</small>
                            </div>
                            <div class="detail-item">
                                <div class="detail-icon">
                                    <i class="fas fa-ruler-combined"></i>
                                </div>
                                <small>{{ property.area or 0 }} م²</small>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-3">
                            <a href="{{ url_for('property_details', id=property.id) }}" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i> عرض
                            </a>
                            <a href="{{ url_for('edit_property', id=property.id) }}" 
                               class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <button class="btn btn-outline-danger btn-sm" 
                                    onclick="deleteProperty({{ property.id }})">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-building fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد عقارات</h4>
                    <p class="text-muted">ابدأ بإضافة عقار جديد</p>
                    <button class="btn btn-add-property" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
                        <i class="fas fa-plus"></i>
                        إضافة عقار جديد
                    </button>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if properties.pages > 1 %}
    <nav aria-label="تنقل الصفحات">
        <ul class="pagination justify-content-center">
            {% if properties.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('properties', page=properties.prev_num, **request.args) }}">السابق</a>
            </li>
            {% endif %}
            
            {% for page_num in properties.iter_pages() %}
                {% if page_num %}
                    {% if page_num != properties.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('properties', page=page_num, **request.args) }}">{{ page_num }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if properties.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('properties', page=properties.next_num, **request.args) }}">التالي</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- Add Property Modal -->
{% include 'properties/modals/add_property_modal.html' %}

{% endblock %}

{% block extra_js %}
<script>
function deleteProperty(propertyId) {
    if (confirm('هل أنت متأكد من حذف هذا العقار؟')) {
        fetch(`/properties/delete/${propertyId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء الحذف');
            }
        });
    }
}

// Auto-submit filter form on change
document.querySelectorAll('#filterForm select').forEach(select => {
    select.addEventListener('change', () => {
        document.getElementById('filterForm').submit();
    });
});
</script>
{% endblock %}
