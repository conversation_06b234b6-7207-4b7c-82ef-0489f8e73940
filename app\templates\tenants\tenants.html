{% extends "base.html" %}

{% block title %}إدارة المستأجرين{% endblock %}

{% block extra_css %}
<style>
    .tenant-card {
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        transition: all 0.3s ease;
        background: white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .tenant-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .tenant-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #fff;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .tenant-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        text-align: center;
    }
    
    .credit-score {
        display: inline-block;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: bold;
        margin-top: 10px;
    }
    
    .score-excellent { background: #d4edda; color: #155724; }
    .score-good { background: #cce5ff; color: #004085; }
    .score-fair { background: #fff3cd; color: #856404; }
    .score-poor { background: #f8d7da; color: #721c24; }
    
    .tenant-info {
        padding: 20px;
    }
    
    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f1f1f1;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
    }
    
    .info-value {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .tenant-actions {
        padding: 15px 20px;
        background: #f8f9fa;
        display: flex;
        gap: 10px;
        justify-content: center;
    }
    
    .btn-tenant-action {
        flex: 1;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.85rem;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        margin-bottom: 20px;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .filter-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
    }
    
    .btn-add-tenant {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-add-tenant:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        color: white;
    }
    
    .search-box {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 12px 20px;
        transition: all 0.3s ease;
    }
    
    .search-box:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    
    .lease-status {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: bold;
    }
    
    .status-active { background: #d4edda; color: #155724; }
    .status-expired { background: #f8d7da; color: #721c24; }
    .status-none { background: #e2e3e5; color: #6c757d; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-0">
                <i class="fas fa-users text-primary"></i>
                إدارة المستأجرين
            </h2>
            <p class="text-muted">إدارة شاملة ومتطورة لجميع المستأجرين</p>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-add-tenant" data-bs-toggle="modal" data-bs-target="#addTenantModal">
                <i class="fas fa-user-plus"></i>
                إضافة مستأجر جديد
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">{{ total_tenants or 0 }}</div>
                <div>إجمالي المستأجرين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="stats-number">{{ active_tenants or 0 }}</div>
                <div>مستأجرين نشطين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <div class="stats-number">{{ avg_credit_score or 0 }}</div>
                <div>متوسط التقييم الائتماني</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                <div class="stats-number">{{ new_tenants_this_month or 0 }}</div>
                <div>مستأجرين جدد هذا الشهر</div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <form method="GET" id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <input type="text" class="form-control search-box" name="search" 
                           placeholder="البحث في المستأجرين..." value="{{ request.args.get('search', '') }}">
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="nationality">
                        <option value="">جميع الجنسيات</option>
                        {% for nationality in nationalities %}
                        <option value="{{ nationality }}" {{ 'selected' if request.args.get('nationality') == nationality }}>{{ nationality }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="employment_status">
                        <option value="">جميع حالات العمل</option>
                        <option value="موظف" {{ 'selected' if request.args.get('employment_status') == 'موظف' }}>موظف</option>
                        <option value="أعمال حرة" {{ 'selected' if request.args.get('employment_status') == 'أعمال حرة' }}>أعمال حرة</option>
                        <option value="متقاعد" {{ 'selected' if request.args.get('employment_status') == 'متقاعد' }}>متقاعد</option>
                        <option value="عاطل" {{ 'selected' if request.args.get('employment_status') == 'عاطل' }}>عاطل</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="credit_score_range">
                        <option value="">جميع التقييمات</option>
                        <option value="excellent" {{ 'selected' if request.args.get('credit_score_range') == 'excellent' }}>ممتاز (800+)</option>
                        <option value="good" {{ 'selected' if request.args.get('credit_score_range') == 'good' }}>جيد (700-799)</option>
                        <option value="fair" {{ 'selected' if request.args.get('credit_score_range') == 'fair' }}>مقبول (600-699)</option>
                        <option value="poor" {{ 'selected' if request.args.get('credit_score_range') == 'poor' }}>ضعيف (<600)</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
                <div class="col-md-1">
                    <a href="{{ url_for('tenants') }}" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Tenants Grid -->
    <div class="row">
        {% if tenants %}
            {% for tenant in tenants %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="tenant-card">
                    <div class="tenant-header">
                        {% if tenant.photo %}
                        <img src="{{ tenant.photo }}" class="tenant-avatar" alt="{{ tenant.full_name }}">
                        {% else %}
                        <img src="{{ url_for('static', filename='images/default-avatar.png') }}" 
                             class="tenant-avatar" alt="صورة افتراضية">
                        {% endif %}
                        
                        <h5 class="mt-3 mb-1">{{ tenant.full_name }}</h5>
                        <small>{{ tenant.nationality }}</small>
                        
                        {% set score_class = 'score-poor' %}
                        {% if tenant.credit_score >= 800 %}
                            {% set score_class = 'score-excellent' %}
                        {% elif tenant.credit_score >= 700 %}
                            {% set score_class = 'score-good' %}
                        {% elif tenant.credit_score >= 600 %}
                            {% set score_class = 'score-fair' %}
                        {% endif %}
                        
                        <div class="credit-score {{ score_class }}">
                            تقييم ائتماني: {{ tenant.credit_score or 0 }}
                        </div>
                    </div>
                    
                    <div class="tenant-info">
                        <div class="info-item">
                            <span class="info-label">رقم الهوية:</span>
                            <span class="info-value">{{ tenant.id_number or 'غير محدد' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">{{ tenant.phone or 'غير محدد' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">العمل:</span>
                            <span class="info-value">{{ tenant.employment_status or 'غير محدد' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الراتب:</span>
                            <span class="info-value">{{ tenant.monthly_salary or 0 }} {{ tenant.salary_currency or 'JOD' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">حالة الإيجار:</span>
                            {% if tenant.current_lease %}
                                <span class="lease-status status-active">نشط</span>
                            {% else %}
                                <span class="lease-status status-none">بدون عقد</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="tenant-actions">
                        <a href="{{ url_for('tenant_details', id=tenant.id) }}" 
                           class="btn btn-outline-primary btn-tenant-action">
                            <i class="fas fa-eye"></i> عرض
                        </a>
                        <a href="{{ url_for('edit_tenant', id=tenant.id) }}" 
                           class="btn btn-outline-warning btn-tenant-action">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <button class="btn btn-outline-danger btn-tenant-action" 
                                onclick="deleteTenant({{ tenant.id }})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-users fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">لا يوجد مستأجرين</h4>
                    <p class="text-muted">ابدأ بإضافة مستأجر جديد</p>
                    <button class="btn btn-add-tenant" data-bs-toggle="modal" data-bs-target="#addTenantModal">
                        <i class="fas fa-user-plus"></i>
                        إضافة مستأجر جديد
                    </button>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if tenants.pages > 1 %}
    <nav aria-label="تنقل الصفحات">
        <ul class="pagination justify-content-center">
            {% if tenants.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('tenants', page=tenants.prev_num, **request.args) }}">السابق</a>
            </li>
            {% endif %}
            
            {% for page_num in tenants.iter_pages() %}
                {% if page_num %}
                    {% if page_num != tenants.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('tenants', page=page_num, **request.args) }}">{{ page_num }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if tenants.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('tenants', page=tenants.next_num, **request.args) }}">التالي</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- Add Tenant Modal -->
{% include 'tenants/modals/add_tenant_modal.html' %}

{% endblock %}

{% block extra_js %}
<script>
function deleteTenant(tenantId) {
    if (confirm('هل أنت متأكد من حذف هذا المستأجر؟')) {
        fetch(`/tenants/delete/${tenantId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء الحذف: ' + data.message);
            }
        });
    }
}

// Auto-submit filter form on change
document.querySelectorAll('#filterForm select').forEach(select => {
    select.addEventListener('change', () => {
        document.getElementById('filterForm').submit();
    });
});
</script>
{% endblock %}
