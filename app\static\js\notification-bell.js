/**
 * نظام إدارة الإشعارات والتنبيهات
 */
class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.overdueItems = [];
        this.isDropdownOpen = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadOverdueItems();
        this.startPeriodicCheck();
    }

    bindEvents() {
        // زر الإشعارات
        const notificationToggle = document.getElementById('notificationToggle');
        if (notificationToggle) {
            notificationToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown();
            });
        }

        // زر تحديد الكل كمقروء
        const markAllRead = document.getElementById('markAllRead');
        if (markAllRead) {
            markAllRead.addEventListener('click', () => {
                this.markAllAsRead();
            });
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', (e) => {
            const dropdown = document.getElementById('notificationDropdown');
            const toggle = document.getElementById('notificationToggle');
            
            if (dropdown && toggle && !dropdown.contains(e.target) && !toggle.contains(e.target)) {
                this.closeDropdown();
            }
        });
    }

    toggleDropdown() {
        const dropdown = document.getElementById('notificationDropdown');
        if (dropdown) {
            if (this.isDropdownOpen) {
                this.closeDropdown();
            } else {
                this.openDropdown();
            }
        }
    }

    openDropdown() {
        const dropdown = document.getElementById('notificationDropdown');
        if (dropdown) {
            dropdown.style.display = 'block';
            this.isDropdownOpen = true;
            this.loadNotifications();
        }
    }

    closeDropdown() {
        const dropdown = document.getElementById('notificationDropdown');
        if (dropdown) {
            dropdown.style.display = 'none';
            this.isDropdownOpen = false;
        }
    }

    async loadOverdueItems() {
        try {
            // محاكاة البيانات المتأخرة
            this.overdueItems = await this.getOverdueItemsFromSystem();
            this.updateNotificationBadge();
            this.createPersistentAlerts();
        } catch (error) {
            console.error('خطأ في تحميل العناصر المتأخرة:', error);
        }
    }

    async getOverdueItemsFromSystem() {
        try {
            const response = await fetch('/api/overdue-items');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            if (data.success) {
                return data.overdue_items || [];
            } else {
                console.error('خطأ في API:', data.error);
                return this.getFallbackData();
            }
        } catch (error) {
            console.error('خطأ في الاتصال بـ API:', error);
            return this.getFallbackData();
        }
    }

    getFallbackData() {
        // بيانات احتياطية في حالة فشل API
        const today = new Date();
        return [
            {
                id: 'task_1',
                type: 'task',
                title: 'مراجعة ملف القضية رقم 123',
                due_date: new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                priority: 'عالية',
                days_overdue: 2
            },
            {
                id: 'appointment_1',
                type: 'appointment',
                title: 'موعد مع العميل أحمد محمد',
                date: today.toISOString().split('T')[0],
                time: '14:00',
                location: 'المكتب',
                status: 'upcoming',
                hours_diff: 2
            }
        ];
    }

    updateNotificationBadge() {
        const badge = document.getElementById('notificationBadge');
        if (badge) {
            const count = this.overdueItems.length;
            if (count > 0) {
                badge.textContent = count;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    createPersistentAlerts() {
        // إنشاء تنبيهات مستمرة للعناصر المتأخرة
        this.overdueItems.forEach(item => {
            this.createNotification(item);
        });

        // إظهار شاشة منبثقة للعناصر المهمة المتأخرة
        const criticalItems = this.overdueItems.filter(item =>
            (item.type === 'task' && (item.days_overdue > 1 || item.priority === 'عالية')) ||
            (item.type === 'appointment' && item.status === 'overdue') ||
            (item.type === 'payment' && item.days_overdue > 3)
        );

        if (criticalItems.length > 0) {
            this.showCriticalAlertsModal(criticalItems);
        }
    }

    showCriticalAlertsModal(criticalItems) {
        // إنشاء شاشة منبثقة للتنبيهات المهمة
        const modalHtml = `
            <div class="modal fade" id="criticalAlertsModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                تنبيهات مهمة - عناصر متأخرة
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-info-circle me-2"></i>
                                يوجد ${criticalItems.length} عنصر مهم يتطلب انتباهك الفوري
                            </div>
                            <div class="critical-alerts-list">
                                ${criticalItems.map(item => `
                                    <div class="alert alert-${this.getAlertType(item)} d-flex align-items-center">
                                        <i class="fas ${this.getItemIcon(item)} me-3"></i>
                                        <div class="flex-grow-1">
                                            <strong>${this.getNotificationTitle(item)}</strong><br>
                                            <small>${this.getNotificationMessage(item)}</small>
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary" onclick="window.notificationSystem.handleItemAction('${item.id}', '${item.type}')">
                                            عرض التفاصيل
                                        </button>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="window.notificationSystem.markAllCriticalAsRead()">
                                تم الاطلاع على الجميع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة الشاشة المنبثقة إلى الصفحة
        const existingModal = document.getElementById('criticalAlertsModal');
        if (existingModal) {
            existingModal.remove();
        }

        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // إظهار الشاشة المنبثقة
        const modal = new bootstrap.Modal(document.getElementById('criticalAlertsModal'));
        modal.show();
    }

    getAlertType(item) {
        switch (item.type) {
            case 'task':
                return item.priority === 'عالية' ? 'danger' : 'warning';
            case 'appointment':
                return 'danger';
            case 'payment':
                return 'warning';
            default:
                return 'info';
        }
    }

    getItemIcon(item) {
        switch (item.type) {
            case 'task':
                return 'fa-tasks';
            case 'appointment':
                return 'fa-calendar-alt';
            case 'payment':
                return 'fa-money-bill';
            default:
                return 'fa-bell';
        }
    }

    handleItemAction(itemId, itemType) {
        // توجيه المستخدم إلى الصفحة المناسبة
        switch (itemType) {
            case 'task':
                window.location.href = '/tasks';
                break;
            case 'appointment':
                window.location.href = '/appointments';
                break;
            case 'payment':
                window.location.href = '/reports';
                break;
        }
    }

    markAllCriticalAsRead() {
        // تحديد جميع التنبيهات المهمة كمقروءة
        this.notifications.forEach(notification => {
            if (notification.isOverdue) {
                notification.isRead = true;
            }
        });

        this.updateNotificationBadge();
        this.loadNotifications();

        // إغلاق الشاشة المنبثقة
        const modal = bootstrap.Modal.getInstance(document.getElementById('criticalAlertsModal'));
        if (modal) {
            modal.hide();
        }
    }

    createNotification(item) {
        const notification = {
            id: `notification_${item.id}`,
            type: item.type,
            title: this.getNotificationTitle(item),
            message: this.getNotificationMessage(item),
            time: new Date(),
            isRead: false,
            isOverdue: true,
            originalItem: item
        };

        // إضافة الإشعار إلى القائمة إذا لم يكن موجوداً
        const existingIndex = this.notifications.findIndex(n => n.id === notification.id);
        if (existingIndex === -1) {
            this.notifications.unshift(notification);
        }
    }

    getNotificationTitle(item) {
        switch (item.type) {
            case 'task':
                return '⚠️ مهمة متأخرة';
            case 'appointment':
                return '📅 موعد متأخر';
            case 'payment':
                return '💰 استحقاق متأخر';
            default:
                return '🔔 تنبيه';
        }
    }

    getNotificationMessage(item) {
        switch (item.type) {
            case 'task':
                const daysOverdue = item.days_overdue || 0;
                const timeText = daysOverdue > 0 ? `منذ ${daysOverdue} يوم` : 'اليوم';
                return `${item.title} - مطلوب ${timeText}`;

            case 'appointment':
                if (item.status === 'overdue') {
                    const hoursOverdue = Math.floor(item.hours_diff || 0);
                    return `${item.title} - تأخر ${hoursOverdue} ساعة`;
                } else {
                    const hoursLeft = Math.floor(item.hours_diff || 0);
                    return `${item.title} - خلال ${hoursLeft} ساعة`;
                }

            case 'payment':
                const amount = item.amount || 0;
                const currency = item.currency || 'شيكل';
                return `${item.title} - ${amount} ${currency} - متأخر`;

            default:
                return item.title || item.description || 'إشعار';
        }
    }

    loadNotifications() {
        const notificationList = document.getElementById('notificationList');
        if (!notificationList) return;

        if (this.notifications.length === 0) {
            notificationList.innerHTML = '<div class="text-center p-3 text-muted">لا توجد إشعارات جديدة</div>';
            return;
        }

        const notificationsHtml = this.notifications.map(notification => {
            const timeAgo = this.getTimeAgo(notification.time);
            const unreadClass = notification.isRead ? '' : 'unread';
            const overdueClass = notification.isOverdue ? 'overdue' : '';
            
            return `
                <div class="notification-item ${unreadClass} ${overdueClass}" data-id="${notification.id}">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-text">${notification.message}</div>
                    <div class="notification-time">${timeAgo}</div>
                </div>
            `;
        }).join('');

        notificationList.innerHTML = notificationsHtml;

        // إضافة مستمعات الأحداث للإشعارات
        notificationList.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', () => {
                const notificationId = item.dataset.id;
                this.markAsRead(notificationId);
                this.handleNotificationClick(notificationId);
            });
        });
    }

    getTimeAgo(date) {
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'الآن';
        if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
        
        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
        
        const diffInDays = Math.floor(diffInHours / 24);
        return `منذ ${diffInDays} يوم`;
    }

    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
            notification.isRead = true;
            this.updateNotificationBadge();
        }
    }

    markAllAsRead() {
        this.notifications.forEach(notification => {
            notification.isRead = true;
        });
        this.updateNotificationBadge();
        this.loadNotifications();
    }

    handleNotificationClick(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification && notification.originalItem) {
            // توجيه المستخدم إلى الصفحة المناسبة
            this.navigateToItem(notification.originalItem);
        }
    }

    navigateToItem(item) {
        switch (item.type) {
            case 'task':
                window.location.href = '/manage_tasks';
                break;
            case 'appointment':
                window.location.href = '/appointments_list';
                break;
            case 'payment':
                if (item.title.includes('أتعاب')) {
                    window.location.href = '/fees_list';
                } else {
                    window.location.href = '/finance_list';
                }
                break;
        }
    }

    startPeriodicCheck() {
        // فحص دوري كل 5 دقائق للعناصر المتأخرة الجديدة
        setInterval(() => {
            this.loadOverdueItems();
        }, 5 * 60 * 1000);
    }
}

// تهيئة نظام الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    new NotificationSystem();
});
