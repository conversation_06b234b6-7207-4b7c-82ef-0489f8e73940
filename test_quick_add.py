#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import re
from bs4 import BeautifulSoup

BASE_URL = "http://localhost:5000"

def test_quick_add_csrf():
    """اختبار CSRF token في الإضافة السريعة"""
    print("🧪 اختبار CSRF token في الإضافة السريعة...")
    
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {
        'username': 'office',
        'password': '66889088'
    }
    
    login_response = session.post(f"{BASE_URL}/lawyersameh", data=login_data)
    if login_response.status_code != 200:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # الحصول على صفحة اللوحة الرئيسية
    dashboard_response = session.get(f"{BASE_URL}/dashboard")
    if dashboard_response.status_code != 200:
        print("❌ فشل في الوصول للوحة الرئيسية")
        return False
    
    # البحث عن CSRF token في الصفحة
    soup = BeautifulSoup(dashboard_response.text, 'html.parser')
    csrf_token = None
    
    # البحث عن CSRF token في meta tag
    csrf_meta = soup.find('meta', {'name': 'csrf-token'})
    if csrf_meta:
        csrf_token = csrf_meta.get('content')
    
    # البحث عن CSRF token في hidden input
    if not csrf_token:
        csrf_input = soup.find('input', {'name': 'csrf_token'})
        if csrf_input:
            csrf_token = csrf_input.get('value')
    
    if not csrf_token:
        print("❌ لم يتم العثور على CSRF token في الصفحة")
        return False
    
    print(f"✅ تم العثور على CSRF token: {csrf_token[:20]}...")
    
    # اختبار إضافة عميل سريع
    client_data = {
        'name': 'عميل تجريبي سريع',
        'phone': '0501234567',
        'email': '<EMAIL>',
        'address': 'عنوان تجريبي',
        'role': 'موكل',
        'csrf_token': csrf_token
    }
    
    print("🔄 محاولة إضافة عميل سريع...")
    add_response = session.post(f"{BASE_URL}/clients/add", data=client_data)
    
    print(f"📊 رمز الاستجابة: {add_response.status_code}")
    
    if add_response.status_code == 200:
        print("✅ تم إضافة العميل بنجاح!")
        return True
    elif add_response.status_code == 400:
        if "CSRF token is missing" in add_response.text:
            print("❌ CSRF token مفقود")
        elif "CSRF token is invalid" in add_response.text:
            print("❌ CSRF token غير صحيح")
        else:
            print(f"❌ خطأ 400: {add_response.text[:200]}")
        return False
    else:
        print(f"❌ خطأ غير متوقع: {add_response.status_code}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار الإضافة السريعة...")
    print("=" * 50)
    
    success = test_quick_add_csrf()
    
    print("=" * 50)
    if success:
        print("🎉 الاختبار نجح! الإضافة السريعة تعمل بشكل صحيح")
    else:
        print("💥 الاختبار فشل! هناك مشكلة في الإضافة السريعة")
