#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ربط الأزرار والحسابات في النظام
Test Buttons and Accounts Integration
"""

import requests
import time
from datetime import datetime

def test_login_functionality():
    """اختبار وظيفة تسجيل الدخول"""
    print("🔐 اختبار تسجيل الدخول:")
    print("-" * 30)
    
    base_url = "http://localhost:5000"
    
    # اختبار الوصول للصفحة الرئيسية
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("   ✅ الخادم يعمل بشكل صحيح")
            print(f"   📊 رمز الاستجابة: {response.status_code}")
            return True
        else:
            print(f"   ❌ مشكلة في الخادم: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"   ❌ لا يمكن الوصول للخادم: {e}")
        return False

def test_database_connections():
    """اختبار اتصالات قاعدة البيانات"""
    print("\n🗄️ اختبار اتصالات قاعدة البيانات:")
    print("-" * 40)
    
    try:
        # استيراد النماذج
        from app import app, db
        from app.models import User, Client, Case, Property, Tenant, Lease, FinancialTransaction
        
        with app.app_context():
            # اختبار الاستعلامات الأساسية
            tests = [
                ("المستخدمين", User.query.count()),
                ("العملاء", Client.query.count()),
                ("القضايا", Case.query.count()),
                ("العقارات", Property.query.count()),
                ("المستأجرين", Tenant.query.count()),
                ("عقود الإيجار", Lease.query.count()),
                ("المعاملات المالية", FinancialTransaction.query.count())
            ]
            
            all_passed = True
            for test_name, count in tests:
                if count >= 0:
                    print(f"   ✅ {test_name}: {count} سجل")
                else:
                    print(f"   ❌ {test_name}: خطأ في الاستعلام")
                    all_passed = False
            
            # اختبار العلاقات
            print("\n   🔗 اختبار العلاقات:")
            
            # اختبار علاقة القضايا بالعملاء
            cases_with_clients = Case.query.filter(Case.client_id.isnot(None)).count()
            total_cases = Case.query.count()
            
            if total_cases > 0:
                if cases_with_clients == total_cases:
                    print(f"   ✅ علاقة القضايا بالعملاء: {cases_with_clients}/{total_cases}")
                else:
                    print(f"   ⚠️ علاقة القضايا بالعملاء: {cases_with_clients}/{total_cases}")
                    all_passed = False
            
            # اختبار علاقة عقود الإيجار
            leases_with_properties = Lease.query.filter(Lease.property_id.isnot(None)).count()
            total_leases = Lease.query.count()
            
            if total_leases > 0:
                if leases_with_properties == total_leases:
                    print(f"   ✅ علاقة عقود الإيجار بالعقارات: {leases_with_properties}/{total_leases}")
                else:
                    print(f"   ⚠️ علاقة عقود الإيجار بالعقارات: {leases_with_properties}/{total_leases}")
                    all_passed = False
            
            return all_passed
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_route_accessibility():
    """اختبار إمكانية الوصول للروابط"""
    print("\n🌐 اختبار إمكانية الوصول للروابط:")
    print("-" * 40)
    
    # قائمة الروابط المهمة للاختبار
    routes_to_test = [
        ("/", "الصفحة الرئيسية"),
        ("/login", "صفحة تسجيل الدخول"),
        ("/modal/add_client", "نافذة إضافة عميل"),
        ("/modal/add_case", "نافذة إضافة قضية"),
        ("/modal/add_property", "نافذة إضافة عقار")
    ]
    
    base_url = "http://localhost:5000"
    accessible_routes = 0
    
    for route, description in routes_to_test:
        try:
            response = requests.get(f"{base_url}{route}", timeout=5)
            if response.status_code in [200, 302, 401]:  # 401 للصفحات المحمية
                print(f"   ✅ {description}: متاح")
                accessible_routes += 1
            else:
                print(f"   ❌ {description}: غير متاح ({response.status_code})")
        except requests.exceptions.RequestException:
            print(f"   ❌ {description}: خطأ في الاتصال")
    
    success_rate = (accessible_routes / len(routes_to_test)) * 100
    print(f"\n   📊 معدل النجاح: {success_rate:.1f}% ({accessible_routes}/{len(routes_to_test)})")
    
    return success_rate >= 80

def test_modal_forms():
    """اختبار نماذج النوافذ المنبثقة"""
    print("\n📝 اختبار نماذج النوافذ المنبثقة:")
    print("-" * 40)
    
    modal_files = [
        "app/templates/modals/add_client.html",
        "app/templates/modals/add_case.html", 
        "app/templates/modals/add_property.html",
        "app/templates/modals/add_tenant.html",
        "app/templates/modals/add_lease.html"
    ]
    
    valid_modals = 0
    
    for modal_file in modal_files:
        try:
            with open(modal_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص العناصر المطلوبة
            has_form = '<form' in content
            has_csrf = 'csrf_token' in content
            has_action = 'action=' in content
            has_method = 'method="POST"' in content
            
            if has_form and has_csrf and has_action and has_method:
                print(f"   ✅ {modal_file.split('/')[-1]}: صحيح")
                valid_modals += 1
            else:
                missing = []
                if not has_form: missing.append("form")
                if not has_csrf: missing.append("csrf_token")
                if not has_action: missing.append("action")
                if not has_method: missing.append("method")
                print(f"   ❌ {modal_file.split('/')[-1]}: ناقص ({', '.join(missing)})")
                
        except FileNotFoundError:
            print(f"   ❌ {modal_file.split('/')[-1]}: غير موجود")
        except Exception as e:
            print(f"   ❌ {modal_file.split('/')[-1]}: خطأ - {e}")
    
    success_rate = (valid_modals / len(modal_files)) * 100
    print(f"\n   📊 معدل صحة النماذج: {success_rate:.1f}% ({valid_modals}/{len(modal_files)})")
    
    return success_rate >= 80

def generate_integration_report():
    """إنشاء تقرير التكامل النهائي"""
    print("\n" + "=" * 60)
    print("📋 تقرير التكامل النهائي للأزرار والحسابات")
    print("=" * 60)
    print(f"📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # تشغيل جميع الاختبارات
    tests_results = {
        "تسجيل الدخول": test_login_functionality(),
        "قاعدة البيانات": test_database_connections(),
        "الروابط": test_route_accessibility(),
        "النماذج": test_modal_forms()
    }
    
    # حساب النتيجة الإجمالية
    passed_tests = sum(tests_results.values())
    total_tests = len(tests_results)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"\n📊 ملخص النتائج:")
    print("-" * 30)
    
    for test_name, result in tests_results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 النتيجة الإجمالية: {success_rate:.1f}% ({passed_tests}/{total_tests})")
    
    if success_rate >= 90:
        print("\n🎉 ممتاز! النظام يعمل بشكل مثالي")
        print("✅ جميع الأزرار والحسابات مرتبطة بشكل صحيح")
        print("🚀 النظام جاهز للاستخدام الإنتاجي")
        final_status = "EXCELLENT"
    elif success_rate >= 75:
        print("\n👍 جيد! النظام يعمل بشكل جيد مع مشاكل بسيطة")
        print("🔧 يُنصح بإصلاح المشاكل البسيطة")
        final_status = "GOOD"
    else:
        print("\n⚠️ النظام يحتاج إلى إصلاحات")
        print("🔧 يجب إصلاح المشاكل قبل الاستخدام")
        final_status = "NEEDS_REPAIR"
    
    print("\n" + "=" * 60)
    print(f"🏁 الحالة النهائية: {final_status}")
    print("=" * 60)
    
    return final_status

if __name__ == "__main__":
    print("🧪 بدء اختبار ربط الأزرار والحسابات...")
    
    # انتظار قصير للتأكد من تشغيل الخادم
    print("⏳ انتظار تشغيل الخادم...")
    time.sleep(2)
    
    final_status = generate_integration_report()
    
    if final_status in ["EXCELLENT", "GOOD"]:
        print("\n✅ اكتمل الاختبار بنجاح!")
    else:
        print("\n⚠️ الاختبار كشف عن مشاكل تحتاج إلى إصلاح")
