{% extends 'base.html' %}
{% block title %}قائمة العقارات{% endblock %}
{% block head %}
<style>
    .avatar-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: linear-gradient(135deg, #0d6efd 60%, #6c757d 100%);
        color: #fff;
        font-weight: bold;
        font-size: 1.1rem;
        margin-left: 8px;
        box-shadow: 0 2px 6px #eee;
    }
    .table thead th { vertical-align: middle; }
</style>
{% endblock %}
{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-building"></i> إدارة العقارات</h1>
                    <p>عرض وإدارة جميع العقارات المسجلة في النظام</p>
                </div>
                <div class="page-actions">
                    <!-- تم نقل أزرار الإضافة إلى داخل البطاقة -->
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list text-primary me-2"></i>
                            قائمة العقارات
                        </h5>
                        <span class="badge bg-primary">{{ properties|length }} عقار</span>
                    </div>
                </div>
                <div class="card-body">
    <div class="btn-group mb-3">
        <a href="{{ url_for('add_property') }}" class="btn btn-success"><i class="fa fa-plus"></i> إضافة عقار جديد</a>
        <button type="button" class="btn btn-primary"
                onclick="openFormModal('{{ url_for('modal_add_property') }}', 'إضافة عقار جديد', {
                    size: 'xl',
                    onSuccess: function() { window.location.reload(); }
                })"
                >
            <i class="fa fa-plus-circle"></i> إضافة سريعة
        </button>
    </div>
    <div class="d-flex justify-content-between align-items-center mb-3">
        <button onclick="window.print()" class="btn btn-outline-dark"><i class="fa fa-print"></i> طباعة</button>
    </div>
    <form method="GET" class="row g-2 mb-2 mb-md-0 align-items-end" style="max-width:100%;">
            <div class="col-md-3">
                <input type="text" name="name" class="form-control" placeholder="بحث بالاسم" value="{{ request.args.get('name','') }}">
            </div>
            <div class="col-md-3">
                <input type="text" name="type" class="form-control" placeholder="بحث بالنوع (شقة، مكتب...)" value="{{ request.args.get('type','') }}">
            </div>
            <div class="col-md-3">
                <input type="text" name="address" class="form-control" placeholder="بحث بالعنوان" value="{{ request.args.get('address','') }}">
            </div>
            <div class="col-md-3">
                <input type="text" name="q" class="form-control" placeholder="بحث عام..." value="{{ request.args.get('q','') }}">
            </div>
            <div class="col-md-12 mt-2">
                <button type="submit" class="btn btn-outline-primary w-100"><i class="fa fa-search"></i> بحث متقدم</button>
            </div>
        </form>
    <div class="table-responsive">
    <table class="table table-bordered table-hover align-middle text-center bg-white shadow-sm">
        <thead class="table-success">
            <tr>
                <th>اسم العقار</th>
                <th>النوع</th>
                <th>العنوان</th>
                <th>الوصف</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
        {% for property in properties %}
            <tr>
                <td>
                    <span class="avatar-badge" title="{{ property.name }}">{{ property.name[:2] }}</span>
                    <span data-bs-toggle="tooltip" title="{{ property.name }}">{{ property.name }}</span>
                </td>
                <td>{{ property.type }}</td>
                <td>{{ property.address }}</td>
                <td>{{ property.description }}</td>
                <td>
                    <a href="{{ url_for('edit_property', property_id=property.id) }}" class="btn btn-primary btn-sm">تعديل</a>
                    <form action="{{ url_for('delete_property', property_id=property.id) }}" method="post" style="display:inline;">
                        {% if csrf_token is defined and csrf_token %}<input type="hidden" name="csrf_token" value="{{ csrf_token()|safe }}">{% endif %}
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟');">حذف</button>
                    </form>
                </td>
            </tr>
        {% endfor %}
        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script>
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl)
    });
</script>
{% endblock %}
