from app import app, db
from app.models import Case, Client, FinancialTransaction, User, Appointment
from sqlalchemy import func, extract
from datetime import datetime, timedelta
import traceback

def debug_stats():
    """فحص مفصل لدالة الإحصائيات لتحديد المشكلة"""
    try:
        with app.app_context():
            print("🔍 بدء فحص دالة الإحصائيات...")
            today = datetime.today()
            
            # فحص كل استعلام على حدة
            print("\n1. فحص إحصائيات المحاكم...")
            courts_stats = db.session.query(Case.court, func.count(Case.id)).group_by(Case.court).all()
            print(f"✅ إحصائيات المحاكم: {courts_stats}")
            
            print("\n2. فحص إحصائيات العملاء...")
            clients_stats = db.session.query(Client.name, func.count(Case.id)).join(Case).group_by(Client.name).all()
            print(f"✅ إحصائيات العملاء: {clients_stats}")
            
            print("\n3. فحص إحصائيات أنواع القضايا...")
            types_stats = db.session.query(Case.type, func.count(Case.id)).group_by(Case.type).all()
            print(f"✅ إحصائيات أنواع القضايا: {types_stats}")
            
            print("\n4. فحص إحصائيات حالة القضايا...")
            status_stats = db.session.query(Case.status, func.count(Case.id)).group_by(Case.status).all()
            print(f"✅ إحصائيات حالة القضايا: {status_stats}")
            
            print("\n5. فحص إحصائيات صفة الموكل...")
            client_role_stats = db.session.query(Case.client_role, func.count(Case.id)).group_by(Case.client_role).all()
            print(f"✅ إحصائيات صفة الموكل: {client_role_stats}")
            
            print("\n6. فحص الإحصائيات المالية...")
            total_paid = db.session.query(func.sum(FinancialTransaction.amount)).filter(FinancialTransaction.type.in_(['قبض', 'دفعة', 'أتعاب', 'تحصيل إيجار'])).scalar() or 0
            print(f"✅ إجمالي المدفوعات: {total_paid}")
            
            print("\n7. فحص متوسط مدة القضايا...")
            closed_cases = Case.query.filter(Case.status.in_(['مفصولة', 'منتهية', 'محكومة'])).all()
            print(f"✅ القضايا المنتهية: {len(closed_cases)}")
            
            print("\n8. فحص أكثر العملاء نشاطاً...")
            top_clients = db.session.query(Client.name, func.count(Case.id)).join(Case).group_by(Client.name).order_by(func.count(Case.id).desc()).limit(5).all()
            print(f"✅ أكثر العملاء نشاطاً: {top_clients}")
            
            print("\n9. فحص أكثر المحامين نشاطاً...")
            try:
                top_lawyers = db.session.query(User.username, func.count(Case.id)).join(Case, User.id==Case.lawyer_id).group_by(User.username).order_by(func.count(Case.id).desc()).limit(5).all() if hasattr(Case, 'lawyer_id') else []
                print(f"✅ أكثر المحامين نشاطاً: {top_lawyers}")
            except Exception as e:
                print(f"⚠️ خطأ في إحصائيات المحامين (متوقع): {e}")
                top_lawyers = []
            
            print("\n10. فحص أكثر المحاكم نشاطاً...")
            top_courts = db.session.query(Case.court, func.count(Case.id)).group_by(Case.court).order_by(func.count(Case.id).desc()).limit(5).all()
            print(f"✅ أكثر المحاكم نشاطاً: {top_courts}")
            
            print("\n11. فحص توزيع القضايا حسب الشهور...")
            try:
                monthly_cases = db.session.query(extract('year', Case.open_date), extract('month', Case.open_date), func.count(Case.id)).group_by(extract('year', Case.open_date), extract('month', Case.open_date)).order_by(extract('year', Case.open_date), extract('month', Case.open_date)).all()
                print(f"✅ توزيع القضايا الشهري: {monthly_cases}")
            except Exception as e:
                print(f"❌ خطأ في توزيع القضايا الشهري: {e}")
                raise e
            
            print("\n12. فحص القضايا الجديدة...")
            this_month = today.month
            this_year = today.year
            new_cases_month = Case.query.filter(extract('year', Case.open_date)==this_year, extract('month', Case.open_date)==this_month).count()
            print(f"✅ القضايا الجديدة هذا الشهر: {new_cases_month}")
            
            print("\n13. فحص القضايا المنتهية مقابل الجارية...")
            closed_count = Case.query.filter(Case.status.in_(['مفصولة', 'منتهية', 'محكومة'])).count()
            open_count = Case.query.filter(~Case.status.in_(['مفصولة', 'منتهية', 'محكومة'])).count()
            print(f"✅ القضايا المنتهية: {closed_count}, الجارية: {open_count}")
            
            print("\n14. فحص أعلى القضايا من حيث المبالغ...")
            try:
                top_paid_cases = db.session.query(Case.case_number, func.sum(FinancialTransaction.amount)).join(FinancialTransaction).filter(FinancialTransaction.type.in_(['قبض', 'دفعة', 'أتعاب', 'تحصيل إيجار'])).group_by(Case.case_number).order_by(func.sum(FinancialTransaction.amount).desc()).limit(5).all()
                print(f"✅ أعلى القضايا من حيث المبالغ: {top_paid_cases}")
            except Exception as e:
                print(f"❌ خطأ في أعلى القضايا من حيث المبالغ: {e}")
                raise e
            
            print("\n✅ جميع الاستعلامات تعمل بنجاح!")
            return True
            
    except Exception as e:
        print(f"\n❌ خطأ في فحص الإحصائيات: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == '__main__':
    debug_stats()
