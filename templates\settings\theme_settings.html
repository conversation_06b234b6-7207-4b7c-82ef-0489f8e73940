{% extends "base.html" %}

{% block title %}إعدادات المظهر - نظام إدارة المكتب{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-palette me-2"></i>إعدادات المظهر والثيم</h2>
                <button type="button" class="btn btn-outline-primary" onclick="resetToDefaults()">
                    <i class="fas fa-undo me-2"></i>إعادة تعيين للافتراضي
                </button>
            </div>

            <!-- Theme Selection Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-moon me-2"></i>اختيار المظهر</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="theme-option" data-theme="light">
                                <div class="theme-preview light-preview">
                                    <div class="preview-header"></div>
                                    <div class="preview-sidebar"></div>
                                    <div class="preview-content">
                                        <div class="preview-card"></div>
                                        <div class="preview-card"></div>
                                    </div>
                                </div>
                                <div class="theme-info text-center mt-3">
                                    <h6>المظهر الفاتح</h6>
                                    <p class="text-muted small">المظهر الافتراضي المناسب للاستخدام النهاري</p>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="themeChoice" id="lightTheme" value="light">
                                        <label class="form-check-label" for="lightTheme">
                                            اختيار المظهر الفاتح
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="theme-option" data-theme="dark">
                                <div class="theme-preview dark-preview">
                                    <div class="preview-header"></div>
                                    <div class="preview-sidebar"></div>
                                    <div class="preview-content">
                                        <div class="preview-card"></div>
                                        <div class="preview-card"></div>
                                    </div>
                                </div>
                                <div class="theme-info text-center mt-3">
                                    <h6>المظهر المظلم</h6>
                                    <p class="text-muted small">مظهر مريح للعينين في الإضاءة المنخفضة</p>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="themeChoice" id="darkTheme" value="dark">
                                        <label class="form-check-label" for="darkTheme">
                                            اختيار المظهر المظلم
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="theme-option" data-theme="auto">
                                <div class="theme-preview auto-preview">
                                    <div class="preview-header"></div>
                                    <div class="preview-sidebar"></div>
                                    <div class="preview-content">
                                        <div class="preview-card half-light"></div>
                                        <div class="preview-card half-dark"></div>
                                    </div>
                                </div>
                                <div class="theme-info text-center mt-3">
                                    <h6>تلقائي</h6>
                                    <p class="text-muted small">يتبع إعدادات النظام تلقائياً</p>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="themeChoice" id="autoTheme" value="auto">
                                        <label class="form-check-label" for="autoTheme">
                                            التبديل التلقائي
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Theme Customization Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-sliders-h me-2"></i>تخصيص المظهر</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إعدادات عامة</h6>
                            
                            <div class="mb-3">
                                <label for="togglePosition" class="form-label">موضع زر التبديل</label>
                                <select class="form-select" id="togglePosition">
                                    <option value="top-left">أعلى اليسار</option>
                                    <option value="top-right">أعلى اليمين</option>
                                    <option value="bottom-left">أسفل اليسار</option>
                                    <option value="bottom-right">أسفل اليمين</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="showToggleButton" checked>
                                    <label class="form-check-label" for="showToggleButton">
                                        إظهار زر التبديل
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="smoothTransitions" checked>
                                    <label class="form-check-label" for="smoothTransitions">
                                        انتقالات سلسة
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="rememberChoice" checked>
                                    <label class="form-check-label" for="rememberChoice">
                                        تذكر الاختيار
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>اختصارات لوحة المفاتيح</h6>
                            
                            <div class="keyboard-shortcuts">
                                <div class="shortcut-item">
                                    <span class="shortcut-keys">
                                        <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>T</kbd>
                                    </span>
                                    <span class="shortcut-desc">تبديل المظهر</span>
                                </div>
                                
                                <div class="shortcut-item">
                                    <span class="shortcut-keys">
                                        <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>L</kbd>
                                    </span>
                                    <span class="shortcut-desc">المظهر الفاتح</span>
                                </div>
                                
                                <div class="shortcut-item">
                                    <span class="shortcut-keys">
                                        <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>D</kbd>
                                    </span>
                                    <span class="shortcut-desc">المظهر المظلم</span>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableKeyboardShortcuts" checked>
                                    <label class="form-check-label" for="enableKeyboardShortcuts">
                                        تفعيل اختصارات لوحة المفاتيح
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Information Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إعدادات المتصفح</h6>
                            <div class="system-info">
                                <div class="info-item">
                                    <span class="info-label">المظهر المفضل:</span>
                                    <span class="info-value" id="systemPreference">جاري التحميل...</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">دعم الوضع المظلم:</span>
                                    <span class="info-value" id="darkModeSupport">جاري التحميل...</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">دعم الانتقالات:</span>
                                    <span class="info-value" id="transitionSupport">جاري التحميل...</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>الإعدادات المحفوظة</h6>
                            <div class="system-info">
                                <div class="info-item">
                                    <span class="info-label">المظهر الحالي:</span>
                                    <span class="info-value" id="currentTheme">جاري التحميل...</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">آخر تغيير:</span>
                                    <span class="info-value" id="lastChanged">جاري التحميل...</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">عدد التبديلات:</span>
                                    <span class="info-value" id="toggleCount">جاري التحميل...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="text-center">
                <button type="button" class="btn btn-primary me-2" onclick="saveThemeSettings()">
                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                </button>
                <button type="button" class="btn btn-outline-secondary me-2" onclick="previewTheme()">
                    <i class="fas fa-eye me-2"></i>معاينة
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="resetToDefaults()">
                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Theme Settings Specific Styles */
.theme-option {
    cursor: pointer;
    transition: transform 0.3s ease;
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 15px;
}

.theme-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.theme-option.selected {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.theme-preview {
    width: 100%;
    height: 150px;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    border: 1px solid #dee2e6;
}

.light-preview {
    background: #ffffff;
}

.dark-preview {
    background: #1a1a1a;
}

.auto-preview {
    background: linear-gradient(90deg, #ffffff 50%, #1a1a1a 50%);
}

.preview-header {
    height: 20px;
    background: #0d6efd;
    width: 100%;
}

.preview-sidebar {
    position: absolute;
    right: 0;
    top: 20px;
    width: 30%;
    height: calc(100% - 20px);
    background: #23272b;
}

.dark-preview .preview-sidebar {
    background: #2d3748;
}

.preview-content {
    position: absolute;
    left: 0;
    top: 20px;
    width: 70%;
    height: calc(100% - 20px);
    padding: 10px;
}

.preview-card {
    height: 40px;
    margin-bottom: 8px;
    border-radius: 4px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
}

.dark-preview .preview-card {
    background: #2d3748;
    border-color: #4a5568;
}

.half-light {
    background: #f8f9fa;
    border-color: #dee2e6;
}

.half-dark {
    background: #2d3748;
    border-color: #4a5568;
}

.keyboard-shortcuts {
    background: var(--theme-bg-tertiary);
    border-radius: 8px;
    padding: 15px;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.shortcut-item:last-child {
    margin-bottom: 0;
}

.shortcut-keys kbd {
    background: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
}

.system-info {
    background: var(--theme-bg-tertiary);
    border-radius: 8px;
    padding: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
}

.info-value {
    color: var(--theme-text-secondary);
}
</style>
{% endblock %}

{% block scripts %}
<script>
// Theme Settings JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeThemeSettings();
    loadSystemInfo();
    bindThemeEvents();
});

function initializeThemeSettings() {
    // تحديد المظهر الحالي
    const currentTheme = window.themeManager ? window.themeManager.getCurrentTheme() : 'light';
    const storedTheme = localStorage.getItem('theme');
    
    // تحديد الخيار المناسب
    let themeChoice = 'auto';
    if (storedTheme) {
        themeChoice = storedTheme;
    }
    
    document.querySelector(`input[value="${themeChoice}"]`).checked = true;
    document.querySelector(`.theme-option[data-theme="${themeChoice}"]`).classList.add('selected');
    
    // تحميل الإعدادات المحفوظة
    loadSavedSettings();
}

function loadSystemInfo() {
    // معلومات النظام
    const systemPreference = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'مظلم' : 'فاتح';
    const darkModeSupport = window.matchMedia('(prefers-color-scheme: dark)').media !== 'not all' ? 'مدعوم' : 'غير مدعوم';
    const transitionSupport = CSS.supports('transition', 'all 0.3s ease') ? 'مدعوم' : 'غير مدعوم';
    
    document.getElementById('systemPreference').textContent = systemPreference;
    document.getElementById('darkModeSupport').textContent = darkModeSupport;
    document.getElementById('transitionSupport').textContent = transitionSupport;
    
    // الإعدادات المحفوظة
    const currentTheme = window.themeManager ? window.themeManager.getCurrentTheme() : 'light';
    const lastChanged = localStorage.getItem('themeLastChanged') || 'غير محدد';
    const toggleCount = localStorage.getItem('themeToggleCount') || '0';
    
    document.getElementById('currentTheme').textContent = currentTheme === 'dark' ? 'مظلم' : 'فاتح';
    document.getElementById('lastChanged').textContent = lastChanged;
    document.getElementById('toggleCount').textContent = toggleCount;
}

function loadSavedSettings() {
    // تحميل الإعدادات المحفوظة من localStorage
    const settings = {
        togglePosition: localStorage.getItem('themeTogglePosition') || 'top-left',
        showToggleButton: localStorage.getItem('themeShowToggle') !== 'false',
        smoothTransitions: localStorage.getItem('themeSmoothTransitions') !== 'false',
        rememberChoice: localStorage.getItem('themeRememberChoice') !== 'false',
        enableKeyboardShortcuts: localStorage.getItem('themeKeyboardShortcuts') !== 'false'
    };
    
    document.getElementById('togglePosition').value = settings.togglePosition;
    document.getElementById('showToggleButton').checked = settings.showToggleButton;
    document.getElementById('smoothTransitions').checked = settings.smoothTransitions;
    document.getElementById('rememberChoice').checked = settings.rememberChoice;
    document.getElementById('enableKeyboardShortcuts').checked = settings.enableKeyboardShortcuts;
}

function bindThemeEvents() {
    // أحداث اختيار المظهر
    document.querySelectorAll('input[name="themeChoice"]').forEach(input => {
        input.addEventListener('change', function() {
            document.querySelectorAll('.theme-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            const selectedOption = document.querySelector(`.theme-option[data-theme="${this.value}"]`);
            if (selectedOption) {
                selectedOption.classList.add('selected');
            }
            
            // تطبيق المظهر فوراً للمعاينة
            if (this.value !== 'auto') {
                if (window.themeManager) {
                    window.themeManager.setSpecificTheme(this.value);
                }
            }
        });
    });
    
    // أحداث النقر على معاينة المظهر
    document.querySelectorAll('.theme-option').forEach(option => {
        option.addEventListener('click', function() {
            const theme = this.dataset.theme;
            const radio = document.querySelector(`input[value="${theme}"]`);
            if (radio) {
                radio.checked = true;
                radio.dispatchEvent(new Event('change'));
            }
        });
    });
}

function saveThemeSettings() {
    const selectedTheme = document.querySelector('input[name="themeChoice"]:checked').value;
    const settings = {
        theme: selectedTheme,
        togglePosition: document.getElementById('togglePosition').value,
        showToggleButton: document.getElementById('showToggleButton').checked,
        smoothTransitions: document.getElementById('smoothTransitions').checked,
        rememberChoice: document.getElementById('rememberChoice').checked,
        enableKeyboardShortcuts: document.getElementById('enableKeyboardShortcuts').checked
    };
    
    // حفظ الإعدادات
    if (settings.rememberChoice) {
        if (selectedTheme !== 'auto') {
            localStorage.setItem('theme', selectedTheme);
        } else {
            localStorage.removeItem('theme');
        }
    }
    
    localStorage.setItem('themeTogglePosition', settings.togglePosition);
    localStorage.setItem('themeShowToggle', settings.showToggleButton);
    localStorage.setItem('themeSmoothTransitions', settings.smoothTransitions);
    localStorage.setItem('themeRememberChoice', settings.rememberChoice);
    localStorage.setItem('themeKeyboardShortcuts', settings.enableKeyboardShortcuts);
    localStorage.setItem('themeLastChanged', new Date().toLocaleString('ar-SA'));
    
    // تطبيق الإعدادات
    applyThemeSettings(settings);
    
    // إظهار رسالة نجاح
    showAlert('تم حفظ إعدادات المظهر بنجاح!', 'success');
}

function applyThemeSettings(settings) {
    // تطبيق موضع زر التبديل
    const toggleButton = document.getElementById('theme-toggle');
    if (toggleButton) {
        toggleButton.style.display = settings.showToggleButton ? 'flex' : 'none';
        
        // إعادة تعيين المواضع
        toggleButton.style.top = 'auto';
        toggleButton.style.bottom = 'auto';
        toggleButton.style.left = 'auto';
        toggleButton.style.right = 'auto';
        
        // تطبيق الموضع الجديد
        switch(settings.togglePosition) {
            case 'top-left':
                toggleButton.style.top = '20px';
                toggleButton.style.left = '20px';
                break;
            case 'top-right':
                toggleButton.style.top = '20px';
                toggleButton.style.right = '20px';
                break;
            case 'bottom-left':
                toggleButton.style.bottom = '20px';
                toggleButton.style.left = '20px';
                break;
            case 'bottom-right':
                toggleButton.style.bottom = '20px';
                toggleButton.style.right = '20px';
                break;
        }
    }
    
    // تطبيق المظهر
    if (window.themeManager) {
        if (settings.theme === 'auto') {
            const systemPreference = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            window.themeManager.setSpecificTheme(systemPreference);
        } else {
            window.themeManager.setSpecificTheme(settings.theme);
        }
    }
}

function previewTheme() {
    const selectedTheme = document.querySelector('input[name="themeChoice"]:checked').value;
    
    if (selectedTheme === 'auto') {
        const systemPreference = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        if (window.themeManager) {
            window.themeManager.setSpecificTheme(systemPreference);
        }
    } else {
        if (window.themeManager) {
            window.themeManager.setSpecificTheme(selectedTheme);
        }
    }
    
    showAlert('تم تطبيق المعاينة! اضغط "حفظ الإعدادات" للاحتفاظ بالتغييرات.', 'info');
}

function resetToDefaults() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع إعدادات المظهر للافتراضي؟')) {
        // إزالة الإعدادات المحفوظة
        localStorage.removeItem('theme');
        localStorage.removeItem('themeTogglePosition');
        localStorage.removeItem('themeShowToggle');
        localStorage.removeItem('themeSmoothTransitions');
        localStorage.removeItem('themeRememberChoice');
        localStorage.removeItem('themeKeyboardShortcuts');
        localStorage.removeItem('themeLastChanged');
        localStorage.removeItem('themeToggleCount');
        
        // إعادة تحميل الصفحة
        location.reload();
    }
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 1060; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
