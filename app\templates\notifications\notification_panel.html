<!-- لوحة الإشعارات -->
<div id="notificationPanel" class="notification-panel">
    <!-- رأس اللوحة -->
    <div class="notification-panel-header">
        <h5 class="notification-panel-title mb-0">
            <i class="fa fa-bell me-2"></i>
            الإشعارات
            <span id="notificationCount" class="badge bg-light text-dark ms-2">0</span>
        </h5>
        <div class="notification-panel-actions mt-2">
            <button type="button" class="btn btn-sm btn-outline-light" onclick="notificationsManager.markAllAsRead()">
                <i class="fa fa-check-double"></i> تمييز الكل كمقروء
            </button>
            <button type="button" class="btn btn-sm btn-outline-light" onclick="notificationsManager.clearAllNotifications()">
                <i class="fa fa-trash"></i> حذف الكل
            </button>
            <button type="button" class="btn btn-sm btn-outline-light" onclick="notificationsManager.showSettingsModal()">
                <i class="fa fa-cog"></i> الإعدادات
            </button>
        </div>
    </div>

    <!-- قائمة الإشعارات -->
    <div id="notificationsList" class="notifications-list">
        <!-- مؤشر التحميل -->
        <div id="notificationsLoading" class="notification-loading">
            <div class="spinner-border spinner-border-sm" role="status"></div>
            <span class="ms-2">جاري تحميل الإشعارات...</span>
        </div>

        <!-- رسالة عدم وجود إشعارات -->
        <div id="noNotifications" class="no-notifications" style="display: none;">
            <i class="fa fa-bell-slash fa-2x mb-3 text-muted"></i>
            <p class="mb-0">لا توجد إشعارات</p>
        </div>

        <!-- قائمة الإشعارات -->
        <div id="notificationsContainer">
            <!-- سيتم ملء الإشعارات هنا بواسطة JavaScript -->
        </div>
    </div>

    <!-- إحصائيات الإشعارات -->
    <div class="notification-stats p-3 border-top" style="display: none;" id="notificationStats">
        <div class="row g-2">
            <div class="col-6">
                <div class="notification-stat-card">
                    <div class="notification-stat-number" id="totalNotifications">0</div>
                    <div class="notification-stat-label">المجموع</div>
                </div>
            </div>
            <div class="col-6">
                <div class="notification-stat-card">
                    <div class="notification-stat-number" id="unreadNotifications">0</div>
                    <div class="notification-stat-label">غير مقروء</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إعدادات الإشعارات -->
<div class="modal fade notification-settings-modal" id="notificationSettingsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fa fa-cog me-2"></i>
                    إعدادات الإشعارات
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="notificationSettingsForm">
                    <!-- الإعدادات العامة -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="fw-bold mb-3">
                                <i class="fa fa-toggle-on text-primary me-2"></i>
                                الإعدادات العامة
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="enabled" id="notificationsEnabled" checked>
                                <label class="form-check-label" for="notificationsEnabled">
                                    تفعيل الإشعارات
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="sound_enabled" id="soundEnabled" checked>
                                <label class="form-check-label" for="soundEnabled">
                                    تفعيل الأصوات
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="desktop_enabled" id="desktopEnabled" checked>
                                <label class="form-check-label" for="desktopEnabled">
                                    إشعارات سطح المكتب
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="auto_hide" id="autoHide" checked>
                                <label class="form-check-label" for="autoHide">
                                    إخفاء تلقائي
                                </label>
                            </div>
                            <div class="form-group mb-3">
                                <label for="hideDelay" class="form-label">مدة العرض (ثانية)</label>
                                <input type="number" class="form-control" name="hide_delay" id="hideDelay" value="5" min="1" max="30">
                            </div>
                            <div class="form-group mb-3">
                                <label for="maxNotifications" class="form-label">الحد الأقصى للإشعارات</label>
                                <input type="number" class="form-control" name="max_notifications" id="maxNotifications" value="50" min="10" max="200">
                            </div>
                        </div>
                    </div>

                    <!-- أنواع الإشعارات -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="fw-bold mb-3">
                                <i class="fa fa-list text-info me-2"></i>
                                أنواع الإشعارات
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="case_notifications" id="caseNotifications" checked>
                                <label class="form-check-label" for="caseNotifications">
                                    <i class="fa fa-gavel me-2"></i>
                                    إشعارات القضايا
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="client_notifications" id="clientNotifications" checked>
                                <label class="form-check-label" for="clientNotifications">
                                    <i class="fa fa-users me-2"></i>
                                    إشعارات العملاء
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="appointment_notifications" id="appointmentNotifications" checked>
                                <label class="form-check-label" for="appointmentNotifications">
                                    <i class="fa fa-calendar me-2"></i>
                                    إشعارات المواعيد
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="task_notifications" id="taskNotifications" checked>
                                <label class="form-check-label" for="taskNotifications">
                                    <i class="fa fa-tasks me-2"></i>
                                    إشعارات المهام
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="payment_notifications" id="paymentNotifications" checked>
                                <label class="form-check-label" for="paymentNotifications">
                                    <i class="fa fa-money-bill me-2"></i>
                                    إشعارات الدفعات
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="document_notifications" id="documentNotifications" checked>
                                <label class="form-check-label" for="documentNotifications">
                                    <i class="fa fa-file me-2"></i>
                                    إشعارات الوثائق
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- ساعات الهدوء -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="fw-bold mb-3">
                                <i class="fa fa-moon text-warning me-2"></i>
                                ساعات الهدوء
                            </h6>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="quiet_hours_enabled" id="quietHoursEnabled">
                                <label class="form-check-label" for="quietHoursEnabled">
                                    تفعيل ساعات الهدوء
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="quietHoursStart" class="form-label">من الساعة</label>
                                <input type="time" class="form-control" name="quiet_hours_start" id="quietHoursStart" value="22:00">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="quietHoursEnd" class="form-label">إلى الساعة</label>
                                <input type="time" class="form-control" name="quiet_hours_end" id="quietHoursEnd" value="08:00">
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-primary" onclick="notificationsManager.saveSettings()">
                                    <i class="fa fa-save me-2"></i>
                                    حفظ الإعدادات
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="notificationsManager.resetSettings()">
                                    <i class="fa fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                                <button type="button" class="btn btn-info" onclick="notificationsManager.testNotification()">
                                    <i class="fa fa-bell me-2"></i>
                                    إشعار تجريبي
                                </button>
                                <button type="button" class="btn btn-warning" onclick="notificationsManager.exportNotifications('json')">
                                    <i class="fa fa-download me-2"></i>
                                    تصدير JSON
                                </button>
                                <button type="button" class="btn btn-success" onclick="notificationsManager.exportNotifications('csv')">
                                    <i class="fa fa-file-csv me-2"></i>
                                    تصدير CSV
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إنشاء تنبيه مخصص -->
<div class="modal fade" id="customAlertModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fa fa-plus-circle me-2"></i>
                    إنشاء تنبيه مخصص
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customAlertForm">
                    <div class="form-group mb-3">
                        <label for="alertTitle" class="form-label">عنوان التنبيه</label>
                        <input type="text" class="form-control" id="alertTitle" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="alertMessage" class="form-label">رسالة التنبيه</label>
                        <textarea class="form-control" id="alertMessage" rows="3" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="alertDate" class="form-label">التاريخ</label>
                                <input type="date" class="form-control" id="alertDate" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="alertTime" class="form-label">الوقت</label>
                                <input type="time" class="form-control" id="alertTime" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="alertType" class="form-label">نوع التنبيه</label>
                                <select class="form-control" id="alertType">
                                    <option value="info">معلومات</option>
                                    <option value="warning">تحذير</option>
                                    <option value="success">نجاح</option>
                                    <option value="error">خطأ</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="alertPriority" class="form-label">الأولوية</label>
                                <select class="form-control" id="alertPriority">
                                    <option value="low">منخفضة</option>
                                    <option value="normal" selected>عادية</option>
                                    <option value="high">عالية</option>
                                    <option value="urgent">عاجلة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label for="alertRepeat" class="form-label">التكرار</label>
                        <select class="form-control" id="alertRepeat">
                            <option value="none">بدون تكرار</option>
                            <option value="daily">يومي</option>
                            <option value="weekly">أسبوعي</option>
                            <option value="monthly">شهري</option>
                            <option value="yearly">سنوي</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="notificationsManager.saveCustomAlert()">
                    <i class="fa fa-save me-2"></i>
                    حفظ التنبيه
                </button>
            </div>
        </div>
    </div>
</div>
