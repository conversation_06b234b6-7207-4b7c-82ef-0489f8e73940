{% extends "base.html" %}

{% block title %}إدارة المستأجرين المحسنة{% endblock %}

{% block extra_css %}
<style>
    /* تصميم البطاقات الاحترافي للمستأجرين */
    .tenant-card {
        border: none;
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        overflow: hidden;
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        position: relative;
        margin-bottom: 30px;
    }

    .tenant-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }

    .tenant-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .tenant-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 25px;
        position: relative;
        text-align: center;
    }

    .tenant-avatar {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        margin: 0 auto 20px;
        border: 4px solid rgba(255,255,255,0.3);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    .tenant-card:hover .tenant-avatar {
        transform: scale(1.1);
        border-color: rgba(255,255,255,0.6);
    }

    .tenant-name {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .tenant-id {
        opacity: 0.9;
        font-size: 1rem;
        font-weight: 500;
    }

    .tenant-status {
        position: absolute;
        top: 20px;
        right: 20px;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.75rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .status-active {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
    }

    .status-inactive {
        background: linear-gradient(135deg, #dc3545, #fd7e14);
        color: white;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
    }

    .status-pending {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: white;
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
    }

    .tenant-info {
        padding: 25px;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding: 12px 0;
        border-bottom: 2px solid #f8f9fa;
        transition: all 0.3s ease;
    }

    .info-row:hover {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 8px;
        padding-left: 10px;
        padding-right: 10px;
    }

    .info-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .info-label {
        color: #34495e;
        font-size: 0.95rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .info-value {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.95rem;
    }

    .credit-score {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .score-bar {
        flex: 1;
        height: 10px;
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
    }

    .score-fill {
        height: 100%;
        border-radius: 10px;
        transition: width 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
    }

    .score-fill::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .score-excellent { background: linear-gradient(90deg, #28a745, #20c997); }
    .score-good { background: linear-gradient(90deg, #17a2b8, #20c997); }
    .score-fair { background: linear-gradient(90deg, #ffc107, #fd7e14); }
    .score-poor { background: linear-gradient(90deg, #dc3545, #fd7e14); }

    .tenant-actions {
        padding: 20px 25px;
        background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
        display: flex;
        gap: 12px;
        justify-content: center;
        border-top: 2px solid #e9ecef;
    }

    .btn-action {
        flex: 1;
        padding: 12px 16px;
        border-radius: 12px;
        font-size: 0.9rem;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: none;
        text-decoration: none;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        position: relative;
        overflow: hidden;
    }

    .btn-action::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-action:hover::before {
        left: 100%;
    }

    .btn-view {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    .btn-view:hover {
        background: linear-gradient(135deg, #2980b9, #1f4e79);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
    }

    .btn-edit {
        background: linear-gradient(135deg, #f39c12, #e67e22);
        color: white;
        box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
    }

    .btn-edit:hover {
        background: linear-gradient(135deg, #e67e22, #d35400);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
    }

    .btn-contact {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .btn-contact:hover {
        background: linear-gradient(135deg, #20c997, #17a2b8);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
    }

    /* تصميم الصفحة الرئيسي */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px 0;
        margin: -20px -20px 40px -20px;
        border-radius: 0 0 30px 30px;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        position: relative;
        z-index: 1;
    }

    /* بطاقات الإحصائيات */
    .stats-cards {
        margin-bottom: 40px;
    }

    .stat-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: none;
        border-radius: 20px;
        padding: 30px 25px;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        margin-bottom: 20px;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin: 0 auto 20px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 8px;
        color: #2c3e50;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-label {
        font-size: 1rem;
        color: #6c757d;
        font-weight: 600;
    }

    /* فلاتر البحث */
    .search-filters {
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        border: none;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 40px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        position: relative;
    }

    .search-filters::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px 20px 0 0;
    }

    .filter-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 12px 16px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    /* زر الإضافة العائم */
    .floating-add-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        color: white;
        font-size: 1.8rem;
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        z-index: 1000;
        cursor: pointer;
    }
    
    .floating-add-btn:hover {
        transform: scale(1.15) rotate(90deg);
        box-shadow: 0 12px 35px rgba(40, 167, 69, 0.6);
    }

    /* حالة فارغة */
    .empty-state {
        text-align: center;
        padding: 80px 20px;
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        margin: 40px 0;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }

    .empty-state i {
        font-size: 5rem;
        margin-bottom: 30px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .empty-state h3 {
        color: #2c3e50;
        font-weight: 700;
        margin-bottom: 15px;
    }

    .empty-state p {
        color: #6c757d;
        font-size: 1.1rem;
        margin-bottom: 30px;
    }

    /* مؤشرات العقد */
    .lease-indicator {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        margin-top: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .lease-active {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.15), rgba(32, 201, 151, 0.15));
        color: #28a745;
        border: 2px solid rgba(40, 167, 69, 0.3);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
    }

    .lease-expired {
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.15), rgba(253, 126, 20, 0.15));
        color: #dc3545;
        border: 2px solid rgba(220, 53, 69, 0.3);
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
    }

    .lease-none {
        background: linear-gradient(135deg, rgba(108, 117, 125, 0.15), rgba(173, 181, 189, 0.15));
        color: #6c757d;
        border: 2px solid rgba(108, 117, 125, 0.3);
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2);
    }

    /* حالة الدفع */
    .payment-status {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 10px;
    }

    .payment-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .payment-current {
        background: linear-gradient(135deg, #28a745, #20c997);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }
    .payment-late {
        background: linear-gradient(135deg, #dc3545, #fd7e14);
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    }
    .payment-upcoming {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
    }

    .quick-stats {
        display: flex;
        gap: 20px;
        margin-top: 20px;
        padding-top: 20px;
        border-top: 2px solid #f8f9fa;
    }

    .quick-stat {
        text-align: center;
        flex: 1;
        padding: 10px;
        border-radius: 10px;
        background: rgba(102, 126, 234, 0.05);
        transition: all 0.3s ease;
    }

    .quick-stat:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: translateY(-2px);
    }

    .quick-stat-number {
        font-size: 1.4rem;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .quick-stat-label {
        font-size: 0.85rem;
        color: #6c757d;
        margin-top: 5px;
        font-weight: 600;
    }

    /* تحسينات إضافية */
    .animate-fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="fas fa-users me-3"></i>
                    إدارة المستأجرين المحسنة
                </h1>
                <p class="page-subtitle">إدارة شاملة ومتطورة لجميع المستأجرين والعقود</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-light btn-lg shadow-lg" data-bs-toggle="modal" data-bs-target="#addTenantModal">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة مستأجر جديد
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- إحصائيات سريعة -->
    <div class="row stats-cards">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number">{{ total_tenants or 0 }}</div>
                <div class="stat-label">إجمالي المستأجرين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-number">{{ active_tenants or 0 }}</div>
                <div class="stat-label">مستأجرين نشطين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
                    <i class="fas fa-user-clock"></i>
                </div>
                <div class="stat-number">{{ pending_tenants or 0 }}</div>
                <div class="stat-label">في الانتظار</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #dc3545, #fd7e14);">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number">{{ late_payments or 0 }}</div>
                <div class="stat-label">متأخرين في الدفع</div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="search-filters">
        <h5 class="filter-title">
            <i class="fas fa-filter"></i>
            فلاتر البحث والتصفية
        </h5>
        <form method="GET" id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">البحث العام</label>
                    <input type="text" class="form-control" name="search" value="{{ request.args.get('search', '') }}"
                           placeholder="الاسم، الهاتف، أو الهوية">
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="نشط" {{ 'selected' if request.args.get('status') == 'نشط' }}>نشط</option>
                        <option value="غير نشط" {{ 'selected' if request.args.get('status') == 'غير نشط' }}>غير نشط</option>
                        <option value="في الانتظار" {{ 'selected' if request.args.get('status') == 'في الانتظار' }}>في الانتظار</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الجنسية</label>
                    <select class="form-select" name="nationality">
                        <option value="">جميع الجنسيات</option>
                        {% for nationality in nationalities %}
                        <option value="{{ nationality }}" {{ 'selected' if request.args.get('nationality') == nationality }}>{{ nationality }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">التقييم الائتماني</label>
                    <select class="form-select" name="credit_range">
                        <option value="">جميع التقييمات</option>
                        <option value="excellent" {{ 'selected' if request.args.get('credit_range') == 'excellent' }}>ممتاز (750+)</option>
                        <option value="good" {{ 'selected' if request.args.get('credit_range') == 'good' }}>جيد (650-749)</option>
                        <option value="fair" {{ 'selected' if request.args.get('credit_range') == 'fair' }}>مقبول (550-649)</option>
                        <option value="poor" {{ 'selected' if request.args.get('credit_range') == 'poor' }}>ضعيف (أقل من 550)</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">حالة الدفع</label>
                    <select class="form-select" name="payment_status">
                        <option value="">جميع حالات الدفع</option>
                        <option value="current" {{ 'selected' if request.args.get('payment_status') == 'current' }}>محدث</option>
                        <option value="late" {{ 'selected' if request.args.get('payment_status') == 'late' }}>متأخر</option>
                        <option value="upcoming" {{ 'selected' if request.args.get('payment_status') == 'upcoming' }}>مستحق قريباً</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{{ url_for('tenants_enhanced') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        مسح الفلاتر
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- قائمة المستأجرين -->
    <div class="row">
        {% if tenants %}
            {% for tenant in tenants %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card tenant-card">
                    <div class="tenant-header">
                        <div class="tenant-status 
                            {% if tenant.status == 'نشط' %}status-active
                            {% elif tenant.status == 'غير نشط' %}status-inactive
                            {% else %}status-pending{% endif %}">
                            {{ tenant.status }}
                        </div>
                        
                        <div class="tenant-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        
                        <div class="tenant-name">{{ tenant.full_name }}</div>
                        <div class="tenant-id">{{ tenant.national_id or tenant.passport_number }}</div>
                    </div>
                    
                    <div class="tenant-info">
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-phone"></i>
                                الهاتف
                            </div>
                            <div class="info-value">{{ tenant.phone_number or 'غير محدد' }}</div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-envelope"></i>
                                البريد الإلكتروني
                            </div>
                            <div class="info-value">{{ tenant.email or 'غير محدد' }}</div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-flag"></i>
                                الجنسية
                            </div>
                            <div class="info-value">{{ tenant.nationality or 'غير محدد' }}</div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-star"></i>
                                التقييم الائتماني
                            </div>
                            <div class="credit-score">
                                <div class="score-bar">
                                    {% set score = tenant.credit_score or 0 %}
                                    <div class="score-fill 
                                        {% if score >= 750 %}score-excellent
                                        {% elif score >= 650 %}score-good
                                        {% elif score >= 550 %}score-fair
                                        {% else %}score-poor{% endif %}" 
                                        style="width: {{ (score / 850 * 100)|round }}%"></div>
                                </div>
                                <span class="info-value">{{ score }}</span>
                            </div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-home"></i>
                                العقد الحالي
                            </div>
                            <div class="info-value">
                                {% set active_lease = tenant.get_active_lease() %}
                                {% if active_lease %}
                                    <span class="lease-indicator lease-active">نشط</span>
                                {% else %}
                                    <span class="lease-indicator lease-none">لا يوجد</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-credit-card"></i>
                                حالة الدفع
                            </div>
                            <div class="payment-status">
                                {% set payment_status = tenant.get_payment_status() %}
                                <div class="payment-indicator 
                                    {% if payment_status == 'محدث' %}payment-current
                                    {% elif payment_status == 'متأخر' %}payment-late
                                    {% else %}payment-upcoming{% endif %}"></div>
                                <span class="info-value">{{ payment_status }}</span>
                            </div>
                        </div>
                        
                        <div class="quick-stats">
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ tenant.leases.count() }}</div>
                                <div class="quick-stat-label">عقود</div>
                            </div>
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ tenant.payment_history.count() }}</div>
                                <div class="quick-stat-label">مدفوعات</div>
                            </div>
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ tenant.communications.count() }}</div>
                                <div class="quick-stat-label">مراسلات</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tenant-actions">
                        <a href="{{ url_for('tenant_details', id=tenant.id) }}" class="btn btn-action btn-view">
                            <i class="fas fa-eye me-1"></i>
                            عرض
                        </a>
                        <a href="{{ url_for('edit_tenant', id=tenant.id) }}" class="btn btn-action btn-edit">
                            <i class="fas fa-edit me-1"></i>
                            تعديل
                        </a>
                        <button class="btn btn-action btn-contact" onclick="contactTenant({{ tenant.id }}, '{{ tenant.full_name }}')">
                            <i class="fas fa-comments me-1"></i>
                            تواصل
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h4>لا يوجد مستأجرين</h4>
                    <p>لم يتم العثور على مستأجرين يطابقون معايير البحث</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTenantModal">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة مستأجر جديد
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- زر الإضافة العائم -->
<button class="floating-add-btn" data-bs-toggle="modal" data-bs-target="#addTenantModal">
    <i class="fas fa-user-plus"></i>
</button>

<!-- تضمين مودال إضافة مستأجر -->
{% include 'tenants/modals/add_tenant_modal.html' %}
{% endblock %}

{% block extra_js %}
<script>
// تأكيد الحذف
function confirmDelete(tenantId, tenantName) {
    if (confirm(`هل أنت متأكد من حذف المستأجر "${tenantName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        fetch(`/tenants/${tenantId}/delete`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrf_token]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف المستأجر');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف المستأجر');
        });
    }
}

// التواصل مع المستأجر
function contactTenant(tenantId, tenantName) {
    // يمكن تطوير هذه الوظيفة لفتح نافذة تواصل أو إرسال رسالة
    alert(`سيتم فتح نافذة التواصل مع ${tenantName} قريباً`);
}

// تحديث البحث التلقائي
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        filterForm.addEventListener('change', function() {
            this.submit();
        });
    }

    // إضافة تأثيرات التحميل للبطاقات
    const tenantCards = document.querySelectorAll('.tenant-card');
    tenantCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-fade-in');
    });

    // تحسين تجربة المستخدم للمودال
    const addTenantModal = document.getElementById('addTenantModal');
    if (addTenantModal) {
        addTenantModal.addEventListener('show.bs.modal', function() {
            setTimeout(() => {
                const firstInput = this.querySelector('input[type="text"]');
                if (firstInput) firstInput.focus();
            }, 500);
        });
    }

    // تحديث الإحصائيات في الوقت الفعلي
    updateTenantStats();
});

// تحديث إحصائيات المستأجرين
function updateTenantStats() {
    fetch('/api/tenants/stats')
        .then(response => response.json())
        .then(data => {
            const statNumbers = document.querySelectorAll('.stat-number');
            if (statNumbers.length >= 4) {
                statNumbers[0].textContent = data.total || 0;
                statNumbers[1].textContent = data.active || 0;
                statNumbers[2].textContent = data.pending || 0;
                statNumbers[3].textContent = data.late_payments || 0;
            }
        })
        .catch(error => console.error('Error updating tenant stats:', error));
}

// تحديث الإحصائيات كل دقيقة
setInterval(updateTenantStats, 60000);

// إضافة تأثيرات بصرية للأزرار
document.addEventListener('DOMContentLoaded', function() {
    const actionButtons = document.querySelectorAll('.btn-action');
    actionButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // تحسين تجربة المستخدم للفلاتر
    const filterInputs = document.querySelectorAll('.search-filters input, .search-filters select');
    filterInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
});
</script>
{% endblock %}
