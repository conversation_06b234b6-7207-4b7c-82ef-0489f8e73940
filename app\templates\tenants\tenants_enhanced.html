{% extends "base.html" %}

{% block title %}إدارة المستأجرين المحسنة{% endblock %}

{% block extra_css %}
<style>
    .tenant-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        position: relative;
    }
    
    .tenant-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    
    .tenant-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        position: relative;
    }
    
    .tenant-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        margin-bottom: 15px;
        border: 3px solid rgba(255,255,255,0.3);
    }
    
    .tenant-name {
        font-size: 1.3rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .tenant-id {
        opacity: 0.8;
        font-size: 0.9rem;
    }
    
    .tenant-status {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .status-active {
        background: rgba(40, 167, 69, 0.9);
        color: white;
    }
    
    .status-inactive {
        background: rgba(220, 53, 69, 0.9);
        color: white;
    }
    
    .status-pending {
        background: rgba(255, 193, 7, 0.9);
        color: white;
    }
    
    .tenant-info {
        padding: 20px;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .info-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .info-label {
        color: #6c757d;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .info-value {
        font-weight: 500;
        color: #2c3e50;
    }
    
    .credit-score {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .score-bar {
        flex: 1;
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
    }
    
    .score-fill {
        height: 100%;
        border-radius: 4px;
        transition: width 0.3s ease;
    }
    
    .score-excellent { background: linear-gradient(90deg, #28a745, #20c997); }
    .score-good { background: linear-gradient(90deg, #17a2b8, #20c997); }
    .score-fair { background: linear-gradient(90deg, #ffc107, #fd7e14); }
    .score-poor { background: linear-gradient(90deg, #dc3545, #fd7e14); }
    
    .tenant-actions {
        padding: 15px 20px;
        background: #f8f9fa;
        display: flex;
        gap: 10px;
        justify-content: space-between;
    }
    
    .btn-action {
        flex: 1;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
        border: none;
    }
    
    .btn-view {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
    }
    
    .btn-edit {
        background: linear-gradient(45deg, #f39c12, #e67e22);
        color: white;
    }
    
    .btn-contact {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }
    
    .search-filters {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    }
    
    .stats-cards {
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 0 0 20px 20px;
    }
    
    .floating-add-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 20px rgba(40, 167, 69, 0.4);
        transition: all 0.3s ease;
        z-index: 1000;
    }
    
    .floating-add-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(40, 167, 69, 0.6);
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
    
    .lease-indicator {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        margin-top: 5px;
    }
    
    .lease-active {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
        border: 1px solid rgba(40, 167, 69, 0.2);
    }
    
    .lease-expired {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
        border: 1px solid rgba(220, 53, 69, 0.2);
    }
    
    .lease-none {
        background: rgba(108, 117, 125, 0.1);
        color: #6c757d;
        border: 1px solid rgba(108, 117, 125, 0.2);
    }
    
    .payment-status {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .payment-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
    }
    
    .payment-current { background: #28a745; }
    .payment-late { background: #dc3545; }
    .payment-upcoming { background: #ffc107; }
    
    .quick-stats {
        display: flex;
        gap: 15px;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #f1f3f4;
    }
    
    .quick-stat {
        text-align: center;
        flex: 1;
    }
    
    .quick-stat-number {
        font-size: 1.2rem;
        font-weight: bold;
        color: #667eea;
    }
    
    .quick-stat-label {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 2px;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-0">
                    <i class="fas fa-users me-3"></i>
                    إدارة المستأجرين المحسنة
                </h1>
                <p class="mb-0 mt-2 opacity-75">إدارة شاملة ومتطورة لجميع المستأجرين</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#addTenantModal">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة مستأجر جديد
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- إحصائيات سريعة -->
    <div class="row stats-cards">
        <div class="col-md-3">
            <div class="card stat-card">
                <div class="stat-number">{{ total_tenants or 0 }}</div>
                <div class="stat-label">إجمالي المستأجرين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="stat-number">{{ active_tenants or 0 }}</div>
                <div class="stat-label">مستأجرين نشطين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <div class="stat-number">{{ pending_tenants or 0 }}</div>
                <div class="stat-label">في الانتظار</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);">
                <div class="stat-number">{{ late_payments or 0 }}</div>
                <div class="stat-label">متأخرين في الدفع</div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="search-filters">
        <form method="GET" id="searchForm">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" class="form-control" name="search" value="{{ request.args.get('search', '') }}" 
                           placeholder="الاسم، الهاتف، أو الهوية">
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="نشط" {{ 'selected' if request.args.get('status') == 'نشط' }}>نشط</option>
                        <option value="غير نشط" {{ 'selected' if request.args.get('status') == 'غير نشط' }}>غير نشط</option>
                        <option value="في الانتظار" {{ 'selected' if request.args.get('status') == 'في الانتظار' }}>في الانتظار</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الجنسية</label>
                    <select class="form-select" name="nationality">
                        <option value="">جميع الجنسيات</option>
                        {% for nationality in nationalities %}
                        <option value="{{ nationality }}" {{ 'selected' if request.args.get('nationality') == nationality }}>{{ nationality }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">التقييم الائتماني</label>
                    <select class="form-select" name="credit_range">
                        <option value="">جميع التقييمات</option>
                        <option value="excellent" {{ 'selected' if request.args.get('credit_range') == 'excellent' }}>ممتاز (750+)</option>
                        <option value="good" {{ 'selected' if request.args.get('credit_range') == 'good' }}>جيد (650-749)</option>
                        <option value="fair" {{ 'selected' if request.args.get('credit_range') == 'fair' }}>مقبول (550-649)</option>
                        <option value="poor" {{ 'selected' if request.args.get('credit_range') == 'poor' }}>ضعيف (أقل من 550)</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">حالة الدفع</label>
                    <select class="form-select" name="payment_status">
                        <option value="">جميع حالات الدفع</option>
                        <option value="current" {{ 'selected' if request.args.get('payment_status') == 'current' }}>محدث</option>
                        <option value="late" {{ 'selected' if request.args.get('payment_status') == 'late' }}>متأخر</option>
                        <option value="upcoming" {{ 'selected' if request.args.get('payment_status') == 'upcoming' }}>مستحق قريباً</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{{ url_for('tenants_enhanced') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        مسح الفلاتر
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- قائمة المستأجرين -->
    <div class="row">
        {% if tenants %}
            {% for tenant in tenants %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card tenant-card">
                    <div class="tenant-header">
                        <div class="tenant-status 
                            {% if tenant.status == 'نشط' %}status-active
                            {% elif tenant.status == 'غير نشط' %}status-inactive
                            {% else %}status-pending{% endif %}">
                            {{ tenant.status }}
                        </div>
                        
                        <div class="tenant-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        
                        <div class="tenant-name">{{ tenant.full_name }}</div>
                        <div class="tenant-id">{{ tenant.national_id or tenant.passport_number }}</div>
                    </div>
                    
                    <div class="tenant-info">
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-phone"></i>
                                الهاتف
                            </div>
                            <div class="info-value">{{ tenant.phone_number or 'غير محدد' }}</div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-envelope"></i>
                                البريد الإلكتروني
                            </div>
                            <div class="info-value">{{ tenant.email or 'غير محدد' }}</div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-flag"></i>
                                الجنسية
                            </div>
                            <div class="info-value">{{ tenant.nationality or 'غير محدد' }}</div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-star"></i>
                                التقييم الائتماني
                            </div>
                            <div class="credit-score">
                                <div class="score-bar">
                                    {% set score = tenant.credit_score or 0 %}
                                    <div class="score-fill 
                                        {% if score >= 750 %}score-excellent
                                        {% elif score >= 650 %}score-good
                                        {% elif score >= 550 %}score-fair
                                        {% else %}score-poor{% endif %}" 
                                        style="width: {{ (score / 850 * 100)|round }}%"></div>
                                </div>
                                <span class="info-value">{{ score }}</span>
                            </div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-home"></i>
                                العقد الحالي
                            </div>
                            <div class="info-value">
                                {% set active_lease = tenant.get_active_lease() %}
                                {% if active_lease %}
                                    <span class="lease-indicator lease-active">نشط</span>
                                {% else %}
                                    <span class="lease-indicator lease-none">لا يوجد</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="fas fa-credit-card"></i>
                                حالة الدفع
                            </div>
                            <div class="payment-status">
                                {% set payment_status = tenant.get_payment_status() %}
                                <div class="payment-indicator 
                                    {% if payment_status == 'محدث' %}payment-current
                                    {% elif payment_status == 'متأخر' %}payment-late
                                    {% else %}payment-upcoming{% endif %}"></div>
                                <span class="info-value">{{ payment_status }}</span>
                            </div>
                        </div>
                        
                        <div class="quick-stats">
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ tenant.leases.count() }}</div>
                                <div class="quick-stat-label">عقود</div>
                            </div>
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ tenant.payment_history.count() }}</div>
                                <div class="quick-stat-label">مدفوعات</div>
                            </div>
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ tenant.communications.count() }}</div>
                                <div class="quick-stat-label">مراسلات</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tenant-actions">
                        <a href="{{ url_for('tenant_details', id=tenant.id) }}" class="btn btn-action btn-view">
                            <i class="fas fa-eye me-1"></i>
                            عرض
                        </a>
                        <a href="{{ url_for('edit_tenant', id=tenant.id) }}" class="btn btn-action btn-edit">
                            <i class="fas fa-edit me-1"></i>
                            تعديل
                        </a>
                        <button class="btn btn-action btn-contact" onclick="contactTenant({{ tenant.id }}, '{{ tenant.full_name }}')">
                            <i class="fas fa-comments me-1"></i>
                            تواصل
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h4>لا يوجد مستأجرين</h4>
                    <p>لم يتم العثور على مستأجرين يطابقون معايير البحث</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTenantModal">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة مستأجر جديد
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- زر الإضافة العائم -->
<button class="floating-add-btn" data-bs-toggle="modal" data-bs-target="#addTenantModal">
    <i class="fas fa-user-plus"></i>
</button>
{% endblock %}
