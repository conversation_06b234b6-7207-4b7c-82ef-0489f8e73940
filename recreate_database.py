from app import app, db
from app.models import User, Client, Case, FinancialTransaction, Fe<PERSON>, CourtFee, Task, Appointment, Property, Tenant, Debt
from werkzeug.security import generate_password_hash
from datetime import datetime

def recreate_database():
    """إعادة إنشاء قاعدة البيانات مع البيانات الأساسية"""
    try:
        with app.app_context():
            # حذف الجداول الموجودة وإعادة إنشائها
            db.drop_all()
            db.create_all()
            
            # إنشاء المستخدمين الأساسيين
            admin_user = User(
                username='admin',
                password=generate_password_hash('admin123'),
                role='مدير'
            )

            office_user = User(
                username='office',
                password=generate_password_hash('66889088'),
                role='موظف'
            )
            
            db.session.add(admin_user)
            db.session.add(office_user)
            
            # إنشاء عملاء أساسيين
            client1 = Client(
                name='أحمد محمد علي',
                national_id='123456789',
                phone='0599123456',
                email='<EMAIL>',
                address='غزة - الرمال'
            )

            client2 = Client(
                name='فاطمة أحمد سالم',
                national_id='987654321',
                phone='0598765432',
                email='<EMAIL>',
                address='غزة - الشجاعية'
            )
            
            db.session.add(client1)
            db.session.add(client2)
            db.session.commit()
            
            # إنشاء قضايا أساسية
            case1 = Case(
                case_number='2024/001',
                office_case_number='OFF-2024-001',
                title='قضية ميراث',
                type='مدني',
                status='جارية',
                court='محكمة غزة الابتدائية',
                opponent='ورثة المرحوم سالم',
                description='قضية تقسيم ميراث',
                client_id=client1.id,
                client_role='مدعي',
                fees_total=5000,
                fees_currency='شيكل',
                fees_paid=2000,
                fees_remaining=3000,
                court_fees_total=500,
                court_fees_paid=500,
                court_fees_remaining=0,
                priority='متوسطة',
                case_value=50000,
                currency='شيكل',
                open_date=datetime.now()
            )
            
            case2 = Case(
                case_number='2024/002',
                office_case_number='OFF-2024-002',
                title='قضية عمالية',
                type='عمالي',
                status='جارية',
                court='محكمة العمل',
                opponent='شركة الإنشاءات المتحدة',
                description='قضية مطالبة بحقوق عمالية',
                client_id=client2.id,
                client_role='مدعى عليه',
                fees_total=3000,
                fees_currency='دولار',
                fees_paid=1000,
                fees_remaining=2000,
                court_fees_total=300,
                court_fees_paid=0,
                court_fees_remaining=300,
                priority='عالية',
                case_value=25000,
                currency='دولار',
                open_date=datetime.now()
            )
            
            db.session.add(case1)
            db.session.add(case2)
            
            # إنشاء معاملات مالية أساسية
            transaction1 = FinancialTransaction(
                type='أتعاب',
                amount=2000,
                currency='شيكل',
                description='دفعة أولى من أتعاب قضية الميراث',
                date=datetime.now(),
                case_id=case1.id,
                client_id=client1.id
            )
            
            transaction2 = FinancialTransaction(
                type='رسوم',
                amount=500,
                currency='شيكل',
                description='رسوم محكمة قضية الميراث',
                date=datetime.now(),
                case_id=case1.id,
                client_id=client1.id
            )
            
            db.session.add(transaction1)
            db.session.add(transaction2)
            
            # إنشاء مهام أساسية
            task1 = Task(
                title='تحضير لائحة دعوى',
                description='تحضير لائحة الدعوى لقضية الميراث',
                due_date=datetime.now(),
                status='جارية',
                priority='عالية',
                case_id=case1.id
            )
            
            db.session.add(task1)
            
            # إنشاء مواعيد أساسية
            appointment1 = Appointment(
                subject='جلسة محكمة',
                date=datetime.now(),
                location='محكمة غزة الابتدائية',
                notes='الجلسة الأولى لقضية الميراث',
                case_id=case1.id,
                client_id=client1.id
            )
            
            db.session.add(appointment1)
            
            db.session.commit()
            
            print('✅ تم إعادة إنشاء قاعدة البيانات بنجاح')
            print(f'✅ تم إنشاء {User.query.count()} مستخدمين')
            print(f'✅ تم إنشاء {Client.query.count()} عملاء')
            print(f'✅ تم إنشاء {Case.query.count()} قضايا')
            print(f'✅ تم إنشاء {FinancialTransaction.query.count()} معاملات مالية')
            print(f'✅ تم إنشاء {Task.query.count()} مهام')
            print(f'✅ تم إنشاء {Appointment.query.count()} مواعيد')
            
            return True
            
    except Exception as e:
        print(f'❌ خطأ في إعادة إنشاء قاعدة البيانات: {str(e)}')
        return False

if __name__ == '__main__':
    recreate_database()
