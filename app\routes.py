from flask import render_template, redirect, url_for, flash, request, send_from_directory, jsonify, send_file, session
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
import time
import hashlib
from functools import wraps
from .models import User, Case, Client, Task, Appointment, Property, Tenant, Lease, Installment, FinancialTransaction, Document, ClientDocument, Debt, DebtPayment, RentalIncome, Expense, Fee, FeePayment, Event
from .archive_models import ArchivedCase, ArchivedClient, ArchivedDocument, SystemSettings, ArchiveRule, BackupLog, BackupSchedule, get_archive_statistics, get_system_info
from .notification_models import Notification, Alert, NotificationSettings
from .notification_service import NotificationService
from .settings_service import SettingsService
from .backup_service import BackupService
from datetime import datetime, timedelta, timezone
import tempfile
import zipfile
import os
from . import app, db, csrf
from sqlalchemy import func
import shutil
import os

UPLOAD_FOLDER = os.path.join(os.path.dirname(__file__), 'uploads')

# نظام حماية من Brute Force
login_attempts = {}
FAILED_LOGIN_LIMIT = 5
LOCKOUT_DURATION = 300  # 5 دقائق

def rate_limit_login(f):
    """ديكوريتر للحماية من Brute Force"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))
        current_time = time.time()

        # تنظيف المحاولات القديمة
        if client_ip in login_attempts:
            login_attempts[client_ip] = [attempt for attempt in login_attempts[client_ip]
                                       if current_time - attempt < LOCKOUT_DURATION]

        # فحص عدد المحاولات
        if client_ip in login_attempts and len(login_attempts[client_ip]) >= FAILED_LOGIN_LIMIT:
            flash('تم حظر IP الخاص بك مؤقتاً بسبب محاولات تسجيل دخول متكررة. حاول مرة أخرى بعد 5 دقائق.', 'danger')
            return redirect(url_for('login'))

        return f(*args, **kwargs)
    return decorated_function

def record_failed_login():
    """تسجيل محاولة تسجيل دخول فاشلة"""
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))
    current_time = time.time()

    if client_ip not in login_attempts:
        login_attempts[client_ip] = []

    login_attempts[client_ip].append(current_time)

def secure_session():
    """تأمين الجلسة"""
    session.permanent = True
    session['last_activity'] = time.time()
    session['user_agent'] = request.headers.get('User-Agent', '')
    session['ip_address'] = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))

def validate_session():
    """التحقق من صحة الجلسة"""
    if not current_user.is_authenticated:
        return False

    # فحص انتهاء صلاحية الجلسة (30 دقيقة)
    if 'last_activity' in session:
        if time.time() - session['last_activity'] > 1800:  # 30 دقيقة
            logout_user()
            flash('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.', 'warning')
            return False
        session['last_activity'] = time.time()

    # فحص تغيير User Agent
    if 'user_agent' in session and session['user_agent'] != request.headers.get('User-Agent', ''):
        logout_user()
        flash('تم اكتشاف نشاط مشبوه. يرجى تسجيل الدخول مرة أخرى.', 'danger')
        return False

    return True
ALLOWED_EXTENSIONS = {'pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'xls', 'xlsx'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    from .models import User
    last_login = User.query.order_by(User.last_login.desc()).with_entities(User.last_login).first()
    last_login_str = last_login[0].strftime('%Y-%m-%d %H:%M') if last_login and last_login[0] else None
    return render_template('index.html', last_login=last_login_str)

@app.route('/lawyersameh', methods=['GET', 'POST'])
@csrf.exempt
@rate_limit_login
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        user = User.query.filter_by(username=username).first()
        # التحقق من كلمة المرور بطرق متعددة
        password_valid = False
        if user:
            # مقارنة مباشرة
            if user.password == password:
                password_valid = True
            # SHA256 hash
            elif user.password == hashlib.sha256(password.encode()).hexdigest():
                password_valid = True
            # Werkzeug hash
            elif check_password_hash(user.password, password):
                password_valid = True

        if user and password_valid:
            login_user(user)
            user.last_login = datetime.now()
            db.session.commit()
            # تأمين الجلسة
            secure_session()
            # مسح محاولات تسجيل الدخول الفاشلة عند النجاح
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))
            if client_ip in login_attempts:
                del login_attempts[client_ip]
            return redirect(url_for('dashboard'))
        else:
            # تسجيل محاولة فاشلة
            record_failed_login()
            flash('بيانات الدخول غير صحيحة', 'danger')
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    # نسخ احتياطي تلقائي لقاعدة البيانات
    db_path = os.path.join(os.getcwd(), 'instance', 'lawoffice.db')
    backup_dir = os.path.join(os.getcwd(), 'instance', 'backups')
    os.makedirs(backup_dir, exist_ok=True)
    from datetime import datetime
    backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    backup_path = os.path.join(backup_dir, backup_name)
    if os.path.exists(db_path):
        shutil.copy2(db_path, backup_path)
    logout_user()
    return redirect(url_for('login'))

@app.route('/cases')
@login_required
def cases_list():
    q = request.args.get('q', '').strip()
    client_role = request.args.get('client_role', '').strip()

    cases_query = Case.query

    # فلترة النص
    if q:
        cases_query = cases_query.filter(
            (Case.case_number.ilike(f'%{q}%')) |
            (Case.title.ilike(f'%{q}%')) |
            (Case.status.ilike(f'%{q}%')) |
            (Case.court.ilike(f'%{q}%')) |
            (Case.opponent.ilike(f'%{q}%'))
        )

    # فلترة صفة الموكل
    if client_role:
        cases_query = cases_query.filter(Case.client_role == client_role)

    cases = cases_query.all()
    return render_template('cases/list.html', cases=cases)

@app.route('/cases/add', methods=['GET', 'POST'])
@login_required
def add_case():
    from .models import Fee, CourtFee
    clients = Client.query.all()
    if request.method == 'POST':
        try:
            # البيانات الأساسية للقضية
            case_number = request.form['case_number']
            office_case_number = request.form.get('office_case_number', '')
            title = request.form['title']
            type_ = request.form['type']
            status = request.form['status']
            court = request.form['court']
            opponent = request.form['opponent']
            description = request.form['description']
            client_id = request.form['client_id']
            client_role = request.form.get('client_role', 'مدعي')  # صفة الموكل

            # البيانات المالية الجديدة
            fees_total = float(request.form.get('fees_total', 0) or 0)
            fees_paid = float(request.form.get('fees_paid', 0) or 0)
            fees_currency = request.form.get('fees_currency', 'شيكل')  # عملة الأتعاب
            court_fees_total = float(request.form.get('court_fees_total', 0) or 0)
            court_fees_paid = float(request.form.get('court_fees_paid', 0) or 0)
            priority = request.form.get('priority', 'متوسطة')
            case_value = float(request.form.get('case_value', 0) or 0)
            currency = request.form.get('currency', 'شيكل')

            # حساب المتبقي
            fees_remaining = fees_total - fees_paid
            court_fees_remaining = court_fees_total - court_fees_paid

            # إنشاء القضية
            case = Case(
                case_number=case_number, office_case_number=office_case_number,
                title=title, type=type_, status=status,
                court=court, opponent=opponent, description=description,
                client_id=client_id, lawyer_id=current_user.id,
                client_role=client_role, fees_currency=fees_currency,
                fees_total=fees_total, fees_paid=fees_paid, fees_remaining=fees_remaining,
                court_fees_total=court_fees_total, court_fees_paid=court_fees_paid,
                court_fees_remaining=court_fees_remaining, priority=priority,
                case_value=case_value, currency=currency
            )
            db.session.add(case)
            db.session.flush()  # للحصول على ID القضية

            # إنشاء سجل الأتعاب إذا كان هناك مبلغ
            if fees_total > 0:
                fee = Fee(
                    case_id=case.id, client_id=client_id, amount=fees_total,
                    paid_amount=fees_paid, remaining_amount=fees_remaining,
                    fee_type='أتعاب محاماة', status='مستحقة' if fees_remaining > 0 else 'مدفوعة'
                )
                db.session.add(fee)

            # إنشاء سجل الرسوم إذا كان هناك مبلغ
            if court_fees_total > 0:
                court_fee = CourtFee(
                    case_id=case.id, client_id=client_id, amount=court_fees_total,
                    paid_amount=court_fees_paid, remaining_amount=court_fees_remaining,
                    fee_type='رسوم قضائية', status='مستحقة' if court_fees_remaining > 0 else 'مدفوعة'
                )
                db.session.add(court_fee)

            # إضافة المعاملات المالية
            if fees_paid > 0:
                fee_transaction = FinancialTransaction(
                    amount=fees_paid, date=datetime.now(), type='أتعاب',
                    description=f'دفعة أتعاب للقضية: {title}',
                    case_id=case.id, client_id=client_id, currency=currency
                )
                db.session.add(fee_transaction)

            if court_fees_paid > 0:
                court_fee_transaction = FinancialTransaction(
                    amount=court_fees_paid, date=datetime.now(), type='رسوم',
                    description=f'دفعة رسوم للقضية: {title}',
                    case_id=case.id, client_id=client_id, currency=currency
                )
                db.session.add(court_fee_transaction)

            db.session.commit()
            backup_db_file()

            # إذا كان الطلب من AJAX، إرجاع JSON
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': True, 'message': 'تمت إضافة القضية بنجاح'})

            flash('تمت إضافة القضية بنجاح مع البيانات المالية', 'success')
            return redirect(url_for('cases_list'))

        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
            flash(f'حدث خطأ أثناء إضافة القضية: {str(e)}', 'danger')
            return redirect(url_for('cases_list'))
    if not clients:
        flash('يجب إضافة عميل/موكل أولاً قبل إضافة قضية جديدة.', 'warning')
        return redirect(url_for('clients_list'))
    return render_template('cases/add.html', clients=clients)

@app.route('/cases/<int:case_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_case(case_id):
    from .models import Fee, CourtFee
    case = Case.query.get_or_404(case_id)
    clients = Client.query.all()
    # جلب المهام والمواعيد المرتبطة بالقضية
    case_tasks = Task.query.filter_by(case_id=case.id).order_by(Task.due_date.asc()).all() if hasattr(Task, 'case_id') else []
    case_appointments = Appointment.query.filter_by(case_id=case.id).order_by(Appointment.date.asc()).all()
    if request.method == 'POST':
        # البيانات الأساسية
        case.case_number = request.form['case_number']
        case.office_case_number = request.form.get('office_case_number', '')
        case.title = request.form['title']
        case.type = request.form['type']
        case.status = request.form['status']
        case.court = request.form['court']
        case.opponent = request.form['opponent']
        case.description = request.form['description']
        case.client_id = request.form['client_id']
        case.client_role = request.form.get('client_role', 'مدعي')  # صفة الموكل

        # البيانات المالية الجديدة
        old_fees_total = case.fees_total or 0
        old_fees_paid = case.fees_paid or 0
        old_court_fees_total = case.court_fees_total or 0
        old_court_fees_paid = case.court_fees_paid or 0

        case.fees_total = float(request.form.get('fees_total', 0) or 0)
        case.fees_paid = float(request.form.get('fees_paid', 0) or 0)
        case.fees_currency = request.form.get('fees_currency', 'شيكل')  # عملة الأتعاب
        case.court_fees_total = float(request.form.get('court_fees_total', 0) or 0)
        case.court_fees_paid = float(request.form.get('court_fees_paid', 0) or 0)
        case.priority = request.form.get('priority', 'متوسطة')
        case.case_value = float(request.form.get('case_value', 0) or 0)
        case.currency = request.form.get('currency', 'شيكل')

        # حساب المتبقي
        case.fees_remaining = case.fees_total - case.fees_paid
        case.court_fees_remaining = case.court_fees_total - case.court_fees_paid

        # تحديث سجلات الأتعاب والرسوم
        fee = Fee.query.filter_by(case_id=case.id).first()
        if case.fees_total > 0:
            if fee:
                fee.amount = case.fees_total
                fee.paid_amount = case.fees_paid
                fee.remaining_amount = case.fees_remaining
                fee.status = 'مستحقة' if case.fees_remaining > 0 else 'مدفوعة'
            else:
                fee = Fee(
                    case_id=case.id, client_id=case.client_id, amount=case.fees_total,
                    paid_amount=case.fees_paid, remaining_amount=case.fees_remaining,
                    fee_type='أتعاب محاماة', status='مستحقة' if case.fees_remaining > 0 else 'مدفوعة'
                )
                db.session.add(fee)
        elif fee:
            db.session.delete(fee)

        court_fee = CourtFee.query.filter_by(case_id=case.id).first()
        if case.court_fees_total > 0:
            if court_fee:
                court_fee.amount = case.court_fees_total
                court_fee.paid_amount = case.court_fees_paid
                court_fee.remaining_amount = case.court_fees_remaining
                court_fee.status = 'مستحقة' if case.court_fees_remaining > 0 else 'مدفوعة'
            else:
                court_fee = CourtFee(
                    case_id=case.id, client_id=case.client_id, amount=case.court_fees_total,
                    paid_amount=case.court_fees_paid, remaining_amount=case.court_fees_remaining,
                    fee_type='رسوم قضائية', status='مستحقة' if case.court_fees_remaining > 0 else 'مدفوعة'
                )
                db.session.add(court_fee)
        elif court_fee:
            db.session.delete(court_fee)

        # إضافة معاملات مالية للمبالغ الجديدة المدفوعة
        new_fees_paid = case.fees_paid - old_fees_paid
        new_court_fees_paid = case.court_fees_paid - old_court_fees_paid

        if new_fees_paid > 0:
            fee_transaction = FinancialTransaction(
                amount=new_fees_paid, date=datetime.now(), type='أتعاب',
                description=f'دفعة أتعاب إضافية للقضية: {case.title}',
                case_id=case.id, client_id=case.client_id, currency=case.currency
            )
            db.session.add(fee_transaction)

        if new_court_fees_paid > 0:
            court_fee_transaction = FinancialTransaction(
                amount=new_court_fees_paid, date=datetime.now(), type='رسوم',
                description=f'دفعة رسوم إضافية للقضية: {case.title}',
                case_id=case.id, client_id=case.client_id, currency=case.currency
            )
            db.session.add(court_fee_transaction)

        db.session.commit()
        backup_db_file()
        flash('تم تعديل بيانات القضية والبيانات المالية', 'success')
        return redirect(url_for('cases_list'))
    return render_template('cases/edit.html', case=case, clients=clients, case_tasks=case_tasks, case_appointments=case_appointments)

@app.route('/cases/<int:case_id>/delete', methods=['POST'])
@login_required
def delete_case(case_id):
    case = Case.query.get_or_404(case_id)
    db.session.delete(case)
    db.session.commit()
    backup_db_file()
    flash('تم حذف القضية', 'success')
    return redirect(url_for('cases_list'))

# تم حذف routes العقارات - سيتم إعادة بناؤها

# تم حذف routes المستأجرين - سيتم إعادة بناؤها

@app.route('/clients')
@login_required
def clients_list():
    # حقول البحث المتقدم
    name = request.args.get('name', '').strip()
    national_id = request.args.get('national_id', '').strip()
    phone = request.args.get('phone', '').strip()
    email = request.args.get('email', '').strip()
    address = request.args.get('address', '').strip()
    q = request.args.get('q', '').strip()

    clients_query = Client.query
    if name:
        clients_query = clients_query.filter(Client.name.ilike(f'%{name}%'))
    if national_id:
        clients_query = clients_query.filter(Client.national_id.ilike(f'%{national_id}%'))
    if phone:
        clients_query = clients_query.filter(Client.phone.ilike(f'%{phone}%'))
    if email:
        clients_query = clients_query.filter(Client.email.ilike(f'%{email}%'))
    if address:
        clients_query = clients_query.filter(Client.address.ilike(f'%{address}%'))
    if q:
        # بحث عام في كل الحقول
        clients_query = clients_query.filter(
            (Client.name.ilike(f'%{q}%')) |
            (Client.national_id.ilike(f'%{q}%')) |
            (Client.phone.ilike(f'%{q}%')) |
            (Client.email.ilike(f'%{q}%')) |
            (Client.address.ilike(f'%{q}%'))
        )
    clients = clients_query.all()
    return render_template('clients/list.html', clients=clients)

@app.route('/clients/add', methods=['GET', 'POST'])
@login_required
def add_client():
    if request.method == 'POST':
        try:
            name = request.form['name']
            national_id = request.form.get('id_number', '')
            phone = request.form['phone']
            email = request.form.get('email', '')
            address = request.form.get('address', '')
            birth_date = request.form.get('birth_date')
            occupation = request.form.get('occupation', '')
            notes = request.form.get('notes', '')
            role = request.form.get('role', 'موكل')

            # تحويل تاريخ الميلاد إذا كان موجوداً
            birth_date_obj = None
            if birth_date:
                from datetime import datetime
                birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d').date()

            client = Client(
                name=name,
                national_id=national_id,
                phone=phone,
                email=email,
                address=address,
                birth_date=birth_date_obj,
                occupation=occupation,
                notes=notes,
                role=role
            )
            db.session.add(client)
            db.session.commit()
            backup_db_file()

            # إذا كان الطلب من AJAX، إرجاع JSON
            if request.headers.get('Content-Type') == 'application/x-www-form-urlencoded' and request.headers.get('X-CSRFToken'):
                return jsonify({'success': True, 'message': 'تمت إضافة العميل بنجاح'})

            flash('تمت إضافة العميل بنجاح', 'success')
            return redirect(url_for('clients_list'))

        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
            flash(f'حدث خطأ أثناء إضافة العميل: {str(e)}', 'danger')
            return redirect(url_for('clients_list'))

    return render_template('clients/add.html')

@app.route('/clients/<int:client_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_client(client_id):
    client = Client.query.get_or_404(client_id)
    if request.method == 'POST':
        try:
            client.name = request.form['name']
            client.national_id = request.form.get('national_id', '')
            client.phone = request.form['phone']
            client.email = request.form['email']
            client.address = request.form['address']
            client.role = request.form.get('role', 'موكل')
            db.session.commit()
            backup_db_file()
            flash('تم تعديل بيانات العميل', 'success')
            return redirect(url_for('clients_list'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تعديل العميل: {str(e)}', 'danger')
            return redirect(url_for('clients_list'))
    return render_template('clients/edit.html', client=client)

@app.route('/clients/<int:client_id>/delete', methods=['POST'])
@login_required
def delete_client(client_id):
    try:
        client = Client.query.get_or_404(client_id)
        db.session.delete(client)
        db.session.commit()
        backup_db_file()
        flash('تم حذف العميل', 'success')
        return redirect(url_for('clients_list'))
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف العميل: {str(e)}', 'danger')
        return redirect(url_for('clients_list'))

@app.route('/dashboard', methods=['GET', 'POST'])
@login_required
def dashboard():
    # فحص صحة الجلسة
    if not validate_session():
        return redirect(url_for('login'))
    today = datetime.today()
    cases_count = Case.query.count()
    properties_count = Property.query.count() if 'Property' in globals() else 0
    leases_count = 0  # لتحديثها لاحقاً
    tasks_count = Task.query.count() if 'Task' in globals() else 0
    appointments_count = Appointment.query.count() if 'Appointment' in globals() else 0

    # البحث
    search_results = None
    search_field = request.form.get('search_field') if request.method == 'POST' else None
    search_value = request.form.get('search_value') if request.method == 'POST' else None
    if search_field and search_value:
        if search_field == 'client_name':
            search_results = Case.query.join(Client).filter(Client.name.contains(search_value)).all()
        elif search_field == 'national_id':
            search_results = Case.query.join(Client).filter(Client.national_id.contains(search_value)).all()
        elif search_field == 'court':
            search_results = Case.query.filter(Case.court.contains(search_value)).all()
        else:
            search_results = []

    # إحصائيات
    courts_stats = db.session.query(Case.court, func.count(Case.id)).group_by(Case.court).all()
    clients_stats = db.session.query(Client.name, func.count(Case.id)).join(Case).group_by(Client.name).all()
    types_stats = db.session.query(Case.type, func.count(Case.id)).group_by(Case.type).all()

    # تحويل النتائج إلى dict حتى تكون JSON serializable
    courts_stats = {row[0]: row[1] for row in courts_stats}
    clients_stats = {row[0]: row[1] for row in clients_stats}
    types_stats = {row[0]: row[1] for row in types_stats}
    # المهام القريبة (خلال 3 أيام) وغير المنجزة فقط
    upcoming_tasks = Task.query.filter(Task.due_date >= today, Task.due_date <= today + timedelta(days=3), Task.status != 'منجزة').all() if 'Task' in globals() else []
    # الأقساط المالية المستحقة خلال أسبوع
    upcoming_installments = []
    if 'Installment' in globals():
        upcoming_installments = db.session.query(Installment).filter(
            Installment.due_date >= today,
            Installment.due_date <= today + timedelta(days=7),
            Installment.paid == False
        ).all()
    # أقرب موعد خلال يوم واحد فقط
    upcoming_appointment = None
    nearest_appointment = Appointment.query.filter(Appointment.date >= today).order_by(Appointment.date.asc()).first()
    if nearest_appointment:
        delta = nearest_appointment.date - today
        if delta.days == 0:
            upcoming_appointment = nearest_appointment

    def safe_task_dict(task):
        return {
            'id': task.id,
            'title': task.title or '',
            'description': task.description or '',
            'due_date': task.due_date.strftime('%Y-%m-%dT%H:%M') if task.due_date else '',
            'status': task.status or ''
        }
    def safe_installment_dict(inst):
        return {
            'id': inst.id,
            'lease_id': inst.lease_id,
            'due_date': inst.due_date.strftime('%Y-%m-%d') if inst.due_date else '',
            'amount': inst.amount,
            'paid': inst.paid,
            'payment_date': inst.payment_date.strftime('%Y-%m-%d') if inst.payment_date else ''
        }
    def safe_appointment_dict(app):
        return {
            'id': app.id,
            'subject': app.subject or '',
            'date': app.date.strftime('%Y-%m-%dT%H:%M') if app.date else '',
            'location': app.location or '',
            'notes': app.notes or '',
            'client': {'name': app.client.name} if app.client else {},
            'case': {'title': app.case.title} if app.case else {}
        }
    upcoming_tasks_safe = [safe_task_dict(t) for t in upcoming_tasks]
    upcoming_installments_safe = [safe_installment_dict(i) for i in upcoming_installments]

    # عدد المهام غير المنجزة
    not_done_tasks_count = Task.query.filter(Task.status != 'منجزة').count() if 'Task' in globals() else 0

    # البيانات المتأخرة للتنبيهات
    overdue_tasks = []
    overdue_appointments = []
    overdue_payments = []

    # المهام المتأخرة
    if 'TaskItem' in globals():
        overdue_tasks = TaskItem.query.filter(
            TaskItem.due_date < datetime.now(timezone.utc),
            TaskItem.completed == False
        ).order_by(TaskItem.due_date).limit(5).all()
    elif 'Task' in globals():
        overdue_tasks = Task.query.filter(
            Task.due_date < today,
            Task.status != 'منجزة'
        ).order_by(Task.due_date).limit(5).all()

    # المواعيد المتأخرة أو خلال 24 ساعة
    tomorrow = today + timedelta(days=1)
    if 'Appointment' in globals():
        overdue_appointments = Appointment.query.filter(
            Appointment.date <= tomorrow
        ).order_by(Appointment.date).limit(5).all()

    return render_template('dashboard.html',
        cases_count=cases_count,
        properties_count=properties_count,
        leases_count=leases_count,
        tasks_count=tasks_count,
        not_done_tasks_count=not_done_tasks_count,
        appointments_count=appointments_count,
        courts_stats=courts_stats,
        clients_stats=clients_stats,
        types_stats=types_stats,
        search_results=search_results,
        search_field=search_field,
        search_value=search_value,
        upcoming_tasks=upcoming_tasks_safe,
        upcoming_installments=upcoming_installments_safe,
        upcoming_appointment=upcoming_appointment,
        overdue_tasks=overdue_tasks,
        overdue_appointments=overdue_appointments,
        overdue_payments=overdue_payments,
    )

# ------------------ مهام ------------------
@app.route('/tasks')
@login_required
def tasks_list():
    tasks = Task.query.order_by(Task.due_date.asc()).all()
    return render_template('tasks/list.html', tasks=tasks)

@app.route('/tasks/add', methods=['GET', 'POST'])
@login_required
def add_task():
    case_id = request.args.get('case_id')
    if request.method == 'POST':
        title = request.form['title']
        description = request.form['description']
        due_date = request.form['due_date'] or None
        status = request.form['status']
        user_id = current_user.id
        from datetime import datetime
        due_date_obj = datetime.strptime(due_date, '%Y-%m-%dT%H:%M') if due_date else None
        task = Task(title=title, description=description, due_date=due_date_obj, status=status, user_id=user_id)
        if case_id:
            task.case_id = int(case_id)
        db.session.add(task)
        db.session.commit()
        backup_db_file()
        flash('تمت إضافة المهمة بنجاح', 'success')
        return redirect(url_for('tasks_list'))
    return render_template('tasks/add.html', case_id=case_id)

@app.route('/tasks/<int:task_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_task(task_id):
    task = Task.query.get_or_404(task_id)
    if request.method == 'POST':
        task.title = request.form['title']
        task.description = request.form['description']
        due_date = request.form['due_date'] or None
        from datetime import datetime
        task.due_date = datetime.strptime(due_date, '%Y-%m-%dT%H:%M') if due_date else None
        task.status = request.form['status']
        db.session.commit()
        backup_db_file()
        flash('تم تعديل المهمة', 'success')
        return redirect(url_for('tasks_list'))
    return render_template('tasks/edit.html', task=task)

@app.route('/tasks/<int:task_id>/delete', methods=['POST'])
@login_required
def delete_task(task_id):
    task = Task.query.get_or_404(task_id)
    # حذف منطقي: تغيير الحالة إلى "محذوفة" بدلاً من الحذف النهائي
    task.status = 'محذوفة'
    db.session.commit()
    backup_db_file()
    flash('تم نقل المهمة إلى المهام المحذوفة', 'success')
    return redirect(request.referrer or url_for('tasks_list'))

@app.route('/tasks/<int:task_id>/toggle_status', methods=['POST'])
@login_required
def toggle_task_status(task_id):
    task = Task.query.get_or_404(task_id)
    # تبديل الحالة بين منجزة وغير منجزة
    if task.status == 'منجزة':
        task.status = 'غير منجزة'
    else:
        task.status = 'منجزة'
    db.session.commit()
    backup_db_file()
    # دعم AJAX: إذا كان الطلب من نوع JSON أو AJAX، أرجع JSON
    if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.headers.get('Accept') == 'application/json':
        return {'success': True, 'new_status': task.status}
    flash('تم تحديث حالة المهمة', 'success')
    return redirect(request.referrer or url_for('tasks_list'))

@app.route('/tasks/manage')
@login_required
def manage_tasks():
    from datetime import datetime
    status_filter = request.args.get('status')
    if status_filter == 'done':
        tasks = Task.query.filter_by(status='منجزة').order_by(Task.due_date.asc()).all()
    elif status_filter == 'not_done':
        tasks = Task.query.filter(Task.status != 'منجزة').order_by(Task.due_date.asc()).all()
    elif status_filter == 'deleted':
        # المهام المحذوفة: التي حالتها "محذوفة"
        tasks = Task.query.filter_by(status='محذوفة').order_by(Task.due_date.asc()).all()
    else:
        tasks = Task.query.order_by(Task.due_date.asc()).all()
    current_time = datetime.now()
    return render_template('tasks/manage.html', tasks=tasks, status_filter=status_filter, current_time=current_time)

# ------------------ مواعيد ------------------
@app.route('/appointments')
@login_required
def appointments_list():
    appointments = Appointment.query.order_by(Appointment.date.asc()).all()
    return render_template('appointments/list.html', appointments=appointments)

@app.route('/appointments/add', methods=['GET', 'POST'])
@login_required
def add_appointment():
    from .models import Client, Case
    clients = Client.query.all()
    cases = Case.query.all()
    case_id = request.args.get('case_id')
    if request.method == 'POST':
        subject = request.form['subject']
        date = request.form['date']
        location = request.form['location']
        notes = request.form['notes']
        client_id = request.form.get('client_id') or None
        case_id_form = request.form.get('case_id') or case_id
        from datetime import datetime
        date_obj = datetime.strptime(date, '%Y-%m-%dT%H:%M') if date else None
        appointment = Appointment(subject=subject, date=date_obj, location=location, notes=notes,
                                  client_id=client_id if client_id else None, case_id=case_id_form if case_id_form else None)
        db.session.add(appointment)
        db.session.commit()
        backup_db_file()
        flash('تمت إضافة الموعد بنجاح', 'success')
        return redirect(url_for('appointments_list'))
    return render_template('appointments/add.html', clients=clients, cases=cases, case_id=case_id)

@app.route('/appointments/<int:appointment_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_appointment(appointment_id):
    from .models import Client, Case
    appointment = Appointment.query.get_or_404(appointment_id)
    clients = Client.query.all()
    cases = Case.query.all()
    if request.method == 'POST':
        appointment.subject = request.form['subject']
        date = request.form['date']
        appointment.location = request.form['location']
        appointment.notes = request.form['notes']
        appointment.client_id = request.form.get('client_id') or None
        appointment.case_id = request.form.get('case_id') or None
        from datetime import datetime
        appointment.date = datetime.strptime(date, '%Y-%m-%dT%H:%M') if date else None
        db.session.commit()
        backup_db_file()
        flash('تم تعديل الموعد', 'success')
        return redirect(url_for('appointments_list'))
    return render_template('appointments/edit.html', appointment=appointment, clients=clients, cases=cases)

@app.route('/appointments/<int:appointment_id>/delete', methods=['POST'])
@login_required
def delete_appointment(appointment_id):
    appointment = Appointment.query.get_or_404(appointment_id)
    db.session.delete(appointment)
    db.session.commit()
    backup_db_file()
    flash('تم حذف الموعد', 'success')
    return redirect(url_for('appointments_list'))

# ------------------ إدارة المواعيد المتقدمة ------------------
@app.route('/appointments/management')
@login_required
def appointments_management():
    """صفحة إدارة المواعيد المتقدمة"""
    from .models import Client, Case
    from .calendar_models import Event
    from datetime import datetime, timedelta

    # جلب جميع المواعيد من Event model (النظام المحسن)
    appointments = Event.query.filter(
        Event.event_type.in_(['موعد عام', 'جلسة محكمة', 'اجتماع عميل', 'مهمة'])
    ).order_by(Event.start_datetime.desc()).all()

    # تحويل البيانات للتوافق مع القالب
    appointments_data = []
    for event in appointments:
        appointment_data = {
            'id': event.id,
            'title': event.title,
            'description': event.description,
            'date': event.start_datetime.date() if event.start_datetime else None,
            'time': event.start_datetime.time() if event.start_datetime else None,
            'location': event.location,
            'type': event.event_type,
            'status': event.status,
            'priority': event.priority,
            'client': event.client if hasattr(event, 'client') else None,
            'case': event.case if hasattr(event, 'case') else None,
            'client_id': event.client_id,
            'case_id': event.case_id
        }
        appointments_data.append(appointment_data)

    # جلب العملاء والقضايا للنوافذ المنبثقة
    clients = Client.query.all()
    cases = Case.query.all()

    return render_template('appointments/appointments_management.html',
                         appointments=appointments_data,
                         clients=clients,
                         cases=cases)

# ------------------ API للمواعيد المتقدمة ------------------
@app.route('/api/appointments', methods=['POST'])
@login_required
def api_create_appointment():
    """إنشاء موعد جديد عبر API"""
    try:
        from .calendar_models import Event
        from datetime import datetime

        # جلب البيانات من النموذج
        title = request.form.get('title')
        appointment_type = request.form.get('type')
        date = request.form.get('date')
        time = request.form.get('time')
        location = request.form.get('location', '')
        priority = request.form.get('priority', 'متوسطة')
        description = request.form.get('description', '')
        status = request.form.get('status', 'مجدول')
        client_id = request.form.get('client_id') or None
        case_id = request.form.get('case_id') or None
        reminder_minutes = request.form.get('reminder_minutes', 30)

        # التحقق من البيانات المطلوبة
        if not all([title, appointment_type, date, time]):
            return jsonify({'success': False, 'message': 'يرجى ملء جميع الحقول المطلوبة'})

        # تحويل التاريخ والوقت
        start_datetime = datetime.strptime(f"{date} {time}", '%Y-%m-%d %H:%M')
        end_datetime = start_datetime + timedelta(hours=1)  # افتراضي ساعة واحدة

        # إنشاء الموعد الجديد
        new_appointment = Event(
            title=title,
            description=description,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            location=location,
            event_type=appointment_type,
            priority=priority,
            status=status,
            client_id=int(client_id) if client_id else None,
            case_id=int(case_id) if case_id else None,
            created_by=current_user.id
        )

        db.session.add(new_appointment)
        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم إنشاء الموعد بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@app.route('/api/appointments/<int:appointment_id>', methods=['GET'])
@login_required
def api_get_appointment(appointment_id):
    """جلب بيانات موعد محدد"""
    try:
        from .calendar_models import Event

        appointment = Event.query.get_or_404(appointment_id)

        appointment_data = {
            'id': appointment.id,
            'title': appointment.title,
            'description': appointment.description,
            'date': appointment.start_datetime.strftime('%Y-%m-%d') if appointment.start_datetime else '',
            'time': appointment.start_datetime.strftime('%H:%M') if appointment.start_datetime else '',
            'location': appointment.location or '',
            'type': appointment.event_type,
            'priority': appointment.priority,
            'status': appointment.status,
            'client_id': appointment.client_id,
            'case_id': appointment.case_id
        }

        return jsonify({'success': True, 'appointment': appointment_data})

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@app.route('/api/appointments/<int:appointment_id>', methods=['PUT'])
@login_required
def api_update_appointment(appointment_id):
    """تحديث موعد موجود"""
    try:
        from .calendar_models import Event
        from datetime import datetime, timedelta

        appointment = Event.query.get_or_404(appointment_id)

        # تحديث البيانات
        appointment.title = request.form.get('title', appointment.title)
        appointment.event_type = request.form.get('type', appointment.event_type)
        appointment.location = request.form.get('location', appointment.location)
        appointment.priority = request.form.get('priority', appointment.priority)
        appointment.description = request.form.get('description', appointment.description)
        appointment.status = request.form.get('status', appointment.status)

        # تحديث التاريخ والوقت
        date = request.form.get('date')
        time = request.form.get('time')
        if date and time:
            start_datetime = datetime.strptime(f"{date} {time}", '%Y-%m-%d %H:%M')
            appointment.start_datetime = start_datetime
            appointment.end_datetime = start_datetime + timedelta(hours=1)

        # تحديث العلاقات
        client_id = request.form.get('client_id')
        case_id = request.form.get('case_id')
        appointment.client_id = int(client_id) if client_id else None
        appointment.case_id = int(case_id) if case_id else None

        appointment.updated_date = datetime.now()

        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم تحديث الموعد بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@app.route('/api/appointments/<int:appointment_id>', methods=['DELETE'])
@login_required
def api_delete_appointment(appointment_id):
    """حذف موعد"""
    try:
        from .calendar_models import Event

        appointment = Event.query.get_or_404(appointment_id)
        db.session.delete(appointment)
        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم حذف الموعد بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@app.route('/api/appointments/<int:appointment_id>/complete', methods=['POST'])
@login_required
def api_complete_appointment(appointment_id):
    """إكمال موعد"""
    try:
        from .calendar_models import Event
        from datetime import datetime

        appointment = Event.query.get_or_404(appointment_id)
        appointment.status = 'مكتمل'
        appointment.updated_date = datetime.now()

        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم إكمال الموعد بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# ------------------ معاملات مالية ------------------
@app.route('/finance')
@login_required
def finance_list():
    transactions = FinancialTransaction.query.order_by(FinancialTransaction.date.desc()).all()
    # حساب الإجماليات لكل عملة
    summary = {}
    currencies = set([t.currency for t in transactions])
    for currency in currencies:
        income_types = ['قبض', 'دفعة', 'أتعاب', 'تحصيل إيجار']
        expense_types = ['صرف', 'مصاريف مكتب', 'رسوم']
        total_income = sum(t.amount for t in transactions if t.currency == currency and t.type in income_types)
        total_expense = sum(t.amount for t in transactions if t.currency == currency and t.type in expense_types)
        balance = total_income - total_expense
        summary[currency] = {
            'income': total_income,
            'expense': total_expense,
            'balance': balance
        }
    return render_template('finance/list.html', transactions=transactions, summary=summary)



@app.route('/finance/<int:transaction_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_financial_transaction(transaction_id):
    transaction = FinancialTransaction.query.get_or_404(transaction_id)
    cases = Case.query.all()
    clients = Client.query.all()
    if request.method == 'POST':
        transaction.amount = float(request.form['amount'])
        transaction.date = request.form['date']
        transaction.type = request.form['type']
        transaction.description = request.form.get('description')
        transaction.case_id = request.form.get('case_id') or None
        transaction.client_id = request.form.get('client_id') or None
        db.session.commit()
        backup_db_file()
        flash('تم تعديل السند المالي', 'success')
        return redirect(url_for('finance_list'))
    return render_template('finance/edit.html', transaction=transaction, cases=cases, clients=clients)

@app.route('/finance/<int:transaction_id>/delete', methods=['POST'])
@login_required
def delete_financial_transaction(transaction_id):
    transaction = FinancialTransaction.query.get_or_404(transaction_id)
    db.session.delete(transaction)
    db.session.commit()
    backup_db_file()
    flash('تم حذف السند المالي', 'success')
    return redirect(url_for('finance_list'))

@app.route('/finance/reports', methods=['GET'])
@login_required
def finance_reports():
    try:
        from datetime import datetime, timedelta

        # بناء الاستعلام الأساسي
        query = FinancialTransaction.query

        # تطبيق الفلاتر
        client_id = request.args.get('client_id')
        case_id = request.args.get('case_id')
        transaction_type = request.args.get('transaction_type')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        if client_id:
            query = query.filter(FinancialTransaction.client_id == client_id)

        if case_id:
            query = query.filter(FinancialTransaction.case_id == case_id)

        if transaction_type:
            query = query.filter(FinancialTransaction.type.contains(transaction_type))

        if date_from:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(FinancialTransaction.date >= date_from_obj)

        if date_to:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(FinancialTransaction.date <= date_to_obj)

        # جلب المعاملات المفلترة
        transactions = query.order_by(FinancialTransaction.date.desc()).all()

        # جلب جميع العملاء والقضايا للفلاتر
        clients = Client.query.all()
        cases = Case.query.all()

        # حساب الملخصات المالية لكل عملة
        summary = {}
        if transactions:
            currencies = set([t.currency or 'شيكل' for t in transactions])
            for currency in currencies:
                income_types = ['قبض', 'دفعة', 'أتعاب', 'تحصيل إيجار']
                expense_types = ['صرف', 'مصاريف مكتب', 'رسوم', 'مصاريف']

                currency_transactions = [t for t in transactions if (t.currency or 'شيكل') == currency]
                total_income = sum(t.amount for t in currency_transactions if t.type in income_types)
                total_expense = sum(t.amount for t in currency_transactions if t.type in expense_types)
                balance = total_income - total_expense

                summary[currency] = {
                    'income': total_income,
                    'expense': total_expense,
                    'balance': balance
                }

        # إحصائيات إضافية
        stats = {
            'total_transactions': len(transactions),
            'total_clients': len(set([t.client_id for t in transactions if t.client_id])),
            'total_cases': len(set([t.case_id for t in transactions if t.case_id])),
            'date_range': {
                'from': min([t.date for t in transactions if t.date]) if transactions else None,
                'to': max([t.date for t in transactions if t.date]) if transactions else None
            }
        }

        return render_template('finance/reports.html',
                             transactions=transactions,
                             clients=clients,
                             cases=cases,
                             summary=summary,
                             stats=stats)
    except Exception as e:
        flash(f'خطأ في تحميل التقارير المالية: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@app.route('/office-expenses', methods=['GET', 'POST'])
@login_required
def office_expenses():
    from .models import FinancialTransaction
    # نوع المصروفات: مصروفات المكتب فقط
    expenses = FinancialTransaction.query.filter_by(type='مصاريف مكتب').order_by(FinancialTransaction.date.desc()).all()
    if request.method == 'POST':
        # إضافة مصروف جديد
        amount = request.form['amount']
        currency = request.form['currency']
        description = request.form['description']
        from datetime import datetime
        expense = FinancialTransaction(type='مصاريف مكتب', amount=amount, currency=currency, description=description, date=datetime.now())
        db.session.add(expense)
        db.session.commit()
        flash('تمت إضافة مصروف مكتبي بنجاح', 'success')
        return redirect(url_for('office_expenses'))
    return render_template('finance/office_expenses.html', expenses=expenses)

@app.route('/shutdown')
def shutdown():
    from flask import request
    func = request.environ.get('werkzeug.server.shutdown')
    if func is None:
        return 'غير ممكن إيقاف النظام من هنا (سيرفر غير مدعوم)', 500
    func()
    return 'تم إيقاف النظام بنجاح.'

@app.after_request
def add_header(response):
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate, public, max-age=0"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    return response

@app.route('/backup_db', methods=['POST', 'GET'])
def backup_db():
    import shutil, os
    db_path = os.path.join(os.getcwd(), 'instance', 'lawoffice.db')
    backup_dir = os.path.join(os.getcwd(), 'instance', 'backups')
    os.makedirs(backup_dir, exist_ok=True)
    from datetime import datetime
    backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    backup_path = os.path.join(backup_dir, backup_name)
    if os.path.exists(db_path):
        shutil.copy2(db_path, backup_path)
    return ('', 204)

def backup_db_file():
    db_path = os.path.join(os.getcwd(), 'instance', 'lawoffice.db')
    backup_dir = os.path.join(os.getcwd(), 'instance', 'backups')
    os.makedirs(backup_dir, exist_ok=True)
    from datetime import datetime
    backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    backup_path = os.path.join(backup_dir, backup_name)
    if os.path.exists(db_path):
        shutil.copy2(db_path, backup_path)

@app.route('/stats', methods=['GET', 'POST'])
@login_required
def stats():
    try:
        today = datetime.today()
        # البحث المتقدم
        search_results = None
        search_field = request.form.get('search_field') if request.method == 'POST' else None
        search_value = request.form.get('search_value') if request.method == 'POST' else None
        extra_report = None

        if search_field and search_value:
            if search_field == 'client_name':
                search_results = Case.query.join(Client).filter(Client.name.contains(search_value)).all()
            elif search_field == 'national_id':
                search_results = Case.query.join(Client).filter(Client.national_id.contains(search_value)).all()
            elif search_field == 'court':
                search_results = Case.query.filter(Case.court.contains(search_value)).all()
            elif search_field == 'case_number':
                search_results = Case.query.filter(Case.case_number.contains(search_value)).all()
            elif search_field == 'case_status':
                search_results = Case.query.filter(Case.status.contains(search_value)).all()
            else:
                search_results = []

            # تقارير مالية مرتبطة بنتائج البحث
            if search_results:
                case_ids = [c.id for c in search_results]
                paid = db.session.query(func.sum(FinancialTransaction.amount)).filter(FinancialTransaction.case_id.in_(case_ids), FinancialTransaction.type.in_(['قبض', 'دفعة', 'أتعاب', 'تحصيل إيجار'])).scalar() or 0
                expense = db.session.query(func.sum(FinancialTransaction.amount)).filter(FinancialTransaction.case_id.in_(case_ids), FinancialTransaction.type.in_(['صرف', 'مصاريف مكتب', 'رسوم'])).scalar() or 0
                extra_report = {
                    'paid': paid,
                    'expense': expense,
                    'balance': paid - expense
                }

        # إحصائيات عامة
        courts_stats = db.session.query(Case.court, func.count(Case.id)).group_by(Case.court).all()
        clients_stats = db.session.query(Client.name, func.count(Case.id)).join(Case).group_by(Client.name).all()
        types_stats = db.session.query(Case.type, func.count(Case.id)).group_by(Case.type).all()
        status_stats = db.session.query(Case.status, func.count(Case.id)).group_by(Case.status).all()
        client_role_stats = db.session.query(Case.client_role, func.count(Case.id)).group_by(Case.client_role).all()

        courts_stats = {row[0]: row[1] for row in courts_stats}
        clients_stats = {row[0]: row[1] for row in clients_stats}
        types_stats = {row[0]: row[1] for row in types_stats}
        status_stats = {row[0]: row[1] for row in status_stats}
        client_role_stats = {row[0] or 'مدعي': row[1] for row in client_role_stats}

        # تقارير مالية إجمالية
        total_paid = db.session.query(func.sum(FinancialTransaction.amount)).filter(FinancialTransaction.type.in_(['قبض', 'دفعة', 'أتعاب', 'تحصيل إيجار'])).scalar() or 0
        total_expense = db.session.query(func.sum(FinancialTransaction.amount)).filter(FinancialTransaction.type.in_(['صرف', 'مصاريف مكتب', 'رسوم'])).scalar() or 0
        total_balance = total_paid - total_expense

        # إحصائيات أساسية
        top_clients = db.session.query(Client.name, func.count(Case.id)).join(Case).group_by(Client.name).order_by(func.count(Case.id).desc()).limit(5).all()
        top_courts = db.session.query(Case.court, func.count(Case.id)).group_by(Case.court).order_by(func.count(Case.id).desc()).limit(5).all()

        # القضايا المنتهية مقابل الجارية
        closed_count = Case.query.filter(Case.status.in_(['مفصولة', 'منتهية', 'محكومة'])).count()
        open_count = Case.query.filter(~Case.status.in_(['مفصولة', 'منتهية', 'محكومة'])).count()
        total_cases = Case.query.count()
        closed_ratio = (closed_count / total_cases * 100) if total_cases else 0

        # القضايا الجديدة هذا الشهر/السنة
        from sqlalchemy import extract
        this_month = today.month
        this_year = today.year
        new_cases_month = Case.query.filter(extract('year', Case.open_date)==this_year, extract('month', Case.open_date)==this_month).count()
        new_cases_year = Case.query.filter(extract('year', Case.open_date)==this_year).count()

        # متغيرات افتراضية للمتغيرات المتقدمة
        avg_duration = None
        top_lawyers = []
        monthly_cases = []
        top_paid_cases = []
        unpaid_cases = []
        old_cases = []
        opponent_stats = []
        client_source_stats = []
        monthly_payments = []
        risky_cases = []
        avg_rating = None
        upcoming_sessions = []
        upcoming_big_installments = []
        win_count = None
        lose_count = None
        service_type_stats = []
        late_clients = []
        prev_month_cases = 0
        prev_month_paid = 0
        property_status_stats = []
        late_tasks = []
        top_case_types = db.session.query(Case.type, func.count(Case.id)).group_by(Case.type).order_by(func.count(Case.id).desc()).limit(5).all()

        return render_template('stats.html',
            courts_stats=courts_stats,
            clients_stats=clients_stats,
            types_stats=types_stats,
            status_stats=status_stats,
            client_role_stats=client_role_stats,
            search_results=search_results,
            search_field=search_field,
            search_value=search_value,
            extra_report=extra_report,
            total_paid=total_paid,
            total_expense=total_expense,
            total_balance=total_balance,
            avg_duration=avg_duration,
            top_clients=top_clients,
            top_lawyers=top_lawyers,
            top_courts=top_courts,
            monthly_cases=monthly_cases,
            new_cases_month=new_cases_month,
            new_cases_year=new_cases_year,
            closed_count=closed_count,
            open_count=open_count,
            top_paid_cases=top_paid_cases,
            unpaid_cases=unpaid_cases,
            old_cases=old_cases,
            closed_ratio=closed_ratio,
            opponent_stats=opponent_stats,
            client_source_stats=client_source_stats,
            monthly_payments=monthly_payments,
            risky_cases=risky_cases,
            avg_rating=avg_rating,
            upcoming_sessions=upcoming_sessions,
            upcoming_big_installments=upcoming_big_installments,
            win_count=win_count,
            lose_count=lose_count,
            service_type_stats=service_type_stats,
            late_clients=late_clients,
            prev_month_cases=prev_month_cases,
            prev_month_paid=prev_month_paid,
            property_status_stats=property_status_stats,
            late_tasks=late_tasks,
            top_case_types=top_case_types
        )
    except Exception as e:
        flash(f'خطأ في تحميل الإحصائيات: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

# ------------------ إدارة المستخدمين ------------------
@app.route('/users')
@login_required
def users_list():
    if current_user.role != 'مدير':
        flash('غير مصرح لك بالدخول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))
    users = User.query.all()
    return render_template('users.html', users=users)

@app.route('/users/add', methods=['GET', 'POST'])
@login_required
def add_user():
    if current_user.role != 'مدير':
        flash('غير مصرح لك بإضافة مستخدمين', 'danger')
        return redirect(url_for('users_list'))
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        role = request.form.get('role', 'موظف')
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل!', 'danger')
            return redirect(url_for('add_user'))
        user = User(username=username, password=password, role=role)
        db.session.add(user)
        db.session.commit()
        flash('تمت إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('users_list'))
    return render_template('add_user.html')

@app.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    if current_user.role != 'مدير':
        flash('غير مصرح لك بتعديل المستخدمين', 'danger')
        return redirect(url_for('users_list'))
    user = User.query.get_or_404(user_id)
    if request.method == 'POST':
        user.username = request.form['username']
        if request.form['password']:
            user.password = request.form['password']
        user.role = request.form.get('role', user.role)
        db.session.commit()
        flash('تم تعديل بيانات المستخدم', 'success')
        return redirect(url_for('users_list'))
    return render_template('edit_user.html', user=user)

@app.route('/users/<int:user_id>/delete')
@login_required
def delete_user(user_id):
    if current_user.role != 'مدير':
        flash('غير مصرح لك بحذف المستخدمين', 'danger')
        return redirect(url_for('users_list'))
    user = User.query.get_or_404(user_id)
    db.session.delete(user)
    db.session.commit()
    flash('تم حذف المستخدم', 'success')
    return redirect(url_for('users_list'))

@app.route('/cases/<int:case_id>/upload', methods=['POST'])
@login_required
def upload_document(case_id):
    if 'document' not in request.files:
        flash('لم يتم اختيار ملف!', 'danger')
        return redirect(request.referrer)
    file = request.files['document']
    if file.filename == '':
        flash('لم يتم اختيار ملف!', 'danger')
        return redirect(request.referrer)
    if file and allowed_file(file.filename):
        filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{file.filename}"
        file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
        doc = Document(filename=filename, upload_date=datetime.now(), case_id=case_id)
        db.session.add(doc)
        db.session.commit()
        flash('تم رفع المستند بنجاح', 'success')
    else:
        flash('نوع الملف غير مدعوم!', 'danger')
    return redirect(request.referrer)

@app.route('/uploads/<filename>')
@login_required
def download_document(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename, as_attachment=True)

@app.route('/cases/<int:case_id>/documents/<int:doc_id>/delete')
@login_required
def delete_document(case_id, doc_id):
    doc = Document.query.get_or_404(doc_id)
    if doc.case_id != case_id:
        flash('المستند غير مرتبط بهذه القضية', 'danger')
        return redirect(request.referrer)
    try:
        os.remove(os.path.join(app.config['UPLOAD_FOLDER'], doc.filename))
    except Exception:
        pass
    db.session.delete(doc)
    db.session.commit()
    flash('تم حذف المستند', 'success')
    return redirect(request.referrer)

@app.route('/clients/<int:client_id>/upload', methods=['POST'])
@login_required
def upload_client_document(client_id):
    if 'document' not in request.files:
        flash('لم يتم اختيار ملف!', 'danger')
        return redirect(request.referrer)
    file = request.files['document']
    if file.filename == '':
        flash('لم يتم اختيار ملف!', 'danger')
        return redirect(request.referrer)
    if file and allowed_file(file.filename):
        filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{file.filename}"
        file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
        doc = ClientDocument(filename=filename, upload_date=datetime.now(), client_id=client_id)
        db.session.add(doc)
        db.session.commit()
        flash('تم رفع المستند للعميل بنجاح', 'success')
    else:
        flash('نوع الملف غير مدعوم!', 'danger')
    return redirect(request.referrer)

@app.route('/clients/<int:client_id>/documents/<int:doc_id>/delete')
@login_required
def delete_client_document(client_id, doc_id):
    doc = ClientDocument.query.get_or_404(doc_id)
    if doc.client_id != client_id:
        flash('المستند غير مرتبط بهذا العميل', 'danger')
        return redirect(request.referrer)
    try:
        os.remove(os.path.join(app.config['UPLOAD_FOLDER'], doc.filename))
    except Exception:
        pass
    db.session.delete(doc)
    db.session.commit()
    flash('تم حذف مستند العميل', 'success')
    return redirect(request.referrer)

# ------------------ إدارة الديون ------------------
@app.route('/debts')
@login_required
def debts_list():
    debts = Debt.query.order_by(Debt.due_date.asc()).all()
    # حساب الإجماليات
    total_debt = sum(debt.amount for debt in debts)
    total_paid = sum(debt.paid_amount for debt in debts)
    total_remaining = sum(debt.remaining_amount for debt in debts)

    # إحصائيات حسب الحالة
    pending_debts = [d for d in debts if d.status == 'مستحق']
    overdue_debts = [d for d in debts if d.status == 'متأخر']
    paid_debts = [d for d in debts if d.status == 'مسدد']

    return render_template('debts/list.html',
                         debts=debts,
                         total_debt=total_debt,
                         total_paid=total_paid,
                         total_remaining=total_remaining,
                         pending_count=len(pending_debts),
                         overdue_count=len(overdue_debts),
                         paid_count=len(paid_debts))

@app.route('/debts/add', methods=['GET', 'POST'])
@login_required
def add_debt():
    if request.method == 'POST':
        try:
            creditor_name = request.form['creditor_name']
            amount = float(request.form['amount'])
            debt_type = request.form['debt_type']
            description = request.form.get('description', '')
            due_date_str = request.form.get('due_date')
            priority = request.form.get('priority', 'متوسطة')
            currency = request.form.get('currency', 'شيكل')
            case_id = request.form.get('case_id') if request.form.get('case_id') else None
            client_id = request.form.get('client_id') if request.form.get('client_id') else None

            due_date = datetime.strptime(due_date_str, '%Y-%m-%d') if due_date_str else None

            debt = Debt(
                creditor_name=creditor_name,
                amount=amount,
                debt_type=debt_type,
                description=description,
                due_date=due_date,
                priority=priority,
                currency=currency,
                case_id=case_id,
                client_id=client_id
            )
            debt.calculate_remaining()
            debt.update_status()

            db.session.add(debt)
            db.session.commit()

            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': True, 'message': 'تمت إضافة الدين بنجاح'})

            flash('تمت إضافة الدين بنجاح', 'success')
            return redirect(url_for('debts_list'))

        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
            flash(f'حدث خطأ أثناء إضافة الدين: {str(e)}', 'danger')
            return redirect(url_for('debts_list'))

    cases = Case.query.all()
    clients = Client.query.all()
    return render_template('debts/add.html', cases=cases, clients=clients)

@app.route('/debts/<int:debt_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_debt(debt_id):
    debt = Debt.query.get_or_404(debt_id)

    if request.method == 'POST':
        debt.creditor_name = request.form['creditor_name']
        debt.amount = float(request.form['amount'])
        debt.debt_type = request.form['debt_type']
        debt.description = request.form.get('description', '')
        due_date_str = request.form.get('due_date')
        debt.priority = request.form.get('priority', 'متوسطة')
        debt.currency = request.form.get('currency', 'شيكل')
        debt.case_id = request.form.get('case_id') if request.form.get('case_id') else None
        debt.client_id = request.form.get('client_id') if request.form.get('client_id') else None

        debt.due_date = datetime.strptime(due_date_str, '%Y-%m-%d') if due_date_str else None

        debt.calculate_remaining()
        debt.update_status()

        db.session.commit()
        flash('تم تحديث الدين بنجاح', 'success')
        return redirect(url_for('debts_list'))

    cases = Case.query.all()
    clients = Client.query.all()
    return render_template('debts/edit.html', debt=debt, cases=cases, clients=clients)

@app.route('/debts/<int:debt_id>/pay', methods=['GET', 'POST'])
@login_required
def pay_debt(debt_id):
    debt = Debt.query.get_or_404(debt_id)

    if request.method == 'POST':
        payment_amount = float(request.form['amount'])
        payment_method = request.form.get('payment_method', '')
        notes = request.form.get('notes', '')

        if payment_amount > debt.remaining_amount:
            flash('مبلغ الدفعة أكبر من المبلغ المتبقي', 'danger')
            return redirect(url_for('pay_debt', debt_id=debt_id))

        # إنشاء سجل دفعة
        payment = DebtPayment(
            debt_id=debt.id,
            amount=payment_amount,
            payment_method=payment_method,
            notes=notes,
            currency=debt.currency
        )

        # تحديث الدين
        debt.paid_amount += payment_amount
        debt.calculate_remaining()
        debt.update_status()

        # إنشاء معاملة مالية
        transaction = FinancialTransaction(
            amount=payment_amount,
            date=datetime.now(),
            type='سداد دين',
            description=f'سداد دين: {debt.creditor_name} - {debt.debt_type}',
            currency=debt.currency
        )

        db.session.add(payment)
        db.session.add(transaction)
        db.session.commit()

        flash('تم تسجيل الدفعة بنجاح', 'success')
        return redirect(url_for('debts_list'))

    return render_template('debts/pay.html', debt=debt)

@app.route('/debts/<int:debt_id>/delete', methods=['POST'])
@login_required
def delete_debt(debt_id):
    debt = Debt.query.get_or_404(debt_id)

    # حذف جميع الدفعات المرتبطة
    DebtPayment.query.filter_by(debt_id=debt.id).delete()

    db.session.delete(debt)
    db.session.commit()

    flash('تم حذف الدين بنجاح', 'success')
    return redirect(url_for('debts_list'))

# تم حذف routes عقود الإيجار - سيتم إعادة بناؤها

# ------------------ النوافذ المنبثقة للإضافة السريعة ------------------
@app.route('/modal/add_client')
@login_required
def modal_add_client():
    return render_template('modals/add_client.html')

@app.route('/modal/add_case')
@login_required
def modal_add_case():
    clients = Client.query.all()
    return render_template('modals/add_case.html', clients=clients)

# تم حذف modal_add_property - سيتم إعادة بناؤه

@app.route('/modal/add_debt')
@login_required
def modal_add_debt():
    from datetime import date
    today = date.today().strftime('%Y-%m-%d')
    return render_template('modals/add_debt.html', today=today)

# تم حذف modal_add_tenant - سيتم إعادة بناؤه

@app.route('/modal/add_expense')
@login_required
def modal_add_expense():
    from datetime import date
    today = date.today().strftime('%Y-%m-%d')
    return render_template('modals/add_expense.html', today=today)

@app.route('/modal/add_rental_income')
@login_required
def modal_add_rental_income():
    from datetime import date
    today = date.today().strftime('%Y-%m-%d')
    current_year = date.today().year
    properties = Property.query.all()
    tenants = Tenant.query.all()
    return render_template('modals/add_rental_income.html',
                         today=today,
                         current_year=current_year,
                         properties=properties,
                         tenants=tenants)

@app.route('/modal/add_financial_transaction')
@login_required
def modal_add_financial_transaction():
    from datetime import date
    today = date.today().strftime('%Y-%m-%d')
    clients = Client.query.all()
    cases = Case.query.all()
    return render_template('modals/add_financial_transaction.html', today=today, clients=clients, cases=cases)

# تم حذف modal_add_lease - سيتم إعادة بناؤه

# ==================== روتات الطباعة ====================

@app.route('/print/financial_report')
@login_required
def print_financial_report():
    """طباعة التقرير المالي"""
    from datetime import date, datetime
    from sqlalchemy import extract, and_
    from .models import FinancialTransaction, Case, Fee, Debt

    # الحصول على المعاملات
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    report_type = request.args.get('type', 'شامل')

    if not start_date or not end_date:
        # افتراضي: آخر 3 أشهر
        end_date = date.today()
        start_date = date(end_date.year, max(1, end_date.month - 2), 1)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # جمع البيانات المالية من FinancialTransaction
    transactions = FinancialTransaction.query.filter(
        and_(FinancialTransaction.date >= start_date, FinancialTransaction.date <= end_date)
    ).all()

    # إيرادات القضايا
    cases_income = Case.query.filter(
        and_(Case.case_date >= start_date, Case.case_date <= end_date)
    ).all()

    # الأتعاب
    fees = Fee.query.filter(
        and_(Fee.due_date >= start_date, Fee.due_date <= end_date)
    ).all()

    # الديون
    debts = Debt.query.filter(
        Debt.status != 'مدفوع'
    ).all()

    # حساب المجاميع
    income_types = ['قبض', 'دفعة', 'أتعاب', 'تحصيل إيجار']
    expense_types = ['صرف', 'مصاريف مكتب', 'رسوم']

    total_income = sum([t.amount for t in transactions if t.type in income_types])
    total_expenses = sum([t.amount for t in transactions if t.type in expense_types])
    net_profit = total_income - total_expenses
    total_debts = sum([debt.amount - (debt.paid_amount or 0) for debt in debts])

    # إعداد البيانات للقالب
    cases_data = []
    for case in cases_income:
        # حساب المدفوع من المعاملات المالية
        paid_amount = sum([t.amount for t in transactions if t.case_id == case.id and t.type in income_types])
        cases_data.append({
            'case_number': case.case_number,
            'client_name': case.client.name if case.client else 'غير محدد',
            'case_type': case.case_type,
            'agreed_fees': case.agreed_fees or 0,
            'paid_amount': paid_amount,
            'remaining_amount': (case.agreed_fees or 0) - paid_amount,
            'payment_status': 'مدفوع' if (case.agreed_fees or 0) <= paid_amount else 'غير مكتمل'
        })

    # بيانات المعاملات المالية
    transactions_data = []
    for transaction in transactions:
        transactions_data.append({
            'date': transaction.date.strftime('%Y-%m-%d') if transaction.date else '',
            'type': transaction.type,
            'amount': transaction.amount or 0,
            'currency': transaction.currency or 'شيكل',
            'description': transaction.description or '',
            'client_name': transaction.client.name if transaction.client else 'غير محدد',
            'case_title': transaction.case.title if transaction.case else 'غير محدد'
        })

    # بيانات الأتعاب
    fees_data = []
    for fee in fees:
        fees_data.append({
            'case_number': fee.case.case_number if fee.case else 'غير محدد',
            'client_name': fee.case.client.name if fee.case and fee.case.client else 'غير محدد',
            'amount': fee.amount or 0,
            'currency': fee.currency or 'شيكل',
            'due_date': fee.due_date.strftime('%Y-%m-%d') if fee.due_date else '',
            'status': fee.status or 'غير محدد'
        })

    debt_data = []
    for debt in debts:
        debt_data.append({
            'creditor_name': debt.creditor_name,
            'description': debt.description,
            'due_date': debt.due_date.strftime('%Y-%m-%d') if debt.due_date else '',
            'amount': debt.amount or 0,
            'paid_amount': debt.paid_amount or 0,
            'remaining_amount': (debt.amount or 0) - (debt.paid_amount or 0),
            'status': debt.status
        })

    # تحليل المعاملات حسب النوع
    transaction_categories = {}
    for transaction in transactions:
        category = transaction.type or 'غير مصنف'
        if category not in transaction_categories:
            transaction_categories[category] = {'count': 0, 'total': 0}
        transaction_categories[category]['count'] += 1
        transaction_categories[category]['total'] += transaction.amount or 0

    transaction_categories_data = []
    for category, data in transaction_categories.items():
        total_amount = total_income + total_expenses
        percentage = (data['total'] / total_amount * 100) if total_amount > 0 else 0
        transaction_categories_data.append({
            'name': category,
            'count': data['count'],
            'total': data['total'],
            'percentage': round(percentage, 2)
        })

    return render_template('print/financial_report.html',
                         report_title=f'التقرير المالي من {start_date} إلى {end_date}',
                         report_type=report_type,
                         period=f'من {start_date} إلى {end_date}',
                         report_date=date.today().strftime('%Y-%m-%d'),
                         report_number=f'FR-{datetime.now().strftime("%Y%m%d%H%M")}',
                         current_date=date.today().strftime('%Y-%m-%d'),
                         total_income=total_income,
                         total_expenses=total_expenses,
                         net_profit=net_profit,
                         total_debts=total_debts,
                         cases_income=cases_data,
                         cases_total_fees=sum([case['agreed_fees'] for case in cases_data]),
                         cases_total_paid=sum([case['paid_amount'] for case in cases_data]),
                         cases_total_remaining=sum([case['remaining_amount'] for case in cases_data]),
                         transactions=transactions_data,
                         fees=fees_data,
                         fees_total=sum([fee['amount'] for fee in fees_data]),
                         transaction_categories=transaction_categories_data,
                         debts=debt_data,
                         debts_total=total_debts,
                         expense_ratio=round((total_expenses / total_income * 100) if total_income > 0 else 0, 2),
                         monthly_avg_income=round(total_income / 3, 2))

@app.route('/print/contract/<int:lease_id>')
@login_required
def print_contract(lease_id):
    """طباعة عقد الإيجار"""
    from datetime import date
    lease = Lease.query.get_or_404(lease_id)

    # إعداد بيانات العقد
    first_party = {
        'name': 'مكتب المحامي سامح',
        'id_number': '123456789',
        'address': 'فلسطين - غزة - شارع الجلاء',
        'phone': '+970-59-123-4567',
        'email': '<EMAIL>'
    }

    second_party = {
        'name': lease.tenant.name if lease.tenant else 'غير محدد',
        'id_number': lease.tenant.id_number if lease.tenant else '',
        'address': lease.tenant.address if lease.tenant else '',
        'phone': lease.tenant.phone if lease.tenant else '',
        'email': lease.tenant.email if lease.tenant else ''
    }

    # جدول الدفعات
    payment_schedule = []
    if lease.start_date and lease.end_date:
        current_date = lease.start_date
        month_count = 1
        while current_date <= lease.end_date:
            payment_schedule.append({
                'month': f'الشهر {month_count}',
                'due_date': current_date.strftime('%Y-%m-%d'),
                'amount': lease.monthly_rent or 0,
                'status': 'غير مدفوع',
                'paid_date': None,
                'notes': ''
            })
            # الانتقال للشهر التالي
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)
            month_count += 1

    return render_template('print/contract.html',
                         contract_type='إيجار',
                         contract_number=f'LEASE-{lease.id}',
                         contract_date=lease.start_date.strftime('%Y-%m-%d') if lease.start_date else '',
                         contract_duration=f'{lease.duration_months} شهر' if lease.duration_months else '',
                         contract_status=lease.status,
                         current_date=date.today().strftime('%Y-%m-%d'),
                         first_party=first_party,
                         second_party=second_party,
                         property=lease.property,
                         lease=lease,
                         payment_schedule=payment_schedule,
                         contract={
                             'start_date': lease.start_date.strftime('%Y-%m-%d') if lease.start_date else '',
                             'end_date': lease.end_date.strftime('%Y-%m-%d') if lease.end_date else '',
                             'clause_1': f'يتفق الطرف الأول على تأجير العقار المذكور للطرف الثاني لمدة {lease.duration_months} شهر.',
                             'clause_2': f'تبدأ مدة الإيجار من {lease.start_date} وتنتهي في {lease.end_date}.',
                             'clause_3': f'يلتزم المستأجر بدفع مبلغ {lease.monthly_rent} شيكل شهرياً.',
                             'clause_4': 'يلتزم المستأجر بالمحافظة على العقار وعدم إحداث أي تغييرات بدون موافقة المؤجر.',
                             'clause_5': 'يمكن إنهاء العقد بإشعار مسبق 30 يوماً من أي من الطرفين.',
                             'clause_6': 'أي نزاع ينشأ عن هذا العقد يحل عن طريق المحاكم المختصة في غزة.',
                             'additional_terms': lease.terms or ''
                         })

@app.route('/print/invoice/<int:case_id>')
@login_required
def print_invoice(case_id):
    """طباعة فاتورة القضية"""
    from datetime import date, timedelta
    case = Case.query.get_or_404(case_id)

    # إعداد بيانات الفاتورة
    invoice_data = {
        'number': f'INV-{case.id}-{datetime.now().strftime("%Y%m%d")}',
        'type': 'أتعاب قانونية',
        'issue_date': date.today().strftime('%Y-%m-%d'),
        'due_date': (date.today() + timedelta(days=30)).strftime('%Y-%m-%d'),
        'status': 'مدفوعة' if (case.paid_amount or 0) >= (case.agreed_fees or 0) else 'غير مدفوعة',
        'payment_method': 'نقداً',
        'payment_terms': 'الدفع خلال 30 يوماً',
        'currency': 'شيكل',
        'reference_number': case.case_number,
        'notes': f'أتعاب قانونية للقضية رقم {case.case_number}',
        'payment_days': 30,
        'late_fee_rate': 2,
        'validity_days': 90
    }

    # بنود الفاتورة
    invoice_items = []

    # الأتعاب الأساسية
    if case.agreed_fees:
        invoice_items.append({
            'description': f'أتعاب قانونية - {case.case_type}',
            'details': f'القضية رقم: {case.case_number}',
            'quantity': 1,
            'unit_price': case.agreed_fees,
            'discount': 0,
            'total': case.agreed_fees
        })

    # رسوم المحكمة
    if case.court_fees:
        invoice_items.append({
            'description': 'رسوم المحكمة',
            'details': 'رسوم إجراءات المحكمة والطوابع',
            'quantity': 1,
            'unit_price': case.court_fees,
            'discount': 0,
            'total': case.court_fees
        })

    # حساب المجاميع
    subtotal = sum([item['total'] for item in invoice_items])
    discount_amount = 0
    tax_amount = 0
    additional_fees = 0
    total_amount = subtotal - discount_amount + tax_amount + additional_fees
    paid_amount = case.paid_amount or 0
    remaining_amount = total_amount - paid_amount

    invoice_data.update({
        'items': invoice_items,
        'subtotal': subtotal,
        'discount_amount': discount_amount,
        'discount_percentage': 0,
        'tax_amount': tax_amount,
        'tax_rate': 0,
        'additional_fees': additional_fees,
        'total_amount': total_amount,
        'paid_amount': paid_amount,
        'remaining_amount': remaining_amount,
        'amount_in_words': number_to_words(total_amount)
    })

    # سجل المدفوعات
    payments = []
    if paid_amount > 0:
        payments.append({
            'date': case.case_date.strftime('%Y-%m-%d') if case.case_date else date.today().strftime('%Y-%m-%d'),
            'amount': paid_amount,
            'method': 'نقداً',
            'reference': case.case_number,
            'notes': 'دفعة أولى'
        })

    invoice_data['payments'] = payments

    return render_template('print/invoice.html',
                         invoice=invoice_data,
                         client=case.client,
                         current_date=date.today().strftime('%Y-%m-%d'))

def number_to_words(number):
    """تحويل الرقم إلى كلمات باللغة العربية"""
    if number == 0:
        return "صفر شيكل"

    ones = ["", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة"]
    tens = ["", "", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون"]
    teens = ["عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر",
             "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر"]

    def convert_hundreds(n):
        result = ""
        if n >= 100:
            if n // 100 == 1:
                result += "مائة "
            elif n // 100 == 2:
                result += "مائتان "
            else:
                result += ones[n // 100] + " مائة "
            n %= 100

        if n >= 20:
            result += tens[n // 10] + " "
            if n % 10 != 0:
                result += ones[n % 10] + " "
        elif n >= 10:
            result += teens[n - 10] + " "
        elif n > 0:
            result += ones[n] + " "

        return result.strip()

    if number < 1000:
        return convert_hundreds(int(number)) + " شيكل"
    elif number < 1000000:
        thousands = number // 1000
        remainder = number % 1000
        result = convert_hundreds(int(thousands)) + " ألف"
        if remainder > 0:
            result += " و" + convert_hundreds(int(remainder))
        return result + " شيكل"
    else:
        return f"{number:,.0f} شيكل"

# ==================== إدارة الأتعاب الشاملة ====================

@app.route('/fees')
@login_required
def fees_list():
    """عرض قائمة الأتعاب مع الفلترة والبحث"""
    page = request.args.get('page', 1, type=int)
    per_page = 12

    # بناء الاستعلام الأساسي
    query = Fee.query

    # تطبيق الفلاتر
    search = request.args.get('search', '').strip()
    if search:
        query = query.filter(
            db.or_(
                Fee.fee_number.contains(search),
                Fee.description.contains(search),
                Fee.fee_type.contains(search),
                Fee.client.has(Client.name.contains(search))
            )
        )

    fee_type = request.args.get('fee_type', '').strip()
    if fee_type:
        query = query.filter(Fee.fee_type == fee_type)

    payment_status = request.args.get('payment_status', '').strip()
    if payment_status:
        query = query.filter(Fee.payment_status == payment_status)

    date_from = request.args.get('date_from', '').strip()
    if date_from:
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
        query = query.filter(Fee.service_date >= date_from_obj)

    date_to = request.args.get('date_to', '').strip()
    if date_to:
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
        query = query.filter(Fee.service_date <= date_to_obj)

    # ترتيب النتائج
    query = query.order_by(Fee.created_date.desc())

    # تطبيق التصفح
    fees = query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    # حساب الإحصائيات
    all_fees = Fee.query.all()
    total_fees = sum(fee.total_amount for fee in all_fees)
    total_paid = sum(fee.paid_amount for fee in all_fees)
    total_remaining = sum(fee.remaining_amount for fee in all_fees)
    overdue_count = len([fee for fee in all_fees if fee.is_overdue()])

    return render_template('fees/list.html',
                         fees=fees,
                         total_fees=total_fees,
                         total_paid=total_paid,
                         total_remaining=total_remaining,
                         overdue_count=overdue_count)

@app.route('/modal/add_fee', methods=['GET', 'POST'])
@login_required
def modal_add_fee():
    """نموذج إضافة أتعاب جديدة"""
    if request.method == 'POST':
        try:
            # استخراج البيانات من النموذج
            client_id = request.form['client_id']
            case_id = request.form.get('case_id') if request.form.get('case_id') else None
            fee_type = request.form['fee_type']
            service_category = request.form['service_category']
            description = request.form['description']
            service_date = datetime.strptime(request.form['service_date'], '%Y-%m-%d')
            due_date = datetime.strptime(request.form['due_date'], '%Y-%m-%d')

            # المبالغ المالية
            base_amount = float(request.form['base_amount'])
            additional_fees = float(request.form.get('additional_fees', 0))
            discount_amount = float(request.form.get('discount_amount', 0))
            tax_amount = float(request.form.get('tax_amount', 0))

            # معلومات إضافية
            priority = request.form.get('priority', 'متوسطة')
            currency = request.form.get('currency', 'شيكل')
            payment_terms = request.form.get('payment_terms', 'خلال 30 يوم')
            is_recurring = 'is_recurring' in request.form
            recurring_period = request.form.get('recurring_period') if is_recurring else None
            notes = request.form.get('notes', '')

            # إنشاء الأتعاب
            fee = Fee(
                client_id=client_id,
                case_id=case_id,
                fee_type=fee_type,
                service_category=service_category,
                description=description,
                service_date=service_date,
                due_date=due_date,
                base_amount=base_amount,
                additional_fees=additional_fees,
                discount_amount=discount_amount,
                tax_amount=tax_amount,
                priority=priority,
                currency=currency,
                payment_terms=payment_terms,
                is_recurring=is_recurring,
                recurring_period=recurring_period,
                notes=notes
            )

            db.session.add(fee)
            db.session.commit()

            flash('تم إضافة الأتعاب بنجاح', 'success')
            return redirect(url_for('fees_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة الأتعاب: {str(e)}', 'danger')
            return redirect(url_for('fees_list'))

    # GET request - عرض النموذج
    from datetime import date
    today = date.today().strftime('%Y-%m-%d')
    clients = Client.query.all()
    cases = Case.query.all()

    return render_template('fees/add_modal.html',
                         clients=clients,
                         cases=cases,
                         today=today)

@app.route('/modal/add_fee_payment/<int:fee_id>', methods=['GET', 'POST'])
@login_required
def modal_add_fee_payment(fee_id):
    """نموذج إضافة دفعة للأتعاب"""
    fee = Fee.query.get_or_404(fee_id)

    if request.method == 'POST':
        try:
            amount = float(request.form['amount'])
            payment_date = datetime.strptime(request.form['payment_date'], '%Y-%m-%d')
            payment_method = request.form['payment_method']
            reference = request.form.get('reference', '')
            notes = request.form.get('notes', '')

            # التحقق من صحة المبلغ
            if amount <= 0:
                flash('مبلغ الدفعة يجب أن يكون أكبر من صفر', 'danger')
                return redirect(url_for('fees_list'))

            if amount > fee.remaining_amount:
                flash('مبلغ الدفعة أكبر من المبلغ المتبقي', 'danger')
                return redirect(url_for('fees_list'))

            # إضافة الدفعة
            payment = fee.add_payment(
                amount=amount,
                payment_method=payment_method,
                reference=reference,
                notes=notes
            )
            payment.payment_date = payment_date

            # إنشاء معاملة مالية
            transaction = FinancialTransaction(
                amount=amount,
                date=payment_date,
                type='تحصيل أتعاب',
                description=f'تحصيل أتعاب: {fee.fee_number} - {fee.client.name}',
                currency=fee.currency
            )

            db.session.add(transaction)
            db.session.commit()

            flash('تم تسجيل الدفعة بنجاح', 'success')
            return redirect(url_for('fees_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تسجيل الدفعة: {str(e)}', 'danger')
            return redirect(url_for('fees_list'))

    # GET request - عرض النموذج
    from datetime import date
    today = date.today().strftime('%Y-%m-%d')

    return render_template('fees/add_payment_modal.html',
                         fee=fee,
                         today=today)

@app.route('/modal/view_fee/<int:fee_id>')
@login_required
def modal_view_fee(fee_id):
    """عرض تفاصيل الأتعاب"""
    fee = Fee.query.get_or_404(fee_id)
    payments = fee.payments.order_by(FeePayment.payment_date.desc()).all()

    return render_template('fees/view_modal.html',
                         fee=fee,
                         payments=payments)

@app.route('/modal/edit_fee/<int:fee_id>', methods=['GET', 'POST'])
@login_required
def modal_edit_fee(fee_id):
    """نموذج تعديل الأتعاب"""
    fee = Fee.query.get_or_404(fee_id)

    if request.method == 'POST':
        try:
            # تحديث البيانات
            fee.client_id = request.form['client_id']
            fee.case_id = request.form.get('case_id') if request.form.get('case_id') else None
            fee.fee_type = request.form['fee_type']
            fee.service_category = request.form['service_category']
            fee.description = request.form['description']
            fee.service_date = datetime.strptime(request.form['service_date'], '%Y-%m-%d')
            fee.due_date = datetime.strptime(request.form['due_date'], '%Y-%m-%d')

            # المبالغ المالية
            fee.base_amount = float(request.form['base_amount'])
            fee.additional_fees = float(request.form.get('additional_fees', 0))
            fee.discount_amount = float(request.form.get('discount_amount', 0))
            fee.tax_amount = float(request.form.get('tax_amount', 0))

            # معلومات إضافية
            fee.priority = request.form.get('priority', 'متوسطة')
            fee.currency = request.form.get('currency', 'شيكل')
            fee.payment_terms = request.form.get('payment_terms', 'خلال 30 يوم')
            fee.is_recurring = 'is_recurring' in request.form
            fee.recurring_period = request.form.get('recurring_period') if fee.is_recurring else None
            fee.notes = request.form.get('notes', '')

            # إعادة حساب المجاميع
            fee.calculate_totals()

            db.session.commit()
            flash('تم تحديث الأتعاب بنجاح', 'success')
            return redirect(url_for('fees_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الأتعاب: {str(e)}', 'danger')
            return redirect(url_for('fees_list'))

    # GET request - عرض النموذج
    clients = Client.query.all()
    cases = Case.query.all()

    return render_template('fees/edit_modal.html',
                         fee=fee,
                         clients=clients,
                         cases=cases)

@app.route('/fees/<int:fee_id>/delete', methods=['POST'])
@login_required
def delete_fee(fee_id):
    """حذف الأتعاب"""
    fee = Fee.query.get_or_404(fee_id)

    try:
        # حذف جميع الدفعات المرتبطة
        FeePayment.query.filter_by(fee_id=fee.id).delete()

        db.session.delete(fee)
        db.session.commit()

        flash('تم حذف الأتعاب بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الأتعاب: {str(e)}', 'danger')

    return redirect(url_for('fees_list'))

@app.route('/fees/report')
@login_required
def fees_report():
    """تقرير الأتعاب الشامل"""
    try:
        from .models import Fee, FeePayment, Case, Client
        from datetime import date, datetime, timedelta
        from sqlalchemy import func, extract

        # جلب جميع الأتعاب
        fees = Fee.query.order_by(Fee.due_date.desc()).all()

        # حساب الإحصائيات العامة
        total_fees = sum(fee.total_amount for fee in fees)
        total_paid = sum(fee.paid_amount for fee in fees)
        total_remaining = sum(fee.remaining_amount for fee in fees)

        # الأتعاب المستحقة (متأخرة)
        today = date.today()
        overdue_fees = [fee for fee in fees if fee.due_date and fee.due_date < today and fee.remaining_amount > 0]
        overdue_amount = sum(fee.remaining_amount for fee in overdue_fees)

        # الأتعاب المدفوعة بالكامل
        paid_fees = [fee for fee in fees if fee.remaining_amount <= 0]
        paid_count = len(paid_fees)

        # الأتعاب المعلقة
        pending_fees = [fee for fee in fees if fee.remaining_amount > 0]
        pending_count = len(pending_fees)

        # تحليل الأتعاب حسب العملة
        currency_stats = {}
        for fee in fees:
            currency = fee.currency or 'شيكل'
            if currency not in currency_stats:
                currency_stats[currency] = {'total': 0, 'paid': 0, 'remaining': 0, 'count': 0}
            currency_stats[currency]['total'] += fee.total_amount
            currency_stats[currency]['paid'] += fee.paid_amount
            currency_stats[currency]['remaining'] += fee.remaining_amount
            currency_stats[currency]['count'] += 1

        # تحليل الأتعاب حسب الشهر (آخر 12 شهر)
        monthly_stats = {}
        for i in range(12):
            month_date = today.replace(day=1) - timedelta(days=30*i)
            month_key = month_date.strftime('%Y-%m')
            monthly_stats[month_key] = {'total': 0, 'paid': 0, 'count': 0}

        for fee in fees:
            if fee.due_date:
                month_key = fee.due_date.strftime('%Y-%m')
                if month_key in monthly_stats:
                    monthly_stats[month_key]['total'] += fee.total_amount
                    monthly_stats[month_key]['paid'] += fee.paid_amount
                    monthly_stats[month_key]['count'] += 1

        # أعلى العملاء من حيث الأتعاب
        client_stats = {}
        for fee in fees:
            if fee.client:
                client_name = fee.client.name
                if client_name not in client_stats:
                    client_stats[client_name] = {'total': 0, 'paid': 0, 'remaining': 0, 'count': 0}
                client_stats[client_name]['total'] += fee.total_amount
                client_stats[client_name]['paid'] += fee.paid_amount
                client_stats[client_name]['remaining'] += fee.remaining_amount
                client_stats[client_name]['count'] += 1

        # ترتيب العملاء حسب إجمالي الأتعاب
        top_clients = sorted(client_stats.items(), key=lambda x: x[1]['total'], reverse=True)[:10]

        return render_template('fees/report.html',
                             fees=fees,
                             total_fees=total_fees,
                             total_paid=total_paid,
                             total_remaining=total_remaining,
                             overdue_fees=overdue_fees,
                             overdue_amount=overdue_amount,
                             paid_count=paid_count,
                             pending_count=pending_count,
                             currency_stats=currency_stats,
                             monthly_stats=monthly_stats,
                             top_clients=top_clients,
                             today=today)

    except Exception as e:
        flash(f'حدث خطأ في تحميل تقرير الأتعاب: {str(e)}', 'danger')
        return redirect(url_for('fees_list'))

@app.route('/api/client/<int:client_id>/cases')
@login_required
def api_client_cases(client_id):
    """API للحصول على قضايا العميل"""
    cases = Case.query.filter_by(client_id=client_id).all()
    cases_data = []
    for case in cases:
        cases_data.append({
            'id': case.id,
            'case_number': case.case_number,
            'title': case.title or case.case_type
        })
    return {'cases': cases_data}


# ==================== Calendar and Task Management Routes ====================

@app.route('/calendar')
@login_required
def calendar():
    """عرض صفحة التقويم والمهام المحسن"""
    try:
        from datetime import datetime, timedelta

        # جلب البيانات الأساسية
        clients = Client.query.all()
        cases = Case.query.all()
        users = User.query.all()

        # جلب الأحداث والمهام مع معالجة الأخطاء
        try:
            events = Event.query.order_by(Event.start_datetime).all() if 'Event' in globals() else []
        except:
            events = []

        try:
            tasks = Task.query.order_by(Task.due_date).all() if 'Task' in globals() else []
        except:
            tasks = []

        # الأحداث القادمة (الأسبوع القادم)
        today = datetime.now()
        next_week = today + timedelta(days=7)
        try:
            upcoming_events = Event.query.filter(
                Event.start_datetime >= today,
                Event.start_datetime <= next_week
            ).order_by(Event.start_datetime).limit(5).all() if 'Event' in globals() else []
        except:
            upcoming_events = []

        # حساب الإحصائيات مع معالجة الأخطاء
        try:
            total_events = Event.query.count() if 'Event' in globals() else 0
        except:
            total_events = 0

        try:
            pending_tasks = Task.query.filter(Task.status.in_(['معلقة', 'قيد التنفيذ'])).count() if 'Task' in globals() else 0
        except:
            pending_tasks = 0

        try:
            completed_tasks = Task.query.filter(Task.status == 'منجزة').count() if 'Task' in globals() else 0
        except:
            completed_tasks = 0

        try:
            overdue_tasks = Task.query.filter(Task.due_date < today, Task.status != 'منجزة').count() if 'Task' in globals() else 0
        except:
            overdue_tasks = 0

        try:
            today_events = Event.query.filter(
                Event.start_datetime >= today.replace(hour=0, minute=0, second=0),
                Event.start_datetime < today.replace(hour=23, minute=59, second=59)
            ).count() if 'Event' in globals() else 0
        except:
            today_events = 0

        try:
            this_week_tasks = Task.query.filter(
                Task.due_date >= today,
                Task.due_date <= next_week
            ).count() if 'Task' in globals() else 0
        except:
            this_week_tasks = 0

        stats = {
            'total_events': total_events,
            'pending_tasks': pending_tasks,
            'completed_tasks': completed_tasks,
            'overdue_tasks': overdue_tasks,
            'today_events': today_events,
            'this_week_tasks': this_week_tasks
        }

        return render_template('calendar/calendar.html',
                             clients=clients,
                             cases=cases,
                             users=users,
                             events=events,
                             tasks=tasks,
                             upcoming_events=upcoming_events,
                             stats=stats)
    except Exception as e:
        flash(f'خطأ في تحميل التقويم: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@app.route('/api/calendar/events', methods=['GET'])
@login_required
def get_events():
    """جلب جميع الأحداث"""
    try:
        events = Event.query.all()
        events_data = []

        for event in events:
            event_data = {
                'id': event.id,
                'title': event.title,
                'description': event.description,
                'start_datetime': event.start_datetime.isoformat() if event.start_datetime else None,
                'end_datetime': event.end_datetime.isoformat() if event.end_datetime else None,
                'location': event.location,
                'event_type': event.event_type,
                'priority': event.priority,
                'status': event.status,
                'color': event.color,
                'is_all_day': event.is_all_day,
                'is_recurring': event.is_recurring,
                'recurring_pattern': event.recurring_pattern,
                'client_id': event.client_id,
                'case_id': event.case_id,
                'created_by': event.created_by,
                'client_name': event.client.name if event.client else None,
                'case_title': event.case.title if event.case else None
            }
            events_data.append(event_data)

        return jsonify({'success': True, 'events': events_data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/calendar/events', methods=['POST'])
@login_required
def add_event():
    """إضافة حدث جديد"""
    try:
        data = request.get_json()

        # تحويل التواريخ
        start_datetime = datetime.fromisoformat(data['start_datetime'].replace('T', ' '))
        end_datetime = datetime.fromisoformat(data['end_datetime'].replace('T', ' '))

        event = Event(
            title=data['title'],
            description=data.get('description'),
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            location=data.get('location'),
            event_type=data['event_type'],
            priority=data.get('priority', 'medium'),
            status='scheduled',
            color=data.get('color', '#007bff'),
            is_all_day=data.get('is_all_day', False),
            is_recurring=data.get('is_recurring', False),
            client_id=data.get('client_id'),
            case_id=data.get('case_id'),
            created_by=current_user.id,
            created_date=datetime.now(),
            updated_date=datetime.now()
        )

        db.session.add(event)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم إضافة الحدث بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

# ==================== Enhanced Calendar Routes ====================

@app.route('/api/calendar/events/enhanced', methods=['POST'])
@login_required
def add_event_enhanced():
    """إضافة حدث جديد محسن"""
    try:
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()

        # التحقق من البيانات المطلوبة
        if not data.get('title'):
            return jsonify({'success': False, 'message': 'عنوان الحدث مطلوب'})

        if not data.get('start_date'):
            return jsonify({'success': False, 'message': 'تاريخ البداية مطلوب'})

        # تحويل التواريخ
        from datetime import datetime
        start_date = datetime.fromisoformat(data['start_date'])
        end_date = None
        if data.get('end_date'):
            end_date = datetime.fromisoformat(data['end_date'])

        event = Event(
            title=data['title'],
            description=data.get('description'),
            start_datetime=start_date,
            end_datetime=end_date,
            location=data.get('location'),
            event_type=data.get('event_type', 'موعد'),
            status=data.get('status', 'مجدول'),
            color=data.get('color', '#007bff'),
            is_all_day=data.get('all_day') == 'on',
            is_recurring=data.get('recurring') == 'on',
            created_by=current_user.id,
            client_id=int(data['client_id']) if data.get('client_id') else None,
            case_id=int(data['case_id']) if data.get('case_id') else None,
            created_date=datetime.now(),
            updated_date=datetime.now()
        )

        db.session.add(event)
        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم إضافة الحدث بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'خطأ في إضافة الحدث: {str(e)}'})

@app.route('/api/calendar/tasks/enhanced', methods=['POST'])
@login_required
def add_task_enhanced():
    """إضافة مهمة جديدة محسنة"""
    try:
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()

        # التحقق من البيانات المطلوبة
        if not data.get('title'):
            return jsonify({'success': False, 'message': 'عنوان المهمة مطلوب'})

        # تحويل التاريخ
        from datetime import datetime
        due_date = None
        if data.get('due_date'):
            due_date = datetime.fromisoformat(data['due_date'])

        task = Task(
            title=data['title'],
            description=data.get('description'),
            due_date=due_date,
            status=data.get('status', 'معلقة'),
            user_id=current_user.id,
            case_id=int(data['case_id']) if data.get('case_id') else None
        )

        db.session.add(task)
        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم إضافة المهمة بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'خطأ في إضافة المهمة: {str(e)}'})

@app.route('/api/calendar/tasks/<int:task_id>/progress', methods=['POST'])
@login_required
def update_task_progress(task_id):
    """تحديث تقدم المهمة"""
    try:
        task = Task.query.get_or_404(task_id)

        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()

        # تحديث الحالة
        if data.get('status'):
            task.status = data['status']

        # إذا تم إكمال المهمة
        if task.status == 'منجزة':
            from datetime import datetime
            # لا نحتاج completed_date في النموذج الحالي

        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم تحديث تقدم المهمة بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'خطأ في تحديث المهمة: {str(e)}'})

@app.route('/api/calendar/stats', methods=['GET'])
@login_required
def get_calendar_stats():
    """جلب إحصائيات التقويم"""
    try:
        from datetime import datetime, timedelta
        today = datetime.now()
        next_week = today + timedelta(days=7)

        # حساب الإحصائيات
        total_events = Event.query.filter_by(created_by=current_user.id).count()
        pending_tasks = Task.query.filter(
            Task.user_id == current_user.id,
            Task.status.in_(['معلقة', 'قيد التنفيذ'])
        ).count()
        completed_tasks = Task.query.filter(
            Task.user_id == current_user.id,
            Task.status == 'منجزة'
        ).count()
        overdue_tasks = Task.query.filter(
            Task.user_id == current_user.id,
            Task.due_date < today,
            Task.status != 'منجزة'
        ).count()
        today_events = Event.query.filter(
            Event.created_by == current_user.id,
            Event.start_datetime >= today.replace(hour=0, minute=0, second=0),
            Event.start_datetime < today.replace(hour=23, minute=59, second=59)
        ).count()
        this_week_tasks = Task.query.filter(
            Task.user_id == current_user.id,
            Task.due_date >= today,
            Task.due_date <= next_week
        ).count()

        stats = {
            'total_events': total_events,
            'pending_tasks': pending_tasks,
            'completed_tasks': completed_tasks,
            'overdue_tasks': overdue_tasks,
            'today_events': today_events,
            'this_week_tasks': this_week_tasks
        }

        return jsonify({'success': True, 'stats': stats})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/calendar/tasks', methods=['GET'])
@login_required
def get_tasks():
    """جلب جميع المهام"""
    try:
        tasks = TaskItem.query.all()
        tasks_data = []

        for task in tasks:
            task_data = {
                'id': task.id,
                'title': task.title,
                'description': task.description,
                'due_date': task.due_date.isoformat() if task.due_date else None,
                'completed_date': task.completed_date.isoformat() if task.completed_date else None,
                'priority': task.priority,
                'status': task.status,
                'category': task.category,
                'estimated_hours': task.estimated_hours,
                'actual_hours': task.actual_hours,
                'completion_percentage': task.completion_percentage or 0,
                'client_id': task.client_id,
                'case_id': task.case_id,
                'assigned_to': task.assigned_to,
                'created_by': task.created_by,
                'parent_task_id': task.parent_task_id,
                'client_name': task.client.name if task.client else None,
                'case_title': task.case.title if task.case else None,
                'assigned_to_name': task.assigned_user.username if task.assigned_user else None
            }
            tasks_data.append(task_data)

        return jsonify({'success': True, 'tasks': tasks_data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/calendar/tasks', methods=['POST'])
@login_required
def add_calendar_task():
    """إضافة مهمة جديدة"""
    try:
        data = request.get_json()

        # تحويل تاريخ الاستحقاق إذا كان موجوداً
        due_date = None
        if data.get('due_date'):
            due_date = datetime.fromisoformat(data['due_date'].replace('T', ' '))

        task = TaskItem(
            title=data['title'],
            description=data.get('description'),
            due_date=due_date,
            priority=data.get('priority', 'medium'),
            status='not_started',
            category=data.get('category', 'general'),
            estimated_hours=data.get('estimated_hours'),
            completion_percentage=0,
            client_id=data.get('client_id'),
            case_id=data.get('case_id'),
            assigned_to=data.get('assigned_to', current_user.id),
            created_by=current_user.id,
            parent_task_id=data.get('parent_task_id'),
            created_date=datetime.now(),
            updated_date=datetime.now()
        )

        db.session.add(task)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم إضافة المهمة بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/calendar/tasks/<int:task_id>/update', methods=['POST'])
@login_required
def update_task(task_id):
    """تحديث مهمة"""
    try:
        task = TaskItem.query.get_or_404(task_id)
        data = request.get_json()

        # تحديث الحقول
        if 'completion_percentage' in data:
            task.completion_percentage = data['completion_percentage']
            if data['completion_percentage'] == 100:
                task.status = 'completed'
                task.completed_date = datetime.now()
            else:
                task.status = 'in_progress'
                task.completed_date = None

        if 'actual_hours' in data:
            task.actual_hours = data['actual_hours']

        task.updated_date = datetime.now()
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تحديث المهمة بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/calendar/reminders', methods=['GET'])
@login_required
def get_reminders():
    """جلب التذكيرات"""
    try:
        reminders = Reminder.query.filter_by(user_id=current_user.id, is_sent=False).all()
        reminders_data = []

        for reminder in reminders:
            reminder_data = {
                'id': reminder.id,
                'title': reminder.title,
                'message': reminder.message,
                'reminder_datetime': reminder.reminder_datetime.isoformat() if reminder.reminder_datetime else None,
                'reminder_type': reminder.reminder_type,
                'event_id': reminder.event_id,
                'task_id': reminder.task_id
            }
            reminders_data.append(reminder_data)

        return jsonify({'success': True, 'reminders': reminders_data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== Archive and Settings Routes ====================

@app.route('/archive')
@login_required
def archive():
    """صفحة الأرشيف والإعدادات"""
    return render_template('archive/archive.html')

@app.route('/settings/theme')
@login_required
def theme_settings():
    """صفحة إعدادات المظهر والثيم"""
    return render_template('settings/theme_settings.html')

@app.route('/api/theme/save', methods=['POST'])
@login_required
def save_theme_settings():
    """حفظ إعدادات المظهر"""
    try:
        data = request.get_json()
        theme = data.get('theme', 'light')
        font_size = data.get('font_size', 16)

        # حفظ الإعدادات في قاعدة البيانات
        from .settings_service import SettingsService
        settings_service = SettingsService()

        settings_service.set_setting('ui_theme', theme, 'string', current_user.id)
        settings_service.set_setting('ui_font_size', str(font_size), 'string', current_user.id)

        return jsonify({
            'success': True,
            'message': 'تم حفظ إعدادات المظهر بنجاح'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في حفظ الإعدادات: {str(e)}'
        }), 500

@app.route('/api/theme/load', methods=['GET'])
@login_required
def load_theme_settings():
    """تحميل إعدادات المظهر"""
    try:
        from .settings_service import SettingsService
        settings_service = SettingsService()

        theme = settings_service.get_setting('ui_theme', 'light')
        font_size = int(settings_service.get_setting('ui_font_size', '16'))

        return jsonify({
            'success': True,
            'theme': theme,
            'font_size': font_size
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في تحميل الإعدادات: {str(e)}'
        }), 500

# ==================== Advanced Search API Routes ====================

@app.route('/api/search', methods=['POST'])
@login_required
def advanced_search():
    """API للبحث المتقدم في جميع أقسام النظام"""
    try:
        data = request.get_json()
        section = data.get('section', 'all')
        search_term = data.get('term', '').strip()
        filters = data.get('filters', {})

        if not search_term:
            return jsonify({'success': False, 'error': 'يرجى إدخال كلمة البحث'}), 400

        results = []

        if section == 'all' or section == 'cases':
            case_results = search_cases(search_term, filters)
            results.extend([{**case, 'type': 'case'} for case in case_results])

        if section == 'all' or section == 'clients':
            client_results = search_clients(search_term, filters)
            results.extend([{**client, 'type': 'client'} for client in client_results])

        if section == 'all' or section == 'properties':
            property_results = search_properties(search_term, filters)
            results.extend([{**prop, 'type': 'property'} for prop in property_results])

        if section == 'all' or section == 'tenants':
            tenant_results = search_tenants(search_term, filters)
            results.extend([{**tenant, 'type': 'tenant'} for tenant in tenant_results])

        if section == 'all' or section == 'financial':
            financial_results = search_financial(search_term, filters)
            results.extend([{**fin, 'type': 'financial'} for fin in financial_results])

        if section == 'all' or section == 'documents':
            document_results = search_documents(search_term, filters)
            results.extend([{**doc, 'type': 'document'} for doc in document_results])

        if section == 'all' or section == 'calendar':
            calendar_results = search_calendar(search_term, filters)
            results.extend([{**cal, 'type': 'calendar'} for cal in calendar_results])

        return jsonify({
            'success': True,
            'results': results,
            'total': len(results)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/search/suggestions', methods=['POST'])
@login_required
def search_suggestions():
    """API لاقتراحات البحث"""
    try:
        data = request.get_json()
        search_term = data.get('term', '').strip()

        if len(search_term) < 2:
            return jsonify([])

        suggestions = []

        # اقتراحات من القضايا
        cases = Case.query.filter(
            Case.title.contains(search_term) |
            Case.case_number.contains(search_term)
        ).limit(5).all()

        for case in cases:
            suggestions.append({
                'text': case.title,
                'type': 'cases',
                'type_name': 'قضية',
                'id': case.id
            })

        # اقتراحات من العملاء
        clients = Client.query.filter(Client.name.contains(search_term)).limit(5).all()
        for client in clients:
            suggestions.append({
                'text': client.name,
                'type': 'clients',
                'type_name': 'عميل',
                'id': client.id
            })

        # اقتراحات من العقارات
        properties = Property.query.filter(
            Property.name.contains(search_term) |
            Property.address.contains(search_term)
        ).limit(5).all()

        for prop in properties:
            suggestions.append({
                'text': prop.name,
                'type': 'properties',
                'type_name': 'عقار',
                'id': prop.id
            })

        return jsonify(suggestions[:15])  # أقصى 15 اقتراح

    except Exception as e:
        return jsonify([])

@app.route('/api/search/export', methods=['POST'])
@login_required
def export_search_results():
    """API لتصدير نتائج البحث"""
    try:
        data = request.get_json()
        results = data.get('results', [])
        export_format = data.get('format', 'excel')

        if not results:
            return jsonify({'success': False, 'error': 'لا توجد نتائج للتصدير'}), 400

        if export_format == 'excel':
            return export_to_excel(results)
        elif export_format == 'pdf':
            return export_to_pdf(results)
        else:
            return jsonify({'success': False, 'error': 'تنسيق غير مدعوم'}), 400

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# ==================== Search Helper Functions ====================

def search_cases(search_term, filters):
    """البحث في القضايا"""
    query = Case.query

    # البحث النصي
    query = query.filter(
        Case.title.contains(search_term) |
        Case.case_number.contains(search_term) |
        Case.description.contains(search_term)
    )

    # تطبيق الفلاتر
    if filters.get('status'):
        query = query.filter(Case.status == filters['status'])

    if filters.get('caseType'):
        query = query.filter(Case.case_type == filters['caseType'])

    if filters.get('client'):
        query = query.filter(Case.client_id == filters['client'])

    if filters.get('dateFrom'):
        query = query.filter(Case.created_date >= filters['dateFrom'])

    if filters.get('dateTo'):
        query = query.filter(Case.created_date <= filters['dateTo'])

    cases = query.limit(50).all()

    results = []
    for case in cases:
        results.append({
            'id': case.id,
            'case_number': case.case_number,
            'title': case.title,
            'description': case.description,
            'status': case.status,
            'case_type': case.case_type,
            'client_name': case.client.name if case.client else None,
            'created_date': case.created_date.isoformat() if case.created_date else None
        })

    return results

def search_clients(search_term, filters):
    """البحث في العملاء"""
    query = Client.query

    # البحث النصي
    query = query.filter(
        Client.name.contains(search_term) |
        Client.phone.contains(search_term) |
        Client.email.contains(search_term) |
        Client.address.contains(search_term)
    )

    # تطبيق الفلاتر
    if filters.get('dateFrom'):
        query = query.filter(Client.created_date >= filters['dateFrom'])

    if filters.get('dateTo'):
        query = query.filter(Client.created_date <= filters['dateTo'])

    clients = query.limit(50).all()

    results = []
    for client in clients:
        results.append({
            'id': client.id,
            'name': client.name,
            'phone': client.phone,
            'email': client.email,
            'address': client.address,
            'cases_count': len(client.cases),
            'created_date': client.created_date.isoformat() if client.created_date else None
        })

    return results

def search_properties(search_term, filters):
    """البحث في العقارات"""
    query = Property.query

    # البحث النصي
    query = query.filter(
        Property.name.contains(search_term) |
        Property.address.contains(search_term) |
        Property.description.contains(search_term)
    )

    # تطبيق الفلاتر
    if filters.get('status'):
        query = query.filter(Property.status == filters['status'])

    if filters.get('amountMin'):
        query = query.filter(Property.value >= float(filters['amountMin']))

    if filters.get('amountMax'):
        query = query.filter(Property.value <= float(filters['amountMax']))

    if filters.get('dateFrom'):
        query = query.filter(Property.created_date >= filters['dateFrom'])

    if filters.get('dateTo'):
        query = query.filter(Property.created_date <= filters['dateTo'])

    properties = query.limit(50).all()

    results = []
    for prop in properties:
        results.append({
            'id': prop.id,
            'name': prop.name,
            'address': prop.address,
            'property_type': prop.property_type,
            'status': prop.status,
            'value': float(prop.value) if prop.value else 0,
            'description': prop.description,
            'created_date': prop.created_date.isoformat() if prop.created_date else None
        })

    return results

def search_tenants(search_term, filters):
    """البحث في المستأجرين"""
    query = Tenant.query

    # البحث النصي
    query = query.filter(
        Tenant.name.contains(search_term) |
        Tenant.phone.contains(search_term) |
        Tenant.email.contains(search_term)
    )

    # تطبيق الفلاتر
    if filters.get('status'):
        query = query.filter(Tenant.status == filters['status'])

    if filters.get('dateFrom'):
        query = query.filter(Tenant.created_date >= filters['dateFrom'])

    if filters.get('dateTo'):
        query = query.filter(Tenant.created_date <= filters['dateTo'])

    tenants = query.limit(50).all()

    results = []
    for tenant in tenants:
        results.append({
            'id': tenant.id,
            'name': tenant.name,
            'phone': tenant.phone,
            'email': tenant.email,
            'status': tenant.status,
            'property_name': tenant.property.name if tenant.property else None,
            'created_date': tenant.created_date.isoformat() if tenant.created_date else None
        })

    return results

def search_financial(search_term, filters):
    """البحث في السجلات المالية"""
    results = []

    # البحث في الرسوم
    fee_query = Fee.query.filter(
        Fee.description.contains(search_term) |
        Fee.fee_type.contains(search_term)
    )

    if filters.get('amountMin'):
        fee_query = fee_query.filter(Fee.amount >= float(filters['amountMin']))

    if filters.get('amountMax'):
        fee_query = fee_query.filter(Fee.amount <= float(filters['amountMax']))

    fees = fee_query.limit(25).all()
    for fee in fees:
        results.append({
            'id': fee.id,
            'type': 'رسوم',
            'description': fee.description,
            'amount': float(fee.amount),
            'date': fee.date.isoformat() if fee.date else None,
            'status': fee.status,
            'case_title': fee.case.title if fee.case else None
        })

    # البحث في الديون
    debt_query = Debt.query.filter(
        Debt.description.contains(search_term)
    )

    if filters.get('amountMin'):
        debt_query = debt_query.filter(Debt.amount >= float(filters['amountMin']))

    if filters.get('amountMax'):
        debt_query = debt_query.filter(Debt.amount <= float(filters['amountMax']))

    debts = debt_query.limit(25).all()
    for debt in debts:
        results.append({
            'id': debt.id,
            'type': 'دين',
            'description': debt.description,
            'amount': float(debt.amount),
            'date': debt.date.isoformat() if debt.date else None,
            'status': debt.status,
            'debtor_name': debt.debtor_name
        })

    return results[:50]

def search_documents(search_term, filters):
    """البحث في المستندات"""
    # هذه الوظيفة ستحتاج إلى تطوير نظام إدارة المستندات
    # حالياً سنعيد قائمة فارغة
    return []

def search_calendar(search_term, filters):
    """البحث في التقويم والمهام"""
    results = []

    # البحث في الأحداث
    try:
        event_query = Event.query.filter(
            Event.title.contains(search_term) |
            Event.description.contains(search_term)
        )

        if filters.get('dateFrom'):
            event_query = event_query.filter(Event.start_date >= filters['dateFrom'])

        if filters.get('dateTo'):
            event_query = event_query.filter(Event.end_date <= filters['dateTo'])

        events = event_query.limit(25).all()
        for event in events:
            results.append({
                'id': event.id,
                'type': 'حدث',
                'title': event.title,
                'description': event.description,
                'start_date': event.start_date.isoformat() if event.start_date else None,
                'end_date': event.end_date.isoformat() if event.end_date else None,
                'event_type': event.event_type
            })
    except:
        pass  # تجاهل الأخطاء إذا لم تكن الجداول موجودة

    # البحث في المهام
    try:
        task_query = Task.query.filter(
            Task.title.contains(search_term) |
            Task.description.contains(search_term)
        )

        if filters.get('priority'):
            task_query = task_query.filter(Task.priority == filters['priority'])

        if filters.get('status'):
            task_query = task_query.filter(Task.status == filters['status'])

        tasks = task_query.limit(25).all()
        for task in tasks:
            results.append({
                'id': task.id,
                'type': 'مهمة',
                'title': task.title,
                'description': task.description,
                'due_date': task.due_date.isoformat() if task.due_date else None,
                'priority': task.priority,
                'status': task.status
            })
    except:
        pass  # تجاهل الأخطاء إذا لم تكن الجداول موجودة

    return results[:50]

def export_to_excel(results):
    """تصدير النتائج إلى Excel"""
    try:
        import pandas as pd
        from io import BytesIO

        # تحويل النتائج إلى DataFrame
        df = pd.DataFrame(results)

        # إنشاء ملف Excel في الذاكرة
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='نتائج البحث', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'search_results_{datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    except ImportError:
        return jsonify({'success': False, 'error': 'مكتبة pandas غير متوفرة'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

def export_to_pdf(results):
    """تصدير النتائج إلى PDF"""
    try:
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.lib import colors
        from io import BytesIO

        output = BytesIO()
        doc = SimpleDocTemplate(output, pagesize=A4)

        # إنشاء المحتوى
        story = []
        styles = getSampleStyleSheet()

        # العنوان
        title = Paragraph("نتائج البحث", styles['Title'])
        story.append(title)

        # الجدول
        if results:
            # تحضير البيانات للجدول
            headers = list(results[0].keys())
            data = [headers]

            for result in results:
                row = [str(result.get(header, '')) for header in headers]
                data.append(row)

            table = Table(data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(table)

        doc.build(story)
        output.seek(0)

        return send_file(
            output,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f'search_results_{datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")}.pdf'
        )

    except ImportError:
        return jsonify({'success': False, 'error': 'مكتبة reportlab غير متوفرة'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/clients')
@login_required
def get_clients_api():
    """API لجلب قائمة العملاء للفلاتر"""
    try:
        clients = Client.query.all()
        client_list = []
        for client in clients:
            client_list.append({
                'id': client.id,
                'name': client.name
            })
        return jsonify(client_list)
    except Exception as e:
        return jsonify([])

# ==================== Advanced Search Template Route ====================

@app.route('/advanced-search')
@login_required
def advanced_search_page():
    """صفحة البحث المتقدم"""
    return render_template('search/advanced_search.html')

@app.route('/api/search/stats')
@login_required
def search_stats():
    """API لجلب إحصائيات البحث"""
    try:
        stats = {
            'success': True,
            'cases': Case.query.count(),
            'clients': Client.query.count(),
            'properties': Property.query.count(),
            'financial': Fee.query.count() + Debt.query.count()
        }
        return jsonify(stats)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== Reports System APIs ====================

@app.route('/reports')
@login_required
def reports_dashboard():
    """صفحة لوحة تحكم التقارير"""
    return render_template('reports/dashboard.html')

@app.route('/api/reports/quick', methods=['POST'])
@login_required
def generate_quick_report():
    """API لإنشاء التقارير السريعة"""
    try:
        data = request.get_json()
        report_type = data.get('type')
        filters = data.get('filters', {})

        if report_type == 'cases_summary':
            return generate_cases_summary_report(filters)
        elif report_type == 'clients_analysis':
            return generate_clients_analysis_report(filters)
        elif report_type == 'financial_overview':
            return generate_financial_overview_report(filters)
        elif report_type == 'properties_performance':
            return generate_properties_performance_report(filters)
        elif report_type == 'monthly_summary':
            return generate_monthly_summary_report(filters)
        elif report_type == 'yearly_analysis':
            return generate_yearly_analysis_report(filters)
        elif report_type == 'appointments_analysis':
            return generate_appointments_analysis_report(filters)
        elif report_type == 'tasks_productivity':
            return generate_tasks_productivity_report(filters)
        elif report_type == 'performance_dashboard':
            return generate_performance_dashboard_report(filters)
        elif report_type == 'debts_analysis':
            return generate_debts_analysis_report(filters)
        elif report_type == 'courts_statistics':
            return generate_courts_statistics_report(filters)
        elif report_type == 'fees_collection':
            return generate_fees_collection_report(filters)
        else:
            return jsonify({'success': False, 'error': 'نوع التقرير غير مدعوم'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/reports/custom', methods=['POST'])
@login_required
def generate_custom_report():
    """API لإنشاء التقارير المخصصة"""
    try:
        criteria = request.get_json()

        # بناء التقرير المخصص بناءً على المعايير
        report_data = build_custom_report(criteria)

        return jsonify({
            'success': True,
            'data': report_data['data'],
            'metadata': report_data['metadata'],
            'charts': report_data['charts']
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/reports/export', methods=['POST'])
@login_required
def export_report():
    """API لتصدير التقارير"""
    try:
        data = request.get_json()
        report = data.get('report')
        format_type = data.get('format')

        if format_type == 'pdf':
            return export_report_to_pdf(report)
        elif format_type == 'excel':
            return export_report_to_excel(report)
        else:
            return jsonify({'success': False, 'error': 'صيغة التصدير غير مدعومة'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== Report Generation Functions ====================

def generate_cases_summary_report(filters):
    """إنشاء تقرير ملخص القضايا"""
    try:
        # جلب بيانات القضايا
        cases_query = Case.query

        # تطبيق الفلاتر
        if filters.get('dateFrom'):
            cases_query = cases_query.filter(Case.created_at >= filters['dateFrom'])
        if filters.get('dateTo'):
            cases_query = cases_query.filter(Case.created_at <= filters['dateTo'])
        if filters.get('status'):
            cases_query = cases_query.filter(Case.status == filters['status'])

        cases = cases_query.all()

        # إحصائيات الملخص
        total_cases = len(cases)
        active_cases = len([c for c in cases if c.status == 'active'])
        closed_cases = len([c for c in cases if c.status == 'closed'])
        pending_cases = len([c for c in cases if c.status == 'pending'])

        # بيانات الرسوم البيانية
        status_chart = {
            'type': 'doughnut',
            'title': 'توزيع القضايا حسب الحالة',
            'data': {
                'labels': ['نشطة', 'مغلقة', 'معلقة'],
                'datasets': [{
                    'data': [active_cases, closed_cases, pending_cases],
                    'backgroundColor': ['#28a745', '#dc3545', '#ffc107']
                }]
            }
        }

        # جدول القضايا
        cases_table = {
            'title': 'قائمة القضايا',
            'headers': ['رقم القضية', 'العنوان', 'العميل', 'الحالة', 'تاريخ الإنشاء'],
            'rows': []
        }

        for case in cases[:50]:  # أول 50 قضية
            cases_table['rows'].append([
                case.case_number or f"#{case.id}",
                case.title,
                case.client.name if case.client else 'غير محدد',
                case.status,
                case.created_at.strftime('%Y-%m-%d') if case.created_at else 'غير محدد'
            ])

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'cases', 'label': 'إجمالي القضايا', 'value': total_cases},
                    {'type': 'cases', 'label': 'القضايا النشطة', 'value': active_cases},
                    {'type': 'cases', 'label': 'القضايا المغلقة', 'value': closed_cases},
                    {'type': 'cases', 'label': 'القضايا المعلقة', 'value': pending_cases}
                ],
                'charts': [status_chart],
                'tables': [cases_table]
            },
            'metadata': {
                'title': 'تقرير ملخص القضايا',
                'description': 'تقرير شامل عن حالة وإحصائيات القضايا',
                'type': 'cases_summary',
                'recordCount': total_cases,
                'period': f"{filters.get('dateFrom', 'البداية')} - {filters.get('dateTo', 'النهاية')}"
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_clients_analysis_report(filters):
    """إنشاء تقرير تحليل العملاء"""
    try:
        # جلب بيانات العملاء
        clients_query = Client.query

        if filters.get('dateFrom'):
            clients_query = clients_query.filter(Client.created_at >= filters['dateFrom'])
        if filters.get('dateTo'):
            clients_query = clients_query.filter(Client.created_at <= filters['dateTo'])

        clients = clients_query.all()

        # إحصائيات العملاء
        total_clients = len(clients)
        active_clients = len([c for c in clients if hasattr(c, 'cases') and c.cases])
        new_clients_this_month = len([c for c in clients if c.created_at and c.created_at.month == datetime.now(timezone.utc).month])

        # رسم بياني للعملاء الجدد شهرياً
        monthly_chart = {
            'type': 'line',
            'title': 'العملاء الجدد شهرياً',
            'data': {
                'labels': ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                'datasets': [{
                    'label': 'عملاء جدد',
                    'data': [12, 19, 15, 25, 22, 30],
                    'borderColor': '#007bff',
                    'backgroundColor': 'rgba(0, 123, 255, 0.1)'
                }]
            }
        }

        # جدول العملاء
        clients_table = {
            'title': 'قائمة العملاء',
            'headers': ['الاسم', 'البريد الإلكتروني', 'الهاتف', 'عدد القضايا', 'تاريخ التسجيل'],
            'rows': []
        }

        for client in clients[:50]:
            case_count = len(client.cases) if hasattr(client, 'cases') else 0
            clients_table['rows'].append([
                client.name,
                client.email or 'غير محدد',
                client.phone or 'غير محدد',
                case_count,
                client.created_at.strftime('%Y-%m-%d') if client.created_at else 'غير محدد'
            ])

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'clients', 'label': 'إجمالي العملاء', 'value': total_clients},
                    {'type': 'clients', 'label': 'العملاء النشطون', 'value': active_clients},
                    {'type': 'clients', 'label': 'عملاء جدد هذا الشهر', 'value': new_clients_this_month}
                ],
                'charts': [monthly_chart],
                'tables': [clients_table]
            },
            'metadata': {
                'title': 'تقرير تحليل العملاء',
                'description': 'تحليل شامل لبيانات العملاء والنشاط',
                'type': 'clients_analysis',
                'recordCount': total_clients
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_financial_overview_report(filters):
    """إنشاء تقرير نظرة عامة مالية"""
    try:
        # جلب البيانات المالية
        fees_query = Fee.query
        debts_query = Debt.query

        if filters.get('dateFrom'):
            fees_query = fees_query.filter(Fee.date >= filters['dateFrom'])
            debts_query = debts_query.filter(Debt.date >= filters['dateFrom'])
        if filters.get('dateTo'):
            fees_query = fees_query.filter(Fee.date <= filters['dateTo'])
            debts_query = debts_query.filter(Debt.date <= filters['dateTo'])

        fees = fees_query.all()
        debts = debts_query.all()

        # حساب الإحصائيات
        total_fees = sum(float(fee.amount) for fee in fees)
        total_debts = sum(float(debt.amount) for debt in debts)
        net_income = total_fees - total_debts

        paid_fees = sum(float(fee.amount) for fee in fees if fee.status == 'paid')
        pending_fees = sum(float(fee.amount) for fee in fees if fee.status == 'pending')

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'revenue', 'label': 'إجمالي الرسوم', 'value': f"{total_fees:,.2f} ريال"},
                    {'type': 'expenses', 'label': 'إجمالي الديون', 'value': f"{total_debts:,.2f} ريال"},
                    {'type': 'revenue', 'label': 'صافي الدخل', 'value': f"{net_income:,.2f} ريال"},
                    {'type': 'revenue', 'label': 'الرسوم المدفوعة', 'value': f"{paid_fees:,.2f} ريال"}
                ],
                'charts': [{
                    'type': 'bar',
                    'title': 'المقارنة المالية',
                    'data': {
                        'labels': ['الرسوم', 'الديون', 'صافي الدخل'],
                        'datasets': [{
                            'label': 'المبلغ (ريال)',
                            'data': [total_fees, total_debts, net_income],
                            'backgroundColor': ['#28a745', '#dc3545', '#007bff']
                        }]
                    }
                }]
            },
            'metadata': {
                'title': 'التقرير المالي الشامل',
                'description': 'نظرة عامة على الوضع المالي للمكتب',
                'type': 'financial_overview',
                'recordCount': len(fees) + len(debts)
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_properties_performance_report(filters):
    """إنشاء تقرير أداء العقارات"""
    try:
        properties = Property.query.all()

        total_properties = len(properties)
        rented_properties = len([p for p in properties if p.status == 'rented'])
        available_properties = len([p for p in properties if p.status == 'available'])

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'properties', 'label': 'إجمالي العقارات', 'value': total_properties},
                    {'type': 'properties', 'label': 'العقارات المؤجرة', 'value': rented_properties},
                    {'type': 'properties', 'label': 'العقارات المتاحة', 'value': available_properties}
                ]
            },
            'metadata': {
                'title': 'تقرير أداء العقارات',
                'description': 'تحليل أداء وحالة العقارات',
                'type': 'properties_performance',
                'recordCount': total_properties
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_monthly_summary_report(filters):
    """إنشاء تقرير الملخص الشهري"""
    try:
        current_month = datetime.now(timezone.utc).month
        current_year = datetime.now(timezone.utc).year

        # إحصائيات الشهر الحالي
        monthly_cases = Case.query.filter(
            func.extract('month', Case.created_at) == current_month,
            func.extract('year', Case.created_at) == current_year
        ).count()

        monthly_clients = Client.query.filter(
            func.extract('month', Client.created_at) == current_month,
            func.extract('year', Client.created_at) == current_year
        ).count()

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'cases', 'label': 'قضايا هذا الشهر', 'value': monthly_cases},
                    {'type': 'clients', 'label': 'عملاء جدد هذا الشهر', 'value': monthly_clients}
                ]
            },
            'metadata': {
                'title': 'الملخص الشهري',
                'description': f'ملخص أنشطة شهر {current_month}/{current_year}',
                'type': 'monthly_summary'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_yearly_analysis_report(filters):
    """إنشاء تقرير التحليل السنوي"""
    try:
        current_year = datetime.now(timezone.utc).year

        yearly_cases = Case.query.filter(
            func.extract('year', Case.created_at) == current_year
        ).count()

        yearly_clients = Client.query.filter(
            func.extract('year', Client.created_at) == current_year
        ).count()

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'cases', 'label': f'قضايا عام {current_year}', 'value': yearly_cases},
                    {'type': 'clients', 'label': f'عملاء جدد عام {current_year}', 'value': yearly_clients}
                ]
            },
            'metadata': {
                'title': 'التحليل السنوي',
                'description': f'تحليل شامل لأنشطة عام {current_year}',
                'type': 'yearly_analysis'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def build_custom_report(criteria):
    """بناء تقرير مخصص بناءً على المعايير"""
    try:
        # هذه وظيفة مبسطة للتقارير المخصصة
        # يمكن توسيعها لاحقاً لدعم معايير أكثر تعقيداً

        data = {
            'summary': [
                {'type': 'default', 'label': 'تقرير مخصص', 'value': 'جاري التطوير'}
            ],
            'charts': [],
            'tables': []
        }

        metadata = {
            'title': 'تقرير مخصص',
            'description': 'تقرير مخصص بناءً على معايير محددة',
            'type': 'custom'
        }

        return {
            'data': data,
            'metadata': metadata,
            'charts': []
        }

    except Exception as e:
        raise e

def export_report_to_pdf(report):
    """تصدير التقرير إلى PDF"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet
        from io import BytesIO

        output = BytesIO()
        doc = SimpleDocTemplate(output, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()

        # إضافة عنوان التقرير
        title = Paragraph(report['metadata']['title'], styles['Title'])
        story.append(title)
        story.append(Spacer(1, 20))

        # إضافة الملخص
        if 'summary' in report['data']:
            for item in report['data']['summary']:
                text = f"{item['label']}: {item['value']}"
                para = Paragraph(text, styles['Normal'])
                story.append(para)
                story.append(Spacer(1, 10))

        doc.build(story)
        output.seek(0)

        return send_file(
            output,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f"report_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}.pdf"
        )

    except ImportError:
        return jsonify({'success': False, 'error': 'مكتبة reportlab غير متوفرة'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def export_report_to_excel(report):
    """تصدير التقرير إلى Excel"""
    try:
        import pandas as pd
        from io import BytesIO

        output = BytesIO()

        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # إضافة الملخص
            if 'summary' in report['data']:
                summary_data = []
                for item in report['data']['summary']:
                    summary_data.append({
                        'البيان': item['label'],
                        'القيمة': item['value']
                    })

                df_summary = pd.DataFrame(summary_data)
                df_summary.to_excel(writer, sheet_name='الملخص', index=False)

            # إضافة الجداول
            if 'tables' in report['data']:
                for i, table in enumerate(report['data']['tables']):
                    df_table = pd.DataFrame(table['rows'], columns=table['headers'])
                    sheet_name = f"جدول_{i+1}"
                    df_table.to_excel(writer, sheet_name=sheet_name, index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f"report_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}.xlsx"
        )

    except ImportError:
        return jsonify({'success': False, 'error': 'مكتبة pandas غير متوفرة'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/archive/cases')
@login_required
def get_archived_cases():
    """الحصول على القضايا المؤرشفة"""
    try:
        # فلاتر البحث
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        reason = request.args.get('reason')

        query = ArchivedCase.query

        if date_from:
            query = query.filter(ArchivedCase.archived_date >= datetime.strptime(date_from, '%Y-%m-%d'))
        if date_to:
            query = query.filter(ArchivedCase.archived_date <= datetime.strptime(date_to, '%Y-%m-%d'))
        if reason:
            query = query.filter(ArchivedCase.archive_reason == reason)

        cases = query.order_by(ArchivedCase.archived_date.desc()).all()

        return jsonify({
            'success': True,
            'cases': [case.to_dict() for case in cases]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/clients')
@login_required
def get_archived_clients():
    """الحصول على العملاء المؤرشفين"""
    try:
        clients = ArchivedClient.query.order_by(ArchivedClient.archived_date.desc()).all()

        return jsonify({
            'success': True,
            'clients': [client.to_dict() for client in clients]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/documents')
@login_required
def get_archived_documents():
    """الحصول على المستندات المؤرشفة"""
    try:
        documents = ArchivedDocument.query.order_by(ArchivedDocument.archived_date.desc()).all()

        return jsonify({
            'success': True,
            'documents': [doc.to_dict() for doc in documents]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/transactions')
@login_required
def get_archived_transactions():
    """الحصول على المعاملات المالية المؤرشفة"""
    try:
        # هنا يمكن إضافة منطق للمعاملات المؤرشفة
        # حالياً سنعيد قائمة فارغة
        return jsonify({
            'success': True,
            'transactions': []
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/invoices')
@login_required
def get_archived_invoices():
    """الحصول على الفواتير المؤرشفة"""
    try:
        # هنا يمكن إضافة منطق للفواتير المؤرشفة
        # حالياً سنعيد قائمة فارغة
        return jsonify({
            'success': True,
            'invoices': []
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/statistics')
@login_required
def get_archive_statistics_api():
    """الحصول على إحصائيات الأرشيف"""
    try:
        stats = get_archive_statistics()
        return jsonify(stats)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/system/info')
@login_required
def get_system_info_api():
    """الحصول على معلومات النظام"""
    try:
        info = get_system_info()
        return jsonify(info)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/cases/<int:case_id>/restore', methods=['POST'])
@login_required
def restore_archived_case(case_id):
    """استعادة قضية مؤرشفة"""
    try:
        archived_case = ArchivedCase.query.get_or_404(case_id)

        # إنشاء قضية جديدة من البيانات المؤرشفة
        new_case = Case(
            case_number=archived_case.case_number + '_restored',
            title=archived_case.title,
            description=archived_case.description,
            case_type=archived_case.case_type,
            status='active',
            client_id=archived_case.client_id,
            created_date=datetime.now(timezone.utc)
        )

        db.session.add(new_case)
        db.session.delete(archived_case)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم استعادة القضية بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/cases/<int:case_id>/delete', methods=['DELETE'])
@login_required
def delete_archived_case(case_id):
    """حذف قضية مؤرشفة نهائياً"""
    try:
        archived_case = ArchivedCase.query.get_or_404(case_id)
        db.session.delete(archived_case)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حذف القضية نهائياً'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/clients/<int:client_id>/restore', methods=['POST'])
@login_required
def restore_archived_client(client_id):
    """استعادة عميل مؤرشف"""
    try:
        archived_client = ArchivedClient.query.get_or_404(client_id)

        # إنشاء عميل جديد من البيانات المؤرشفة
        new_client = Client(
            name=archived_client.name,
            id_number=archived_client.id_number,
            phone=archived_client.phone,
            email=archived_client.email,
            address=archived_client.address,
            created_date=datetime.now(timezone.utc)
        )

        db.session.add(new_client)
        db.session.delete(archived_client)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم استعادة العميل بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/system', methods=['POST'])
@login_required
def save_system_settings():
    """حفظ إعدادات النظام"""
    try:
        data = request.get_json()

        # حفظ الإعدادات
        SystemSettings.set_setting('office_name', data.get('office_name', ''), user_id=current_user.id)
        SystemSettings.set_setting('office_address', data.get('office_address', ''), user_id=current_user.id)
        SystemSettings.set_setting('office_phone', data.get('office_phone', ''), user_id=current_user.id)
        SystemSettings.set_setting('office_email', data.get('office_email', ''), user_id=current_user.id)
        SystemSettings.set_setting('currency', data.get('currency', 'JOD'), user_id=current_user.id)

        return jsonify({'success': True, 'message': 'تم حفظ الإعدادات بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/archive', methods=['POST'])
@login_required
def save_archive_settings():
    """حفظ إعدادات الأرشفة"""
    try:
        data = request.get_json()

        # حفظ إعدادات الأرشفة
        SystemSettings.set_setting('auto_archive_days', data.get('auto_archive_days', 365), 'integer', user_id=current_user.id)
        SystemSettings.set_setting('enable_auto_archive', data.get('enable_auto_archive', True), 'boolean', user_id=current_user.id)
        SystemSettings.set_setting('archive_notifications', data.get('archive_notifications', True), 'boolean', user_id=current_user.id)
        SystemSettings.set_setting('backup_frequency', data.get('backup_frequency', 'weekly'), user_id=current_user.id)

        return jsonify({'success': True, 'message': 'تم حفظ إعدادات الأرشفة بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/rules', methods=['POST'])
@login_required
def save_archive_rules():
    """حفظ قواعد الأرشفة"""
    try:
        data = request.get_json()

        # حفظ قواعد الأرشفة
        SystemSettings.set_setting('completed_cases_archive_days', data.get('completed_cases_archive_days', 90), 'integer', user_id=current_user.id)
        SystemSettings.set_setting('cancelled_cases_archive_days', data.get('cancelled_cases_archive_days', 30), 'integer', user_id=current_user.id)
        SystemSettings.set_setting('documents_archive_days', data.get('documents_archive_days', 365), 'integer', user_id=current_user.id)
        SystemSettings.set_setting('archive_empty_folders', data.get('archive_empty_folders', False), 'boolean', user_id=current_user.id)

        return jsonify({'success': True, 'message': 'تم حفظ قواعد الأرشفة بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backup/create', methods=['POST'])
@login_required
def create_backup():
    """إنشاء نسخة احتياطية"""
    try:
        import sqlite3
        import tempfile
        import zipfile
        from flask import send_file

        # إنشاء ملف مؤقت للنسخة الاحتياطية
        backup_file = tempfile.NamedTemporaryFile(delete=False, suffix='.sql')

        # الاتصال بقاعدة البيانات
        db_path = app.config.get('DATABASE_PATH', 'law_office.db')

        # إنشاء النسخة الاحتياطية
        with sqlite3.connect(db_path) as conn:
            with open(backup_file.name, 'w', encoding='utf-8') as f:
                for line in conn.iterdump():
                    f.write('%s\n' % line)

        # تسجيل النسخة الاحتياطية في السجل
        backup_log = BackupLog(
            backup_type='manual',
            backup_path=backup_file.name,
            backup_size=os.path.getsize(backup_file.name),
            backup_status='completed',
            created_by=current_user.id
        )
        db.session.add(backup_log)
        db.session.commit()

        return send_file(backup_file.name, as_attachment=True, download_name=f'backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.sql')

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backup/restore', methods=['POST'])
@login_required
def restore_backup():
    """استعادة نسخة احتياطية"""
    try:
        if 'backup_file' not in request.files:
            return jsonify({'success': False, 'error': 'لم يتم اختيار ملف'}), 400

        file = request.files['backup_file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'لم يتم اختيار ملف'}), 400

        # حفظ الملف مؤقتاً
        temp_path = os.path.join(tempfile.gettempdir(), file.filename)
        file.save(temp_path)

        # استعادة قاعدة البيانات
        import sqlite3
        db_path = app.config.get('DATABASE_PATH', 'law_office.db')

        # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
        backup_current = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(db_path, backup_current)

        # استعادة قاعدة البيانات
        with open(temp_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()

        # حذف قاعدة البيانات الحالية وإنشاء جديدة
        os.remove(db_path)

        with sqlite3.connect(db_path) as conn:
            conn.executescript(sql_script)

        # تنظيف الملف المؤقت
        os.remove(temp_path)

        return jsonify({'success': True, 'message': 'تم استعادة النسخة الاحتياطية بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


# ===== مسارات نظام الإشعارات والتنبيهات =====

@app.route('/notifications')
@login_required
def notifications():
    """صفحة الإشعارات الرئيسية"""
    try:
        return render_template('notifications/list.html')
    except Exception as e:
        flash(f'خطأ في تحميل صفحة الإشعارات: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@app.route('/api/notifications')
@login_required
def api_notifications():
    """API للحصول على إشعارات المستخدم"""
    try:
        limit = request.args.get('limit', 50, type=int)
        unread_only = request.args.get('unread_only', False, type=bool)

        notifications = NotificationService.get_user_notifications(
            user_id=current_user.id,
            limit=limit,
            unread_only=unread_only
        )

        return jsonify({
            'success': True,
            'notifications': [n.to_dict() for n in notifications],
            'unread_count': Notification.get_unread_count(current_user.id)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/new')
@login_required
def api_notifications_new():
    """API للحصول على الإشعارات الجديدة"""
    try:
        since = request.args.get('since', '0')
        since_timestamp = datetime.fromtimestamp(int(since) / 1000, tz=timezone.utc)

        new_notifications = Notification.query.filter(
            Notification.user_id == current_user.id,
            Notification.created_at > since_timestamp
        ).order_by(Notification.created_at.desc()).all()

        return jsonify({
            'success': True,
            'notifications': [n.to_dict() for n in new_notifications]
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/<int:notification_id>/read', methods=['POST'])
@login_required
def api_notification_mark_read(notification_id):
    """تمييز إشعار كمقروء"""
    try:
        success = NotificationService.mark_notification_as_read(
            notification_id=notification_id,
            user_id=current_user.id
        )

        if success:
            return jsonify({'success': True, 'message': 'تم تمييز الإشعار كمقروء'})
        else:
            return jsonify({'success': False, 'error': 'الإشعار غير موجود'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/mark-all-read', methods=['POST'])
@login_required
def api_notifications_mark_all_read():
    """تمييز جميع الإشعارات كمقروءة"""
    try:
        count = NotificationService.mark_all_as_read(current_user.id)

        return jsonify({
            'success': True,
            'message': f'تم تمييز {count} إشعار كمقروء',
            'marked_count': count
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/<int:notification_id>', methods=['DELETE'])
@login_required
def api_notification_delete(notification_id):
    """حذف إشعار"""
    try:
        success = NotificationService.delete_notification(
            notification_id=notification_id,
            user_id=current_user.id
        )

        if success:
            return jsonify({'success': True, 'message': 'تم حذف الإشعار'})
        else:
            return jsonify({'success': False, 'error': 'الإشعار غير موجود'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/clear-all', methods=['POST'])
@login_required
def api_notifications_clear_all():
    """حذف جميع الإشعارات"""
    try:
        count = NotificationService.clear_all_notifications(current_user.id)

        return jsonify({
            'success': True,
            'message': f'تم حذف {count} إشعار',
            'deleted_count': count
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/alerts')
@login_required
def api_alerts():
    """API للحصول على التنبيهات"""
    try:
        alerts = Alert.query.filter_by(user_id=current_user.id)\
                           .order_by(Alert.created_at.desc()).all()

        return jsonify({
            'success': True,
            'alerts': [a.to_dict() for a in alerts]
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/alerts', methods=['POST'])
@login_required
def api_alerts_create():
    """إنشاء تنبيه جديد"""
    try:
        data = request.get_json()

        # تحويل وقت الجدولة
        schedule_time = datetime.fromisoformat(data['schedule_time'].replace('Z', '+00:00'))

        alert = NotificationService.create_scheduled_alert(
            user_id=current_user.id,
            title=data['title'],
            message=data['message'],
            schedule_time=schedule_time,
            type=data.get('type', 'info'),
            priority=data.get('priority', 'normal'),
            repeat_type=data.get('repeat', 'none'),
            url=data.get('url'),
            action=data.get('action'),
            data=data.get('data')
        )

        return jsonify({
            'success': True,
            'message': 'تم إنشاء التنبيه بنجاح',
            'alert': alert.to_dict()
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/alerts/check')
@login_required
def api_alerts_check():
    """فحص التنبيهات المعلقة"""
    try:
        processed_count = NotificationService.process_pending_alerts()

        return jsonify({
            'success': True,
            'processed_count': processed_count,
            'message': f'تم معالجة {processed_count} تنبيه'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/appointments/upcoming')
@login_required
def api_appointments_upcoming():
    """API للحصول على المواعيد القادمة"""
    try:
        # المواعيد في الـ 24 ساعة القادمة
        now = datetime.now(timezone.utc)
        tomorrow = now + timedelta(days=1)

        upcoming_appointments = Event.query.filter(
            Event.start_time >= now,
            Event.start_time <= tomorrow,
            Event.type == 'appointment'
        ).order_by(Event.start_time).all()

        appointments_data = []
        for appointment in upcoming_appointments:
            appointments_data.append({
                'id': appointment.id,
                'title': appointment.title,
                'client_name': appointment.client_name,
                'start_time': appointment.start_time.isoformat(),
                'location': appointment.location,
                'description': appointment.description
            })

        return jsonify({
            'success': True,
            'appointments': appointments_data,
            'count': len(appointments_data)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/overdue-items')
@login_required
def api_overdue_items():
    """API شامل للحصول على جميع العناصر المتأخرة"""
    try:
        now = datetime.now(timezone.utc)
        today = now.date()
        tomorrow = today + timedelta(days=1)

        overdue_items = []

        # المهام المتأخرة
        overdue_tasks = TaskItem.query.filter(
            TaskItem.due_date < now,
            TaskItem.completed == False
        ).order_by(TaskItem.due_date).all()

        for task in overdue_tasks:
            overdue_items.append({
                'id': f'task_{task.id}',
                'type': 'task',
                'title': task.title,
                'description': task.description,
                'due_date': task.due_date.isoformat(),
                'priority': task.priority,
                'category': task.category,
                'days_overdue': (now.date() - task.due_date.date()).days
            })

        # المواعيد المتأخرة أو خلال 24 ساعة
        overdue_appointments = Appointment.query.filter(
            Appointment.date <= tomorrow
        ).order_by(Appointment.date, Appointment.time).all()

        for appointment in overdue_appointments:
            appointment_datetime = datetime.combine(appointment.date, appointment.time or datetime.min.time())
            if appointment_datetime < now:
                status = 'overdue'
                hours_diff = (now - appointment_datetime).total_seconds() / 3600
            else:
                status = 'upcoming'
                hours_diff = (appointment_datetime - now).total_seconds() / 3600

            overdue_items.append({
                'id': f'appointment_{appointment.id}',
                'type': 'appointment',
                'title': f'موعد مع {appointment.client_name}',
                'description': appointment.description,
                'date': appointment.date.isoformat(),
                'time': appointment.time.strftime('%H:%M') if appointment.time else '',
                'location': appointment.location,
                'status': status,
                'hours_diff': round(hours_diff, 1)
            })

        # الاستحقاقات المالية المتأخرة (محاكاة - يمكن ربطها بنظام الفواتير)
        # يمكن إضافة استعلامات للفواتير المتأخرة هنا

        return jsonify({
            'success': True,
            'overdue_items': overdue_items,
            'total_count': len(overdue_items)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/tasks/overdue')
@login_required
def api_tasks_overdue():
    """API للحصول على المهام المتأخرة"""
    try:
        now = datetime.now(timezone.utc)

        overdue_tasks = TaskItem.query.filter(
            TaskItem.due_date < now,
            TaskItem.completed == False
        ).order_by(TaskItem.due_date).all()

        tasks_data = []
        for task in overdue_tasks:
            tasks_data.append({
                'id': task.id,
                'title': task.title,
                'description': task.description,
                'due_date': task.due_date.isoformat(),
                'priority': task.priority,
                'category': task.category,
                'days_overdue': (now - task.due_date).days
            })

        return jsonify({
            'success': True,
            'tasks': tasks_data,
            'count': len(tasks_data)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/settings')
@login_required
def api_notification_settings():
    """API للحصول على إعدادات الإشعارات"""
    try:
        settings = NotificationService.get_notification_settings(current_user.id)

        return jsonify({
            'success': True,
            'settings': settings.to_dict()
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/settings', methods=['POST'])
@login_required
def api_notification_settings_update():
    """تحديث إعدادات الإشعارات"""
    try:
        data = request.get_json()

        settings = NotificationService.update_notification_settings(
            user_id=current_user.id,
            settings_data=data
        )

        return jsonify({
            'success': True,
            'message': 'تم تحديث الإعدادات بنجاح',
            'settings': settings.to_dict()
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/stats')
@login_required
def api_notification_stats():
    """إحصائيات الإشعارات"""
    try:
        from .notification_models import get_notification_stats

        stats = get_notification_stats(current_user.id)

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500





@app.route('/api/notifications/cleanup', methods=['POST'])
@login_required
def api_notifications_cleanup():
    """تنظيف الإشعارات القديمة"""
    try:
        days_old = request.json.get('days_old', 30)

        cleaned_count = NotificationService.cleanup_old_notifications(days_old)

        return jsonify({
            'success': True,
            'message': f'تم حذف {cleaned_count} إشعار قديم',
            'cleaned_count': cleaned_count
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500





# ==================== Routes للمصروفات والإيرادات ====================

@app.route('/expenses/add', methods=['GET', 'POST'])
@login_required
def add_expense():
    if request.method == 'POST':
        try:
            category = request.form['category']
            subcategory = request.form.get('subcategory', '')
            amount = float(request.form['amount'])
            description = request.form.get('description', '')
            expense_date_str = request.form.get('expense_date')
            payment_method = request.form.get('payment_method', 'نقدي')
            currency = request.form.get('currency', 'شيكل')
            property_id = request.form.get('property_id') if request.form.get('property_id') else None

            expense_date = datetime.strptime(expense_date_str, '%Y-%m-%d') if expense_date_str else datetime.now()

            expense = Expense(
                category=category,
                subcategory=subcategory,
                amount=amount,
                description=description,
                expense_date=expense_date,
                payment_method=payment_method,
                currency=currency,
                property_id=property_id
            )

            db.session.add(expense)
            db.session.commit()
            backup_db_file()

            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': True, 'message': 'تمت إضافة المصروف بنجاح'})

            flash('تمت إضافة المصروف بنجاح', 'success')
            return redirect(url_for('finance_dashboard'))

        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
            flash(f'حدث خطأ أثناء إضافة المصروف: {str(e)}', 'danger')
            return redirect(url_for('finance_dashboard'))

    properties = Property.query.all()
    return render_template('expenses/add.html', properties=properties)

@app.route('/rental_incomes/add', methods=['GET', 'POST'])
@login_required
def add_rental_income():
    if request.method == 'POST':
        try:
            property_id = request.form['property_id']
            tenant_id = request.form.get('tenant_id')
            amount = float(request.form['amount'])
            income_date_str = request.form.get('income_date')
            payment_method = request.form.get('payment_method', 'نقدي')
            currency = request.form.get('currency', 'شيكل')
            description = request.form.get('description', '')
            month_year = request.form.get('month_year', '')

            income_date = datetime.strptime(income_date_str, '%Y-%m-%d') if income_date_str else datetime.now()

            rental_income = RentalIncome(
                property_id=property_id,
                tenant_id=tenant_id,
                amount=amount,
                income_date=income_date,
                payment_method=payment_method,
                currency=currency,
                description=description,
                month_year=month_year
            )

            db.session.add(rental_income)
            db.session.commit()
            backup_db_file()

            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': True, 'message': 'تمت إضافة الإيجار بنجاح'})

            flash('تمت إضافة الإيجار بنجاح', 'success')
            return redirect(url_for('finance_dashboard'))

        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
            flash(f'حدث خطأ أثناء إضافة الإيجار: {str(e)}', 'danger')
            return redirect(url_for('finance_dashboard'))

    properties = Property.query.all()
    tenants = Tenant.query.all()
    return render_template('rental_incomes/add.html', properties=properties, tenants=tenants)

@app.route('/finance/add', methods=['GET', 'POST'])
@login_required
def add_financial_transaction():
    if request.method == 'POST':
        try:
            transaction_type = request.form['type']
            amount = float(request.form['amount'])
            currency = request.form.get('currency', 'شيكل')
            date_str = request.form.get('date')
            client_id = request.form.get('client_id') if request.form.get('client_id') else None
            case_id = request.form.get('case_id') if request.form.get('case_id') else None
            payment_method = request.form.get('payment_method', 'نقدي')
            reference_number = request.form.get('reference_number', '')
            description = request.form['description']
            notes = request.form.get('notes', '')

            transaction_date = datetime.strptime(date_str, '%Y-%m-%d') if date_str else datetime.now()

            transaction = FinancialTransaction(
                type=transaction_type,
                amount=amount,
                currency=currency,
                date=transaction_date,
                client_id=client_id,
                case_id=case_id,
                description=description
            )

            db.session.add(transaction)
            db.session.commit()
            backup_db_file()

            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': True, 'message': 'تمت إضافة السند المالي بنجاح'})

            flash('تمت إضافة السند المالي بنجاح', 'success')
            return redirect(url_for('financial_transactions_list'))

        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
            flash(f'حدث خطأ أثناء إضافة السند المالي: {str(e)}', 'danger')
            return redirect(url_for('add_financial_transaction'))

    clients = Client.query.all()
    cases = Case.query.all()
    properties = Property.query.all()
    tenants = Tenant.query.all()
    return render_template('finance/add.html', clients=clients, cases=cases, properties=properties, tenants=tenants)

# تم حذف route إضافة عقد الإيجار - سيتم إعادة بناؤه

# ==================== Advanced Settings and Backup System Routes ====================

@app.route('/api/settings/all')
@login_required
def get_all_settings():
    """API لجلب جميع الإعدادات"""
    try:
        settings_service = SettingsService()
        all_settings = settings_service.get_all_settings()

        return jsonify({
            'success': True,
            'settings': all_settings
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/save', methods=['POST'])
@login_required
def save_all_settings():
    """API لحفظ جميع الإعدادات"""
    try:
        data = request.get_json()
        settings_service = SettingsService()

        # حفظ الإعدادات بشكل مجمع
        settings_service.update_settings_batch(data, current_user.id)

        return jsonify({
            'success': True,
            'message': 'تم حفظ جميع الإعدادات بنجاح'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/reset', methods=['POST'])
@login_required
def reset_all_settings():
    """API لإعادة تعيين جميع الإعدادات"""
    try:
        settings_service = SettingsService()
        settings_service.reset_to_defaults(current_user.id)

        return jsonify({
            'success': True,
            'message': 'تم إعادة تعيين الإعدادات بنجاح'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/export')
@login_required
def export_settings():
    """API لتصدير الإعدادات"""
    try:
        settings_service = SettingsService()
        settings_data = settings_service.export_settings()

        # إنشاء ملف JSON للتصدير
        from io import BytesIO
        import json

        output = BytesIO()
        output.write(json.dumps(settings_data, ensure_ascii=False, indent=2).encode('utf-8'))
        output.seek(0)

        return send_file(
            output,
            mimetype='application/json',
            as_attachment=True,
            download_name=f'settings_backup_{datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")}.json'
        )
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/import', methods=['POST'])
@login_required
def import_settings():
    """API لاستيراد الإعدادات"""
    try:
        data = request.get_json()
        settings_service = SettingsService()

        # استيراد الإعدادات
        settings_service.import_settings(data, current_user.id)

        return jsonify({
            'success': True,
            'message': 'تم استيراد الإعدادات بنجاح'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backup/create-manual', methods=['POST'])
@login_required
def create_manual_backup():
    """API لإنشاء نسخة احتياطية يدوية"""
    try:
        backup_service = BackupService()
        backup_result = backup_service.create_manual_backup(current_user.id)

        if backup_result['success']:
            return jsonify({
                'success': True,
                'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
                'backup_id': backup_result['backup_id'],
                'file_path': backup_result['file_path']
            })
        else:
            return jsonify({
                'success': False,
                'error': backup_result['error']
            }), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backup/stats')
@login_required
def get_backup_stats():
    """API لجلب إحصائيات النسخ الاحتياطي"""
    try:
        backup_service = BackupService()
        stats = backup_service.get_backup_statistics()

        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backup/history')
@login_required
def get_backup_history():
    """API لجلب سجل النسخ الاحتياطي"""
    try:
        backup_service = BackupService()
        history = backup_service.get_backup_history()

        return jsonify({
            'success': True,
            'history': history
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backup/restore-file', methods=['POST'])
@login_required
def restore_backup_file():
    """API لاستعادة نسخة احتياطية"""
    try:
        if 'backup_file' not in request.files:
            return jsonify({'success': False, 'error': 'لم يتم اختيار ملف'}), 400

        file = request.files['backup_file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'لم يتم اختيار ملف'}), 400

        backup_service = BackupService()
        restore_result = backup_service.restore_backup(file, current_user.id)

        if restore_result['success']:
            return jsonify({
                'success': True,
                'message': 'تم استعادة النسخة الاحتياطية بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'error': restore_result['error']
            }), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# ==================== وظائف التقارير المتقدمة الجديدة ====================

def generate_appointments_analysis_report(filters):
    """إنشاء تقرير تحليل المواعيد"""
    try:
        from .calendar_models import Event
        from datetime import datetime, timedelta

        # جلب المواعيد
        appointments = Event.query.filter(
            Event.event_type.in_(['موعد عام', 'جلسة محكمة', 'اجتماع عميل', 'مهمة'])
        ).all()

        # إحصائيات أساسية
        total_appointments = len(appointments)
        completed_appointments = len([a for a in appointments if a.status == 'مكتمل'])
        pending_appointments = len([a for a in appointments if a.status == 'مجدول'])
        cancelled_appointments = len([a for a in appointments if a.status == 'ملغي'])

        # تحليل حسب النوع
        type_stats = {}
        for appointment in appointments:
            type_name = appointment.event_type
            if type_name not in type_stats:
                type_stats[type_name] = 0
            type_stats[type_name] += 1

        # تحليل حسب الأولوية
        priority_stats = {}
        for appointment in appointments:
            priority = appointment.priority or 'متوسطة'
            if priority not in priority_stats:
                priority_stats[priority] = 0
            priority_stats[priority] += 1

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'total', 'label': 'إجمالي المواعيد', 'value': total_appointments},
                    {'type': 'completed', 'label': 'المواعيد المكتملة', 'value': completed_appointments},
                    {'type': 'pending', 'label': 'المواعيد المعلقة', 'value': pending_appointments},
                    {'type': 'cancelled', 'label': 'المواعيد الملغية', 'value': cancelled_appointments}
                ],
                'type_analysis': [{'type': k, 'count': v} for k, v in type_stats.items()],
                'priority_analysis': [{'priority': k, 'count': v} for k, v in priority_stats.items()]
            },
            'metadata': {
                'title': 'تحليل المواعيد',
                'description': 'تحليل شامل لإحصائيات المواعيد والجلسات',
                'type': 'appointments_analysis'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_tasks_productivity_report(filters):
    """إنشاء تقرير إنتاجية المهام"""
    try:
        from .calendar_models import TaskItem
        from datetime import datetime

        # جلب المهام
        tasks = TaskItem.query.all()

        # إحصائيات أساسية
        total_tasks = len(tasks)
        completed_tasks = len([t for t in tasks if t.status == 'مكتملة'])
        in_progress_tasks = len([t for t in tasks if t.status == 'قيد التنفيذ'])
        pending_tasks = len([t for t in tasks if t.status == 'معلقة'])

        # حساب معدل الإنجاز
        completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0

        # تحليل حسب الأولوية
        priority_stats = {}
        for task in tasks:
            priority = task.priority or 'متوسطة'
            if priority not in priority_stats:
                priority_stats[priority] = {'total': 0, 'completed': 0}
            priority_stats[priority]['total'] += 1
            if task.status == 'مكتملة':
                priority_stats[priority]['completed'] += 1

        # تحليل حسب الفئة
        category_stats = {}
        for task in tasks:
            category = task.category or 'عامة'
            if category not in category_stats:
                category_stats[category] = 0
            category_stats[category] += 1

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'total', 'label': 'إجمالي المهام', 'value': total_tasks},
                    {'type': 'completed', 'label': 'المهام المكتملة', 'value': completed_tasks},
                    {'type': 'in_progress', 'label': 'المهام قيد التنفيذ', 'value': in_progress_tasks},
                    {'type': 'completion_rate', 'label': 'معدل الإنجاز', 'value': f'{completion_rate:.1f}%'}
                ],
                'priority_analysis': [{'priority': k, 'total': v['total'], 'completed': v['completed']} for k, v in priority_stats.items()],
                'category_analysis': [{'category': k, 'count': v} for k, v in category_stats.items()]
            },
            'metadata': {
                'title': 'إنتاجية المهام',
                'description': 'تحليل أداء المهام ومعدلات الإنجاز',
                'type': 'tasks_productivity'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_performance_dashboard_report(filters):
    """إنشاء تقرير لوحة الأداء"""
    try:
        from datetime import datetime, timedelta

        # إحصائيات القضايا
        total_cases = Case.query.count()
        active_cases = Case.query.filter(Case.status.in_(['نشطة', 'قيد النظر'])).count()
        closed_cases = Case.query.filter(Case.status == 'مغلقة').count()

        # إحصائيات العملاء
        total_clients = Client.query.count()
        new_clients_month = Client.query.filter(
            Client.created_at >= datetime.now() - timedelta(days=30)
        ).count()

        # إحصائيات مالية
        total_fees = db.session.query(func.sum(Fee.total_amount)).scalar() or 0
        total_paid = db.session.query(func.sum(Fee.paid_amount)).scalar() or 0
        total_remaining = total_fees - total_paid

        # إحصائيات العقارات
        total_properties = Property.query.count()
        active_leases = Lease.query.filter(Lease.end_date >= datetime.now().date()).count()

        return jsonify({
            'success': True,
            'data': {
                'kpis': [
                    {'label': 'إجمالي القضايا', 'value': total_cases, 'icon': 'gavel', 'color': 'primary'},
                    {'label': 'القضايا النشطة', 'value': active_cases, 'icon': 'play-circle', 'color': 'success'},
                    {'label': 'إجمالي العملاء', 'value': total_clients, 'icon': 'users', 'color': 'info'},
                    {'label': 'عملاء جدد (شهر)', 'value': new_clients_month, 'icon': 'user-plus', 'color': 'warning'},
                    {'label': 'إجمالي الأتعاب', 'value': f'{total_fees:,.0f}', 'icon': 'money-bill', 'color': 'success'},
                    {'label': 'المبلغ المحصل', 'value': f'{total_paid:,.0f}', 'icon': 'check-circle', 'color': 'primary'},
                    {'label': 'المبلغ المتبقي', 'value': f'{total_remaining:,.0f}', 'icon': 'clock', 'color': 'danger'},
                    {'label': 'العقارات المؤجرة', 'value': active_leases, 'icon': 'building', 'color': 'info'}
                ]
            },
            'metadata': {
                'title': 'لوحة الأداء',
                'description': 'مؤشرات الأداء الرئيسية للمكتب',
                'type': 'performance_dashboard'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_debts_analysis_report(filters):
    """إنشاء تقرير تحليل الديون"""
    try:
        from datetime import datetime

        # جلب الديون
        debts = Debt.query.all()

        # إحصائيات أساسية
        total_debts = len(debts)
        total_amount = sum([d.amount for d in debts])
        paid_amount = sum([d.paid_amount for d in debts])
        remaining_amount = total_amount - paid_amount

        # تحليل حسب النوع
        type_stats = {}
        for debt in debts:
            debt_type = debt.debt_type or 'عامة'
            if debt_type not in type_stats:
                type_stats[debt_type] = {'count': 0, 'amount': 0}
            type_stats[debt_type]['count'] += 1
            type_stats[debt_type]['amount'] += debt.amount

        # تحليل حسب الحالة
        status_stats = {}
        for debt in debts:
            status = debt.status or 'معلقة'
            if status not in status_stats:
                status_stats[status] = 0
            status_stats[status] += 1

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'total', 'label': 'إجمالي الديون', 'value': total_debts},
                    {'type': 'total_amount', 'label': 'إجمالي المبلغ', 'value': f'{total_amount:,.0f}'},
                    {'type': 'paid_amount', 'label': 'المبلغ المدفوع', 'value': f'{paid_amount:,.0f}'},
                    {'type': 'remaining', 'label': 'المبلغ المتبقي', 'value': f'{remaining_amount:,.0f}'}
                ],
                'type_analysis': [{'type': k, 'count': v['count'], 'amount': v['amount']} for k, v in type_stats.items()],
                'status_analysis': [{'status': k, 'count': v} for k, v in status_stats.items()]
            },
            'metadata': {
                'title': 'تحليل الديون',
                'description': 'تقرير شامل عن الديون والمستحقات',
                'type': 'debts_analysis'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_courts_statistics_report(filters):
    """إنشاء تقرير إحصائيات المحاكم"""
    try:
        # جلب القضايا
        cases = Case.query.all()

        # تحليل حسب المحكمة
        court_stats = {}
        for case in cases:
            court = case.court or 'غير محدد'
            if court not in court_stats:
                court_stats[court] = {'count': 0, 'active': 0, 'closed': 0}
            court_stats[court]['count'] += 1
            if case.status in ['نشطة', 'قيد النظر']:
                court_stats[court]['active'] += 1
            elif case.status == 'مغلقة':
                court_stats[court]['closed'] += 1

        # تحليل حسب نوع القضية
        case_type_stats = {}
        for case in cases:
            case_type = case.case_type or 'عامة'
            if case_type not in case_type_stats:
                case_type_stats[case_type] = 0
            case_type_stats[case_type] += 1

        # تحليل حسب الحالة
        status_stats = {}
        for case in cases:
            status = case.status or 'غير محدد'
            if status not in status_stats:
                status_stats[status] = 0
            status_stats[status] += 1

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'total', 'label': 'إجمالي القضايا', 'value': len(cases)},
                    {'type': 'courts', 'label': 'عدد المحاكم', 'value': len(court_stats)},
                    {'type': 'case_types', 'label': 'أنواع القضايا', 'value': len(case_type_stats)}
                ],
                'court_analysis': [{'court': k, 'total': v['count'], 'active': v['active'], 'closed': v['closed']} for k, v in court_stats.items()],
                'type_analysis': [{'type': k, 'count': v} for k, v in case_type_stats.items()],
                'status_analysis': [{'status': k, 'count': v} for k, v in status_stats.items()]
            },
            'metadata': {
                'title': 'إحصائيات المحاكم',
                'description': 'تحليل القضايا حسب المحاكم والأنواع',
                'type': 'courts_statistics'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_fees_collection_report(filters):
    """إنشاء تقرير تحصيل الأتعاب"""
    try:
        # جلب الأتعاب
        fees = Fee.query.all()

        # إحصائيات أساسية
        total_fees = len(fees)
        total_amount = sum([f.total_amount for f in fees])
        paid_amount = sum([f.paid_amount for f in fees])
        remaining_amount = total_amount - paid_amount
        collection_rate = (paid_amount / total_amount * 100) if total_amount > 0 else 0

        # تحليل حسب العملة
        currency_stats = {}
        for fee in fees:
            currency = fee.currency or 'شيكل'
            if currency not in currency_stats:
                currency_stats[currency] = {'total': 0, 'paid': 0}
            currency_stats[currency]['total'] += fee.total_amount
            currency_stats[currency]['paid'] += fee.paid_amount

        # تحليل حسب طريقة الدفع
        payment_stats = {}
        for fee in fees:
            payment_method = fee.payment_method or 'نقدي'
            if payment_method not in payment_stats:
                payment_stats[payment_method] = 0
            payment_stats[payment_method] += fee.paid_amount

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'total', 'label': 'إجمالي الأتعاب', 'value': total_fees},
                    {'type': 'total_amount', 'label': 'إجمالي المبلغ', 'value': f'{total_amount:,.0f}'},
                    {'type': 'paid_amount', 'label': 'المبلغ المحصل', 'value': f'{paid_amount:,.0f}'},
                    {'type': 'remaining', 'label': 'المبلغ المتبقي', 'value': f'{remaining_amount:,.0f}'},
                    {'type': 'collection_rate', 'label': 'معدل التحصيل', 'value': f'{collection_rate:.1f}%'}
                ],
                'currency_analysis': [{'currency': k, 'total': v['total'], 'paid': v['paid']} for k, v in currency_stats.items()],
                'payment_analysis': [{'method': k, 'amount': v} for k, v in payment_stats.items()]
            },
            'metadata': {
                'title': 'تحصيل الأتعاب',
                'description': 'تقرير تحصيل الأتعاب والمدفوعات',
                'type': 'fees_collection'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== مسارات إضافية للتوافق ====================

@app.route('/rentals')
@login_required
def rentals():
    """صفحة الإيجارات - توجيه إلى العقارات"""
    return redirect(url_for('properties_list'))

@app.route('/expenses')
@login_required
def expenses():
    """صفحة المصاريف - توجيه إلى مصاريف المكتب"""
    return redirect(url_for('office_expenses'))


# إضافة رؤوس الحماية
@app.after_request
def add_security_headers(response):
    # منع XSS
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    
    # HTTPS Strict Transport Security
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    
    # Content Security Policy
    response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';"
    
    # منع تخزين المعلومات الحساسة
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    
    return response

# Middleware للحماية الإضافية
@app.before_request
def security_middleware():
    # منع الوصول من IPs مشبوهة
    blocked_ips = []  # قائمة IPs محظورة
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))

    if client_ip in blocked_ips:
        return "Access Denied", 403
    
    # حد معدل الطلبات
    if hasattr(request, 'endpoint') and request.endpoint:
        # تطبيق حد معدل الطلبات للصفحات الحساسة
        sensitive_endpoints = ['login', 'add_user', 'delete_user']
        if request.endpoint in sensitive_endpoints:
            # يمكن إضافة منطق حد معدل الطلبات هنا
            pass

import logging
from datetime import datetime

# إعداد نظام تسجيل الأحداث الأمنية
security_logger = logging.getLogger('security')
security_handler = logging.FileHandler('security.log')
security_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
security_handler.setFormatter(security_formatter)
security_logger.addHandler(security_handler)
security_logger.setLevel(logging.INFO)

def log_security_event(event_type, details, ip_address=None):
    """تسجيل حدث أمني"""
    if not ip_address:
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))

    message = f"{event_type} - IP: {ip_address} - Details: {details}"
    security_logger.info(message)

# ==================== إعدادات النظام ====================

@app.route('/settings')
@login_required
def settings():
    """صفحة الإعدادات"""
    # محاكاة بيانات الإعدادات
    settings_data = {
        'office_name': 'مكتب المحاماة',
        'office_address': '',
        'office_phone': '',
        'office_email': '',
        'default_currency': 'شيقل',
        'enable_notifications': True,
        'email_notifications': False,
        'appointment_reminder': 24,
        'task_reminder': 3,
        'session_timeout': 60,
        'require_strong_password': False,
        'enable_audit_log': True,
        'items_per_page': 25,
        'date_format': 'dd/mm/yyyy',
        'enable_dark_mode': False,
        'auto_backup': True
    }

    # معلومات النظام
    database_size = "15.2 MB"
    user_count = "2"
    last_backup = "2024-01-15 02:00"

    return render_template('settings/settings.html',
                         settings=settings_data,
                         database_size=database_size,
                         user_count=user_count,
                         last_backup=last_backup)

@app.route('/settings/update', methods=['POST'])
@login_required
def update_settings():
    """تحديث الإعدادات العامة"""
    try:
        # هنا يمكن حفظ الإعدادات في قاعدة البيانات
        flash('تم حفظ الإعدادات بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ الإعدادات: {str(e)}', 'danger')

    return redirect(url_for('settings'))

@app.route('/settings/notifications', methods=['POST'])
@login_required
def update_notification_settings():
    """تحديث إعدادات الإشعارات"""
    try:
        # هنا يمكن حفظ إعدادات الإشعارات
        flash('تم حفظ إعدادات الإشعارات بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ إعدادات الإشعارات: {str(e)}', 'danger')

    return redirect(url_for('settings'))

@app.route('/settings/security', methods=['POST'])
@login_required
def update_security_settings():
    """تحديث إعدادات الأمان"""
    try:
        # هنا يمكن حفظ إعدادات الأمان
        flash('تم حفظ إعدادات الأمان بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ إعدادات الأمان: {str(e)}', 'danger')

    return redirect(url_for('settings'))

@app.route('/settings/system', methods=['POST'])
@login_required
def update_system_settings():
    """تحديث إعدادات النظام"""
    try:
        # هنا يمكن حفظ إعدادات النظام
        flash('تم حفظ إعدادات النظام بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ إعدادات النظام: {str(e)}', 'danger')

    return redirect(url_for('settings'))

# ==================== النسخ الاحتياطي ====================

@app.route('/backup')
@login_required
def backup():
    """صفحة النسخ الاحتياطي"""
    # محاكاة بيانات النسخ الاحتياطية
    backups = [
        {
            'id': 1,
            'filename': 'backup_2024_01_15_02_00.sql',
            'created_at': datetime(2024, 1, 15, 2, 0),
            'size': '15.2 MB',
            'type': 'كامل'
        },
        {
            'id': 2,
            'filename': 'backup_2024_01_14_02_00.sql',
            'created_at': datetime(2024, 1, 14, 2, 0),
            'size': '14.8 MB',
            'type': 'كامل'
        }
    ]

    # إعدادات النسخ التلقائي
    auto_backup_enabled = True
    backup_frequency = 'daily'
    backup_time = '02:00'
    keep_backups = 10

    return render_template('settings/backup.html',
                         backups=backups,
                         auto_backup_enabled=auto_backup_enabled,
                         backup_frequency=backup_frequency,
                         backup_time=backup_time,
                         keep_backups=keep_backups)

@app.route('/backup/create', methods=['POST'])
@login_required
def create_backup_form():
    """إنشاء نسخة احتياطية من النموذج"""
    try:
        backup_type = request.form.get('backup_type', 'full')
        export_format = request.form.get('export_format', 'sql')
        include_files = 'include_files' in request.form

        # هنا يمكن تنفيذ عملية إنشاء النسخة الاحتياطية
        # وإرجاع الملف للتحميل

        flash('تم إنشاء النسخة الاحتياطية بنجاح', 'success')
        return redirect(url_for('backup'))

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}', 'danger')
        return redirect(url_for('backup'))

@app.route('/backup/restore', methods=['POST'])
@login_required
def restore_backup_form():
    """استعادة نسخة احتياطية من النموذج"""
    try:
        if 'backup_file' not in request.files:
            flash('لم يتم اختيار ملف', 'danger')
            return redirect(url_for('backup'))

        file = request.files['backup_file']
        if file.filename == '':
            flash('لم يتم اختيار ملف', 'danger')
            return redirect(url_for('backup'))

        # هنا يمكن تنفيذ عملية استعادة النسخة الاحتياطية

        flash('تم استعادة النسخة الاحتياطية بنجاح', 'success')
        return redirect(url_for('backup'))

    except Exception as e:
        flash(f'حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}', 'danger')
        return redirect(url_for('backup'))

@app.route('/backup/download/<int:backup_id>')
@login_required
def download_backup(backup_id):
    """تحميل نسخة احتياطية"""
    try:
        # هنا يمكن تنفيذ عملية تحميل النسخة الاحتياطية
        flash('سيتم تحميل النسخة الاحتياطية قريباً', 'info')
        return redirect(url_for('backup'))
    except Exception as e:
        flash(f'حدث خطأ أثناء تحميل النسخة الاحتياطية: {str(e)}', 'danger')
        return redirect(url_for('backup'))

@app.route('/backup/delete/<int:backup_id>', methods=['DELETE'])
@login_required
def delete_backup(backup_id):
    """حذف نسخة احتياطية"""
    try:
        # هنا يمكن تنفيذ عملية حذف النسخة الاحتياطية
        return jsonify({'success': True, 'message': 'تم حذف النسخة الاحتياطية بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@app.route('/backup/auto-settings', methods=['POST'])
@login_required
def update_auto_backup_settings():
    """تحديث إعدادات النسخ التلقائي"""
    try:
        # هنا يمكن حفظ إعدادات النسخ التلقائي
        flash('تم حفظ إعدادات النسخ التلقائي بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ إعدادات النسخ التلقائي: {str(e)}', 'danger')

    return redirect(url_for('backup'))
