from flask import render_template, redirect, url_for, flash, request, send_from_directory, jsonify, send_file, session
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
import time
import hashlib
from functools import wraps
from .models import User, Case, Client, Task, Appointment, Property, Tenant, Lease, LeaseInstallment, FinancialTransaction, Document, ClientDocument, Debt, DebtPayment, RentalIncome, Expense, Fee, FeePayment, Event
from .archive_models import ArchivedCase, ArchivedClient, ArchivedDocument, SystemSettings, ArchiveRule, BackupLog, BackupSchedule, get_archive_statistics, get_system_info
from .models import Notification, NotificationSettings
from .settings_service import SettingsService
from .backup_service import BackupService
from datetime import datetime, timedelta, timezone
import tempfile
import zipfile
import os
from . import app, db, csrf
from sqlalchemy import func
import shutil
import os

UPLOAD_FOLDER = os.path.join(os.path.dirname(__file__), 'uploads')

# نظام حماية من Brute Force
login_attempts = {}
FAILED_LOGIN_LIMIT = 5
LOCKOUT_DURATION = 300  # 5 دقائق

def rate_limit_login(f):
    """ديكوريتر للحماية من Brute Force"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))
        current_time = time.time()

        # تنظيف المحاولات القديمة
        if client_ip in login_attempts:
            login_attempts[client_ip] = [attempt for attempt in login_attempts[client_ip]
                                       if current_time - attempt < LOCKOUT_DURATION]

        # فحص عدد المحاولات
        if client_ip in login_attempts and len(login_attempts[client_ip]) >= FAILED_LOGIN_LIMIT:
            flash('تم حظر IP الخاص بك مؤقتاً بسبب محاولات تسجيل دخول متكررة. حاول مرة أخرى بعد 5 دقائق.', 'danger')
            return redirect(url_for('login'))

        return f(*args, **kwargs)
    return decorated_function

def record_failed_login():
    """تسجيل محاولة تسجيل دخول فاشلة"""
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))
    current_time = time.time()

    if client_ip not in login_attempts:
        login_attempts[client_ip] = []

    login_attempts[client_ip].append(current_time)

def secure_session():
    """تأمين الجلسة"""
    session.permanent = True
    session['last_activity'] = time.time()
    session['user_agent'] = request.headers.get('User-Agent', '')
    session['ip_address'] = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))

def validate_session():
    """التحقق من صحة الجلسة"""
    if not current_user.is_authenticated:
        return False

    # فحص انتهاء صلاحية الجلسة (30 دقيقة)
    if 'last_activity' in session:
        if time.time() - session['last_activity'] > 1800:  # 30 دقيقة
            logout_user()
            flash('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.', 'warning')
            return False
        session['last_activity'] = time.time()

    # فحص تغيير User Agent
    if 'user_agent' in session and session['user_agent'] != request.headers.get('User-Agent', ''):
        logout_user()
        flash('تم اكتشاف نشاط مشبوه. يرجى تسجيل الدخول مرة أخرى.', 'danger')
        return False

    return True
ALLOWED_EXTENSIONS = {'pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'xls', 'xlsx'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    from .models import User
    last_login = User.query.order_by(User.last_login.desc()).with_entities(User.last_login).first()
    last_login_str = last_login[0].strftime('%Y-%m-%d %H:%M') if last_login and last_login[0] else None
    return render_template('index.html', last_login=last_login_str)

@app.route('/lawyersameh', methods=['GET', 'POST'])
@csrf.exempt
@rate_limit_login
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        user = User.query.filter_by(username=username).first()
        # التحقق من كلمة المرور بطرق متعددة
        password_valid = False
        if user:
            # مقارنة مباشرة
            if user.password == password:
                password_valid = True
            # SHA256 hash
            elif user.password == hashlib.sha256(password.encode()).hexdigest():
                password_valid = True
            # Werkzeug hash
            elif check_password_hash(user.password, password):
                password_valid = True

        if user and password_valid:
            login_user(user)
            user.last_login = datetime.now()
            db.session.commit()
            # تأمين الجلسة
            secure_session()
            # مسح محاولات تسجيل الدخول الفاشلة عند النجاح
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))
            if client_ip in login_attempts:
                del login_attempts[client_ip]
            return redirect(url_for('dashboard'))
        else:
            # تسجيل محاولة فاشلة
            record_failed_login()
            flash('بيانات الدخول غير صحيحة', 'danger')
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    # نسخ احتياطي تلقائي لقاعدة البيانات
    db_path = os.path.join(os.getcwd(), 'instance', 'lawoffice.db')
    backup_dir = os.path.join(os.getcwd(), 'instance', 'backups')
    os.makedirs(backup_dir, exist_ok=True)
    from datetime import datetime
    backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    backup_path = os.path.join(backup_dir, backup_name)
    if os.path.exists(db_path):
        shutil.copy2(db_path, backup_path)
    logout_user()
    return redirect(url_for('login'))

@app.route('/cases')
@login_required
def cases_list():
    q = request.args.get('q', '').strip()
    client_role = request.args.get('client_role', '').strip()

    cases_query = Case.query

    # فلترة النص
    if q:
        cases_query = cases_query.filter(
            (Case.case_number.ilike(f'%{q}%')) |
            (Case.title.ilike(f'%{q}%')) |
            (Case.status.ilike(f'%{q}%')) |
            (Case.court.ilike(f'%{q}%')) |
            (Case.opponent.ilike(f'%{q}%'))
        )

    # فلترة صفة الموكل
    if client_role:
        cases_query = cases_query.filter(Case.client_role == client_role)

    cases = cases_query.all()
    return render_template('cases/list.html', cases=cases)

@app.route('/cases/add', methods=['GET', 'POST'])
@login_required
def add_case():
    from .models import Fee, CourtFee
    clients = Client.query.all()
    if request.method == 'POST':
        try:
            # البيانات الأساسية للقضية
            case_number = request.form['case_number']
            office_case_number = request.form.get('office_case_number', '')
            title = request.form['title']
            type_ = request.form['type']
            status = request.form['status']
            court = request.form['court']
            opponent = request.form['opponent']
            description = request.form['description']
            client_id = request.form['client_id']
            client_role = request.form.get('client_role', 'مدعي')  # صفة الموكل

            # البيانات المالية الجديدة
            fees_total = float(request.form.get('fees_total', 0) or 0)
            fees_paid = float(request.form.get('fees_paid', 0) or 0)
            fees_currency = request.form.get('fees_currency', 'شيكل')  # عملة الأتعاب
            court_fees_total = float(request.form.get('court_fees_total', 0) or 0)
            court_fees_paid = float(request.form.get('court_fees_paid', 0) or 0)
            priority = request.form.get('priority', 'متوسطة')
            case_value = float(request.form.get('case_value', 0) or 0)
            currency = request.form.get('currency', 'شيكل')

            # حساب المتبقي
            fees_remaining = fees_total - fees_paid
            court_fees_remaining = court_fees_total - court_fees_paid

            # إنشاء القضية
            case = Case(
                case_number=case_number, office_case_number=office_case_number,
                title=title, type=type_, status=status,
                court=court, opponent=opponent, description=description,
                client_id=client_id, lawyer_id=current_user.id,
                client_role=client_role, fees_currency=fees_currency,
                fees_total=fees_total, fees_paid=fees_paid, fees_remaining=fees_remaining,
                court_fees_total=court_fees_total, court_fees_paid=court_fees_paid,
                court_fees_remaining=court_fees_remaining, priority=priority,
                case_value=case_value, currency=currency
            )
            db.session.add(case)
            db.session.flush()  # للحصول على ID القضية

            # إنشاء سجل الأتعاب إذا كان هناك مبلغ
            if fees_total > 0:
                fee = Fee(
                    case_id=case.id, client_id=client_id, amount=fees_total,
                    paid_amount=fees_paid, remaining_amount=fees_remaining,
                    fee_type='أتعاب محاماة', status='مستحقة' if fees_remaining > 0 else 'مدفوعة'
                )
                db.session.add(fee)

            # إنشاء سجل الرسوم إذا كان هناك مبلغ
            if court_fees_total > 0:
                court_fee = CourtFee(
                    case_id=case.id, client_id=client_id, amount=court_fees_total,
                    paid_amount=court_fees_paid, remaining_amount=court_fees_remaining,
                    fee_type='رسوم قضائية', status='مستحقة' if court_fees_remaining > 0 else 'مدفوعة'
                )
                db.session.add(court_fee)

            # إضافة المعاملات المالية
            if fees_paid > 0:
                fee_transaction = FinancialTransaction(
                    amount=fees_paid, date=datetime.now(), type='أتعاب',
                    description=f'دفعة أتعاب للقضية: {title}',
                    case_id=case.id, client_id=client_id, currency=currency
                )
                db.session.add(fee_transaction)

            if court_fees_paid > 0:
                court_fee_transaction = FinancialTransaction(
                    amount=court_fees_paid, date=datetime.now(), type='رسوم',
                    description=f'دفعة رسوم للقضية: {title}',
                    case_id=case.id, client_id=client_id, currency=currency
                )
                db.session.add(court_fee_transaction)

            db.session.commit()
            backup_db_file()

            # إذا كان الطلب من AJAX، إرجاع JSON
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': True, 'message': 'تمت إضافة القضية بنجاح'})

            flash('تمت إضافة القضية بنجاح مع البيانات المالية', 'success')
            return redirect(url_for('cases_list'))

        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
            flash(f'حدث خطأ أثناء إضافة القضية: {str(e)}', 'danger')
            return redirect(url_for('cases_list'))
    if not clients:
        flash('يجب إضافة عميل/موكل أولاً قبل إضافة قضية جديدة.', 'warning')
        return redirect(url_for('clients_list'))
    return render_template('cases/add.html', clients=clients)

@app.route('/cases/<int:case_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_case(case_id):
    from .models import Fee, CourtFee
    case = Case.query.get_or_404(case_id)
    clients = Client.query.all()
    # جلب المهام والمواعيد المرتبطة بالقضية
    case_tasks = Task.query.filter_by(case_id=case.id).order_by(Task.due_date.asc()).all() if hasattr(Task, 'case_id') else []
    case_appointments = Appointment.query.filter_by(case_id=case.id).order_by(Appointment.date.asc()).all()
    if request.method == 'POST':
        # البيانات الأساسية
        case.case_number = request.form['case_number']
        case.office_case_number = request.form.get('office_case_number', '')
        case.title = request.form['title']
        case.type = request.form['type']
        case.status = request.form['status']
        case.court = request.form['court']
        case.opponent = request.form['opponent']
        case.description = request.form['description']
        case.client_id = request.form['client_id']
        case.client_role = request.form.get('client_role', 'مدعي')  # صفة الموكل

        # البيانات المالية الجديدة
        old_fees_total = case.fees_total or 0
        old_fees_paid = case.fees_paid or 0
        old_court_fees_total = case.court_fees_total or 0
        old_court_fees_paid = case.court_fees_paid or 0

        case.fees_total = float(request.form.get('fees_total', 0) or 0)
        case.fees_paid = float(request.form.get('fees_paid', 0) or 0)
        case.fees_currency = request.form.get('fees_currency', 'شيكل')  # عملة الأتعاب
        case.court_fees_total = float(request.form.get('court_fees_total', 0) or 0)
        case.court_fees_paid = float(request.form.get('court_fees_paid', 0) or 0)
        case.priority = request.form.get('priority', 'متوسطة')
        case.case_value = float(request.form.get('case_value', 0) or 0)
        case.currency = request.form.get('currency', 'شيكل')

        # حساب المتبقي
        case.fees_remaining = case.fees_total - case.fees_paid
        case.court_fees_remaining = case.court_fees_total - case.court_fees_paid

        # تحديث سجلات الأتعاب والرسوم
        fee = Fee.query.filter_by(case_id=case.id).first()
        if case.fees_total > 0:
            if fee:
                fee.amount = case.fees_total
                fee.paid_amount = case.fees_paid
                fee.remaining_amount = case.fees_remaining
                fee.status = 'مستحقة' if case.fees_remaining > 0 else 'مدفوعة'
            else:
                fee = Fee(
                    case_id=case.id, client_id=case.client_id, amount=case.fees_total,
                    paid_amount=case.fees_paid, remaining_amount=case.fees_remaining,
                    fee_type='أتعاب محاماة', status='مستحقة' if case.fees_remaining > 0 else 'مدفوعة'
                )
                db.session.add(fee)
        elif fee:
            db.session.delete(fee)

        court_fee = CourtFee.query.filter_by(case_id=case.id).first()
        if case.court_fees_total > 0:
            if court_fee:
                court_fee.amount = case.court_fees_total
                court_fee.paid_amount = case.court_fees_paid
                court_fee.remaining_amount = case.court_fees_remaining
                court_fee.status = 'مستحقة' if case.court_fees_remaining > 0 else 'مدفوعة'
            else:
                court_fee = CourtFee(
                    case_id=case.id, client_id=case.client_id, amount=case.court_fees_total,
                    paid_amount=case.court_fees_paid, remaining_amount=case.court_fees_remaining,
                    fee_type='رسوم قضائية', status='مستحقة' if case.court_fees_remaining > 0 else 'مدفوعة'
                )
                db.session.add(court_fee)
        elif court_fee:
            db.session.delete(court_fee)

        # إضافة معاملات مالية للمبالغ الجديدة المدفوعة
        new_fees_paid = case.fees_paid - old_fees_paid
        new_court_fees_paid = case.court_fees_paid - old_court_fees_paid

        if new_fees_paid > 0:
            fee_transaction = FinancialTransaction(
                amount=new_fees_paid, date=datetime.now(), type='أتعاب',
                description=f'دفعة أتعاب إضافية للقضية: {case.title}',
                case_id=case.id, client_id=case.client_id, currency=case.currency
            )
            db.session.add(fee_transaction)

        if new_court_fees_paid > 0:
            court_fee_transaction = FinancialTransaction(
                amount=new_court_fees_paid, date=datetime.now(), type='رسوم',
                description=f'دفعة رسوم إضافية للقضية: {case.title}',
                case_id=case.id, client_id=case.client_id, currency=case.currency
            )
            db.session.add(court_fee_transaction)

        db.session.commit()
        backup_db_file()
        flash('تم تعديل بيانات القضية والبيانات المالية', 'success')
        return redirect(url_for('cases_list'))
    return render_template('cases/edit.html', case=case, clients=clients, case_tasks=case_tasks, case_appointments=case_appointments)

@app.route('/cases/<int:case_id>/delete', methods=['POST'])
@login_required
def delete_case(case_id):
    case = Case.query.get_or_404(case_id)
    db.session.delete(case)
    db.session.commit()
    backup_db_file()
    flash('تم حذف القضية', 'success')
    return redirect(url_for('cases_list'))

# ==================== PROPERTY MANAGEMENT ROUTES ====================

@app.route('/properties')
@login_required
def properties():
    """عرض قائمة العقارات مع البحث والفلترة"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    property_type = request.args.get('property_type', '')
    city = request.args.get('city', '')

    # بناء الاستعلام
    query = Property.query

    # تطبيق الفلاتر
    if search:
        query = query.filter(
            db.or_(
                Property.name.contains(search),
                Property.property_code.contains(search),
                Property.description.contains(search),
                Property.city.contains(search),
                Property.district.contains(search)
            )
        )

    if status:
        query = query.filter(Property.status == status)

    if property_type:
        query = query.filter(Property.property_type == property_type)

    if city:
        query = query.filter(Property.city == city)

    # ترتيب النتائج
    query = query.order_by(Property.created_at.desc())

    # تقسيم الصفحات
    properties = query.paginate(
        page=page, per_page=12, error_out=False
    )

    # إحصائيات
    total_properties = Property.query.count()
    available_properties = Property.query.filter_by(status='متاح').count()
    rented_properties = Property.query.filter_by(status='مؤجر').count()

    # حساب الدخل الشهري
    total_income = db.session.query(db.func.sum(Property.monthly_rent)).filter_by(status='مؤجر').scalar() or 0

    # قائمة المدن للفلتر
    cities = db.session.query(Property.city).distinct().all()
    cities = [city[0] for city in cities if city[0]]

    return render_template('properties/properties.html',
                         properties=properties,
                         total_properties=total_properties,
                         available_properties=available_properties,
                         rented_properties=rented_properties,
                         total_income=total_income,
                         cities=cities)

@app.route('/properties/enhanced')
@login_required
def properties_enhanced():
    """واجهة إدارة العقارات المحسنة"""
    search = request.args.get('search', '')
    property_type = request.args.get('type', '')
    status = request.args.get('status', '')
    city = request.args.get('city', '')
    min_price = request.args.get('min_price', type=float)
    max_price = request.args.get('max_price', type=float)

    # بناء الاستعلام
    query = Property.query

    if search:
        query = query.filter(
            db.or_(
                Property.name.contains(search),
                Property.address.contains(search),
                Property.property_code.contains(search),
                Property.district.contains(search)
            )
        )

    if property_type:
        query = query.filter(Property.property_type == property_type)

    if status:
        query = query.filter(Property.status == status)

    if city:
        query = query.filter(Property.city == city)

    if min_price:
        query = query.filter(Property.monthly_rent >= min_price)

    if max_price:
        query = query.filter(Property.monthly_rent <= max_price)

    # ترتيب النتائج
    properties = query.order_by(Property.created_date.desc()).all()

    # حساب الإحصائيات
    total_properties = Property.query.count()
    available_properties = Property.query.filter(Property.status == 'متاح').count()
    rented_properties = Property.query.filter(Property.status == 'مؤجر').count()
    maintenance_properties = Property.query.filter(Property.status == 'صيانة').count()

    # الحصول على قائمة المدن للفلتر
    cities = db.session.query(Property.city).distinct().filter(Property.city.isnot(None)).all()
    cities = [city[0] for city in cities if city[0]]

    return render_template('properties/properties_enhanced.html',
                         properties=properties,
                         total_properties=total_properties,
                         available_properties=available_properties,
                         rented_properties=rented_properties,
                         maintenance_properties=maintenance_properties,
                         cities=cities)

@app.route('/properties/add', methods=['GET', 'POST'])
@login_required
def add_property():
    """إضافة عقار جديد"""
    if request.method == 'POST':
        try:
            # إنشاء عقار جديد
            property = Property(
                property_code=request.form.get('property_code'),
                name=request.form.get('name'),
                property_type=request.form.get('property_type'),
                category=request.form.get('category'),
                description=request.form.get('description'),

                # الموقع
                city=request.form.get('city'),
                district=request.form.get('district'),
                street=request.form.get('street'),
                building_number=request.form.get('building_number'),
                floor_number=request.form.get('floor_number', type=int),
                apartment_number=request.form.get('apartment_number'),
                postal_code=request.form.get('postal_code'),
                latitude=request.form.get('latitude', type=float),
                longitude=request.form.get('longitude', type=float),

                # تفاصيل العقار
                area=request.form.get('area', type=float),
                bedrooms=request.form.get('bedrooms', type=int),
                bathrooms=request.form.get('bathrooms', type=int),
                living_rooms=request.form.get('living_rooms', type=int),
                kitchens=request.form.get('kitchens', type=int),
                parking_spaces=request.form.get('parking_spaces', type=int),

                # المرافق
                has_elevator=bool(request.form.get('has_elevator')),
                has_balcony=bool(request.form.get('has_balcony')),
                has_garden=bool(request.form.get('has_garden')),
                has_pool=bool(request.form.get('has_pool')),
                has_ac=bool(request.form.get('has_ac')),
                has_heating=bool(request.form.get('has_heating')),
                furnished=bool(request.form.get('furnished')),
                has_security=bool(request.form.get('has_security')),

                # المعلومات المالية
                monthly_rent=request.form.get('monthly_rent', type=float),
                annual_rent=request.form.get('annual_rent', type=float),
                security_deposit=request.form.get('security_deposit', type=float),
                commission_rate=request.form.get('commission_rate', type=float),
                property_value=request.form.get('property_value', type=float),
                currency=request.form.get('currency', 'JOD'),

                # معلومات المالك
                owner_name=request.form.get('owner_name'),
                owner_phone=request.form.get('owner_phone'),
                owner_email=request.form.get('owner_email'),
                owner_id_number=request.form.get('owner_id_number'),
                owner_address=request.form.get('owner_address'),

                # معلومات النظام
                status='متاح',
                created_by=current_user.id,
                created_at=datetime.now()
            )

            db.session.add(property)
            db.session.commit()
            backup_db_file()

            flash('تم إضافة العقار بنجاح', 'success')
            return redirect(url_for('property_details', id=property.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة العقار: {str(e)}', 'error')
            return redirect(url_for('properties'))

    return redirect(url_for('properties'))

@app.route('/properties/<int:id>')
@login_required
def property_details(id):
    """عرض تفاصيل العقار"""
    property = Property.query.get_or_404(id)

    # البحث عن العقد الحالي
    current_lease = Lease.query.filter_by(
        property_id=id,
        status='نشط'
    ).first()

    property.current_lease = current_lease

    return render_template('properties/property_details.html', property=property)

@app.route('/properties/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_property(id):
    """تعديل العقار"""
    property = Property.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # تحديث البيانات الأساسية
            property.property_code = request.form.get('property_code')
            property.name = request.form.get('name')
            property.property_type = request.form.get('property_type')
            property.category = request.form.get('category')
            property.description = request.form.get('description')
            property.status = request.form.get('status')

            # تحديث الموقع
            property.city = request.form.get('city')
            property.district = request.form.get('district')
            property.street = request.form.get('street')
            property.building_number = request.form.get('building_number')
            property.floor_number = request.form.get('floor_number', type=int)
            property.apartment_number = request.form.get('apartment_number')
            property.postal_code = request.form.get('postal_code')
            property.latitude = request.form.get('latitude', type=float)
            property.longitude = request.form.get('longitude', type=float)

            # تحديث تفاصيل العقار
            property.area = request.form.get('area', type=float)
            property.bedrooms = request.form.get('bedrooms', type=int)
            property.bathrooms = request.form.get('bathrooms', type=int)
            property.living_rooms = request.form.get('living_rooms', type=int)
            property.kitchens = request.form.get('kitchens', type=int)
            property.parking_spaces = request.form.get('parking_spaces', type=int)

            # تحديث المرافق
            property.has_elevator = bool(request.form.get('has_elevator'))
            property.has_balcony = bool(request.form.get('has_balcony'))
            property.has_garden = bool(request.form.get('has_garden'))
            property.has_pool = bool(request.form.get('has_pool'))
            property.has_ac = bool(request.form.get('has_ac'))
            property.has_heating = bool(request.form.get('has_heating'))
            property.furnished = bool(request.form.get('furnished'))
            property.has_security = bool(request.form.get('has_security'))

            # تحديث المعلومات المالية
            property.monthly_rent = request.form.get('monthly_rent', type=float)
            property.annual_rent = request.form.get('annual_rent', type=float)
            property.security_deposit = request.form.get('security_deposit', type=float)
            property.commission_rate = request.form.get('commission_rate', type=float)
            property.property_value = request.form.get('property_value', type=float)
            property.currency = request.form.get('currency', 'JOD')

            # تحديث معلومات المالك
            property.owner_name = request.form.get('owner_name')
            property.owner_phone = request.form.get('owner_phone')
            property.owner_email = request.form.get('owner_email')
            property.owner_id_number = request.form.get('owner_id_number')
            property.owner_address = request.form.get('owner_address')

            # تحديث معلومات النظام
            property.updated_by = current_user.id
            property.updated_at = datetime.now()

            db.session.commit()
            backup_db_file()

            flash('تم تحديث العقار بنجاح', 'success')
            return redirect(url_for('property_details', id=property.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث العقار: {str(e)}', 'error')

    return render_template('properties/edit_property.html', property=property)

@app.route('/properties/<int:id>/delete', methods=['DELETE'])
@login_required
def delete_property(id):
    """حذف العقار"""
    try:
        property = Property.query.get_or_404(id)

        # التحقق من وجود عقود نشطة
        active_leases = Lease.query.filter_by(property_id=id, status='نشط').count()
        if active_leases > 0:
            return jsonify({
                'success': False,
                'message': 'لا يمكن حذف العقار لوجود عقود إيجار نشطة'
            })

        # حذف العقار
        db.session.delete(property)
        db.session.commit()
        backup_db_file()

        return jsonify({
            'success': True,
            'message': 'تم حذف العقار بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء الحذف: {str(e)}'
        })

@app.route('/properties/<int:id>/status', methods=['POST'])
@login_required
def update_property_status(id):
    """تحديث حالة العقار"""
    try:
        property = Property.query.get_or_404(id)
        new_status = request.json.get('status')

        if new_status not in ['متاح', 'مؤجر', 'صيانة']:
            return jsonify({
                'success': False,
                'message': 'حالة غير صحيحة'
            })

        property.status = new_status
        property.updated_by = current_user.id
        property.updated_at = datetime.now()

        db.session.commit()
        backup_db_file()

        return jsonify({
            'success': True,
            'message': 'تم تحديث حالة العقار بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء التحديث: {str(e)}'
        })

@app.route('/modal_add_property', methods=['GET', 'POST'])
@login_required
def modal_add_property():
    """مودال إضافة عقار سريع"""
    if request.method == 'POST':
        try:
            # إنشاء عقار جديد بالبيانات الأساسية
            property = Property(
                property_code=request.form.get('property_code'),
                name=request.form.get('name'),
                property_type=request.form.get('property_type'),
                city=request.form.get('city'),
                district=request.form.get('district'),
                monthly_rent=request.form.get('monthly_rent', type=float),
                status='متاح',
                created_by=current_user.id,
                created_at=datetime.now()
            )

            db.session.add(property)
            db.session.commit()
            backup_db_file()

            return jsonify({
                'success': True,
                'message': 'تم إضافة العقار بنجاح',
                'property_id': property.id,
                'property_name': property.name
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ أثناء إضافة العقار: {str(e)}'
            })

    return jsonify({'success': False, 'message': 'طريقة غير مدعومة'})

# ==================== ADVANCED PROPERTY APIs ====================

@app.route('/api/properties/search', methods=['POST'])
@login_required
def api_search_properties():
    """API للبحث المتقدم في العقارات"""
    try:
        data = request.get_json()

        query = Property.query

        # فلاتر البحث
        if data.get('search'):
            search_term = f"%{data['search']}%"
            query = query.filter(
                db.or_(
                    Property.name.like(search_term),
                    Property.property_code.like(search_term),
                    Property.address.like(search_term),
                    Property.district.like(search_term)
                )
            )

        if data.get('property_type'):
            query = query.filter(Property.type == data['property_type'])

        if data.get('status'):
            query = query.filter(Property.status == data['status'])

        if data.get('city'):
            query = query.filter(Property.city == data['city'])

        if data.get('min_rent'):
            query = query.filter(Property.monthly_rent >= data['min_rent'])

        if data.get('max_rent'):
            query = query.filter(Property.monthly_rent <= data['max_rent'])

        if data.get('min_area'):
            query = query.filter(Property.area >= data['min_area'])

        if data.get('max_area'):
            query = query.filter(Property.area <= data['max_area'])

        if data.get('rooms_count'):
            query = query.filter(Property.rooms_count >= data['rooms_count'])

        if data.get('furnished') is not None:
            query = query.filter(Property.furnished == data['furnished'])

        # ترتيب النتائج
        sort_by = data.get('sort_by', 'created_date')
        sort_order = data.get('sort_order', 'desc')

        if sort_order == 'desc':
            query = query.order_by(getattr(Property, sort_by).desc())
        else:
            query = query.order_by(getattr(Property, sort_by))

        # تطبيق التصفح
        page = data.get('page', 1)
        per_page = data.get('per_page', 10)

        properties = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # تحويل النتائج إلى JSON
        results = []
        for prop in properties.items:
            results.append({
                'id': prop.id,
                'name': prop.name,
                'property_code': prop.property_code,
                'type': prop.type,
                'address': prop.address,
                'city': prop.city,
                'district': prop.district,
                'area': prop.area,
                'rooms_count': prop.rooms_count,
                'monthly_rent': prop.monthly_rent,
                'currency': prop.currency,
                'status': prop.status,
                'furnished': prop.furnished,
                'created_date': prop.created_date.isoformat() if prop.created_date else None
            })

        return jsonify({
            'success': True,
            'properties': results,
            'pagination': {
                'page': properties.page,
                'pages': properties.pages,
                'per_page': properties.per_page,
                'total': properties.total,
                'has_next': properties.has_next,
                'has_prev': properties.has_prev
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في البحث: {str(e)}'
        }), 500

@app.route('/api/properties/<int:id>/images', methods=['GET', 'POST', 'DELETE'])
@login_required
def api_property_images(id):
    """API لإدارة صور العقارات"""
    property = Property.query.get_or_404(id)

    if request.method == 'GET':
        # جلب صور العقار
        images = PropertyImage.query.filter_by(property_id=id).all()
        return jsonify({
            'success': True,
            'images': [{
                'id': img.id,
                'image_name': img.image_name,
                'image_path': img.image_path,
                'image_url': img.image_url,
                'is_primary': img.is_primary,
                'description': img.description,
                'uploaded_date': img.uploaded_date.isoformat() if img.uploaded_date else None
            } for img in images]
        })

    elif request.method == 'POST':
        # رفع صورة جديدة
        try:
            if 'image' not in request.files:
                return jsonify({'success': False, 'message': 'لم يتم اختيار صورة'}), 400

            file = request.files['image']
            if file.filename == '':
                return jsonify({'success': False, 'message': 'لم يتم اختيار صورة'}), 400

            # حفظ الصورة (يمكن تحسين هذا لاحقاً)
            filename = f"property_{id}_{file.filename}"
            file_path = f"static/uploads/properties/{filename}"

            # إنشاء مجلد إذا لم يكن موجوداً
            import os
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            file.save(file_path)

            # إضافة الصورة إلى قاعدة البيانات
            new_image = PropertyImage(
                property_id=id,
                image_name=file.filename,
                image_path=file_path,
                image_url=f"/static/uploads/properties/{filename}",
                description=request.form.get('description', ''),
                is_primary=request.form.get('is_primary', 'false').lower() == 'true'
            )

            db.session.add(new_image)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم رفع الصورة بنجاح',
                'image': {
                    'id': new_image.id,
                    'image_name': new_image.image_name,
                    'image_url': new_image.image_url,
                    'is_primary': new_image.is_primary
                }
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ أثناء رفع الصورة: {str(e)}'
            }), 500

    elif request.method == 'DELETE':
        # حذف صورة
        try:
            image_id = request.json.get('image_id')
            if not image_id:
                return jsonify({'success': False, 'message': 'معرف الصورة مطلوب'}), 400

            image = PropertyImage.query.filter_by(id=image_id, property_id=id).first()
            if not image:
                return jsonify({'success': False, 'message': 'الصورة غير موجودة'}), 404

            # حذف الملف من النظام
            import os
            if os.path.exists(image.image_path):
                os.remove(image.image_path)

            db.session.delete(image)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم حذف الصورة بنجاح'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ أثناء حذف الصورة: {str(e)}'
            }), 500

# ==================== ADVANCED TENANT APIs ====================

@app.route('/api/tenants/search', methods=['POST'])
@login_required
def api_search_tenants():
    """API للبحث المتقدم في المستأجرين"""
    try:
        data = request.get_json()

        query = Tenant.query

        # فلاتر البحث
        if data.get('search'):
            search_term = f"%{data['search']}%"
            query = query.filter(
                db.or_(
                    Tenant.full_name.like(search_term),
                    Tenant.phone_number.like(search_term),
                    Tenant.national_id.like(search_term),
                    Tenant.passport_number.like(search_term),
                    Tenant.email.like(search_term)
                )
            )

        if data.get('status'):
            query = query.filter(Tenant.status == data['status'])

        if data.get('nationality'):
            query = query.filter(Tenant.nationality == data['nationality'])

        if data.get('min_credit_score'):
            query = query.filter(Tenant.credit_score >= data['min_credit_score'])

        if data.get('max_credit_score'):
            query = query.filter(Tenant.credit_score <= data['max_credit_score'])

        # ترتيب النتائج
        sort_by = data.get('sort_by', 'created_date')
        sort_order = data.get('sort_order', 'desc')

        if sort_order == 'desc':
            query = query.order_by(getattr(Tenant, sort_by).desc())
        else:
            query = query.order_by(getattr(Tenant, sort_by))

        # تطبيق التصفح
        page = data.get('page', 1)
        per_page = data.get('per_page', 10)

        tenants = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # تحويل النتائج إلى JSON
        results = []
        for tenant in tenants.items:
            results.append({
                'id': tenant.id,
                'full_name': tenant.full_name,
                'phone_number': tenant.phone_number,
                'email': tenant.email,
                'national_id': tenant.national_id,
                'passport_number': tenant.passport_number,
                'nationality': tenant.nationality,
                'status': tenant.status,
                'credit_score': tenant.credit_score,
                'created_date': tenant.created_date.isoformat() if tenant.created_date else None,
                'active_leases_count': tenant.leases.filter_by(status='نشط').count(),
                'total_leases_count': tenant.leases.count()
            })

        return jsonify({
            'success': True,
            'tenants': results,
            'pagination': {
                'page': tenants.page,
                'pages': tenants.pages,
                'per_page': tenants.per_page,
                'total': tenants.total,
                'has_next': tenants.has_next,
                'has_prev': tenants.has_prev
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في البحث: {str(e)}'
        }), 500

@app.route('/api/tenants/<int:id>/credit-score', methods=['PUT'])
@login_required
def api_update_tenant_credit_score(id):
    """API لتحديث التقييم الائتماني للمستأجر"""
    try:
        tenant = Tenant.query.get_or_404(id)
        data = request.get_json()

        new_score = data.get('credit_score')
        if not new_score or not (300 <= new_score <= 850):
            return jsonify({
                'success': False,
                'message': 'التقييم الائتماني يجب أن يكون بين 300 و 850'
            }), 400

        old_score = tenant.credit_score
        tenant.credit_score = new_score
        tenant.updated_date = datetime.now()

        # إضافة سجل في التدقيق
        audit_log = AuditLog(
            table_name='tenants',
            record_id=tenant.id,
            action='تحديث التقييم الائتماني',
            old_values=f'التقييم القديم: {old_score}',
            new_values=f'التقييم الجديد: {new_score}',
            user_id=current_user.id,
            timestamp=datetime.now()
        )

        db.session.add(audit_log)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم تحديث التقييم الائتماني بنجاح',
            'old_score': old_score,
            'new_score': new_score
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء التحديث: {str(e)}'
        }), 500

@app.route('/api/tenants/<int:id>/communication', methods=['GET', 'POST'])
@login_required
def api_tenant_communication(id):
    """API لإدارة مراسلات المستأجر"""
    tenant = Tenant.query.get_or_404(id)

    if request.method == 'GET':
        # جلب مراسلات المستأجر
        communications = TenantCommunication.query.filter_by(tenant_id=id)\
                                                 .order_by(TenantCommunication.communication_date.desc())\
                                                 .all()
        return jsonify({
            'success': True,
            'communications': [{
                'id': comm.id,
                'communication_type': comm.communication_type,
                'subject': comm.subject,
                'content': comm.content,
                'communication_date': comm.communication_date.isoformat() if comm.communication_date else None,
                'response_required': comm.response_required,
                'response_date': comm.response_date.isoformat() if comm.response_date else None,
                'status': comm.status,
                'priority': comm.priority
            } for comm in communications]
        })

    elif request.method == 'POST':
        # إضافة مراسلة جديدة
        try:
            data = request.get_json()

            new_communication = TenantCommunication(
                tenant_id=id,
                communication_type=data.get('communication_type', 'عام'),
                subject=data.get('subject', ''),
                content=data.get('content', ''),
                communication_date=datetime.now(),
                response_required=data.get('response_required', False),
                status=data.get('status', 'جديد'),
                priority=data.get('priority', 'متوسط'),
                created_by=current_user.id
            )

            db.session.add(new_communication)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم إضافة المراسلة بنجاح',
                'communication': {
                    'id': new_communication.id,
                    'subject': new_communication.subject,
                    'communication_date': new_communication.communication_date.isoformat()
                }
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ أثناء إضافة المراسلة: {str(e)}'
            }), 500

# ==================== ADVANCED LEASE APIs ====================

@app.route('/api/leases/search', methods=['POST'])
@login_required
def api_search_leases():
    """API للبحث المتقدم في العقود"""
    try:
        data = request.get_json()

        query = Lease.query.join(Property).join(Tenant)

        # فلاتر البحث
        if data.get('search'):
            search_term = f"%{data['search']}%"
            query = query.filter(
                db.or_(
                    Lease.lease_number.like(search_term),
                    Property.name.like(search_term),
                    Property.property_code.like(search_term),
                    Tenant.full_name.like(search_term)
                )
            )

        if data.get('status'):
            query = query.filter(Lease.status == data['status'])

        if data.get('property_type'):
            query = query.filter(Property.type == data['property_type'])

        if data.get('min_rent'):
            query = query.filter(Lease.monthly_rent >= data['min_rent'])

        if data.get('max_rent'):
            query = query.filter(Lease.monthly_rent <= data['max_rent'])

        # فلتر انتهاء العقد
        if data.get('expiry_filter'):
            today = datetime.now().date()
            if data['expiry_filter'] == 'expired':
                query = query.filter(Lease.end_date < today)
            elif data['expiry_filter'] == 'expiring_soon':
                next_month = today + timedelta(days=30)
                query = query.filter(Lease.end_date.between(today, next_month))
            elif data['expiry_filter'] == 'active':
                query = query.filter(Lease.end_date >= today)

        # ترتيب النتائج
        sort_by = data.get('sort_by', 'created_date')
        sort_order = data.get('sort_order', 'desc')

        if sort_by == 'property_name':
            sort_column = Property.name
        elif sort_by == 'tenant_name':
            sort_column = Tenant.full_name
        else:
            sort_column = getattr(Lease, sort_by)

        if sort_order == 'desc':
            query = query.order_by(sort_column.desc())
        else:
            query = query.order_by(sort_column)

        # تطبيق التصفح
        page = data.get('page', 1)
        per_page = data.get('per_page', 10)

        leases = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # تحويل النتائج إلى JSON
        results = []
        for lease in leases.items:
            # حساب إحصائيات العقد
            total_installments = lease.installments.count()
            paid_installments = lease.installments.filter_by(payment_status='مدفوع').count()
            progress = (paid_installments / total_installments * 100) if total_installments > 0 else 0

            # حساب الأيام المتبقية
            days_remaining = (lease.end_date - datetime.now().date()).days if lease.end_date else 0

            results.append({
                'id': lease.id,
                'lease_number': lease.lease_number,
                'property': {
                    'id': lease.property.id,
                    'name': lease.property.name,
                    'property_code': lease.property.property_code,
                    'type': lease.property.type,
                    'address': lease.property.address
                },
                'tenant': {
                    'id': lease.tenant.id,
                    'full_name': lease.tenant.full_name,
                    'phone_number': lease.tenant.phone_number
                },
                'start_date': lease.start_date.isoformat() if lease.start_date else None,
                'end_date': lease.end_date.isoformat() if lease.end_date else None,
                'monthly_rent': lease.monthly_rent,
                'total_amount': lease.total_amount,
                'currency': lease.currency,
                'status': lease.status,
                'progress': round(progress, 1),
                'days_remaining': days_remaining,
                'total_installments': total_installments,
                'paid_installments': paid_installments,
                'created_date': lease.created_date.isoformat() if lease.created_date else None
            })

        return jsonify({
            'success': True,
            'leases': results,
            'pagination': {
                'page': leases.page,
                'pages': leases.pages,
                'per_page': leases.per_page,
                'total': leases.total,
                'has_next': leases.has_next,
                'has_prev': leases.has_prev
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في البحث: {str(e)}'
        }), 500

@app.route('/api/leases/<int:id>/installments', methods=['GET', 'POST'])
@login_required
def api_lease_installments(id):
    """API لإدارة أقساط العقد"""
    lease = Lease.query.get_or_404(id)

    if request.method == 'GET':
        # جلب أقساط العقد
        installments = LeaseInstallment.query.filter_by(lease_id=id)\
                                           .order_by(LeaseInstallment.due_date)\
                                           .all()
        return jsonify({
            'success': True,
            'installments': [{
                'id': inst.id,
                'installment_number': inst.installment_number,
                'amount': inst.amount,
                'due_date': inst.due_date.isoformat() if inst.due_date else None,
                'payment_date': inst.payment_date.isoformat() if inst.payment_date else None,
                'payment_status': inst.payment_status,
                'payment_method': inst.payment_method,
                'late_fee': inst.late_fee,
                'discount': inst.discount,
                'notes': inst.notes,
                'days_overdue': (datetime.now().date() - inst.due_date).days if inst.due_date and inst.payment_status != 'مدفوع' and datetime.now().date() > inst.due_date else 0
            } for inst in installments]
        })

    elif request.method == 'POST':
        # إضافة قسط جديد
        try:
            data = request.get_json()

            new_installment = LeaseInstallment(
                lease_id=id,
                installment_number=data.get('installment_number'),
                amount=data.get('amount'),
                due_date=datetime.strptime(data.get('due_date'), '%Y-%m-%d').date() if data.get('due_date') else None,
                payment_status=data.get('payment_status', 'غير مدفوع'),
                notes=data.get('notes', ''),
                created_by=current_user.id
            )

            db.session.add(new_installment)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم إضافة القسط بنجاح',
                'installment': {
                    'id': new_installment.id,
                    'installment_number': new_installment.installment_number,
                    'amount': new_installment.amount,
                    'due_date': new_installment.due_date.isoformat() if new_installment.due_date else None
                }
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ أثناء إضافة القسط: {str(e)}'
            }), 500

# ==================== FINANCIAL & REPORTING APIs ====================

@app.route('/api/reports/financial-summary', methods=['GET'])
@login_required
def api_financial_summary():
    """API لتقرير الملخص المالي"""
    try:
        # حساب إجمالي الإيرادات
        total_rent_income = db.session.query(db.func.sum(LeaseInstallment.amount))\
                                    .filter(LeaseInstallment.payment_status == 'مدفوع')\
                                    .scalar() or 0

        # حساب المبالغ المستحقة
        overdue_amount = db.session.query(db.func.sum(LeaseInstallment.amount))\
                                 .filter(
                                     LeaseInstallment.payment_status == 'غير مدفوع',
                                     LeaseInstallment.due_date < datetime.now().date()
                                 ).scalar() or 0

        # حساب المبالغ المستحقة هذا الشهر
        current_month_start = datetime.now().replace(day=1).date()
        next_month_start = (current_month_start + timedelta(days=32)).replace(day=1)

        current_month_due = db.session.query(db.func.sum(LeaseInstallment.amount))\
                                    .filter(
                                        LeaseInstallment.payment_status == 'غير مدفوع',
                                        LeaseInstallment.due_date >= current_month_start,
                                        LeaseInstallment.due_date < next_month_start
                                    ).scalar() or 0

        # إحصائيات العقارات
        total_properties = Property.query.count()
        occupied_properties = Property.query.filter_by(status='مؤجر').count()
        vacant_properties = Property.query.filter_by(status='شاغر').count()

        # إحصائيات المستأجرين
        total_tenants = Tenant.query.count()
        active_tenants = Tenant.query.filter_by(status='نشط').count()

        # إحصائيات العقود
        total_leases = Lease.query.count()
        active_leases = Lease.query.filter_by(status='نشط').count()
        expired_leases = Lease.query.filter(Lease.end_date < datetime.now().date()).count()

        # العقود المنتهية قريباً (خلال 30 يوم)
        expiring_soon = Lease.query.filter(
            Lease.end_date.between(
                datetime.now().date(),
                datetime.now().date() + timedelta(days=30)
            ),
            Lease.status == 'نشط'
        ).count()

        return jsonify({
            'success': True,
            'financial_summary': {
                'total_rent_income': total_rent_income,
                'overdue_amount': overdue_amount,
                'current_month_due': current_month_due,
                'collection_rate': round((total_rent_income / (total_rent_income + overdue_amount) * 100), 2) if (total_rent_income + overdue_amount) > 0 else 0
            },
            'property_stats': {
                'total_properties': total_properties,
                'occupied_properties': occupied_properties,
                'vacant_properties': vacant_properties,
                'occupancy_rate': round((occupied_properties / total_properties * 100), 2) if total_properties > 0 else 0
            },
            'tenant_stats': {
                'total_tenants': total_tenants,
                'active_tenants': active_tenants
            },
            'lease_stats': {
                'total_leases': total_leases,
                'active_leases': active_leases,
                'expired_leases': expired_leases,
                'expiring_soon': expiring_soon
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في جلب التقرير: {str(e)}'
        }), 500

@app.route('/api/reports/rent-collection', methods=['POST'])
@login_required
def api_rent_collection_report():
    """API لتقرير تحصيل الإيجارات"""
    try:
        data = request.get_json()

        # تحديد الفترة الزمنية
        start_date = datetime.strptime(data.get('start_date'), '%Y-%m-%d').date() if data.get('start_date') else None
        end_date = datetime.strptime(data.get('end_date'), '%Y-%m-%d').date() if data.get('end_date') else None

        query = LeaseInstallment.query.join(Lease).join(Property).join(Tenant)

        if start_date:
            query = query.filter(LeaseInstallment.due_date >= start_date)
        if end_date:
            query = query.filter(LeaseInstallment.due_date <= end_date)

        # فلتر حسب العقار
        if data.get('property_id'):
            query = query.filter(Lease.property_id == data['property_id'])

        # فلتر حسب المستأجر
        if data.get('tenant_id'):
            query = query.filter(Lease.tenant_id == data['tenant_id'])

        # فلتر حسب حالة الدفع
        if data.get('payment_status'):
            query = query.filter(LeaseInstallment.payment_status == data['payment_status'])

        installments = query.order_by(LeaseInstallment.due_date.desc()).all()

        # تجميع البيانات
        total_amount = sum(inst.amount for inst in installments)
        paid_amount = sum(inst.amount for inst in installments if inst.payment_status == 'مدفوع')
        overdue_amount = sum(inst.amount for inst in installments
                           if inst.payment_status == 'غير مدفوع' and inst.due_date < datetime.now().date())

        # تفاصيل الأقساط
        installment_details = []
        for inst in installments:
            installment_details.append({
                'id': inst.id,
                'lease_number': inst.lease.lease_number,
                'property_name': inst.lease.property.name,
                'tenant_name': inst.lease.tenant.full_name,
                'installment_number': inst.installment_number,
                'amount': inst.amount,
                'due_date': inst.due_date.isoformat() if inst.due_date else None,
                'payment_date': inst.payment_date.isoformat() if inst.payment_date else None,
                'payment_status': inst.payment_status,
                'days_overdue': (datetime.now().date() - inst.due_date).days if inst.due_date and inst.payment_status != 'مدفوع' and datetime.now().date() > inst.due_date else 0
            })

        return jsonify({
            'success': True,
            'summary': {
                'total_amount': total_amount,
                'paid_amount': paid_amount,
                'overdue_amount': overdue_amount,
                'pending_amount': total_amount - paid_amount,
                'collection_rate': round((paid_amount / total_amount * 100), 2) if total_amount > 0 else 0
            },
            'installments': installment_details
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في جلب التقرير: {str(e)}'
        }), 500

@app.route('/api/bulk-operations/mark-installments-paid', methods=['POST'])
@login_required
def api_bulk_mark_installments_paid():
    """API لتحديد عدة أقساط كمدفوعة"""
    try:
        data = request.get_json()
        installment_ids = data.get('installment_ids', [])
        payment_date = datetime.strptime(data.get('payment_date'), '%Y-%m-%d').date() if data.get('payment_date') else datetime.now().date()
        payment_method = data.get('payment_method', 'نقدي')

        if not installment_ids:
            return jsonify({
                'success': False,
                'message': 'يجب تحديد أقساط للتحديث'
            }), 400

        # تحديث الأقساط
        updated_count = 0
        for inst_id in installment_ids:
            installment = LeaseInstallment.query.get(inst_id)
            if installment and installment.payment_status != 'مدفوع':
                installment.payment_status = 'مدفوع'
                installment.payment_date = payment_date
                installment.payment_method = payment_method
                installment.updated_by = current_user.id
                installment.updated_date = datetime.now()
                updated_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'تم تحديث {updated_count} قسط بنجاح',
            'updated_count': updated_count
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء التحديث: {str(e)}'
        }), 500

# ==================== EXPORT & PRINTING APIs ====================

@app.route('/api/export/properties', methods=['POST'])
@login_required
def api_export_properties():
    """API لتصدير العقارات إلى Excel"""
    try:
        import xlsxwriter
        import io

        data = request.get_json()

        # تطبيق الفلاتر
        query = Property.query

        if data.get('property_type'):
            query = query.filter(Property.type == data['property_type'])
        if data.get('status'):
            query = query.filter(Property.status == data['status'])
        if data.get('city'):
            query = query.filter(Property.city == data['city'])

        properties = query.all()

        # إنشاء ملف Excel
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('العقارات')

        # تنسيق الخلايا
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4CAF50',
            'font_color': 'white',
            'align': 'center'
        })

        # كتابة العناوين
        headers = [
            'رقم العقار', 'اسم العقار', 'رمز العقار', 'النوع', 'الفئة', 'العنوان',
            'المدينة', 'الحي', 'المساحة', 'عدد الغرف', 'عدد الحمامات',
            'الإيجار الشهري', 'العملة', 'الحالة', 'اسم المالك', 'هاتف المالك'
        ]

        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)

        # كتابة البيانات
        for row, prop in enumerate(properties, 1):
            worksheet.write(row, 0, prop.id)
            worksheet.write(row, 1, prop.name or '')
            worksheet.write(row, 2, prop.property_code or '')
            worksheet.write(row, 3, prop.type or '')
            worksheet.write(row, 4, prop.category or '')
            worksheet.write(row, 5, prop.address or '')
            worksheet.write(row, 6, prop.city or '')
            worksheet.write(row, 7, prop.district or '')
            worksheet.write(row, 8, prop.area or 0)
            worksheet.write(row, 9, prop.rooms_count or 0)
            worksheet.write(row, 10, prop.bathrooms_count or 0)
            worksheet.write(row, 11, prop.monthly_rent or 0)
            worksheet.write(row, 12, prop.currency or '')
            worksheet.write(row, 13, prop.status or '')
            worksheet.write(row, 14, prop.owner_name or '')
            worksheet.write(row, 15, prop.owner_phone or '')

        # ضبط عرض الأعمدة
        worksheet.set_column('A:P', 15)

        workbook.close()
        output.seek(0)

        # إرسال الملف
        from flask import send_file
        return send_file(
            io.BytesIO(output.read()),
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'properties_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء التصدير: {str(e)}'
        }), 500

@app.route('/api/export/tenants', methods=['POST'])
@login_required
def api_export_tenants():
    """API لتصدير المستأجرين إلى Excel"""
    try:
        import xlsxwriter
        import io

        data = request.get_json()

        # تطبيق الفلاتر
        query = Tenant.query

        if data.get('status'):
            query = query.filter(Tenant.status == data['status'])
        if data.get('nationality'):
            query = query.filter(Tenant.nationality == data['nationality'])

        tenants = query.all()

        # إنشاء ملف Excel
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('المستأجرين')

        # تنسيق الخلايا
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#2196F3',
            'font_color': 'white',
            'align': 'center'
        })

        # كتابة العناوين
        headers = [
            'رقم المستأجر', 'الاسم الكامل', 'رقم الهوية', 'رقم الجواز', 'الجنسية',
            'رقم الهاتف', 'البريد الإلكتروني', 'العنوان', 'التقييم الائتماني',
            'الحالة', 'تاريخ الإنشاء'
        ]

        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)

        # كتابة البيانات
        for row, tenant in enumerate(tenants, 1):
            worksheet.write(row, 0, tenant.id)
            worksheet.write(row, 1, tenant.full_name or '')
            worksheet.write(row, 2, tenant.national_id or '')
            worksheet.write(row, 3, tenant.passport_number or '')
            worksheet.write(row, 4, tenant.nationality or '')
            worksheet.write(row, 5, tenant.phone_number or '')
            worksheet.write(row, 6, tenant.email or '')
            worksheet.write(row, 7, tenant.address or '')
            worksheet.write(row, 8, tenant.credit_score or 0)
            worksheet.write(row, 9, tenant.status or '')
            worksheet.write(row, 10, tenant.created_date.strftime('%Y-%m-%d') if tenant.created_date else '')

        # ضبط عرض الأعمدة
        worksheet.set_column('A:K', 15)

        workbook.close()
        output.seek(0)

        # إرسال الملف
        from flask import send_file
        return send_file(
            io.BytesIO(output.read()),
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'tenants_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء التصدير: {str(e)}'
        }), 500

@app.route('/api/print/lease-contract/<int:id>')
@login_required
def api_print_lease_contract(id):
    """API لطباعة عقد الإيجار"""
    try:
        lease = Lease.query.get_or_404(id)

        # إنشاء HTML للطباعة
        contract_html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>عقد إيجار رقم {lease.lease_number}</title>
            <style>
                body {{ font-family: 'Arial', sans-serif; margin: 20px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .title {{ font-size: 24px; font-weight: bold; margin-bottom: 20px; }}
                .section {{ margin-bottom: 20px; }}
                .section-title {{ font-size: 18px; font-weight: bold; margin-bottom: 10px; }}
                .info-row {{ margin-bottom: 8px; }}
                .label {{ font-weight: bold; }}
                .signature-section {{ margin-top: 50px; display: flex; justify-content: space-between; }}
                .signature-box {{ width: 200px; text-align: center; }}
                @media print {{ body {{ margin: 0; }} }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="title">عقد إيجار</div>
                <div>رقم العقد: {lease.lease_number}</div>
            </div>

            <div class="section">
                <div class="section-title">بيانات العقار:</div>
                <div class="info-row"><span class="label">اسم العقار:</span> {lease.property.name}</div>
                <div class="info-row"><span class="label">رمز العقار:</span> {lease.property.property_code or ''}</div>
                <div class="info-row"><span class="label">النوع:</span> {lease.property.type}</div>
                <div class="info-row"><span class="label">العنوان:</span> {lease.property.address or ''}</div>
                <div class="info-row"><span class="label">المساحة:</span> {lease.property.area or 0} متر مربع</div>
            </div>

            <div class="section">
                <div class="section-title">بيانات المستأجر:</div>
                <div class="info-row"><span class="label">الاسم:</span> {lease.tenant.full_name}</div>
                <div class="info-row"><span class="label">رقم الهوية:</span> {lease.tenant.national_id or ''}</div>
                <div class="info-row"><span class="label">رقم الهاتف:</span> {lease.tenant.phone_number or ''}</div>
                <div class="info-row"><span class="label">البريد الإلكتروني:</span> {lease.tenant.email or ''}</div>
                <div class="info-row"><span class="label">الجنسية:</span> {lease.tenant.nationality or ''}</div>
            </div>

            <div class="section">
                <div class="section-title">تفاصيل العقد:</div>
                <div class="info-row"><span class="label">تاريخ البداية:</span> {lease.start_date.strftime('%Y-%m-%d') if lease.start_date else ''}</div>
                <div class="info-row"><span class="label">تاريخ النهاية:</span> {lease.end_date.strftime('%Y-%m-%d') if lease.end_date else ''}</div>
                <div class="info-row"><span class="label">الإيجار الشهري:</span> {lease.monthly_rent} {lease.currency}</div>
                <div class="info-row"><span class="label">إجمالي المبلغ:</span> {lease.total_amount} {lease.currency}</div>
                <div class="info-row"><span class="label">مبلغ التأمين:</span> {lease.security_deposit or 0} {lease.currency}</div>
            </div>

            <div class="signature-section">
                <div class="signature-box">
                    <div>توقيع المؤجر</div>
                    <div style="border-top: 1px solid #000; margin-top: 50px;"></div>
                </div>
                <div class="signature-box">
                    <div>توقيع المستأجر</div>
                    <div style="border-top: 1px solid #000; margin-top: 50px;"></div>
                </div>
            </div>
        </body>
        </html>
        """

        return contract_html

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء إنشاء العقد: {str(e)}'
        }), 500

# ==================== DOCUMENT & FILE MANAGEMENT APIs ====================

@app.route('/api/upload/property-document/<int:property_id>', methods=['POST'])
@login_required
def api_upload_property_document(property_id):
    """API لرفع مستندات العقار"""
    try:
        property = Property.query.get_or_404(property_id)

        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'}), 400

        # التحقق من نوع الملف
        allowed_extensions = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'}
        if not ('.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'success': False, 'message': 'نوع الملف غير مدعوم'}), 400

        # إنشاء اسم ملف فريد
        import uuid
        import os
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
        file_path = os.path.join('static', 'uploads', 'properties', unique_filename)

        # حفظ الملف
        file.save(file_path)

        # إضافة المستند إلى قاعدة البيانات
        document = PropertyDocument(
            property_id=property_id,
            document_name=file.filename,
            document_type=request.form.get('document_type', 'عام'),
            file_path=file_path,
            file_url=f"/static/uploads/properties/{unique_filename}",
            file_size=os.path.getsize(file_path),
            description=request.form.get('description', ''),
            uploaded_by=current_user.id,
            uploaded_date=datetime.now()
        )

        db.session.add(document)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم رفع المستند بنجاح',
            'document': {
                'id': document.id,
                'document_name': document.document_name,
                'document_type': document.document_type,
                'file_url': document.file_url,
                'file_size': document.file_size,
                'uploaded_date': document.uploaded_date.isoformat()
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء رفع المستند: {str(e)}'
        }), 500

@app.route('/api/upload/tenant-document/<int:tenant_id>', methods=['POST'])
@login_required
def api_upload_tenant_document(tenant_id):
    """API لرفع مستندات المستأجر"""
    try:
        tenant = Tenant.query.get_or_404(tenant_id)

        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'}), 400

        # التحقق من نوع الملف
        allowed_extensions = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'}
        if not ('.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'success': False, 'message': 'نوع الملف غير مدعوم'}), 400

        # إنشاء اسم ملف فريد
        import uuid
        import os
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
        file_path = os.path.join('static', 'uploads', 'tenants', unique_filename)

        # حفظ الملف
        file.save(file_path)

        # إضافة المستند إلى قاعدة البيانات
        document = TenantDocument(
            tenant_id=tenant_id,
            document_name=file.filename,
            document_type=request.form.get('document_type', 'عام'),
            file_path=file_path,
            file_url=f"/static/uploads/tenants/{unique_filename}",
            file_size=os.path.getsize(file_path),
            description=request.form.get('description', ''),
            uploaded_by=current_user.id,
            uploaded_date=datetime.now()
        )

        db.session.add(document)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم رفع المستند بنجاح',
            'document': {
                'id': document.id,
                'document_name': document.document_name,
                'document_type': document.document_type,
                'file_url': document.file_url,
                'file_size': document.file_size,
                'uploaded_date': document.uploaded_date.isoformat()
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء رفع المستند: {str(e)}'
        }), 500

@app.route('/api/upload/lease-document/<int:lease_id>', methods=['POST'])
@login_required
def api_upload_lease_document(lease_id):
    """API لرفع مستندات العقد"""
    try:
        lease = Lease.query.get_or_404(lease_id)

        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'}), 400

        # التحقق من نوع الملف
        allowed_extensions = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'}
        if not ('.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'success': False, 'message': 'نوع الملف غير مدعوم'}), 400

        # إنشاء اسم ملف فريد
        import uuid
        import os
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
        file_path = os.path.join('static', 'uploads', 'leases', unique_filename)

        # حفظ الملف
        file.save(file_path)

        # إضافة المستند إلى قاعدة البيانات
        document = LeaseDocument(
            lease_id=lease_id,
            document_name=file.filename,
            document_type=request.form.get('document_type', 'عام'),
            file_path=file_path,
            file_url=f"/static/uploads/leases/{unique_filename}",
            file_size=os.path.getsize(file_path),
            description=request.form.get('description', ''),
            uploaded_by=current_user.id,
            uploaded_date=datetime.now()
        )

        db.session.add(document)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم رفع المستند بنجاح',
            'document': {
                'id': document.id,
                'document_name': document.document_name,
                'document_type': document.document_type,
                'file_url': document.file_url,
                'file_size': document.file_size,
                'uploaded_date': document.uploaded_date.isoformat()
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء رفع المستند: {str(e)}'
        }), 500

@app.route('/api/documents/property/<int:property_id>', methods=['GET'])
@login_required
def api_get_property_documents(property_id):
    """API لجلب مستندات العقار"""
    try:
        property = Property.query.get_or_404(property_id)
        documents = PropertyDocument.query.filter_by(property_id=property_id)\
                                         .order_by(PropertyDocument.uploaded_date.desc())\
                                         .all()

        return jsonify({
            'success': True,
            'documents': [{
                'id': doc.id,
                'document_name': doc.document_name,
                'document_type': doc.document_type,
                'file_url': doc.file_url,
                'file_size': doc.file_size,
                'description': doc.description,
                'uploaded_date': doc.uploaded_date.isoformat() if doc.uploaded_date else None,
                'uploaded_by': doc.uploaded_by
            } for doc in documents]
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في جلب المستندات: {str(e)}'
        }), 500

@app.route('/api/documents/tenant/<int:tenant_id>', methods=['GET'])
@login_required
def api_get_tenant_documents(tenant_id):
    """API لجلب مستندات المستأجر"""
    try:
        tenant = Tenant.query.get_or_404(tenant_id)
        documents = TenantDocument.query.filter_by(tenant_id=tenant_id)\
                                       .order_by(TenantDocument.uploaded_date.desc())\
                                       .all()

        return jsonify({
            'success': True,
            'documents': [{
                'id': doc.id,
                'document_name': doc.document_name,
                'document_type': doc.document_type,
                'file_url': doc.file_url,
                'file_size': doc.file_size,
                'description': doc.description,
                'uploaded_date': doc.uploaded_date.isoformat() if doc.uploaded_date else None,
                'uploaded_by': doc.uploaded_by
            } for doc in documents]
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في جلب المستندات: {str(e)}'
        }), 500

@app.route('/api/documents/lease/<int:lease_id>', methods=['GET'])
@login_required
def api_get_lease_documents(lease_id):
    """API لجلب مستندات العقد"""
    try:
        lease = Lease.query.get_or_404(lease_id)
        documents = LeaseDocument.query.filter_by(lease_id=lease_id)\
                                      .order_by(LeaseDocument.uploaded_date.desc())\
                                      .all()

        return jsonify({
            'success': True,
            'documents': [{
                'id': doc.id,
                'document_name': doc.document_name,
                'document_type': doc.document_type,
                'file_url': doc.file_url,
                'file_size': doc.file_size,
                'description': doc.description,
                'uploaded_date': doc.uploaded_date.isoformat() if doc.uploaded_date else None,
                'uploaded_by': doc.uploaded_by
            } for doc in documents]
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في جلب المستندات: {str(e)}'
        }), 500

@app.route('/api/documents/delete/<int:document_id>/<document_type>', methods=['DELETE'])
@login_required
def api_delete_document(document_id, document_type):
    """API لحذف المستندات"""
    try:
        import os

        # تحديد نوع المستند والنموذج المناسب
        if document_type == 'property':
            document = PropertyDocument.query.get_or_404(document_id)
        elif document_type == 'tenant':
            document = TenantDocument.query.get_or_404(document_id)
        elif document_type == 'lease':
            document = LeaseDocument.query.get_or_404(document_id)
        else:
            return jsonify({'success': False, 'message': 'نوع المستند غير صحيح'}), 400

        # حذف الملف من النظام
        if os.path.exists(document.file_path):
            os.remove(document.file_path)

        # حذف السجل من قاعدة البيانات
        db.session.delete(document)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حذف المستند بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء حذف المستند: {str(e)}'
        }), 500

@app.route('/api/documents/preview/<int:document_id>/<document_type>')
@login_required
def api_preview_document(document_id, document_type):
    """API لمعاينة المستندات"""
    try:
        # تحديد نوع المستند والنموذج المناسب
        if document_type == 'property':
            document = PropertyDocument.query.get_or_404(document_id)
        elif document_type == 'tenant':
            document = TenantDocument.query.get_or_404(document_id)
        elif document_type == 'lease':
            document = LeaseDocument.query.get_or_404(document_id)
        else:
            return jsonify({'success': False, 'message': 'نوع المستند غير صحيح'}), 400

        # إرسال الملف للمعاينة
        from flask import send_file
        return send_file(document.file_path, as_attachment=False)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في معاينة المستند: {str(e)}'
        }), 500

@app.route('/api/documents/download/<int:document_id>/<document_type>')
@login_required
def api_download_document(document_id, document_type):
    """API لتحميل المستندات"""
    try:
        # تحديد نوع المستند والنموذج المناسب
        if document_type == 'property':
            document = PropertyDocument.query.get_or_404(document_id)
        elif document_type == 'tenant':
            document = TenantDocument.query.get_or_404(document_id)
        elif document_type == 'lease':
            document = LeaseDocument.query.get_or_404(document_id)
        else:
            return jsonify({'success': False, 'message': 'نوع المستند غير صحيح'}), 400

        # إرسال الملف للتحميل
        from flask import send_file
        return send_file(
            document.file_path,
            as_attachment=True,
            download_name=document.document_name
        )

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في تحميل المستند: {str(e)}'
        }), 500

# ==================== DOCUMENT MANAGEMENT PAGES ====================

@app.route('/documents')
@login_required
def documents_manager():
    """صفحة إدارة المستندات"""
    return render_template('documents/document_manager.html')

@app.route('/reports')
@login_required
def reports_dashboard():
    """صفحة التقارير والإحصائيات"""
    return render_template('reports/reports_dashboard.html')

# ==================== REPORTS & ANALYTICS APIs ====================

@app.route('/api/reports/dashboard-stats')
@login_required
def api_dashboard_stats():
    """API لجلب إحصائيات لوحة التحكم"""
    try:
        # إحصائيات العقارات
        total_properties = Property.query.count()

        # إحصائيات المستأجرين
        total_tenants = Tenant.query.count()

        # إحصائيات العقود النشطة
        from datetime import datetime
        active_leases = Lease.query.filter(
            Lease.status == 'نشط',
            Lease.end_date >= datetime.now()
        ).count()

        # الإيرادات الشهرية
        current_month = datetime.now().month
        current_year = datetime.now().year

        monthly_revenue = db.session.query(db.func.sum(Lease.monthly_rent)).filter(
            Lease.status == 'نشط',
            db.extract('month', Lease.start_date) <= current_month,
            db.extract('year', Lease.start_date) <= current_year,
            Lease.end_date >= datetime.now()
        ).scalar() or 0

        return jsonify({
            'success': True,
            'stats': {
                'total_properties': total_properties,
                'total_tenants': total_tenants,
                'active_leases': active_leases,
                'monthly_revenue': float(monthly_revenue)
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في جلب الإحصائيات: {str(e)}'
        }), 500

@app.route('/api/reports/property-type-distribution', methods=['POST'])
@login_required
def api_property_type_distribution():
    """API لجلب توزيع العقارات حسب النوع"""
    try:
        data = request.get_json()

        # استعلام توزيع العقارات حسب النوع
        query = db.session.query(
            Property.type,
            db.func.count(Property.id).label('count')
        ).group_by(Property.type)

        # تطبيق فلتر المدينة إذا وجد
        if data.get('city'):
            query = query.filter(Property.city == data['city'])

        results = query.all()

        labels = [result.type or 'غير محدد' for result in results]
        values = [result.count for result in results]

        return jsonify({
            'success': True,
            'data': {
                'labels': labels,
                'values': values
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في جلب توزيع العقارات: {str(e)}'
        }), 500

@app.route('/api/reports/monthly-revenue', methods=['POST'])
@login_required
def api_monthly_revenue():
    """API لجلب الإيرادات الشهرية"""
    try:
        data = request.get_json()
        from datetime import datetime, timedelta

        # تحديد الفترة الزمنية
        if data.get('start_date') and data.get('end_date'):
            start_date = datetime.strptime(data['start_date'], '%Y-%m-%d')
            end_date = datetime.strptime(data['end_date'], '%Y-%m-%d')
        else:
            # آخر 12 شهر
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)

        # استعلام الإيرادات الشهرية
        query = db.session.query(
            db.extract('year', Lease.start_date).label('year'),
            db.extract('month', Lease.start_date).label('month'),
            db.func.sum(Lease.monthly_rent).label('revenue')
        ).filter(
            Lease.status == 'نشط',
            Lease.start_date >= start_date,
            Lease.start_date <= end_date
        ).group_by(
            db.extract('year', Lease.start_date),
            db.extract('month', Lease.start_date)
        ).order_by(
            db.extract('year', Lease.start_date),
            db.extract('month', Lease.start_date)
        )

        results = query.all()

        labels = []
        values = []

        for result in results:
            month_name = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ][int(result.month) - 1]

            labels.append(f"{month_name} {int(result.year)}")
            values.append(float(result.revenue or 0))

        return jsonify({
            'success': True,
            'data': {
                'labels': labels,
                'values': values
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في جلب الإيرادات الشهرية: {str(e)}'
        }), 500

@app.route('/api/reports/occupancy-rate', methods=['POST'])
@login_required
def api_occupancy_rate():
    """API لجلب معدل الإشغال"""
    try:
        data = request.get_json()
        from datetime import datetime

        # عدد العقارات المؤجرة
        rented_count = Property.query.filter(Property.status == 'مؤجر').count()

        # عدد العقارات الشاغرة
        vacant_count = Property.query.filter(Property.status == 'شاغر').count()

        # عدد العقارات قيد الصيانة
        maintenance_count = Property.query.filter(Property.status == 'صيانة').count()

        return jsonify({
            'success': True,
            'data': {
                'values': [rented_count, vacant_count, maintenance_count]
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في جلب معدل الإشغال: {str(e)}'
        }), 500

@app.route('/api/reports/city-distribution', methods=['POST'])
@login_required
def api_city_distribution():
    """API لجلب توزيع العقارات حسب المدينة"""
    try:
        data = request.get_json()

        # استعلام توزيع العقارات حسب المدينة
        query = db.session.query(
            Property.city,
            db.func.count(Property.id).label('count')
        ).group_by(Property.city)

        results = query.all()

        labels = [result.city or 'غير محدد' for result in results]
        values = [result.count for result in results]

        return jsonify({
            'success': True,
            'data': {
                'labels': labels,
                'values': values
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في جلب توزيع المدن: {str(e)}'
        }), 500

@app.route('/api/reports/detailed-report', methods=['POST'])
@login_required
def api_detailed_report():
    """API لجلب التقرير المفصل"""
    try:
        data = request.get_json()
        report_type = data.get('report_type', 'financial')

        if report_type == 'financial':
            # تقرير مالي مفصل
            query = db.session.query(
                Property.name.label('property_name'),
                Tenant.full_name.label('tenant_name'),
                Lease.monthly_rent,
                Lease.total_amount,
                Lease.status
            ).join(Lease, Property.id == Lease.property_id)\
             .join(Tenant, Lease.tenant_id == Tenant.id)

            if data.get('city'):
                query = query.filter(Property.city == data['city'])

            results = query.all()

            report_data = []
            for result in results:
                report_data.append({
                    'property_name': result.property_name,
                    'tenant_name': result.tenant_name,
                    'monthly_rent': result.monthly_rent,
                    'total_amount': result.total_amount,
                    'status': result.status
                })

        elif report_type == 'properties':
            # تقرير العقارات
            query = Property.query

            if data.get('city'):
                query = query.filter(Property.city == data['city'])

            properties = query.all()

            report_data = []
            for prop in properties:
                # البحث عن المستأجر الحالي
                current_lease = Lease.query.filter(
                    Lease.property_id == prop.id,
                    Lease.status == 'نشط'
                ).first()

                tenant_name = current_lease.tenant.full_name if current_lease else 'غير مؤجر'

                report_data.append({
                    'property_name': prop.name,
                    'type': prop.type,
                    'city': prop.city,
                    'status': prop.status,
                    'monthly_rent': prop.monthly_rent,
                    'tenant_name': tenant_name
                })

        elif report_type == 'tenants':
            # تقرير المستأجرين
            tenants = Tenant.query.all()

            report_data = []
            for tenant in tenants:
                # البحث عن العقد الحالي
                current_lease = Lease.query.filter(
                    Lease.tenant_id == tenant.id,
                    Lease.status == 'نشط'
                ).first()

                property_name = current_lease.property.name if current_lease else 'لا يوجد'
                start_date = current_lease.start_date.strftime('%Y-%m-%d') if current_lease else '-'

                report_data.append({
                    'tenant_name': tenant.full_name,
                    'phone_number': tenant.phone_number,
                    'property_name': property_name,
                    'start_date': start_date,
                    'status': tenant.status
                })

        elif report_type == 'leases':
            # تقرير العقود
            leases = Lease.query.all()

            report_data = []
            for lease in leases:
                report_data.append({
                    'lease_number': lease.lease_number,
                    'property_name': lease.property.name,
                    'tenant_name': lease.tenant.full_name,
                    'start_date': lease.start_date.strftime('%Y-%m-%d') if lease.start_date else '-',
                    'end_date': lease.end_date.strftime('%Y-%m-%d') if lease.end_date else '-',
                    'status': lease.status
                })

        else:
            report_data = []

        return jsonify({
            'success': True,
            'data': report_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في جلب التقرير المفصل: {str(e)}'
        }), 500

@app.route('/api/reports/export-excel', methods=['POST'])
@login_required
def api_export_excel_report():
    """API لتصدير التقرير إلى Excel"""
    try:
        import xlsxwriter
        import io

        data = request.get_json()
        report_type = data.get('report_type', 'financial')

        # الحصول على بيانات التقرير
        report_response = api_detailed_report()
        report_data = report_response.get_json()

        if not report_data.get('success'):
            return jsonify({
                'success': False,
                'message': 'فشل في الحصول على بيانات التقرير'
            }), 500

        # إنشاء ملف Excel
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('التقرير')

        # تنسيق الخلايا
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4CAF50',
            'font_color': 'white',
            'align': 'center'
        })

        # تحديد العناوين حسب نوع التقرير
        if report_type == 'financial':
            headers = ['اسم العقار', 'اسم المستأجر', 'الإيجار الشهري', 'إجمالي المبلغ', 'الحالة']
        elif report_type == 'properties':
            headers = ['اسم العقار', 'النوع', 'المدينة', 'الحالة', 'الإيجار الشهري', 'المستأجر']
        elif report_type == 'tenants':
            headers = ['اسم المستأجر', 'رقم الهاتف', 'العقار', 'تاريخ البداية', 'الحالة']
        elif report_type == 'leases':
            headers = ['رقم العقد', 'العقار', 'المستأجر', 'تاريخ البداية', 'تاريخ النهاية', 'الحالة']
        else:
            headers = ['البيان', 'القيمة']

        # كتابة العناوين
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)

        # كتابة البيانات
        for row, item in enumerate(report_data['data'], 1):
            for col, value in enumerate(item.values()):
                worksheet.write(row, col, value or '')

        # ضبط عرض الأعمدة
        for col in range(len(headers)):
            worksheet.set_column(col, col, 20)

        workbook.close()
        output.seek(0)

        # إرسال الملف
        from flask import send_file
        return send_file(
            io.BytesIO(output.read()),
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'{report_type}_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تصدير التقرير: {str(e)}'
        }), 500

@app.route('/api/reports/generate-pdf')
@login_required
def api_generate_pdf_report():
    """API لإنشاء تقرير PDF"""
    try:
        report_type = request.args.get('report_type', 'financial')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        city = request.args.get('city', '')

        # إنشاء HTML للتقرير
        report_html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير {report_type}</title>
            <style>
                body {{ font-family: 'Arial', sans-serif; margin: 20px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .title {{ font-size: 24px; font-weight: bold; margin-bottom: 20px; }}
                .info {{ margin-bottom: 20px; }}
                table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                th {{ background-color: #f2f2f2; font-weight: bold; }}
                .footer {{ margin-top: 30px; text-align: center; font-size: 12px; color: #666; }}
                @media print {{ body {{ margin: 0; }} }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="title">تقرير إدارة العقارات</div>
                <div class="info">
                    <p>نوع التقرير: {report_type}</p>
                    <p>الفترة: من {start_date} إلى {end_date}</p>
                    <p>تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
                </div>
            </div>

            <div class="content">
                <p>هذا تقرير تجريبي. يمكن تطوير المحتوى ليشمل البيانات الفعلية.</p>
            </div>

            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة العقارات</p>
            </div>
        </body>
        </html>
        """

        return report_html

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء إنشاء التقرير: {str(e)}'
        }), 500

@app.route('/properties/export')
@login_required
def export_properties():
    """تصدير قائمة العقارات"""
    try:
        properties = Property.query.all()

        # إنشاء ملف Excel
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('العقارات')

        # تنسيق الخلايا
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4472C4',
            'font_color': 'white',
            'border': 1
        })

        # كتابة العناوين
        headers = [
            'رمز العقار', 'اسم العقار', 'نوع العقار', 'المدينة', 'الحي',
            'المساحة', 'الغرف', 'الحمامات', 'الإيجار الشهري', 'الحالة',
            'اسم المالك', 'هاتف المالك', 'تاريخ الإضافة'
        ]

        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)

        # كتابة البيانات
        for row, property in enumerate(properties, 1):
            worksheet.write(row, 0, property.property_code or '')
            worksheet.write(row, 1, property.name or '')
            worksheet.write(row, 2, property.property_type or '')
            worksheet.write(row, 3, property.city or '')
            worksheet.write(row, 4, property.district or '')
            worksheet.write(row, 5, property.area or 0)
            worksheet.write(row, 6, property.bedrooms or 0)
            worksheet.write(row, 7, property.bathrooms or 0)
            worksheet.write(row, 8, property.monthly_rent or 0)
            worksheet.write(row, 9, property.status or '')
            worksheet.write(row, 10, property.owner_name or '')
            worksheet.write(row, 11, property.owner_phone or '')
            worksheet.write(row, 12, property.created_at.strftime('%Y-%m-%d') if property.created_at else '')

        workbook.close()
        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'properties_{datetime.now().strftime("%Y%m%d")}.xlsx'
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('properties'))

# ==================== TENANT MANAGEMENT ROUTES ====================

@app.route('/tenants')
@login_required
def tenants():
    """عرض قائمة المستأجرين مع البحث والفلترة"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    nationality = request.args.get('nationality', '')
    employment_status = request.args.get('employment_status', '')
    credit_score_range = request.args.get('credit_score_range', '')

    # بناء الاستعلام
    query = Tenant.query

    # تطبيق الفلاتر
    if search:
        query = query.filter(
            db.or_(
                Tenant.full_name.contains(search),
                Tenant.id_number.contains(search),
                Tenant.phone.contains(search),
                Tenant.email.contains(search),
                Tenant.employer_name.contains(search)
            )
        )

    if nationality:
        query = query.filter(Tenant.nationality == nationality)

    if employment_status:
        query = query.filter(Tenant.employment_status == employment_status)

    if credit_score_range:
        if credit_score_range == 'excellent':
            query = query.filter(Tenant.credit_score >= 800)
        elif credit_score_range == 'good':
            query = query.filter(Tenant.credit_score.between(700, 799))
        elif credit_score_range == 'fair':
            query = query.filter(Tenant.credit_score.between(600, 699))
        elif credit_score_range == 'poor':
            query = query.filter(Tenant.credit_score < 600)

    # ترتيب النتائج
    query = query.order_by(Tenant.created_at.desc())

    # تقسيم الصفحات
    tenants = query.paginate(
        page=page, per_page=12, error_out=False
    )

    # إحصائيات
    total_tenants = Tenant.query.count()
    active_tenants = db.session.query(Tenant).join(Lease).filter(Lease.status == 'نشط').count()
    avg_credit_score = db.session.query(db.func.avg(Tenant.credit_score)).scalar() or 0
    avg_credit_score = round(avg_credit_score, 0)

    # مستأجرين جدد هذا الشهر
    from datetime import datetime, timedelta
    start_of_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    new_tenants_this_month = Tenant.query.filter(Tenant.created_at >= start_of_month).count()

    # قائمة الجنسيات للفلتر
    nationalities = db.session.query(Tenant.nationality).distinct().all()
    nationalities = [nat[0] for nat in nationalities if nat[0]]

    # إضافة العقد الحالي لكل مستأجر
    for tenant in tenants.items:
        current_lease = Lease.query.filter_by(
            tenant_id=tenant.id,
            status='نشط'
        ).first()
        tenant.current_lease = current_lease

    return render_template('tenants/tenants.html',
                         tenants=tenants,
                         total_tenants=total_tenants,
                         active_tenants=active_tenants,
                         avg_credit_score=avg_credit_score,
                         new_tenants_this_month=new_tenants_this_month,
                         nationalities=nationalities)

@app.route('/tenants/enhanced')
@login_required
def tenants_enhanced():
    """واجهة إدارة المستأجرين المحسنة"""
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    nationality = request.args.get('nationality', '')
    credit_range = request.args.get('credit_range', '')
    payment_status = request.args.get('payment_status', '')

    # بناء الاستعلام
    query = Tenant.query

    if search:
        query = query.filter(
            db.or_(
                Tenant.full_name.contains(search),
                Tenant.phone_number.contains(search),
                Tenant.national_id.contains(search),
                Tenant.passport_number.contains(search),
                Tenant.email.contains(search)
            )
        )

    if status:
        query = query.filter(Tenant.status == status)

    if nationality:
        query = query.filter(Tenant.nationality == nationality)

    if credit_range:
        if credit_range == 'excellent':
            query = query.filter(Tenant.credit_score >= 750)
        elif credit_range == 'good':
            query = query.filter(Tenant.credit_score.between(650, 749))
        elif credit_range == 'fair':
            query = query.filter(Tenant.credit_score.between(550, 649))
        elif credit_range == 'poor':
            query = query.filter(Tenant.credit_score < 550)

    # ترتيب النتائج
    tenants = query.order_by(Tenant.created_date.desc()).all()

    # حساب الإحصائيات
    total_tenants = Tenant.query.count()
    active_tenants = Tenant.query.filter(Tenant.status == 'نشط').count()
    pending_tenants = Tenant.query.filter(Tenant.status == 'في الانتظار').count()

    # حساب المتأخرين في الدفع (يمكن تحسينه لاحقاً)
    late_payments = 0
    for tenant in tenants:
        if hasattr(tenant, 'get_payment_status') and tenant.get_payment_status() == 'متأخر':
            late_payments += 1

    # الحصول على قائمة الجنسيات للفلتر
    nationalities = db.session.query(Tenant.nationality).distinct().filter(Tenant.nationality.isnot(None)).all()
    nationalities = [nat[0] for nat in nationalities if nat[0]]

    return render_template('tenants/tenants_enhanced.html',
                         tenants=tenants,
                         total_tenants=total_tenants,
                         active_tenants=active_tenants,
                         pending_tenants=pending_tenants,
                         late_payments=late_payments,
                         nationalities=nationalities)

@app.route('/tenants/add', methods=['GET', 'POST'])
@login_required
def add_tenant():
    """إضافة مستأجر جديد"""
    if request.method == 'POST':
        try:
            # إنشاء مستأجر جديد
            tenant = Tenant(
                # المعلومات الشخصية
                full_name=request.form.get('full_name'),
                id_number=request.form.get('id_number'),
                nationality=request.form.get('nationality'),
                date_of_birth=datetime.strptime(request.form.get('date_of_birth'), '%Y-%m-%d').date() if request.form.get('date_of_birth') else None,
                gender=request.form.get('gender'),
                marital_status=request.form.get('marital_status'),
                family_members=request.form.get('family_members', type=int),
                children_count=request.form.get('children_count', type=int),

                # معلومات الاتصال
                phone=request.form.get('phone'),
                mobile=request.form.get('mobile'),
                email=request.form.get('email'),
                whatsapp=request.form.get('whatsapp'),
                current_address=request.form.get('current_address'),
                permanent_address=request.form.get('permanent_address'),

                # معلومات العمل
                employment_status=request.form.get('employment_status'),
                job_title=request.form.get('job_title'),
                employer_name=request.form.get('employer_name'),
                work_phone=request.form.get('work_phone'),
                work_address=request.form.get('work_address'),
                employment_start_date=datetime.strptime(request.form.get('employment_start_date'), '%Y-%m-%d').date() if request.form.get('employment_start_date') else None,
                work_experience_years=request.form.get('work_experience_years', type=int),

                # المعلومات المالية
                monthly_salary=request.form.get('monthly_salary', type=float),
                salary_currency=request.form.get('salary_currency', 'JOD'),
                additional_income=request.form.get('additional_income', type=float),
                total_monthly_income=request.form.get('total_monthly_income', type=float),
                bank_name=request.form.get('bank_name'),
                bank_account_number=request.form.get('bank_account_number'),
                credit_score=request.form.get('credit_score', type=int),
                debt_to_income_ratio=request.form.get('debt_to_income_ratio', type=float),

                # المراجع والطوارئ
                emergency_contact_name=request.form.get('emergency_contact_name'),
                emergency_contact_relationship=request.form.get('emergency_contact_relationship'),
                emergency_contact_phone=request.form.get('emergency_contact_phone'),
                emergency_contact_address=request.form.get('emergency_contact_address'),
                reference1_name=request.form.get('reference1_name'),
                reference1_phone=request.form.get('reference1_phone'),
                reference2_name=request.form.get('reference2_name'),
                reference2_phone=request.form.get('reference2_phone'),
                notes=request.form.get('notes'),

                # معلومات النظام
                created_by=current_user.id,
                created_at=datetime.now()
            )

            db.session.add(tenant)
            db.session.commit()
            backup_db_file()

            flash('تم إضافة المستأجر بنجاح', 'success')
            return redirect(url_for('tenant_details', id=tenant.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة المستأجر: {str(e)}', 'error')
            return redirect(url_for('tenants'))

    return redirect(url_for('tenants'))

@app.route('/tenants/<int:id>')
@login_required
def tenant_details(id):
    """عرض تفاصيل المستأجر"""
    tenant = Tenant.query.get_or_404(id)

    # البحث عن العقود المرتبطة
    tenant.leases = Lease.query.filter_by(tenant_id=id).order_by(Lease.created_at.desc()).all()

    return render_template('tenants/tenant_details.html', tenant=tenant)

@app.route('/tenants/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_tenant(id):
    """تعديل المستأجر"""
    tenant = Tenant.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # تحديث المعلومات الشخصية
            tenant.full_name = request.form.get('full_name')
            tenant.id_number = request.form.get('id_number')
            tenant.nationality = request.form.get('nationality')
            tenant.date_of_birth = datetime.strptime(request.form.get('date_of_birth'), '%Y-%m-%d').date() if request.form.get('date_of_birth') else None
            tenant.gender = request.form.get('gender')
            tenant.marital_status = request.form.get('marital_status')
            tenant.family_members = request.form.get('family_members', type=int)
            tenant.children_count = request.form.get('children_count', type=int)

            # تحديث معلومات الاتصال
            tenant.phone = request.form.get('phone')
            tenant.mobile = request.form.get('mobile')
            tenant.email = request.form.get('email')
            tenant.whatsapp = request.form.get('whatsapp')
            tenant.current_address = request.form.get('current_address')
            tenant.permanent_address = request.form.get('permanent_address')

            # تحديث معلومات العمل
            tenant.employment_status = request.form.get('employment_status')
            tenant.job_title = request.form.get('job_title')
            tenant.employer_name = request.form.get('employer_name')
            tenant.work_phone = request.form.get('work_phone')
            tenant.work_address = request.form.get('work_address')
            tenant.employment_start_date = datetime.strptime(request.form.get('employment_start_date'), '%Y-%m-%d').date() if request.form.get('employment_start_date') else None
            tenant.work_experience_years = request.form.get('work_experience_years', type=int)

            # تحديث المعلومات المالية
            tenant.monthly_salary = request.form.get('monthly_salary', type=float)
            tenant.salary_currency = request.form.get('salary_currency', 'JOD')
            tenant.additional_income = request.form.get('additional_income', type=float)
            tenant.total_monthly_income = request.form.get('total_monthly_income', type=float)
            tenant.bank_name = request.form.get('bank_name')
            tenant.bank_account_number = request.form.get('bank_account_number')
            tenant.credit_score = request.form.get('credit_score', type=int)
            tenant.debt_to_income_ratio = request.form.get('debt_to_income_ratio', type=float)

            # تحديث المراجع والطوارئ
            tenant.emergency_contact_name = request.form.get('emergency_contact_name')
            tenant.emergency_contact_relationship = request.form.get('emergency_contact_relationship')
            tenant.emergency_contact_phone = request.form.get('emergency_contact_phone')
            tenant.emergency_contact_address = request.form.get('emergency_contact_address')
            tenant.reference1_name = request.form.get('reference1_name')
            tenant.reference1_phone = request.form.get('reference1_phone')
            tenant.reference2_name = request.form.get('reference2_name')
            tenant.reference2_phone = request.form.get('reference2_phone')
            tenant.notes = request.form.get('notes')

            # تحديث معلومات النظام
            tenant.updated_by = current_user.id
            tenant.updated_at = datetime.now()

            db.session.commit()
            backup_db_file()

            flash('تم تحديث المستأجر بنجاح', 'success')
            return redirect(url_for('tenant_details', id=tenant.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث المستأجر: {str(e)}', 'error')

    return render_template('tenants/edit_tenant.html', tenant=tenant)

@app.route('/tenants/delete/<int:id>', methods=['DELETE'])
@login_required
def delete_tenant(id):
    """حذف المستأجر"""
    try:
        tenant = Tenant.query.get_or_404(id)

        # التحقق من وجود عقود نشطة
        active_leases = Lease.query.filter_by(tenant_id=id, status='نشط').count()
        if active_leases > 0:
            return jsonify({
                'success': False,
                'message': 'لا يمكن حذف المستأجر لوجود عقود إيجار نشطة'
            })

        # حذف المستأجر
        db.session.delete(tenant)
        db.session.commit()
        backup_db_file()

        return jsonify({
            'success': True,
            'message': 'تم حذف المستأجر بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء الحذف: {str(e)}'
        })

@app.route('/tenants/export')
@login_required
def export_tenants():
    """تصدير قائمة المستأجرين"""
    try:
        tenants = Tenant.query.all()

        # إنشاء ملف Excel
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('المستأجرين')

        # تنسيق الخلايا
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4472C4',
            'font_color': 'white',
            'border': 1
        })

        # كتابة العناوين
        headers = [
            'الاسم الكامل', 'رقم الهوية', 'الجنسية', 'الهاتف', 'البريد الإلكتروني',
            'حالة العمل', 'المسمى الوظيفي', 'جهة العمل', 'الراتب الشهري', 'التقييم الائتماني',
            'العنوان الحالي', 'تاريخ التسجيل'
        ]

        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)

        # كتابة البيانات
        for row, tenant in enumerate(tenants, 1):
            worksheet.write(row, 0, tenant.full_name or '')
            worksheet.write(row, 1, tenant.id_number or '')
            worksheet.write(row, 2, tenant.nationality or '')
            worksheet.write(row, 3, tenant.phone or '')
            worksheet.write(row, 4, tenant.email or '')
            worksheet.write(row, 5, tenant.employment_status or '')
            worksheet.write(row, 6, tenant.job_title or '')
            worksheet.write(row, 7, tenant.employer_name or '')
            worksheet.write(row, 8, tenant.monthly_salary or 0)
            worksheet.write(row, 9, tenant.credit_score or 0)
            worksheet.write(row, 10, tenant.current_address or '')
            worksheet.write(row, 11, tenant.created_at.strftime('%Y-%m-%d') if tenant.created_at else '')

        workbook.close()
        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'tenants_{datetime.now().strftime("%Y%m%d")}.xlsx'
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('tenants'))

@app.route('/tenants/export/<int:id>')
@login_required
def export_tenant(id):
    """تصدير بيانات مستأجر واحد"""
    try:
        tenant = Tenant.query.get_or_404(id)

        # إنشاء ملف PDF أو Excel حسب الحاجة
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('بيانات المستأجر')

        # تنسيق الخلايا
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4472C4',
            'font_color': 'white',
            'border': 1
        })

        # كتابة بيانات المستأجر
        data = [
            ['الاسم الكامل', tenant.full_name or ''],
            ['رقم الهوية', tenant.id_number or ''],
            ['الجنسية', tenant.nationality or ''],
            ['تاريخ الميلاد', tenant.date_of_birth.strftime('%Y-%m-%d') if tenant.date_of_birth else ''],
            ['الجنس', tenant.gender or ''],
            ['الحالة الاجتماعية', tenant.marital_status or ''],
            ['الهاتف', tenant.phone or ''],
            ['الجوال', tenant.mobile or ''],
            ['البريد الإلكتروني', tenant.email or ''],
            ['العنوان الحالي', tenant.current_address or ''],
            ['حالة العمل', tenant.employment_status or ''],
            ['المسمى الوظيفي', tenant.job_title or ''],
            ['جهة العمل', tenant.employer_name or ''],
            ['الراتب الشهري', f"{tenant.monthly_salary or 0} {tenant.salary_currency or 'JOD'}"],
            ['التقييم الائتماني', tenant.credit_score or 0],
            ['اسم البنك', tenant.bank_name or ''],
            ['رقم الحساب', tenant.bank_account_number or ''],
        ]

        for row, (label, value) in enumerate(data):
            worksheet.write(row, 0, label, header_format)
            worksheet.write(row, 1, str(value))

        workbook.close()
        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'tenant_{tenant.full_name}_{datetime.now().strftime("%Y%m%d")}.xlsx'
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('tenant_details', id=id))

# ==================== LEASE MANAGEMENT ROUTES ====================

@app.route('/leases')
@login_required
def leases():
    """عرض قائمة عقود الإيجار مع البحث والفلترة"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    property_type = request.args.get('property_type', '')
    payment_status = request.args.get('payment_status', '')

    # بناء الاستعلام
    query = Lease.query.join(Property, Lease.property_id == Property.id, isouter=True)\
                      .join(Tenant, Lease.tenant_id == Tenant.id, isouter=True)

    # تطبيق الفلاتر
    if search:
        query = query.filter(
            db.or_(
                Lease.lease_number.contains(search),
                Property.name.contains(search),
                Tenant.full_name.contains(search),
                Tenant.phone.contains(search)
            )
        )

    if status:
        if status == 'expiring':
            # العقود التي ستنتهي خلال 30 يوم
            from datetime import datetime, timedelta
            expiry_date = datetime.now().date() + timedelta(days=30)
            query = query.filter(
                Lease.end_date <= expiry_date,
                Lease.status == 'نشط'
            )
        else:
            query = query.filter(Lease.status == status)

    if property_type:
        query = query.filter(Property.property_type == property_type)

    # ترتيب النتائج
    query = query.order_by(Lease.created_at.desc())

    # تقسيم الصفحات
    leases = query.paginate(
        page=page, per_page=12, error_out=False
    )

    # إحصائيات
    total_leases = Lease.query.count()
    active_leases = Lease.query.filter_by(status='نشط').count()
    expired_leases = Lease.query.filter_by(status='منتهي').count()

    # العقود التي ستنتهي قريباً
    from datetime import datetime, timedelta
    expiry_date = datetime.now().date() + timedelta(days=30)
    expiring_leases_count = Lease.query.filter(
        Lease.end_date <= expiry_date,
        Lease.status == 'نشط'
    ).count()

    # إجمالي الدخل الشهري
    total_monthly_income = db.session.query(db.func.sum(Lease.monthly_rent))\
                                   .filter(Lease.status == 'نشط').scalar() or 0

    # أنواع العقارات للفلتر
    property_types = db.session.query(Property.property_type).distinct().all()
    property_types = [pt[0] for pt in property_types if pt[0]]

    # إضافة معلومات إضافية لكل عقد
    for lease in leases.items:
        # حساب تقدم العقد
        if lease.start_date and lease.end_date and lease.status == 'نشط':
            total_days = (lease.end_date - lease.start_date).days
            elapsed_days = (datetime.now().date() - lease.start_date).days
            lease.progress_percentage = min(100, max(0, (elapsed_days / total_days) * 100)) if total_days > 0 else 0
            lease.days_remaining = max(0, (lease.end_date - datetime.now().date()).days)
        else:
            lease.progress_percentage = 0
            lease.days_remaining = 0

        # حالة الدفع (مبسطة)
        lease.payment_status = 'current'  # يمكن تطويرها لاحقاً

    # العقارات والمستأجرين المتاحين للعقود الجديدة
    available_properties = Property.query.filter_by(status='متاح').all()
    available_tenants = Tenant.query.all()

    return render_template('leases/leases.html',
                         leases=leases,
                         total_leases=total_leases,
                         active_leases=active_leases,
                         expiring_leases=expired_leases,
                         expiring_leases_count=expiring_leases_count,
                         total_monthly_income=total_monthly_income,
                         property_types=property_types,
                         available_properties=available_properties,
                         available_tenants=available_tenants)

@app.route('/leases/enhanced')
@login_required
def leases_enhanced():
    """واجهة إدارة عقود الإيجار المحسنة"""
    from datetime import datetime, timedelta

    search = request.args.get('search', '')
    status = request.args.get('status', '')
    property_type = request.args.get('property_type', '')
    expiry_period = request.args.get('expiry_period', '')
    min_rent = request.args.get('min_rent', type=float)
    max_rent = request.args.get('max_rent', type=float)

    # بناء الاستعلام
    query = Lease.query.join(Property, Lease.property_id == Property.id, isouter=True)\
                      .join(Tenant, Lease.tenant_id == Tenant.id, isouter=True)

    if search:
        query = query.filter(
            db.or_(
                Lease.lease_code.contains(search),
                Property.name.contains(search),
                Tenant.full_name.contains(search),
                Property.property_code.contains(search)
            )
        )

    if status:
        query = query.filter(Lease.status == status)

    if property_type:
        query = query.filter(Property.property_type == property_type)

    if expiry_period:
        today = datetime.now().date()
        if expiry_period == 'week':
            end_date = today + timedelta(days=7)
        elif expiry_period == 'month':
            end_date = today + timedelta(days=30)
        elif expiry_period == 'quarter':
            end_date = today + timedelta(days=90)

        query = query.filter(
            Lease.end_date.between(today, end_date),
            Lease.status == 'نشط'
        )

    if min_rent:
        query = query.filter(Lease.monthly_rent >= min_rent)

    if max_rent:
        query = query.filter(Lease.monthly_rent <= max_rent)

    # ترتيب النتائج
    leases = query.order_by(Lease.created_at.desc()).all()

    # حساب الإحصائيات
    total_leases = Lease.query.count()
    active_leases = Lease.query.filter(Lease.status == 'نشط').count()
    expired_leases = Lease.query.filter(Lease.status == 'منتهي').count()

    # حساب العقود التي تنتهي قريباً
    today = datetime.now().date()
    expiring_soon_date = today + timedelta(days=30)
    expiring_soon = Lease.query.filter(
        Lease.end_date.between(today, expiring_soon_date),
        Lease.status == 'نشط'
    ).count()

    return render_template('leases/leases_enhanced.html',
                         leases=leases,
                         total_leases=total_leases,
                         active_leases=active_leases,
                         expired_leases=expired_leases,
                         expiring_soon=expiring_soon)

@app.route('/leases/add', methods=['GET', 'POST'])
@login_required
def add_lease():
    """إضافة عقد إيجار جديد"""
    if request.method == 'POST':
        try:
            # إنشاء رقم عقد تلقائي إذا لم يتم توفيره
            lease_number = request.form.get('lease_number')
            if not lease_number:
                last_lease = Lease.query.order_by(Lease.id.desc()).first()
                lease_number = f"L{(last_lease.id + 1) if last_lease else 1:06d}"

            # إنشاء عقد جديد
            lease = Lease(
                # المعلومات الأساسية
                lease_number=lease_number,
                property_id=request.form.get('property_id', type=int),
                tenant_id=request.form.get('tenant_id', type=int),
                lease_type=request.form.get('lease_type'),
                start_date=datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date() if request.form.get('start_date') else None,
                end_date=datetime.strptime(request.form.get('end_date'), '%Y-%m-%d').date() if request.form.get('end_date') else None,
                lease_duration_months=request.form.get('lease_duration_months', type=int),
                status=request.form.get('status', 'نشط'),

                # المعلومات المالية
                monthly_rent=request.form.get('monthly_rent', type=float),
                currency=request.form.get('currency', 'JOD'),
                security_deposit=request.form.get('security_deposit', type=float),
                commission_amount=request.form.get('commission_amount', type=float),
                payment_frequency=request.form.get('payment_frequency', 'شهري'),
                payment_due_day=request.form.get('payment_due_day', type=int),
                late_fee_amount=request.form.get('late_fee_amount', type=float),
                grace_period_days=request.form.get('grace_period_days', type=int),
                total_lease_value=request.form.get('total_lease_value', type=float),

                # الشروط والأحكام
                terms_and_conditions=request.form.get('terms_and_conditions'),
                special_conditions=request.form.get('special_conditions'),
                pets_allowed=bool(request.form.get('pets_allowed')),
                smoking_allowed=bool(request.form.get('smoking_allowed')),
                subletting_allowed=bool(request.form.get('subletting_allowed')),
                auto_renewal=bool(request.form.get('auto_renewal')),

                # معلومات إضافية
                utilities_included=request.form.get('utilities_included'),
                maintenance_responsibility=request.form.get('maintenance_responsibility'),
                parking_spaces=request.form.get('parking_spaces', type=int),
                furnished_status=request.form.get('furnished_status'),
                notes=request.form.get('notes'),

                # معلومات النظام
                created_by=current_user.id,
                created_at=datetime.now()
            )

            db.session.add(lease)

            # تحديث حالة العقار إلى مؤجر
            if lease.property_id:
                property_obj = Property.query.get(lease.property_id)
                if property_obj:
                    property_obj.status = 'مؤجر'
                    property_obj.current_tenant_id = lease.tenant_id

            db.session.commit()
            backup_db_file()

            flash('تم إنشاء العقد بنجاح', 'success')
            return redirect(url_for('lease_details', id=lease.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء العقد: {str(e)}', 'error')
            return redirect(url_for('leases'))

    return redirect(url_for('leases'))

@app.route('/leases/<int:id>')
@login_required
def lease_details(id):
    """عرض تفاصيل العقد"""
    lease = Lease.query.get_or_404(id)

    # حساب تقدم العقد
    if lease.start_date and lease.end_date and lease.status == 'نشط':
        total_days = (lease.end_date - lease.start_date).days
        elapsed_days = (datetime.now().date() - lease.start_date).days
        lease.progress_percentage = min(100, max(0, (elapsed_days / total_days) * 100)) if total_days > 0 else 0
        lease.days_remaining = max(0, (lease.end_date - datetime.now().date()).days)
    else:
        lease.progress_percentage = 0
        lease.days_remaining = 0

    # إحصائيات مالية (مبسطة - يمكن تطويرها لاحقاً)
    lease.total_paid = 0
    lease.remaining_amount = lease.total_lease_value or 0
    lease.payment_count = 0
    lease.overdue_payments = 0
    lease.recent_payments = []

    return render_template('leases/lease_details.html', lease=lease)

@app.route('/leases/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_lease(id):
    """تعديل العقد"""
    lease = Lease.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # تحديث المعلومات الأساسية
            lease.lease_number = request.form.get('lease_number')
            lease.property_id = request.form.get('property_id', type=int)
            lease.tenant_id = request.form.get('tenant_id', type=int)
            lease.lease_type = request.form.get('lease_type')
            lease.start_date = datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date() if request.form.get('start_date') else None
            lease.end_date = datetime.strptime(request.form.get('end_date'), '%Y-%m-%d').date() if request.form.get('end_date') else None
            lease.lease_duration_months = request.form.get('lease_duration_months', type=int)
            lease.status = request.form.get('status')

            # تحديث المعلومات المالية
            lease.monthly_rent = request.form.get('monthly_rent', type=float)
            lease.currency = request.form.get('currency', 'JOD')
            lease.security_deposit = request.form.get('security_deposit', type=float)
            lease.commission_amount = request.form.get('commission_amount', type=float)
            lease.payment_frequency = request.form.get('payment_frequency', 'شهري')
            lease.payment_due_day = request.form.get('payment_due_day', type=int)
            lease.late_fee_amount = request.form.get('late_fee_amount', type=float)
            lease.grace_period_days = request.form.get('grace_period_days', type=int)
            lease.total_lease_value = request.form.get('total_lease_value', type=float)

            # تحديث الشروط والأحكام
            lease.terms_and_conditions = request.form.get('terms_and_conditions')
            lease.special_conditions = request.form.get('special_conditions')
            lease.pets_allowed = bool(request.form.get('pets_allowed'))
            lease.smoking_allowed = bool(request.form.get('smoking_allowed'))
            lease.subletting_allowed = bool(request.form.get('subletting_allowed'))
            lease.auto_renewal = bool(request.form.get('auto_renewal'))

            # تحديث معلومات إضافية
            lease.utilities_included = request.form.get('utilities_included')
            lease.maintenance_responsibility = request.form.get('maintenance_responsibility')
            lease.parking_spaces = request.form.get('parking_spaces', type=int)
            lease.furnished_status = request.form.get('furnished_status')
            lease.notes = request.form.get('notes')

            # تحديث معلومات النظام
            lease.updated_by = current_user.id
            lease.updated_at = datetime.now()

            db.session.commit()
            backup_db_file()

            flash('تم تحديث العقد بنجاح', 'success')
            return redirect(url_for('lease_details', id=lease.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث العقد: {str(e)}', 'error')

    # العقارات والمستأجرين المتاحين
    available_properties = Property.query.all()
    available_tenants = Tenant.query.all()

    return render_template('leases/edit_lease.html',
                         lease=lease,
                         available_properties=available_properties,
                         available_tenants=available_tenants)

@app.route('/leases/<int:id>/terminate', methods=['POST'])
@login_required
def terminate_lease(id):
    """إنهاء العقد"""
    try:
        lease = Lease.query.get_or_404(id)

        # تحديث حالة العقد
        lease.status = 'ملغي'
        lease.updated_by = current_user.id
        lease.updated_at = datetime.now()

        # تحديث حالة العقار إلى متاح
        if lease.property_id:
            property_obj = Property.query.get(lease.property_id)
            if property_obj:
                property_obj.status = 'متاح'
                property_obj.current_tenant_id = None

        db.session.commit()
        backup_db_file()

        return jsonify({
            'success': True,
            'message': 'تم إنهاء العقد بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء إنهاء العقد: {str(e)}'
        })

@app.route('/leases/delete/<int:id>', methods=['DELETE'])
@login_required
def delete_lease(id):
    """حذف العقد"""
    try:
        lease = Lease.query.get_or_404(id)

        # تحديث حالة العقار إلى متاح
        if lease.property_id:
            property_obj = Property.query.get(lease.property_id)
            if property_obj:
                property_obj.status = 'متاح'
                property_obj.current_tenant_id = None

        # حذف العقد
        db.session.delete(lease)
        db.session.commit()
        backup_db_file()

        return jsonify({
            'success': True,
            'message': 'تم حذف العقد بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء الحذف: {str(e)}'
        })

@app.route('/leases/export')
@login_required
def export_leases():
    """تصدير قائمة العقود"""
    try:
        leases = Lease.query.join(Property, Lease.property_id == Property.id, isouter=True)\
                           .join(Tenant, Lease.tenant_id == Tenant.id, isouter=True).all()

        # إنشاء ملف Excel
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('عقود الإيجار')

        # تنسيق الخلايا
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#667eea',
            'font_color': 'white',
            'border': 1
        })

        # كتابة العناوين
        headers = [
            'رقم العقد', 'العقار', 'المستأجر', 'نوع العقد', 'تاريخ البداية', 'تاريخ الانتهاء',
            'الإيجار الشهري', 'العملة', 'الحالة', 'مدة العقد (شهر)', 'تاريخ الإنشاء'
        ]

        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)

        # كتابة البيانات
        for row, lease in enumerate(leases, 1):
            worksheet.write(row, 0, lease.lease_number or f'L{lease.id:06d}')
            worksheet.write(row, 1, lease.property.name if lease.property else 'عقار محذوف')
            worksheet.write(row, 2, lease.tenant.full_name if lease.tenant else 'مستأجر محذوف')
            worksheet.write(row, 3, lease.lease_type or '')
            worksheet.write(row, 4, lease.start_date.strftime('%Y-%m-%d') if lease.start_date else '')
            worksheet.write(row, 5, lease.end_date.strftime('%Y-%m-%d') if lease.end_date else '')
            worksheet.write(row, 6, lease.monthly_rent or 0)
            worksheet.write(row, 7, lease.currency or 'JOD')
            worksheet.write(row, 8, lease.status or '')
            worksheet.write(row, 9, lease.lease_duration_months or 0)
            worksheet.write(row, 10, lease.created_at.strftime('%Y-%m-%d') if lease.created_at else '')

        workbook.close()
        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'leases_{datetime.now().strftime("%Y%m%d")}.xlsx'
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('leases'))

@app.route('/leases/<int:id>/print')
@login_required
def print_lease(id):
    """طباعة العقد"""
    lease = Lease.query.get_or_404(id)
    return render_template('leases/print_lease.html', lease=lease)

# تم حذف routes المستأجرين - سيتم إعادة بناؤها

@app.route('/clients')
@login_required
def clients_list():
    # حقول البحث المتقدم
    name = request.args.get('name', '').strip()
    national_id = request.args.get('national_id', '').strip()
    phone = request.args.get('phone', '').strip()
    email = request.args.get('email', '').strip()
    address = request.args.get('address', '').strip()
    q = request.args.get('q', '').strip()

    clients_query = Client.query
    if name:
        clients_query = clients_query.filter(Client.name.ilike(f'%{name}%'))
    if national_id:
        clients_query = clients_query.filter(Client.national_id.ilike(f'%{national_id}%'))
    if phone:
        clients_query = clients_query.filter(Client.phone.ilike(f'%{phone}%'))
    if email:
        clients_query = clients_query.filter(Client.email.ilike(f'%{email}%'))
    if address:
        clients_query = clients_query.filter(Client.address.ilike(f'%{address}%'))
    if q:
        # بحث عام في كل الحقول
        clients_query = clients_query.filter(
            (Client.name.ilike(f'%{q}%')) |
            (Client.national_id.ilike(f'%{q}%')) |
            (Client.phone.ilike(f'%{q}%')) |
            (Client.email.ilike(f'%{q}%')) |
            (Client.address.ilike(f'%{q}%'))
        )
    clients = clients_query.all()
    return render_template('clients/list.html', clients=clients)

@app.route('/clients/add', methods=['GET', 'POST'])
@login_required
def add_client():
    if request.method == 'POST':
        try:
            name = request.form['name']
            national_id = request.form.get('id_number', '')
            phone = request.form['phone']
            email = request.form.get('email', '')
            address = request.form.get('address', '')
            birth_date = request.form.get('birth_date')
            occupation = request.form.get('occupation', '')
            notes = request.form.get('notes', '')
            role = request.form.get('role', 'موكل')

            # تحويل تاريخ الميلاد إذا كان موجوداً
            birth_date_obj = None
            if birth_date:
                from datetime import datetime
                birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d').date()

            client = Client(
                name=name,
                national_id=national_id,
                phone=phone,
                email=email,
                address=address,
                birth_date=birth_date_obj,
                occupation=occupation,
                notes=notes,
                role=role
            )
            db.session.add(client)
            db.session.commit()
            backup_db_file()

            # إذا كان الطلب من AJAX، إرجاع JSON
            if request.headers.get('Content-Type') == 'application/x-www-form-urlencoded' and request.headers.get('X-CSRFToken'):
                return jsonify({'success': True, 'message': 'تمت إضافة العميل بنجاح'})

            flash('تمت إضافة العميل بنجاح', 'success')
            return redirect(url_for('clients_list'))

        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
            flash(f'حدث خطأ أثناء إضافة العميل: {str(e)}', 'danger')
            return redirect(url_for('clients_list'))

    return render_template('clients/add.html')

@app.route('/clients/<int:client_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_client(client_id):
    client = Client.query.get_or_404(client_id)
    if request.method == 'POST':
        try:
            client.name = request.form['name']
            client.national_id = request.form.get('national_id', '')
            client.phone = request.form['phone']
            client.email = request.form['email']
            client.address = request.form['address']
            client.role = request.form.get('role', 'موكل')
            db.session.commit()
            backup_db_file()
            flash('تم تعديل بيانات العميل', 'success')
            return redirect(url_for('clients_list'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تعديل العميل: {str(e)}', 'danger')
            return redirect(url_for('clients_list'))
    return render_template('clients/edit.html', client=client)

@app.route('/clients/<int:client_id>/delete', methods=['POST'])
@login_required
def delete_client(client_id):
    try:
        client = Client.query.get_or_404(client_id)
        db.session.delete(client)
        db.session.commit()
        backup_db_file()
        flash('تم حذف العميل', 'success')
        return redirect(url_for('clients_list'))
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف العميل: {str(e)}', 'danger')
        return redirect(url_for('clients_list'))

@app.route('/dashboard', methods=['GET', 'POST'])
@login_required
def dashboard():
    # فحص صحة الجلسة
    if not validate_session():
        return redirect(url_for('login'))
    today = datetime.today()
    cases_count = Case.query.count()
    properties_count = Property.query.count() if 'Property' in globals() else 0
    leases_count = 0  # لتحديثها لاحقاً
    tasks_count = Task.query.count() if 'Task' in globals() else 0
    appointments_count = Appointment.query.count() if 'Appointment' in globals() else 0

    # البحث
    search_results = None
    search_field = request.form.get('search_field') if request.method == 'POST' else None
    search_value = request.form.get('search_value') if request.method == 'POST' else None
    if search_field and search_value:
        if search_field == 'client_name':
            search_results = Case.query.join(Client).filter(Client.name.contains(search_value)).all()
        elif search_field == 'national_id':
            search_results = Case.query.join(Client).filter(Client.national_id.contains(search_value)).all()
        elif search_field == 'court':
            search_results = Case.query.filter(Case.court.contains(search_value)).all()
        else:
            search_results = []

    # إحصائيات
    courts_stats = db.session.query(Case.court, func.count(Case.id)).group_by(Case.court).all()
    clients_stats = db.session.query(Client.name, func.count(Case.id)).join(Case).group_by(Client.name).all()
    types_stats = db.session.query(Case.type, func.count(Case.id)).group_by(Case.type).all()

    # تحويل النتائج إلى dict حتى تكون JSON serializable
    courts_stats = {row[0]: row[1] for row in courts_stats}
    clients_stats = {row[0]: row[1] for row in clients_stats}
    types_stats = {row[0]: row[1] for row in types_stats}
    # المهام القريبة (خلال 3 أيام) وغير المنجزة فقط
    upcoming_tasks = Task.query.filter(Task.due_date >= today, Task.due_date <= today + timedelta(days=3), Task.status != 'منجزة').all() if 'Task' in globals() else []
    # الأقساط المالية المستحقة خلال أسبوع
    upcoming_installments = []
    if 'LeaseInstallment' in globals():
        upcoming_installments = db.session.query(LeaseInstallment).filter(
            LeaseInstallment.due_date >= today,
            LeaseInstallment.due_date <= today + timedelta(days=7),
            LeaseInstallment.is_paid == False
        ).all()
    # أقرب موعد خلال يوم واحد فقط
    upcoming_appointment = None
    nearest_appointment = Appointment.query.filter(Appointment.date >= today).order_by(Appointment.date.asc()).first()
    if nearest_appointment:
        delta = nearest_appointment.date - today
        if delta.days == 0:
            upcoming_appointment = nearest_appointment

    def safe_task_dict(task):
        return {
            'id': task.id,
            'title': task.title or '',
            'description': task.description or '',
            'due_date': task.due_date.strftime('%Y-%m-%dT%H:%M') if task.due_date else '',
            'status': task.status or ''
        }
    def safe_installment_dict(inst):
        return {
            'id': inst.id,
            'lease_id': inst.lease_id,
            'due_date': inst.due_date.strftime('%Y-%m-%d') if inst.due_date else '',
            'amount': inst.amount,
            'paid': inst.paid,
            'payment_date': inst.payment_date.strftime('%Y-%m-%d') if inst.payment_date else ''
        }
    def safe_appointment_dict(app):
        return {
            'id': app.id,
            'subject': app.subject or '',
            'date': app.date.strftime('%Y-%m-%dT%H:%M') if app.date else '',
            'location': app.location or '',
            'notes': app.notes or '',
            'client': {'name': app.client.name} if app.client else {},
            'case': {'title': app.case.title} if app.case else {}
        }
    upcoming_tasks_safe = [safe_task_dict(t) for t in upcoming_tasks]
    upcoming_installments_safe = [safe_installment_dict(i) for i in upcoming_installments]

    # عدد المهام غير المنجزة
    not_done_tasks_count = Task.query.filter(Task.status != 'منجزة').count() if 'Task' in globals() else 0

    # البيانات المتأخرة للتنبيهات
    overdue_tasks = []
    overdue_appointments = []
    overdue_payments = []

    # المهام المتأخرة
    if 'TaskItem' in globals():
        overdue_tasks = TaskItem.query.filter(
            TaskItem.due_date < datetime.now(timezone.utc),
            TaskItem.completed == False
        ).order_by(TaskItem.due_date).limit(5).all()
    elif 'Task' in globals():
        overdue_tasks = Task.query.filter(
            Task.due_date < today,
            Task.status != 'منجزة'
        ).order_by(Task.due_date).limit(5).all()

    # المواعيد المتأخرة أو خلال 24 ساعة
    tomorrow = today + timedelta(days=1)
    if 'Appointment' in globals():
        overdue_appointments = Appointment.query.filter(
            Appointment.date <= tomorrow
        ).order_by(Appointment.date).limit(5).all()

    return render_template('dashboard.html',
        cases_count=cases_count,
        properties_count=properties_count,
        leases_count=leases_count,
        tasks_count=tasks_count,
        not_done_tasks_count=not_done_tasks_count,
        appointments_count=appointments_count,
        courts_stats=courts_stats,
        clients_stats=clients_stats,
        types_stats=types_stats,
        search_results=search_results,
        search_field=search_field,
        search_value=search_value,
        upcoming_tasks=upcoming_tasks_safe,
        upcoming_installments=upcoming_installments_safe,
        upcoming_appointment=upcoming_appointment,
        overdue_tasks=overdue_tasks,
        overdue_appointments=overdue_appointments,
        overdue_payments=overdue_payments,
    )

# ------------------ مهام ------------------
@app.route('/tasks')
@login_required
def tasks_list():
    tasks = Task.query.order_by(Task.due_date.asc()).all()
    return render_template('tasks/list.html', tasks=tasks)

@app.route('/tasks/add', methods=['GET', 'POST'])
@login_required
def add_task():
    case_id = request.args.get('case_id')
    if request.method == 'POST':
        title = request.form['title']
        description = request.form['description']
        due_date = request.form['due_date'] or None
        status = request.form['status']
        user_id = current_user.id
        from datetime import datetime
        due_date_obj = datetime.strptime(due_date, '%Y-%m-%dT%H:%M') if due_date else None
        task = Task(title=title, description=description, due_date=due_date_obj, status=status, user_id=user_id)
        if case_id:
            task.case_id = int(case_id)
        db.session.add(task)
        db.session.commit()
        backup_db_file()
        flash('تمت إضافة المهمة بنجاح', 'success')
        return redirect(url_for('tasks_list'))
    return render_template('tasks/add.html', case_id=case_id)

@app.route('/tasks/<int:task_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_task(task_id):
    task = Task.query.get_or_404(task_id)
    if request.method == 'POST':
        task.title = request.form['title']
        task.description = request.form['description']
        due_date = request.form['due_date'] or None
        from datetime import datetime
        task.due_date = datetime.strptime(due_date, '%Y-%m-%dT%H:%M') if due_date else None
        task.status = request.form['status']
        db.session.commit()
        backup_db_file()
        flash('تم تعديل المهمة', 'success')
        return redirect(url_for('tasks_list'))
    return render_template('tasks/edit.html', task=task)

@app.route('/tasks/<int:task_id>/delete', methods=['POST'])
@login_required
def delete_task(task_id):
    task = Task.query.get_or_404(task_id)
    # حذف منطقي: تغيير الحالة إلى "محذوفة" بدلاً من الحذف النهائي
    task.status = 'محذوفة'
    db.session.commit()
    backup_db_file()
    flash('تم نقل المهمة إلى المهام المحذوفة', 'success')
    return redirect(request.referrer or url_for('tasks_list'))

@app.route('/tasks/<int:task_id>/toggle_status', methods=['POST'])
@login_required
def toggle_task_status(task_id):
    task = Task.query.get_or_404(task_id)
    # تبديل الحالة بين منجزة وغير منجزة
    if task.status == 'منجزة':
        task.status = 'غير منجزة'
    else:
        task.status = 'منجزة'
    db.session.commit()
    backup_db_file()
    # دعم AJAX: إذا كان الطلب من نوع JSON أو AJAX، أرجع JSON
    if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.headers.get('Accept') == 'application/json':
        return {'success': True, 'new_status': task.status}
    flash('تم تحديث حالة المهمة', 'success')
    return redirect(request.referrer or url_for('tasks_list'))

@app.route('/tasks/manage')
@login_required
def manage_tasks():
    from datetime import datetime
    status_filter = request.args.get('status')
    if status_filter == 'done':
        tasks = Task.query.filter_by(status='منجزة').order_by(Task.due_date.asc()).all()
    elif status_filter == 'not_done':
        tasks = Task.query.filter(Task.status != 'منجزة').order_by(Task.due_date.asc()).all()
    elif status_filter == 'deleted':
        # المهام المحذوفة: التي حالتها "محذوفة"
        tasks = Task.query.filter_by(status='محذوفة').order_by(Task.due_date.asc()).all()
    else:
        tasks = Task.query.order_by(Task.due_date.asc()).all()
    current_time = datetime.now()
    return render_template('tasks/manage.html', tasks=tasks, status_filter=status_filter, current_time=current_time)

# ------------------ مواعيد ------------------
@app.route('/appointments')
@login_required
def appointments_list():
    appointments = Appointment.query.order_by(Appointment.date.asc()).all()
    return render_template('appointments/list.html', appointments=appointments)

@app.route('/appointments/add', methods=['GET', 'POST'])
@login_required
def add_appointment():
    from .models import Client, Case
    clients = Client.query.all()
    cases = Case.query.all()
    case_id = request.args.get('case_id')
    if request.method == 'POST':
        subject = request.form['subject']
        date = request.form['date']
        location = request.form['location']
        notes = request.form['notes']
        client_id = request.form.get('client_id') or None
        case_id_form = request.form.get('case_id') or case_id
        from datetime import datetime
        date_obj = datetime.strptime(date, '%Y-%m-%dT%H:%M') if date else None
        appointment = Appointment(subject=subject, date=date_obj, location=location, notes=notes,
                                  client_id=client_id if client_id else None, case_id=case_id_form if case_id_form else None)
        db.session.add(appointment)
        db.session.commit()
        backup_db_file()
        flash('تمت إضافة الموعد بنجاح', 'success')
        return redirect(url_for('appointments_list'))
    return render_template('appointments/add.html', clients=clients, cases=cases, case_id=case_id)

@app.route('/appointments/<int:appointment_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_appointment(appointment_id):
    from .models import Client, Case
    appointment = Appointment.query.get_or_404(appointment_id)
    clients = Client.query.all()
    cases = Case.query.all()
    if request.method == 'POST':
        appointment.subject = request.form['subject']
        date = request.form['date']
        appointment.location = request.form['location']
        appointment.notes = request.form['notes']
        appointment.client_id = request.form.get('client_id') or None
        appointment.case_id = request.form.get('case_id') or None
        from datetime import datetime
        appointment.date = datetime.strptime(date, '%Y-%m-%dT%H:%M') if date else None
        db.session.commit()
        backup_db_file()
        flash('تم تعديل الموعد', 'success')
        return redirect(url_for('appointments_list'))
    return render_template('appointments/edit.html', appointment=appointment, clients=clients, cases=cases)

@app.route('/appointments/<int:appointment_id>/delete', methods=['POST'])
@login_required
def delete_appointment(appointment_id):
    appointment = Appointment.query.get_or_404(appointment_id)
    db.session.delete(appointment)
    db.session.commit()
    backup_db_file()
    flash('تم حذف الموعد', 'success')
    return redirect(url_for('appointments_list'))

# ------------------ إدارة المواعيد المتقدمة ------------------
@app.route('/appointments/management')
@login_required
def appointments_management():
    """صفحة إدارة المواعيد المتقدمة"""
    from .models import Client, Case
    from .calendar_models import Event
    from datetime import datetime, timedelta

    # جلب جميع المواعيد من Event model (النظام المحسن)
    appointments = Event.query.filter(
        Event.event_type.in_(['موعد عام', 'جلسة محكمة', 'اجتماع عميل', 'مهمة'])
    ).order_by(Event.start_datetime.desc()).all()

    # تحويل البيانات للتوافق مع القالب
    appointments_data = []
    for event in appointments:
        appointment_data = {
            'id': event.id,
            'title': event.title,
            'description': event.description,
            'date': event.start_datetime.date() if event.start_datetime else None,
            'time': event.start_datetime.time() if event.start_datetime else None,
            'location': event.location,
            'type': event.event_type,
            'status': event.status,
            'priority': event.priority,
            'client': event.client if hasattr(event, 'client') else None,
            'case': event.case if hasattr(event, 'case') else None,
            'client_id': event.client_id,
            'case_id': event.case_id
        }
        appointments_data.append(appointment_data)

    # جلب العملاء والقضايا للنوافذ المنبثقة
    clients = Client.query.all()
    cases = Case.query.all()

    return render_template('appointments/appointments_management.html',
                         appointments=appointments_data,
                         clients=clients,
                         cases=cases)

# ------------------ API للمواعيد المتقدمة ------------------
@app.route('/api/appointments', methods=['POST'])
@login_required
def api_create_appointment():
    """إنشاء موعد جديد عبر API"""
    try:
        from .calendar_models import Event
        from datetime import datetime

        # جلب البيانات من النموذج
        title = request.form.get('title')
        appointment_type = request.form.get('type')
        date = request.form.get('date')
        time = request.form.get('time')
        location = request.form.get('location', '')
        priority = request.form.get('priority', 'متوسطة')
        description = request.form.get('description', '')
        status = request.form.get('status', 'مجدول')
        client_id = request.form.get('client_id') or None
        case_id = request.form.get('case_id') or None
        reminder_minutes = request.form.get('reminder_minutes', 30)

        # التحقق من البيانات المطلوبة
        if not all([title, appointment_type, date, time]):
            return jsonify({'success': False, 'message': 'يرجى ملء جميع الحقول المطلوبة'})

        # تحويل التاريخ والوقت
        start_datetime = datetime.strptime(f"{date} {time}", '%Y-%m-%d %H:%M')
        end_datetime = start_datetime + timedelta(hours=1)  # افتراضي ساعة واحدة

        # إنشاء الموعد الجديد
        new_appointment = Event(
            title=title,
            description=description,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            location=location,
            event_type=appointment_type,
            priority=priority,
            status=status,
            client_id=int(client_id) if client_id else None,
            case_id=int(case_id) if case_id else None,
            created_by=current_user.id
        )

        db.session.add(new_appointment)
        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم إنشاء الموعد بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@app.route('/api/appointments/<int:appointment_id>', methods=['GET'])
@login_required
def api_get_appointment(appointment_id):
    """جلب بيانات موعد محدد"""
    try:
        from .calendar_models import Event

        appointment = Event.query.get_or_404(appointment_id)

        appointment_data = {
            'id': appointment.id,
            'title': appointment.title,
            'description': appointment.description,
            'date': appointment.start_datetime.strftime('%Y-%m-%d') if appointment.start_datetime else '',
            'time': appointment.start_datetime.strftime('%H:%M') if appointment.start_datetime else '',
            'location': appointment.location or '',
            'type': appointment.event_type,
            'priority': appointment.priority,
            'status': appointment.status,
            'client_id': appointment.client_id,
            'case_id': appointment.case_id
        }

        return jsonify({'success': True, 'appointment': appointment_data})

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@app.route('/api/appointments/<int:appointment_id>', methods=['PUT'])
@login_required
def api_update_appointment(appointment_id):
    """تحديث موعد موجود"""
    try:
        from .calendar_models import Event
        from datetime import datetime, timedelta

        appointment = Event.query.get_or_404(appointment_id)

        # تحديث البيانات
        appointment.title = request.form.get('title', appointment.title)
        appointment.event_type = request.form.get('type', appointment.event_type)
        appointment.location = request.form.get('location', appointment.location)
        appointment.priority = request.form.get('priority', appointment.priority)
        appointment.description = request.form.get('description', appointment.description)
        appointment.status = request.form.get('status', appointment.status)

        # تحديث التاريخ والوقت
        date = request.form.get('date')
        time = request.form.get('time')
        if date and time:
            start_datetime = datetime.strptime(f"{date} {time}", '%Y-%m-%d %H:%M')
            appointment.start_datetime = start_datetime
            appointment.end_datetime = start_datetime + timedelta(hours=1)

        # تحديث العلاقات
        client_id = request.form.get('client_id')
        case_id = request.form.get('case_id')
        appointment.client_id = int(client_id) if client_id else None
        appointment.case_id = int(case_id) if case_id else None

        appointment.updated_date = datetime.now()

        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم تحديث الموعد بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@app.route('/api/appointments/<int:appointment_id>', methods=['DELETE'])
@login_required
def api_delete_appointment(appointment_id):
    """حذف موعد"""
    try:
        from .calendar_models import Event

        appointment = Event.query.get_or_404(appointment_id)
        db.session.delete(appointment)
        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم حذف الموعد بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@app.route('/api/appointments/<int:appointment_id>/complete', methods=['POST'])
@login_required
def api_complete_appointment(appointment_id):
    """إكمال موعد"""
    try:
        from .calendar_models import Event
        from datetime import datetime

        appointment = Event.query.get_or_404(appointment_id)
        appointment.status = 'مكتمل'
        appointment.updated_date = datetime.now()

        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم إكمال الموعد بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# ------------------ معاملات مالية ------------------
@app.route('/finance')
@login_required
def finance_list():
    transactions = FinancialTransaction.query.order_by(FinancialTransaction.date.desc()).all()
    # حساب الإجماليات لكل عملة
    summary = {}
    currencies = set([t.currency for t in transactions])
    for currency in currencies:
        income_types = ['قبض', 'دفعة', 'أتعاب', 'تحصيل إيجار']
        expense_types = ['صرف', 'مصاريف مكتب', 'رسوم']
        total_income = sum(t.amount for t in transactions if t.currency == currency and t.type in income_types)
        total_expense = sum(t.amount for t in transactions if t.currency == currency and t.type in expense_types)
        balance = total_income - total_expense
        summary[currency] = {
            'income': total_income,
            'expense': total_expense,
            'balance': balance
        }
    return render_template('finance/list.html', transactions=transactions, summary=summary)



@app.route('/finance/<int:transaction_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_financial_transaction(transaction_id):
    transaction = FinancialTransaction.query.get_or_404(transaction_id)
    cases = Case.query.all()
    clients = Client.query.all()
    if request.method == 'POST':
        transaction.amount = float(request.form['amount'])
        transaction.date = request.form['date']
        transaction.type = request.form['type']
        transaction.description = request.form.get('description')
        transaction.case_id = request.form.get('case_id') or None
        transaction.client_id = request.form.get('client_id') or None
        db.session.commit()
        backup_db_file()
        flash('تم تعديل السند المالي', 'success')
        return redirect(url_for('finance_list'))
    return render_template('finance/edit.html', transaction=transaction, cases=cases, clients=clients)

@app.route('/finance/<int:transaction_id>/delete', methods=['POST'])
@login_required
def delete_financial_transaction(transaction_id):
    transaction = FinancialTransaction.query.get_or_404(transaction_id)
    db.session.delete(transaction)
    db.session.commit()
    backup_db_file()
    flash('تم حذف السند المالي', 'success')
    return redirect(url_for('finance_list'))

@app.route('/finance/reports', methods=['GET'])
@login_required
def finance_reports():
    try:
        from datetime import datetime, timedelta

        # بناء الاستعلام الأساسي
        query = FinancialTransaction.query

        # تطبيق الفلاتر
        client_id = request.args.get('client_id')
        case_id = request.args.get('case_id')
        transaction_type = request.args.get('transaction_type')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        if client_id:
            query = query.filter(FinancialTransaction.client_id == client_id)

        if case_id:
            query = query.filter(FinancialTransaction.case_id == case_id)

        if transaction_type:
            query = query.filter(FinancialTransaction.type.contains(transaction_type))

        if date_from:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(FinancialTransaction.date >= date_from_obj)

        if date_to:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(FinancialTransaction.date <= date_to_obj)

        # جلب المعاملات المفلترة
        transactions = query.order_by(FinancialTransaction.date.desc()).all()

        # جلب جميع العملاء والقضايا للفلاتر
        clients = Client.query.all()
        cases = Case.query.all()

        # حساب الملخصات المالية لكل عملة
        summary = {}
        if transactions:
            currencies = set([t.currency or 'شيكل' for t in transactions])
            for currency in currencies:
                income_types = ['قبض', 'دفعة', 'أتعاب', 'تحصيل إيجار']
                expense_types = ['صرف', 'مصاريف مكتب', 'رسوم', 'مصاريف']

                currency_transactions = [t for t in transactions if (t.currency or 'شيكل') == currency]
                total_income = sum(t.amount for t in currency_transactions if t.type in income_types)
                total_expense = sum(t.amount for t in currency_transactions if t.type in expense_types)
                balance = total_income - total_expense

                summary[currency] = {
                    'income': total_income,
                    'expense': total_expense,
                    'balance': balance
                }

        # إحصائيات إضافية
        stats = {
            'total_transactions': len(transactions),
            'total_clients': len(set([t.client_id for t in transactions if t.client_id])),
            'total_cases': len(set([t.case_id for t in transactions if t.case_id])),
            'date_range': {
                'from': min([t.date for t in transactions if t.date]) if transactions else None,
                'to': max([t.date for t in transactions if t.date]) if transactions else None
            }
        }

        return render_template('finance/reports.html',
                             transactions=transactions,
                             clients=clients,
                             cases=cases,
                             summary=summary,
                             stats=stats)
    except Exception as e:
        flash(f'خطأ في تحميل التقارير المالية: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@app.route('/office-expenses', methods=['GET', 'POST'])
@login_required
def office_expenses():
    from .models import FinancialTransaction
    # نوع المصروفات: مصروفات المكتب فقط
    expenses = FinancialTransaction.query.filter_by(type='مصاريف مكتب').order_by(FinancialTransaction.date.desc()).all()
    if request.method == 'POST':
        # إضافة مصروف جديد
        amount = request.form['amount']
        currency = request.form['currency']
        description = request.form['description']
        from datetime import datetime
        expense = FinancialTransaction(type='مصاريف مكتب', amount=amount, currency=currency, description=description, date=datetime.now())
        db.session.add(expense)
        db.session.commit()
        flash('تمت إضافة مصروف مكتبي بنجاح', 'success')
        return redirect(url_for('office_expenses'))
    return render_template('finance/office_expenses.html', expenses=expenses)

@app.route('/shutdown')
def shutdown():
    from flask import request
    func = request.environ.get('werkzeug.server.shutdown')
    if func is None:
        return 'غير ممكن إيقاف النظام من هنا (سيرفر غير مدعوم)', 500
    func()
    return 'تم إيقاف النظام بنجاح.'

@app.after_request
def add_header(response):
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate, public, max-age=0"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    return response

@app.route('/backup_db', methods=['POST', 'GET'])
def backup_db():
    import shutil, os
    db_path = os.path.join(os.getcwd(), 'instance', 'lawoffice.db')
    backup_dir = os.path.join(os.getcwd(), 'instance', 'backups')
    os.makedirs(backup_dir, exist_ok=True)
    from datetime import datetime
    backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    backup_path = os.path.join(backup_dir, backup_name)
    if os.path.exists(db_path):
        shutil.copy2(db_path, backup_path)
    return ('', 204)

def backup_db_file():
    db_path = os.path.join(os.getcwd(), 'instance', 'lawoffice.db')
    backup_dir = os.path.join(os.getcwd(), 'instance', 'backups')
    os.makedirs(backup_dir, exist_ok=True)
    from datetime import datetime
    backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    backup_path = os.path.join(backup_dir, backup_name)
    if os.path.exists(db_path):
        shutil.copy2(db_path, backup_path)

@app.route('/stats', methods=['GET', 'POST'])
@login_required
def stats():
    try:
        today = datetime.today()
        # البحث المتقدم
        search_results = None
        search_field = request.form.get('search_field') if request.method == 'POST' else None
        search_value = request.form.get('search_value') if request.method == 'POST' else None
        extra_report = None

        if search_field and search_value:
            if search_field == 'client_name':
                search_results = Case.query.join(Client).filter(Client.name.contains(search_value)).all()
            elif search_field == 'national_id':
                search_results = Case.query.join(Client).filter(Client.national_id.contains(search_value)).all()
            elif search_field == 'court':
                search_results = Case.query.filter(Case.court.contains(search_value)).all()
            elif search_field == 'case_number':
                search_results = Case.query.filter(Case.case_number.contains(search_value)).all()
            elif search_field == 'case_status':
                search_results = Case.query.filter(Case.status.contains(search_value)).all()
            else:
                search_results = []

            # تقارير مالية مرتبطة بنتائج البحث
            if search_results:
                case_ids = [c.id for c in search_results]
                paid = db.session.query(func.sum(FinancialTransaction.amount)).filter(FinancialTransaction.case_id.in_(case_ids), FinancialTransaction.type.in_(['قبض', 'دفعة', 'أتعاب', 'تحصيل إيجار'])).scalar() or 0
                expense = db.session.query(func.sum(FinancialTransaction.amount)).filter(FinancialTransaction.case_id.in_(case_ids), FinancialTransaction.type.in_(['صرف', 'مصاريف مكتب', 'رسوم'])).scalar() or 0
                extra_report = {
                    'paid': paid,
                    'expense': expense,
                    'balance': paid - expense
                }

        # إحصائيات عامة
        courts_stats = db.session.query(Case.court, func.count(Case.id)).group_by(Case.court).all()
        clients_stats = db.session.query(Client.name, func.count(Case.id)).join(Case).group_by(Client.name).all()
        types_stats = db.session.query(Case.type, func.count(Case.id)).group_by(Case.type).all()
        status_stats = db.session.query(Case.status, func.count(Case.id)).group_by(Case.status).all()
        client_role_stats = db.session.query(Case.client_role, func.count(Case.id)).group_by(Case.client_role).all()

        courts_stats = {row[0]: row[1] for row in courts_stats}
        clients_stats = {row[0]: row[1] for row in clients_stats}
        types_stats = {row[0]: row[1] for row in types_stats}
        status_stats = {row[0]: row[1] for row in status_stats}
        client_role_stats = {row[0] or 'مدعي': row[1] for row in client_role_stats}

        # تقارير مالية إجمالية
        total_paid = db.session.query(func.sum(FinancialTransaction.amount)).filter(FinancialTransaction.type.in_(['قبض', 'دفعة', 'أتعاب', 'تحصيل إيجار'])).scalar() or 0
        total_expense = db.session.query(func.sum(FinancialTransaction.amount)).filter(FinancialTransaction.type.in_(['صرف', 'مصاريف مكتب', 'رسوم'])).scalar() or 0
        total_balance = total_paid - total_expense

        # إحصائيات أساسية
        top_clients = db.session.query(Client.name, func.count(Case.id)).join(Case).group_by(Client.name).order_by(func.count(Case.id).desc()).limit(5).all()
        top_courts = db.session.query(Case.court, func.count(Case.id)).group_by(Case.court).order_by(func.count(Case.id).desc()).limit(5).all()

        # القضايا المنتهية مقابل الجارية
        closed_count = Case.query.filter(Case.status.in_(['مفصولة', 'منتهية', 'محكومة'])).count()
        open_count = Case.query.filter(~Case.status.in_(['مفصولة', 'منتهية', 'محكومة'])).count()
        total_cases = Case.query.count()
        closed_ratio = (closed_count / total_cases * 100) if total_cases else 0

        # القضايا الجديدة هذا الشهر/السنة
        from sqlalchemy import extract
        this_month = today.month
        this_year = today.year
        new_cases_month = Case.query.filter(extract('year', Case.open_date)==this_year, extract('month', Case.open_date)==this_month).count()
        new_cases_year = Case.query.filter(extract('year', Case.open_date)==this_year).count()

        # متغيرات افتراضية للمتغيرات المتقدمة
        avg_duration = None
        top_lawyers = []
        monthly_cases = []
        top_paid_cases = []
        unpaid_cases = []
        old_cases = []
        opponent_stats = []
        client_source_stats = []
        monthly_payments = []
        risky_cases = []
        avg_rating = None
        upcoming_sessions = []
        upcoming_big_installments = []
        win_count = None
        lose_count = None
        service_type_stats = []
        late_clients = []
        prev_month_cases = 0
        prev_month_paid = 0
        property_status_stats = []
        late_tasks = []
        top_case_types = db.session.query(Case.type, func.count(Case.id)).group_by(Case.type).order_by(func.count(Case.id).desc()).limit(5).all()

        return render_template('stats.html',
            courts_stats=courts_stats,
            clients_stats=clients_stats,
            types_stats=types_stats,
            status_stats=status_stats,
            client_role_stats=client_role_stats,
            search_results=search_results,
            search_field=search_field,
            search_value=search_value,
            extra_report=extra_report,
            total_paid=total_paid,
            total_expense=total_expense,
            total_balance=total_balance,
            avg_duration=avg_duration,
            top_clients=top_clients,
            top_lawyers=top_lawyers,
            top_courts=top_courts,
            monthly_cases=monthly_cases,
            new_cases_month=new_cases_month,
            new_cases_year=new_cases_year,
            closed_count=closed_count,
            open_count=open_count,
            top_paid_cases=top_paid_cases,
            unpaid_cases=unpaid_cases,
            old_cases=old_cases,
            closed_ratio=closed_ratio,
            opponent_stats=opponent_stats,
            client_source_stats=client_source_stats,
            monthly_payments=monthly_payments,
            risky_cases=risky_cases,
            avg_rating=avg_rating,
            upcoming_sessions=upcoming_sessions,
            upcoming_big_installments=upcoming_big_installments,
            win_count=win_count,
            lose_count=lose_count,
            service_type_stats=service_type_stats,
            late_clients=late_clients,
            prev_month_cases=prev_month_cases,
            prev_month_paid=prev_month_paid,
            property_status_stats=property_status_stats,
            late_tasks=late_tasks,
            top_case_types=top_case_types
        )
    except Exception as e:
        flash(f'خطأ في تحميل الإحصائيات: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

# ------------------ إدارة المستخدمين ------------------
@app.route('/users')
@login_required
def users_list():
    if current_user.role != 'مدير':
        flash('غير مصرح لك بالدخول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))
    users = User.query.all()
    return render_template('users.html', users=users)

@app.route('/users/add', methods=['GET', 'POST'])
@login_required
def add_user():
    if current_user.role != 'مدير':
        flash('غير مصرح لك بإضافة مستخدمين', 'danger')
        return redirect(url_for('users_list'))
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        role = request.form.get('role', 'موظف')
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل!', 'danger')
            return redirect(url_for('add_user'))
        user = User(username=username, password=password, role=role)
        db.session.add(user)
        db.session.commit()
        flash('تمت إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('users_list'))
    return render_template('add_user.html')

@app.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    if current_user.role != 'مدير':
        flash('غير مصرح لك بتعديل المستخدمين', 'danger')
        return redirect(url_for('users_list'))
    user = User.query.get_or_404(user_id)
    if request.method == 'POST':
        user.username = request.form['username']
        if request.form['password']:
            user.password = request.form['password']
        user.role = request.form.get('role', user.role)
        db.session.commit()
        flash('تم تعديل بيانات المستخدم', 'success')
        return redirect(url_for('users_list'))
    return render_template('edit_user.html', user=user)

@app.route('/users/<int:user_id>/delete')
@login_required
def delete_user(user_id):
    if current_user.role != 'مدير':
        flash('غير مصرح لك بحذف المستخدمين', 'danger')
        return redirect(url_for('users_list'))
    user = User.query.get_or_404(user_id)
    db.session.delete(user)
    db.session.commit()
    flash('تم حذف المستخدم', 'success')
    return redirect(url_for('users_list'))

@app.route('/cases/<int:case_id>/upload', methods=['POST'])
@login_required
def upload_document(case_id):
    if 'document' not in request.files:
        flash('لم يتم اختيار ملف!', 'danger')
        return redirect(request.referrer)
    file = request.files['document']
    if file.filename == '':
        flash('لم يتم اختيار ملف!', 'danger')
        return redirect(request.referrer)
    if file and allowed_file(file.filename):
        filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{file.filename}"
        file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
        doc = Document(filename=filename, upload_date=datetime.now(), case_id=case_id)
        db.session.add(doc)
        db.session.commit()
        flash('تم رفع المستند بنجاح', 'success')
    else:
        flash('نوع الملف غير مدعوم!', 'danger')
    return redirect(request.referrer)

@app.route('/uploads/<filename>')
@login_required
def download_document(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename, as_attachment=True)

@app.route('/cases/<int:case_id>/documents/<int:doc_id>/delete')
@login_required
def delete_document(case_id, doc_id):
    doc = Document.query.get_or_404(doc_id)
    if doc.case_id != case_id:
        flash('المستند غير مرتبط بهذه القضية', 'danger')
        return redirect(request.referrer)
    try:
        os.remove(os.path.join(app.config['UPLOAD_FOLDER'], doc.filename))
    except Exception:
        pass
    db.session.delete(doc)
    db.session.commit()
    flash('تم حذف المستند', 'success')
    return redirect(request.referrer)

@app.route('/clients/<int:client_id>/upload', methods=['POST'])
@login_required
def upload_client_document(client_id):
    if 'document' not in request.files:
        flash('لم يتم اختيار ملف!', 'danger')
        return redirect(request.referrer)
    file = request.files['document']
    if file.filename == '':
        flash('لم يتم اختيار ملف!', 'danger')
        return redirect(request.referrer)
    if file and allowed_file(file.filename):
        filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{file.filename}"
        file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
        doc = ClientDocument(filename=filename, upload_date=datetime.now(), client_id=client_id)
        db.session.add(doc)
        db.session.commit()
        flash('تم رفع المستند للعميل بنجاح', 'success')
    else:
        flash('نوع الملف غير مدعوم!', 'danger')
    return redirect(request.referrer)

@app.route('/clients/<int:client_id>/documents/<int:doc_id>/delete')
@login_required
def delete_client_document(client_id, doc_id):
    doc = ClientDocument.query.get_or_404(doc_id)
    if doc.client_id != client_id:
        flash('المستند غير مرتبط بهذا العميل', 'danger')
        return redirect(request.referrer)
    try:
        os.remove(os.path.join(app.config['UPLOAD_FOLDER'], doc.filename))
    except Exception:
        pass
    db.session.delete(doc)
    db.session.commit()
    flash('تم حذف مستند العميل', 'success')
    return redirect(request.referrer)

# ------------------ إدارة الديون ------------------
@app.route('/debts')
@login_required
def debts_list():
    debts = Debt.query.order_by(Debt.due_date.asc()).all()
    # حساب الإجماليات
    total_debt = sum(debt.amount for debt in debts)
    total_paid = sum(debt.paid_amount for debt in debts)
    total_remaining = sum(debt.remaining_amount for debt in debts)

    # إحصائيات حسب الحالة
    pending_debts = [d for d in debts if d.status == 'مستحق']
    overdue_debts = [d for d in debts if d.status == 'متأخر']
    paid_debts = [d for d in debts if d.status == 'مسدد']

    return render_template('debts/list.html',
                         debts=debts,
                         total_debt=total_debt,
                         total_paid=total_paid,
                         total_remaining=total_remaining,
                         pending_count=len(pending_debts),
                         overdue_count=len(overdue_debts),
                         paid_count=len(paid_debts))

@app.route('/debts/add', methods=['GET', 'POST'])
@login_required
def add_debt():
    if request.method == 'POST':
        try:
            creditor_name = request.form['creditor_name']
            amount = float(request.form['amount'])
            debt_type = request.form['debt_type']
            description = request.form.get('description', '')
            due_date_str = request.form.get('due_date')
            priority = request.form.get('priority', 'متوسطة')
            currency = request.form.get('currency', 'شيكل')
            case_id = request.form.get('case_id') if request.form.get('case_id') else None
            client_id = request.form.get('client_id') if request.form.get('client_id') else None

            due_date = datetime.strptime(due_date_str, '%Y-%m-%d') if due_date_str else None

            debt = Debt(
                creditor_name=creditor_name,
                amount=amount,
                debt_type=debt_type,
                description=description,
                due_date=due_date,
                priority=priority,
                currency=currency,
                case_id=case_id,
                client_id=client_id
            )
            debt.calculate_remaining()
            debt.update_status()

            db.session.add(debt)
            db.session.commit()

            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': True, 'message': 'تمت إضافة الدين بنجاح'})

            flash('تمت إضافة الدين بنجاح', 'success')
            return redirect(url_for('debts_list'))

        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
            flash(f'حدث خطأ أثناء إضافة الدين: {str(e)}', 'danger')
            return redirect(url_for('debts_list'))

    cases = Case.query.all()
    clients = Client.query.all()
    return render_template('debts/add.html', cases=cases, clients=clients)

@app.route('/debts/<int:debt_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_debt(debt_id):
    debt = Debt.query.get_or_404(debt_id)

    if request.method == 'POST':
        debt.creditor_name = request.form['creditor_name']
        debt.amount = float(request.form['amount'])
        debt.debt_type = request.form['debt_type']
        debt.description = request.form.get('description', '')
        due_date_str = request.form.get('due_date')
        debt.priority = request.form.get('priority', 'متوسطة')
        debt.currency = request.form.get('currency', 'شيكل')
        debt.case_id = request.form.get('case_id') if request.form.get('case_id') else None
        debt.client_id = request.form.get('client_id') if request.form.get('client_id') else None

        debt.due_date = datetime.strptime(due_date_str, '%Y-%m-%d') if due_date_str else None

        debt.calculate_remaining()
        debt.update_status()

        db.session.commit()
        flash('تم تحديث الدين بنجاح', 'success')
        return redirect(url_for('debts_list'))

    cases = Case.query.all()
    clients = Client.query.all()
    return render_template('debts/edit.html', debt=debt, cases=cases, clients=clients)

@app.route('/debts/<int:debt_id>/pay', methods=['GET', 'POST'])
@login_required
def pay_debt(debt_id):
    debt = Debt.query.get_or_404(debt_id)

    if request.method == 'POST':
        payment_amount = float(request.form['amount'])
        payment_method = request.form.get('payment_method', '')
        notes = request.form.get('notes', '')

        if payment_amount > debt.remaining_amount:
            flash('مبلغ الدفعة أكبر من المبلغ المتبقي', 'danger')
            return redirect(url_for('pay_debt', debt_id=debt_id))

        # إنشاء سجل دفعة
        payment = DebtPayment(
            debt_id=debt.id,
            amount=payment_amount,
            payment_method=payment_method,
            notes=notes,
            currency=debt.currency
        )

        # تحديث الدين
        debt.paid_amount += payment_amount
        debt.calculate_remaining()
        debt.update_status()

        # إنشاء معاملة مالية
        transaction = FinancialTransaction(
            amount=payment_amount,
            date=datetime.now(),
            type='سداد دين',
            description=f'سداد دين: {debt.creditor_name} - {debt.debt_type}',
            currency=debt.currency
        )

        db.session.add(payment)
        db.session.add(transaction)
        db.session.commit()

        flash('تم تسجيل الدفعة بنجاح', 'success')
        return redirect(url_for('debts_list'))

    return render_template('debts/pay.html', debt=debt)

@app.route('/debts/<int:debt_id>/delete', methods=['POST'])
@login_required
def delete_debt(debt_id):
    debt = Debt.query.get_or_404(debt_id)

    # حذف جميع الدفعات المرتبطة
    DebtPayment.query.filter_by(debt_id=debt.id).delete()

    db.session.delete(debt)
    db.session.commit()

    flash('تم حذف الدين بنجاح', 'success')
    return redirect(url_for('debts_list'))

# تم حذف routes عقود الإيجار - سيتم إعادة بناؤها

# ------------------ النوافذ المنبثقة للإضافة السريعة ------------------
@app.route('/modal/add_client')
@login_required
def modal_add_client():
    return render_template('modals/add_client.html')

@app.route('/modal/add_case')
@login_required
def modal_add_case():
    clients = Client.query.all()
    return render_template('modals/add_case.html', clients=clients)

# تم حذف modal_add_property - سيتم إعادة بناؤه

@app.route('/modal/add_debt')
@login_required
def modal_add_debt():
    from datetime import date
    today = date.today().strftime('%Y-%m-%d')
    return render_template('modals/add_debt.html', today=today)

# تم حذف modal_add_tenant - سيتم إعادة بناؤه

@app.route('/modal/add_expense')
@login_required
def modal_add_expense():
    from datetime import date
    today = date.today().strftime('%Y-%m-%d')
    return render_template('modals/add_expense.html', today=today)

@app.route('/modal/add_rental_income')
@login_required
def modal_add_rental_income():
    from datetime import date
    today = date.today().strftime('%Y-%m-%d')
    current_year = date.today().year
    properties = Property.query.all()
    tenants = Tenant.query.all()
    return render_template('modals/add_rental_income.html',
                         today=today,
                         current_year=current_year,
                         properties=properties,
                         tenants=tenants)

@app.route('/modal/add_financial_transaction')
@login_required
def modal_add_financial_transaction():
    from datetime import date
    today = date.today().strftime('%Y-%m-%d')
    clients = Client.query.all()
    cases = Case.query.all()
    return render_template('modals/add_financial_transaction.html', today=today, clients=clients, cases=cases)

# تم حذف modal_add_lease - سيتم إعادة بناؤه

# ==================== روتات الطباعة ====================

@app.route('/print/financial_report')
@login_required
def print_financial_report():
    """طباعة التقرير المالي"""
    from datetime import date, datetime
    from sqlalchemy import extract, and_
    from .models import FinancialTransaction, Case, Fee, Debt

    # الحصول على المعاملات
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    report_type = request.args.get('type', 'شامل')

    if not start_date or not end_date:
        # افتراضي: آخر 3 أشهر
        end_date = date.today()
        start_date = date(end_date.year, max(1, end_date.month - 2), 1)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # جمع البيانات المالية من FinancialTransaction
    transactions = FinancialTransaction.query.filter(
        and_(FinancialTransaction.date >= start_date, FinancialTransaction.date <= end_date)
    ).all()

    # إيرادات القضايا
    cases_income = Case.query.filter(
        and_(Case.case_date >= start_date, Case.case_date <= end_date)
    ).all()

    # الأتعاب
    fees = Fee.query.filter(
        and_(Fee.due_date >= start_date, Fee.due_date <= end_date)
    ).all()

    # الديون
    debts = Debt.query.filter(
        Debt.status != 'مدفوع'
    ).all()

    # حساب المجاميع
    income_types = ['قبض', 'دفعة', 'أتعاب', 'تحصيل إيجار']
    expense_types = ['صرف', 'مصاريف مكتب', 'رسوم']

    total_income = sum([t.amount for t in transactions if t.type in income_types])
    total_expenses = sum([t.amount for t in transactions if t.type in expense_types])
    net_profit = total_income - total_expenses
    total_debts = sum([debt.amount - (debt.paid_amount or 0) for debt in debts])

    # إعداد البيانات للقالب
    cases_data = []
    for case in cases_income:
        # حساب المدفوع من المعاملات المالية
        paid_amount = sum([t.amount for t in transactions if t.case_id == case.id and t.type in income_types])
        cases_data.append({
            'case_number': case.case_number,
            'client_name': case.client.name if case.client else 'غير محدد',
            'case_type': case.case_type,
            'agreed_fees': case.agreed_fees or 0,
            'paid_amount': paid_amount,
            'remaining_amount': (case.agreed_fees or 0) - paid_amount,
            'payment_status': 'مدفوع' if (case.agreed_fees or 0) <= paid_amount else 'غير مكتمل'
        })

    # بيانات المعاملات المالية
    transactions_data = []
    for transaction in transactions:
        transactions_data.append({
            'date': transaction.date.strftime('%Y-%m-%d') if transaction.date else '',
            'type': transaction.type,
            'amount': transaction.amount or 0,
            'currency': transaction.currency or 'شيكل',
            'description': transaction.description or '',
            'client_name': transaction.client.name if transaction.client else 'غير محدد',
            'case_title': transaction.case.title if transaction.case else 'غير محدد'
        })

    # بيانات الأتعاب
    fees_data = []
    for fee in fees:
        fees_data.append({
            'case_number': fee.case.case_number if fee.case else 'غير محدد',
            'client_name': fee.case.client.name if fee.case and fee.case.client else 'غير محدد',
            'amount': fee.amount or 0,
            'currency': fee.currency or 'شيكل',
            'due_date': fee.due_date.strftime('%Y-%m-%d') if fee.due_date else '',
            'status': fee.status or 'غير محدد'
        })

    debt_data = []
    for debt in debts:
        debt_data.append({
            'creditor_name': debt.creditor_name,
            'description': debt.description,
            'due_date': debt.due_date.strftime('%Y-%m-%d') if debt.due_date else '',
            'amount': debt.amount or 0,
            'paid_amount': debt.paid_amount or 0,
            'remaining_amount': (debt.amount or 0) - (debt.paid_amount or 0),
            'status': debt.status
        })

    # تحليل المعاملات حسب النوع
    transaction_categories = {}
    for transaction in transactions:
        category = transaction.type or 'غير مصنف'
        if category not in transaction_categories:
            transaction_categories[category] = {'count': 0, 'total': 0}
        transaction_categories[category]['count'] += 1
        transaction_categories[category]['total'] += transaction.amount or 0

    transaction_categories_data = []
    for category, data in transaction_categories.items():
        total_amount = total_income + total_expenses
        percentage = (data['total'] / total_amount * 100) if total_amount > 0 else 0
        transaction_categories_data.append({
            'name': category,
            'count': data['count'],
            'total': data['total'],
            'percentage': round(percentage, 2)
        })

    return render_template('print/financial_report.html',
                         report_title=f'التقرير المالي من {start_date} إلى {end_date}',
                         report_type=report_type,
                         period=f'من {start_date} إلى {end_date}',
                         report_date=date.today().strftime('%Y-%m-%d'),
                         report_number=f'FR-{datetime.now().strftime("%Y%m%d%H%M")}',
                         current_date=date.today().strftime('%Y-%m-%d'),
                         total_income=total_income,
                         total_expenses=total_expenses,
                         net_profit=net_profit,
                         total_debts=total_debts,
                         cases_income=cases_data,
                         cases_total_fees=sum([case['agreed_fees'] for case in cases_data]),
                         cases_total_paid=sum([case['paid_amount'] for case in cases_data]),
                         cases_total_remaining=sum([case['remaining_amount'] for case in cases_data]),
                         transactions=transactions_data,
                         fees=fees_data,
                         fees_total=sum([fee['amount'] for fee in fees_data]),
                         transaction_categories=transaction_categories_data,
                         debts=debt_data,
                         debts_total=total_debts,
                         expense_ratio=round((total_expenses / total_income * 100) if total_income > 0 else 0, 2),
                         monthly_avg_income=round(total_income / 3, 2))

@app.route('/print/contract/<int:lease_id>')
@login_required
def print_contract(lease_id):
    """طباعة عقد الإيجار"""
    from datetime import date
    lease = Lease.query.get_or_404(lease_id)

    # إعداد بيانات العقد
    first_party = {
        'name': 'مكتب المحامي سامح',
        'id_number': '123456789',
        'address': 'فلسطين - غزة - شارع الجلاء',
        'phone': '+970-59-123-4567',
        'email': '<EMAIL>'
    }

    second_party = {
        'name': lease.tenant.name if lease.tenant else 'غير محدد',
        'id_number': lease.tenant.id_number if lease.tenant else '',
        'address': lease.tenant.address if lease.tenant else '',
        'phone': lease.tenant.phone if lease.tenant else '',
        'email': lease.tenant.email if lease.tenant else ''
    }

    # جدول الدفعات
    payment_schedule = []
    if lease.start_date and lease.end_date:
        current_date = lease.start_date
        month_count = 1
        while current_date <= lease.end_date:
            payment_schedule.append({
                'month': f'الشهر {month_count}',
                'due_date': current_date.strftime('%Y-%m-%d'),
                'amount': lease.monthly_rent or 0,
                'status': 'غير مدفوع',
                'paid_date': None,
                'notes': ''
            })
            # الانتقال للشهر التالي
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)
            month_count += 1

    return render_template('print/contract.html',
                         contract_type='إيجار',
                         contract_number=f'LEASE-{lease.id}',
                         contract_date=lease.start_date.strftime('%Y-%m-%d') if lease.start_date else '',
                         contract_duration=f'{lease.duration_months} شهر' if lease.duration_months else '',
                         contract_status=lease.status,
                         current_date=date.today().strftime('%Y-%m-%d'),
                         first_party=first_party,
                         second_party=second_party,
                         property=lease.property,
                         lease=lease,
                         payment_schedule=payment_schedule,
                         contract={
                             'start_date': lease.start_date.strftime('%Y-%m-%d') if lease.start_date else '',
                             'end_date': lease.end_date.strftime('%Y-%m-%d') if lease.end_date else '',
                             'clause_1': f'يتفق الطرف الأول على تأجير العقار المذكور للطرف الثاني لمدة {lease.duration_months} شهر.',
                             'clause_2': f'تبدأ مدة الإيجار من {lease.start_date} وتنتهي في {lease.end_date}.',
                             'clause_3': f'يلتزم المستأجر بدفع مبلغ {lease.monthly_rent} شيكل شهرياً.',
                             'clause_4': 'يلتزم المستأجر بالمحافظة على العقار وعدم إحداث أي تغييرات بدون موافقة المؤجر.',
                             'clause_5': 'يمكن إنهاء العقد بإشعار مسبق 30 يوماً من أي من الطرفين.',
                             'clause_6': 'أي نزاع ينشأ عن هذا العقد يحل عن طريق المحاكم المختصة في غزة.',
                             'additional_terms': lease.terms or ''
                         })

@app.route('/print/invoice/<int:case_id>')
@login_required
def print_invoice(case_id):
    """طباعة فاتورة القضية"""
    from datetime import date, timedelta
    case = Case.query.get_or_404(case_id)

    # إعداد بيانات الفاتورة
    invoice_data = {
        'number': f'INV-{case.id}-{datetime.now().strftime("%Y%m%d")}',
        'type': 'أتعاب قانونية',
        'issue_date': date.today().strftime('%Y-%m-%d'),
        'due_date': (date.today() + timedelta(days=30)).strftime('%Y-%m-%d'),
        'status': 'مدفوعة' if (case.paid_amount or 0) >= (case.agreed_fees or 0) else 'غير مدفوعة',
        'payment_method': 'نقداً',
        'payment_terms': 'الدفع خلال 30 يوماً',
        'currency': 'شيكل',
        'reference_number': case.case_number,
        'notes': f'أتعاب قانونية للقضية رقم {case.case_number}',
        'payment_days': 30,
        'late_fee_rate': 2,
        'validity_days': 90
    }

    # بنود الفاتورة
    invoice_items = []

    # الأتعاب الأساسية
    if case.agreed_fees:
        invoice_items.append({
            'description': f'أتعاب قانونية - {case.case_type}',
            'details': f'القضية رقم: {case.case_number}',
            'quantity': 1,
            'unit_price': case.agreed_fees,
            'discount': 0,
            'total': case.agreed_fees
        })

    # رسوم المحكمة
    if case.court_fees:
        invoice_items.append({
            'description': 'رسوم المحكمة',
            'details': 'رسوم إجراءات المحكمة والطوابع',
            'quantity': 1,
            'unit_price': case.court_fees,
            'discount': 0,
            'total': case.court_fees
        })

    # حساب المجاميع
    subtotal = sum([item['total'] for item in invoice_items])
    discount_amount = 0
    tax_amount = 0
    additional_fees = 0
    total_amount = subtotal - discount_amount + tax_amount + additional_fees
    paid_amount = case.paid_amount or 0
    remaining_amount = total_amount - paid_amount

    invoice_data.update({
        'items': invoice_items,
        'subtotal': subtotal,
        'discount_amount': discount_amount,
        'discount_percentage': 0,
        'tax_amount': tax_amount,
        'tax_rate': 0,
        'additional_fees': additional_fees,
        'total_amount': total_amount,
        'paid_amount': paid_amount,
        'remaining_amount': remaining_amount,
        'amount_in_words': number_to_words(total_amount)
    })

    # سجل المدفوعات
    payments = []
    if paid_amount > 0:
        payments.append({
            'date': case.case_date.strftime('%Y-%m-%d') if case.case_date else date.today().strftime('%Y-%m-%d'),
            'amount': paid_amount,
            'method': 'نقداً',
            'reference': case.case_number,
            'notes': 'دفعة أولى'
        })

    invoice_data['payments'] = payments

    return render_template('print/invoice.html',
                         invoice=invoice_data,
                         client=case.client,
                         current_date=date.today().strftime('%Y-%m-%d'))

def number_to_words(number):
    """تحويل الرقم إلى كلمات باللغة العربية"""
    if number == 0:
        return "صفر شيكل"

    ones = ["", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة"]
    tens = ["", "", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون"]
    teens = ["عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر",
             "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر"]

    def convert_hundreds(n):
        result = ""
        if n >= 100:
            if n // 100 == 1:
                result += "مائة "
            elif n // 100 == 2:
                result += "مائتان "
            else:
                result += ones[n // 100] + " مائة "
            n %= 100

        if n >= 20:
            result += tens[n // 10] + " "
            if n % 10 != 0:
                result += ones[n % 10] + " "
        elif n >= 10:
            result += teens[n - 10] + " "
        elif n > 0:
            result += ones[n] + " "

        return result.strip()

    if number < 1000:
        return convert_hundreds(int(number)) + " شيكل"
    elif number < 1000000:
        thousands = number // 1000
        remainder = number % 1000
        result = convert_hundreds(int(thousands)) + " ألف"
        if remainder > 0:
            result += " و" + convert_hundreds(int(remainder))
        return result + " شيكل"
    else:
        return f"{number:,.0f} شيكل"

# ==================== إدارة الأتعاب الشاملة ====================

@app.route('/fees')
@login_required
def fees_list():
    """عرض قائمة الأتعاب مع الفلترة والبحث"""
    page = request.args.get('page', 1, type=int)
    per_page = 12

    # بناء الاستعلام الأساسي
    query = Fee.query

    # تطبيق الفلاتر
    search = request.args.get('search', '').strip()
    if search:
        query = query.filter(
            db.or_(
                Fee.fee_number.contains(search),
                Fee.description.contains(search),
                Fee.fee_type.contains(search),
                Fee.client.has(Client.name.contains(search))
            )
        )

    fee_type = request.args.get('fee_type', '').strip()
    if fee_type:
        query = query.filter(Fee.fee_type == fee_type)

    payment_status = request.args.get('payment_status', '').strip()
    if payment_status:
        query = query.filter(Fee.payment_status == payment_status)

    date_from = request.args.get('date_from', '').strip()
    if date_from:
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
        query = query.filter(Fee.service_date >= date_from_obj)

    date_to = request.args.get('date_to', '').strip()
    if date_to:
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
        query = query.filter(Fee.service_date <= date_to_obj)

    # ترتيب النتائج
    query = query.order_by(Fee.created_date.desc())

    # تطبيق التصفح
    fees = query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    # حساب الإحصائيات
    all_fees = Fee.query.all()
    total_fees = sum(fee.total_amount for fee in all_fees)
    total_paid = sum(fee.paid_amount for fee in all_fees)
    total_remaining = sum(fee.remaining_amount for fee in all_fees)
    overdue_count = len([fee for fee in all_fees if fee.is_overdue()])

    return render_template('fees/list.html',
                         fees=fees,
                         total_fees=total_fees,
                         total_paid=total_paid,
                         total_remaining=total_remaining,
                         overdue_count=overdue_count)

@app.route('/modal/add_fee', methods=['GET', 'POST'])
@login_required
def modal_add_fee():
    """نموذج إضافة أتعاب جديدة"""
    if request.method == 'POST':
        try:
            # استخراج البيانات من النموذج
            client_id = request.form['client_id']
            case_id = request.form.get('case_id') if request.form.get('case_id') else None
            fee_type = request.form['fee_type']
            service_category = request.form['service_category']
            description = request.form['description']
            service_date = datetime.strptime(request.form['service_date'], '%Y-%m-%d')
            due_date = datetime.strptime(request.form['due_date'], '%Y-%m-%d')

            # المبالغ المالية
            base_amount = float(request.form['base_amount'])
            additional_fees = float(request.form.get('additional_fees', 0))
            discount_amount = float(request.form.get('discount_amount', 0))
            tax_amount = float(request.form.get('tax_amount', 0))

            # معلومات إضافية
            priority = request.form.get('priority', 'متوسطة')
            currency = request.form.get('currency', 'شيكل')
            payment_terms = request.form.get('payment_terms', 'خلال 30 يوم')
            is_recurring = 'is_recurring' in request.form
            recurring_period = request.form.get('recurring_period') if is_recurring else None
            notes = request.form.get('notes', '')

            # إنشاء الأتعاب
            fee = Fee(
                client_id=client_id,
                case_id=case_id,
                fee_type=fee_type,
                service_category=service_category,
                description=description,
                service_date=service_date,
                due_date=due_date,
                base_amount=base_amount,
                additional_fees=additional_fees,
                discount_amount=discount_amount,
                tax_amount=tax_amount,
                priority=priority,
                currency=currency,
                payment_terms=payment_terms,
                is_recurring=is_recurring,
                recurring_period=recurring_period,
                notes=notes
            )

            db.session.add(fee)
            db.session.commit()

            flash('تم إضافة الأتعاب بنجاح', 'success')
            return redirect(url_for('fees_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة الأتعاب: {str(e)}', 'danger')
            return redirect(url_for('fees_list'))

    # GET request - عرض النموذج
    from datetime import date
    today = date.today().strftime('%Y-%m-%d')
    clients = Client.query.all()
    cases = Case.query.all()

    return render_template('fees/add_modal.html',
                         clients=clients,
                         cases=cases,
                         today=today)

@app.route('/modal/add_fee_payment/<int:fee_id>', methods=['GET', 'POST'])
@login_required
def modal_add_fee_payment(fee_id):
    """نموذج إضافة دفعة للأتعاب"""
    fee = Fee.query.get_or_404(fee_id)

    if request.method == 'POST':
        try:
            amount = float(request.form['amount'])
            payment_date = datetime.strptime(request.form['payment_date'], '%Y-%m-%d')
            payment_method = request.form['payment_method']
            reference = request.form.get('reference', '')
            notes = request.form.get('notes', '')

            # التحقق من صحة المبلغ
            if amount <= 0:
                flash('مبلغ الدفعة يجب أن يكون أكبر من صفر', 'danger')
                return redirect(url_for('fees_list'))

            if amount > fee.remaining_amount:
                flash('مبلغ الدفعة أكبر من المبلغ المتبقي', 'danger')
                return redirect(url_for('fees_list'))

            # إضافة الدفعة
            payment = fee.add_payment(
                amount=amount,
                payment_method=payment_method,
                reference=reference,
                notes=notes
            )
            payment.payment_date = payment_date

            # إنشاء معاملة مالية
            transaction = FinancialTransaction(
                amount=amount,
                date=payment_date,
                type='تحصيل أتعاب',
                description=f'تحصيل أتعاب: {fee.fee_number} - {fee.client.name}',
                currency=fee.currency
            )

            db.session.add(transaction)
            db.session.commit()

            flash('تم تسجيل الدفعة بنجاح', 'success')
            return redirect(url_for('fees_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تسجيل الدفعة: {str(e)}', 'danger')
            return redirect(url_for('fees_list'))

    # GET request - عرض النموذج
    from datetime import date
    today = date.today().strftime('%Y-%m-%d')

    return render_template('fees/add_payment_modal.html',
                         fee=fee,
                         today=today)

@app.route('/modal/view_fee/<int:fee_id>')
@login_required
def modal_view_fee(fee_id):
    """عرض تفاصيل الأتعاب"""
    fee = Fee.query.get_or_404(fee_id)
    payments = fee.payments.order_by(FeePayment.payment_date.desc()).all()

    return render_template('fees/view_modal.html',
                         fee=fee,
                         payments=payments)

@app.route('/modal/edit_fee/<int:fee_id>', methods=['GET', 'POST'])
@login_required
def modal_edit_fee(fee_id):
    """نموذج تعديل الأتعاب"""
    fee = Fee.query.get_or_404(fee_id)

    if request.method == 'POST':
        try:
            # تحديث البيانات
            fee.client_id = request.form['client_id']
            fee.case_id = request.form.get('case_id') if request.form.get('case_id') else None
            fee.fee_type = request.form['fee_type']
            fee.service_category = request.form['service_category']
            fee.description = request.form['description']
            fee.service_date = datetime.strptime(request.form['service_date'], '%Y-%m-%d')
            fee.due_date = datetime.strptime(request.form['due_date'], '%Y-%m-%d')

            # المبالغ المالية
            fee.base_amount = float(request.form['base_amount'])
            fee.additional_fees = float(request.form.get('additional_fees', 0))
            fee.discount_amount = float(request.form.get('discount_amount', 0))
            fee.tax_amount = float(request.form.get('tax_amount', 0))

            # معلومات إضافية
            fee.priority = request.form.get('priority', 'متوسطة')
            fee.currency = request.form.get('currency', 'شيكل')
            fee.payment_terms = request.form.get('payment_terms', 'خلال 30 يوم')
            fee.is_recurring = 'is_recurring' in request.form
            fee.recurring_period = request.form.get('recurring_period') if fee.is_recurring else None
            fee.notes = request.form.get('notes', '')

            # إعادة حساب المجاميع
            fee.calculate_totals()

            db.session.commit()
            flash('تم تحديث الأتعاب بنجاح', 'success')
            return redirect(url_for('fees_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الأتعاب: {str(e)}', 'danger')
            return redirect(url_for('fees_list'))

    # GET request - عرض النموذج
    clients = Client.query.all()
    cases = Case.query.all()

    return render_template('fees/edit_modal.html',
                         fee=fee,
                         clients=clients,
                         cases=cases)

@app.route('/fees/<int:fee_id>/delete', methods=['POST'])
@login_required
def delete_fee(fee_id):
    """حذف الأتعاب"""
    fee = Fee.query.get_or_404(fee_id)

    try:
        # حذف جميع الدفعات المرتبطة
        FeePayment.query.filter_by(fee_id=fee.id).delete()

        db.session.delete(fee)
        db.session.commit()

        flash('تم حذف الأتعاب بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الأتعاب: {str(e)}', 'danger')

    return redirect(url_for('fees_list'))

@app.route('/fees/report')
@login_required
def fees_report():
    """تقرير الأتعاب الشامل"""
    try:
        from .models import Fee, FeePayment, Case, Client
        from datetime import date, datetime, timedelta
        from sqlalchemy import func, extract

        # جلب جميع الأتعاب
        fees = Fee.query.order_by(Fee.due_date.desc()).all()

        # حساب الإحصائيات العامة
        total_fees = sum(fee.total_amount for fee in fees)
        total_paid = sum(fee.paid_amount for fee in fees)
        total_remaining = sum(fee.remaining_amount for fee in fees)

        # الأتعاب المستحقة (متأخرة)
        today = date.today()
        overdue_fees = [fee for fee in fees if fee.due_date and fee.due_date < today and fee.remaining_amount > 0]
        overdue_amount = sum(fee.remaining_amount for fee in overdue_fees)

        # الأتعاب المدفوعة بالكامل
        paid_fees = [fee for fee in fees if fee.remaining_amount <= 0]
        paid_count = len(paid_fees)

        # الأتعاب المعلقة
        pending_fees = [fee for fee in fees if fee.remaining_amount > 0]
        pending_count = len(pending_fees)

        # تحليل الأتعاب حسب العملة
        currency_stats = {}
        for fee in fees:
            currency = fee.currency or 'شيكل'
            if currency not in currency_stats:
                currency_stats[currency] = {'total': 0, 'paid': 0, 'remaining': 0, 'count': 0}
            currency_stats[currency]['total'] += fee.total_amount
            currency_stats[currency]['paid'] += fee.paid_amount
            currency_stats[currency]['remaining'] += fee.remaining_amount
            currency_stats[currency]['count'] += 1

        # تحليل الأتعاب حسب الشهر (آخر 12 شهر)
        monthly_stats = {}
        for i in range(12):
            month_date = today.replace(day=1) - timedelta(days=30*i)
            month_key = month_date.strftime('%Y-%m')
            monthly_stats[month_key] = {'total': 0, 'paid': 0, 'count': 0}

        for fee in fees:
            if fee.due_date:
                month_key = fee.due_date.strftime('%Y-%m')
                if month_key in monthly_stats:
                    monthly_stats[month_key]['total'] += fee.total_amount
                    monthly_stats[month_key]['paid'] += fee.paid_amount
                    monthly_stats[month_key]['count'] += 1

        # أعلى العملاء من حيث الأتعاب
        client_stats = {}
        for fee in fees:
            if fee.client:
                client_name = fee.client.name
                if client_name not in client_stats:
                    client_stats[client_name] = {'total': 0, 'paid': 0, 'remaining': 0, 'count': 0}
                client_stats[client_name]['total'] += fee.total_amount
                client_stats[client_name]['paid'] += fee.paid_amount
                client_stats[client_name]['remaining'] += fee.remaining_amount
                client_stats[client_name]['count'] += 1

        # ترتيب العملاء حسب إجمالي الأتعاب
        top_clients = sorted(client_stats.items(), key=lambda x: x[1]['total'], reverse=True)[:10]

        return render_template('fees/report.html',
                             fees=fees,
                             total_fees=total_fees,
                             total_paid=total_paid,
                             total_remaining=total_remaining,
                             overdue_fees=overdue_fees,
                             overdue_amount=overdue_amount,
                             paid_count=paid_count,
                             pending_count=pending_count,
                             currency_stats=currency_stats,
                             monthly_stats=monthly_stats,
                             top_clients=top_clients,
                             today=today)

    except Exception as e:
        flash(f'حدث خطأ في تحميل تقرير الأتعاب: {str(e)}', 'danger')
        return redirect(url_for('fees_list'))

@app.route('/api/client/<int:client_id>/cases')
@login_required
def api_client_cases(client_id):
    """API للحصول على قضايا العميل"""
    cases = Case.query.filter_by(client_id=client_id).all()
    cases_data = []
    for case in cases:
        cases_data.append({
            'id': case.id,
            'case_number': case.case_number,
            'title': case.title or case.case_type
        })
    return {'cases': cases_data}


# ==================== Calendar and Task Management Routes ====================

@app.route('/calendar')
@login_required
def calendar():
    """عرض صفحة التقويم والمهام المحسن"""
    try:
        from datetime import datetime, timedelta

        # جلب البيانات الأساسية
        clients = Client.query.all()
        cases = Case.query.all()
        users = User.query.all()

        # جلب الأحداث والمهام مع معالجة الأخطاء
        try:
            events = Event.query.order_by(Event.start_datetime).all() if 'Event' in globals() else []
        except:
            events = []

        try:
            tasks = Task.query.order_by(Task.due_date).all() if 'Task' in globals() else []
        except:
            tasks = []

        # الأحداث القادمة (الأسبوع القادم)
        today = datetime.now()
        next_week = today + timedelta(days=7)
        try:
            upcoming_events = Event.query.filter(
                Event.start_datetime >= today,
                Event.start_datetime <= next_week
            ).order_by(Event.start_datetime).limit(5).all() if 'Event' in globals() else []
        except:
            upcoming_events = []

        # حساب الإحصائيات مع معالجة الأخطاء
        try:
            total_events = Event.query.count() if 'Event' in globals() else 0
        except:
            total_events = 0

        try:
            pending_tasks = Task.query.filter(Task.status.in_(['معلقة', 'قيد التنفيذ'])).count() if 'Task' in globals() else 0
        except:
            pending_tasks = 0

        try:
            completed_tasks = Task.query.filter(Task.status == 'منجزة').count() if 'Task' in globals() else 0
        except:
            completed_tasks = 0

        try:
            overdue_tasks = Task.query.filter(Task.due_date < today, Task.status != 'منجزة').count() if 'Task' in globals() else 0
        except:
            overdue_tasks = 0

        try:
            today_events = Event.query.filter(
                Event.start_datetime >= today.replace(hour=0, minute=0, second=0),
                Event.start_datetime < today.replace(hour=23, minute=59, second=59)
            ).count() if 'Event' in globals() else 0
        except:
            today_events = 0

        try:
            this_week_tasks = Task.query.filter(
                Task.due_date >= today,
                Task.due_date <= next_week
            ).count() if 'Task' in globals() else 0
        except:
            this_week_tasks = 0

        stats = {
            'total_events': total_events,
            'pending_tasks': pending_tasks,
            'completed_tasks': completed_tasks,
            'overdue_tasks': overdue_tasks,
            'today_events': today_events,
            'this_week_tasks': this_week_tasks
        }

        return render_template('calendar/calendar.html',
                             clients=clients,
                             cases=cases,
                             users=users,
                             events=events,
                             tasks=tasks,
                             upcoming_events=upcoming_events,
                             stats=stats)
    except Exception as e:
        flash(f'خطأ في تحميل التقويم: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@app.route('/api/calendar/events', methods=['GET'])
@login_required
def get_events():
    """جلب جميع الأحداث"""
    try:
        events = Event.query.all()
        events_data = []

        for event in events:
            event_data = {
                'id': event.id,
                'title': event.title,
                'description': event.description,
                'start_datetime': event.start_datetime.isoformat() if event.start_datetime else None,
                'end_datetime': event.end_datetime.isoformat() if event.end_datetime else None,
                'location': event.location,
                'event_type': event.event_type,
                'priority': event.priority,
                'status': event.status,
                'color': event.color,
                'is_all_day': event.is_all_day,
                'is_recurring': event.is_recurring,
                'recurring_pattern': event.recurring_pattern,
                'client_id': event.client_id,
                'case_id': event.case_id,
                'created_by': event.created_by,
                'client_name': event.client.name if event.client else None,
                'case_title': event.case.title if event.case else None
            }
            events_data.append(event_data)

        return jsonify({'success': True, 'events': events_data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/calendar/events', methods=['POST'])
@login_required
def add_event():
    """إضافة حدث جديد"""
    try:
        data = request.get_json()

        # تحويل التواريخ
        start_datetime = datetime.fromisoformat(data['start_datetime'].replace('T', ' '))
        end_datetime = datetime.fromisoformat(data['end_datetime'].replace('T', ' '))

        event = Event(
            title=data['title'],
            description=data.get('description'),
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            location=data.get('location'),
            event_type=data['event_type'],
            priority=data.get('priority', 'medium'),
            status='scheduled',
            color=data.get('color', '#007bff'),
            is_all_day=data.get('is_all_day', False),
            is_recurring=data.get('is_recurring', False),
            client_id=data.get('client_id'),
            case_id=data.get('case_id'),
            created_by=current_user.id,
            created_date=datetime.now(),
            updated_date=datetime.now()
        )

        db.session.add(event)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم إضافة الحدث بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

# ==================== Enhanced Calendar Routes ====================

@app.route('/api/calendar/events/enhanced', methods=['POST'])
@login_required
def add_event_enhanced():
    """إضافة حدث جديد محسن"""
    try:
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()

        # التحقق من البيانات المطلوبة
        if not data.get('title'):
            return jsonify({'success': False, 'message': 'عنوان الحدث مطلوب'})

        if not data.get('start_date'):
            return jsonify({'success': False, 'message': 'تاريخ البداية مطلوب'})

        # تحويل التواريخ
        from datetime import datetime
        start_date = datetime.fromisoformat(data['start_date'])
        end_date = None
        if data.get('end_date'):
            end_date = datetime.fromisoformat(data['end_date'])

        event = Event(
            title=data['title'],
            description=data.get('description'),
            start_datetime=start_date,
            end_datetime=end_date,
            location=data.get('location'),
            event_type=data.get('event_type', 'موعد'),
            status=data.get('status', 'مجدول'),
            color=data.get('color', '#007bff'),
            is_all_day=data.get('all_day') == 'on',
            is_recurring=data.get('recurring') == 'on',
            created_by=current_user.id,
            client_id=int(data['client_id']) if data.get('client_id') else None,
            case_id=int(data['case_id']) if data.get('case_id') else None,
            created_date=datetime.now(),
            updated_date=datetime.now()
        )

        db.session.add(event)
        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم إضافة الحدث بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'خطأ في إضافة الحدث: {str(e)}'})

@app.route('/api/calendar/tasks/enhanced', methods=['POST'])
@login_required
def add_task_enhanced():
    """إضافة مهمة جديدة محسنة"""
    try:
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()

        # التحقق من البيانات المطلوبة
        if not data.get('title'):
            return jsonify({'success': False, 'message': 'عنوان المهمة مطلوب'})

        # تحويل التاريخ
        from datetime import datetime
        due_date = None
        if data.get('due_date'):
            due_date = datetime.fromisoformat(data['due_date'])

        task = Task(
            title=data['title'],
            description=data.get('description'),
            due_date=due_date,
            status=data.get('status', 'معلقة'),
            user_id=current_user.id,
            case_id=int(data['case_id']) if data.get('case_id') else None
        )

        db.session.add(task)
        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم إضافة المهمة بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'خطأ في إضافة المهمة: {str(e)}'})

@app.route('/api/calendar/tasks/<int:task_id>/progress', methods=['POST'])
@login_required
def update_task_progress(task_id):
    """تحديث تقدم المهمة"""
    try:
        task = Task.query.get_or_404(task_id)

        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()

        # تحديث الحالة
        if data.get('status'):
            task.status = data['status']

        # إذا تم إكمال المهمة
        if task.status == 'منجزة':
            from datetime import datetime
            # لا نحتاج completed_date في النموذج الحالي

        db.session.commit()
        backup_db_file()

        return jsonify({'success': True, 'message': 'تم تحديث تقدم المهمة بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'خطأ في تحديث المهمة: {str(e)}'})

@app.route('/api/calendar/stats', methods=['GET'])
@login_required
def get_calendar_stats():
    """جلب إحصائيات التقويم"""
    try:
        from datetime import datetime, timedelta
        today = datetime.now()
        next_week = today + timedelta(days=7)

        # حساب الإحصائيات
        total_events = Event.query.filter_by(created_by=current_user.id).count()
        pending_tasks = Task.query.filter(
            Task.user_id == current_user.id,
            Task.status.in_(['معلقة', 'قيد التنفيذ'])
        ).count()
        completed_tasks = Task.query.filter(
            Task.user_id == current_user.id,
            Task.status == 'منجزة'
        ).count()
        overdue_tasks = Task.query.filter(
            Task.user_id == current_user.id,
            Task.due_date < today,
            Task.status != 'منجزة'
        ).count()
        today_events = Event.query.filter(
            Event.created_by == current_user.id,
            Event.start_datetime >= today.replace(hour=0, minute=0, second=0),
            Event.start_datetime < today.replace(hour=23, minute=59, second=59)
        ).count()
        this_week_tasks = Task.query.filter(
            Task.user_id == current_user.id,
            Task.due_date >= today,
            Task.due_date <= next_week
        ).count()

        stats = {
            'total_events': total_events,
            'pending_tasks': pending_tasks,
            'completed_tasks': completed_tasks,
            'overdue_tasks': overdue_tasks,
            'today_events': today_events,
            'this_week_tasks': this_week_tasks
        }

        return jsonify({'success': True, 'stats': stats})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/calendar/tasks', methods=['GET'])
@login_required
def get_tasks():
    """جلب جميع المهام"""
    try:
        tasks = TaskItem.query.all()
        tasks_data = []

        for task in tasks:
            task_data = {
                'id': task.id,
                'title': task.title,
                'description': task.description,
                'due_date': task.due_date.isoformat() if task.due_date else None,
                'completed_date': task.completed_date.isoformat() if task.completed_date else None,
                'priority': task.priority,
                'status': task.status,
                'category': task.category,
                'estimated_hours': task.estimated_hours,
                'actual_hours': task.actual_hours,
                'completion_percentage': task.completion_percentage or 0,
                'client_id': task.client_id,
                'case_id': task.case_id,
                'assigned_to': task.assigned_to,
                'created_by': task.created_by,
                'parent_task_id': task.parent_task_id,
                'client_name': task.client.name if task.client else None,
                'case_title': task.case.title if task.case else None,
                'assigned_to_name': task.assigned_user.username if task.assigned_user else None
            }
            tasks_data.append(task_data)

        return jsonify({'success': True, 'tasks': tasks_data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/calendar/tasks', methods=['POST'])
@login_required
def add_calendar_task():
    """إضافة مهمة جديدة"""
    try:
        data = request.get_json()

        # تحويل تاريخ الاستحقاق إذا كان موجوداً
        due_date = None
        if data.get('due_date'):
            due_date = datetime.fromisoformat(data['due_date'].replace('T', ' '))

        task = TaskItem(
            title=data['title'],
            description=data.get('description'),
            due_date=due_date,
            priority=data.get('priority', 'medium'),
            status='not_started',
            category=data.get('category', 'general'),
            estimated_hours=data.get('estimated_hours'),
            completion_percentage=0,
            client_id=data.get('client_id'),
            case_id=data.get('case_id'),
            assigned_to=data.get('assigned_to', current_user.id),
            created_by=current_user.id,
            parent_task_id=data.get('parent_task_id'),
            created_date=datetime.now(),
            updated_date=datetime.now()
        )

        db.session.add(task)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم إضافة المهمة بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/calendar/tasks/<int:task_id>/update', methods=['POST'])
@login_required
def update_task(task_id):
    """تحديث مهمة"""
    try:
        task = TaskItem.query.get_or_404(task_id)
        data = request.get_json()

        # تحديث الحقول
        if 'completion_percentage' in data:
            task.completion_percentage = data['completion_percentage']
            if data['completion_percentage'] == 100:
                task.status = 'completed'
                task.completed_date = datetime.now()
            else:
                task.status = 'in_progress'
                task.completed_date = None

        if 'actual_hours' in data:
            task.actual_hours = data['actual_hours']

        task.updated_date = datetime.now()
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تحديث المهمة بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/calendar/reminders', methods=['GET'])
@login_required
def get_reminders():
    """جلب التذكيرات"""
    try:
        reminders = Reminder.query.filter_by(user_id=current_user.id, is_sent=False).all()
        reminders_data = []

        for reminder in reminders:
            reminder_data = {
                'id': reminder.id,
                'title': reminder.title,
                'message': reminder.message,
                'reminder_datetime': reminder.reminder_datetime.isoformat() if reminder.reminder_datetime else None,
                'reminder_type': reminder.reminder_type,
                'event_id': reminder.event_id,
                'task_id': reminder.task_id
            }
            reminders_data.append(reminder_data)

        return jsonify({'success': True, 'reminders': reminders_data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== Archive and Settings Routes ====================

@app.route('/archive')
@login_required
def archive():
    """صفحة الأرشيف والإعدادات"""
    return render_template('archive/archive.html')

@app.route('/settings/theme')
@login_required
def theme_settings():
    """صفحة إعدادات المظهر والثيم"""
    return render_template('settings/theme_settings.html')

@app.route('/api/theme/save', methods=['POST'])
@login_required
def save_theme_settings():
    """حفظ إعدادات المظهر"""
    try:
        data = request.get_json()
        theme = data.get('theme', 'light')
        font_size = data.get('font_size', 16)

        # حفظ الإعدادات في قاعدة البيانات
        from .settings_service import SettingsService
        settings_service = SettingsService()

        settings_service.set_setting('ui_theme', theme, 'string', current_user.id)
        settings_service.set_setting('ui_font_size', str(font_size), 'string', current_user.id)

        return jsonify({
            'success': True,
            'message': 'تم حفظ إعدادات المظهر بنجاح'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في حفظ الإعدادات: {str(e)}'
        }), 500

@app.route('/api/theme/load', methods=['GET'])
@login_required
def load_theme_settings():
    """تحميل إعدادات المظهر"""
    try:
        from .settings_service import SettingsService
        settings_service = SettingsService()

        theme = settings_service.get_setting('ui_theme', 'light')
        font_size = int(settings_service.get_setting('ui_font_size', '16'))

        return jsonify({
            'success': True,
            'theme': theme,
            'font_size': font_size
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في تحميل الإعدادات: {str(e)}'
        }), 500

# ==================== Advanced Search API Routes ====================

@app.route('/api/search', methods=['POST'])
@login_required
def advanced_search():
    """API للبحث المتقدم في جميع أقسام النظام"""
    try:
        data = request.get_json()
        section = data.get('section', 'all')
        search_term = data.get('term', '').strip()
        filters = data.get('filters', {})

        if not search_term:
            return jsonify({'success': False, 'error': 'يرجى إدخال كلمة البحث'}), 400

        results = []

        if section == 'all' or section == 'cases':
            case_results = search_cases(search_term, filters)
            results.extend([{**case, 'type': 'case'} for case in case_results])

        if section == 'all' or section == 'clients':
            client_results = search_clients(search_term, filters)
            results.extend([{**client, 'type': 'client'} for client in client_results])

        if section == 'all' or section == 'properties':
            property_results = search_properties(search_term, filters)
            results.extend([{**prop, 'type': 'property'} for prop in property_results])

        if section == 'all' or section == 'tenants':
            tenant_results = search_tenants(search_term, filters)
            results.extend([{**tenant, 'type': 'tenant'} for tenant in tenant_results])

        if section == 'all' or section == 'financial':
            financial_results = search_financial(search_term, filters)
            results.extend([{**fin, 'type': 'financial'} for fin in financial_results])

        if section == 'all' or section == 'documents':
            document_results = search_documents(search_term, filters)
            results.extend([{**doc, 'type': 'document'} for doc in document_results])

        if section == 'all' or section == 'calendar':
            calendar_results = search_calendar(search_term, filters)
            results.extend([{**cal, 'type': 'calendar'} for cal in calendar_results])

        return jsonify({
            'success': True,
            'results': results,
            'total': len(results)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/search/suggestions', methods=['POST'])
@login_required
def search_suggestions():
    """API لاقتراحات البحث"""
    try:
        data = request.get_json()
        search_term = data.get('term', '').strip()

        if len(search_term) < 2:
            return jsonify([])

        suggestions = []

        # اقتراحات من القضايا
        cases = Case.query.filter(
            Case.title.contains(search_term) |
            Case.case_number.contains(search_term)
        ).limit(5).all()

        for case in cases:
            suggestions.append({
                'text': case.title,
                'type': 'cases',
                'type_name': 'قضية',
                'id': case.id
            })

        # اقتراحات من العملاء
        clients = Client.query.filter(Client.name.contains(search_term)).limit(5).all()
        for client in clients:
            suggestions.append({
                'text': client.name,
                'type': 'clients',
                'type_name': 'عميل',
                'id': client.id
            })

        # اقتراحات من العقارات
        properties = Property.query.filter(
            Property.name.contains(search_term) |
            Property.address.contains(search_term)
        ).limit(5).all()

        for prop in properties:
            suggestions.append({
                'text': prop.name,
                'type': 'properties',
                'type_name': 'عقار',
                'id': prop.id
            })

        return jsonify(suggestions[:15])  # أقصى 15 اقتراح

    except Exception as e:
        return jsonify([])

@app.route('/api/search/export', methods=['POST'])
@login_required
def export_search_results():
    """API لتصدير نتائج البحث"""
    try:
        data = request.get_json()
        results = data.get('results', [])
        export_format = data.get('format', 'excel')

        if not results:
            return jsonify({'success': False, 'error': 'لا توجد نتائج للتصدير'}), 400

        if export_format == 'excel':
            return export_to_excel(results)
        elif export_format == 'pdf':
            return export_to_pdf(results)
        else:
            return jsonify({'success': False, 'error': 'تنسيق غير مدعوم'}), 400

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# ==================== Search Helper Functions ====================

def search_cases(search_term, filters):
    """البحث في القضايا"""
    query = Case.query

    # البحث النصي
    query = query.filter(
        Case.title.contains(search_term) |
        Case.case_number.contains(search_term) |
        Case.description.contains(search_term)
    )

    # تطبيق الفلاتر
    if filters.get('status'):
        query = query.filter(Case.status == filters['status'])

    if filters.get('caseType'):
        query = query.filter(Case.case_type == filters['caseType'])

    if filters.get('client'):
        query = query.filter(Case.client_id == filters['client'])

    if filters.get('dateFrom'):
        query = query.filter(Case.created_date >= filters['dateFrom'])

    if filters.get('dateTo'):
        query = query.filter(Case.created_date <= filters['dateTo'])

    cases = query.limit(50).all()

    results = []
    for case in cases:
        results.append({
            'id': case.id,
            'case_number': case.case_number,
            'title': case.title,
            'description': case.description,
            'status': case.status,
            'case_type': case.case_type,
            'client_name': case.client.name if case.client else None,
            'created_date': case.created_date.isoformat() if case.created_date else None
        })

    return results

def search_clients(search_term, filters):
    """البحث في العملاء"""
    query = Client.query

    # البحث النصي
    query = query.filter(
        Client.name.contains(search_term) |
        Client.phone.contains(search_term) |
        Client.email.contains(search_term) |
        Client.address.contains(search_term)
    )

    # تطبيق الفلاتر
    if filters.get('dateFrom'):
        query = query.filter(Client.created_date >= filters['dateFrom'])

    if filters.get('dateTo'):
        query = query.filter(Client.created_date <= filters['dateTo'])

    clients = query.limit(50).all()

    results = []
    for client in clients:
        results.append({
            'id': client.id,
            'name': client.name,
            'phone': client.phone,
            'email': client.email,
            'address': client.address,
            'cases_count': len(client.cases),
            'created_date': client.created_date.isoformat() if client.created_date else None
        })

    return results

def search_properties(search_term, filters):
    """البحث في العقارات"""
    query = Property.query

    # البحث النصي
    query = query.filter(
        Property.name.contains(search_term) |
        Property.address.contains(search_term) |
        Property.description.contains(search_term)
    )

    # تطبيق الفلاتر
    if filters.get('status'):
        query = query.filter(Property.status == filters['status'])

    if filters.get('amountMin'):
        query = query.filter(Property.value >= float(filters['amountMin']))

    if filters.get('amountMax'):
        query = query.filter(Property.value <= float(filters['amountMax']))

    if filters.get('dateFrom'):
        query = query.filter(Property.created_date >= filters['dateFrom'])

    if filters.get('dateTo'):
        query = query.filter(Property.created_date <= filters['dateTo'])

    properties = query.limit(50).all()

    results = []
    for prop in properties:
        results.append({
            'id': prop.id,
            'name': prop.name,
            'address': prop.address,
            'property_type': prop.property_type,
            'status': prop.status,
            'value': float(prop.value) if prop.value else 0,
            'description': prop.description,
            'created_date': prop.created_date.isoformat() if prop.created_date else None
        })

    return results

def search_tenants(search_term, filters):
    """البحث في المستأجرين"""
    query = Tenant.query

    # البحث النصي
    query = query.filter(
        Tenant.name.contains(search_term) |
        Tenant.phone.contains(search_term) |
        Tenant.email.contains(search_term)
    )

    # تطبيق الفلاتر
    if filters.get('status'):
        query = query.filter(Tenant.status == filters['status'])

    if filters.get('dateFrom'):
        query = query.filter(Tenant.created_date >= filters['dateFrom'])

    if filters.get('dateTo'):
        query = query.filter(Tenant.created_date <= filters['dateTo'])

    tenants = query.limit(50).all()

    results = []
    for tenant in tenants:
        results.append({
            'id': tenant.id,
            'name': tenant.name,
            'phone': tenant.phone,
            'email': tenant.email,
            'status': tenant.status,
            'property_name': tenant.property.name if tenant.property else None,
            'created_date': tenant.created_date.isoformat() if tenant.created_date else None
        })

    return results

def search_financial(search_term, filters):
    """البحث في السجلات المالية"""
    results = []

    # البحث في الرسوم
    fee_query = Fee.query.filter(
        Fee.description.contains(search_term) |
        Fee.fee_type.contains(search_term)
    )

    if filters.get('amountMin'):
        fee_query = fee_query.filter(Fee.amount >= float(filters['amountMin']))

    if filters.get('amountMax'):
        fee_query = fee_query.filter(Fee.amount <= float(filters['amountMax']))

    fees = fee_query.limit(25).all()
    for fee in fees:
        results.append({
            'id': fee.id,
            'type': 'رسوم',
            'description': fee.description,
            'amount': float(fee.amount),
            'date': fee.date.isoformat() if fee.date else None,
            'status': fee.status,
            'case_title': fee.case.title if fee.case else None
        })

    # البحث في الديون
    debt_query = Debt.query.filter(
        Debt.description.contains(search_term)
    )

    if filters.get('amountMin'):
        debt_query = debt_query.filter(Debt.amount >= float(filters['amountMin']))

    if filters.get('amountMax'):
        debt_query = debt_query.filter(Debt.amount <= float(filters['amountMax']))

    debts = debt_query.limit(25).all()
    for debt in debts:
        results.append({
            'id': debt.id,
            'type': 'دين',
            'description': debt.description,
            'amount': float(debt.amount),
            'date': debt.date.isoformat() if debt.date else None,
            'status': debt.status,
            'debtor_name': debt.debtor_name
        })

    return results[:50]

def search_documents(search_term, filters):
    """البحث في المستندات"""
    # هذه الوظيفة ستحتاج إلى تطوير نظام إدارة المستندات
    # حالياً سنعيد قائمة فارغة
    return []

def search_calendar(search_term, filters):
    """البحث في التقويم والمهام"""
    results = []

    # البحث في الأحداث
    try:
        event_query = Event.query.filter(
            Event.title.contains(search_term) |
            Event.description.contains(search_term)
        )

        if filters.get('dateFrom'):
            event_query = event_query.filter(Event.start_date >= filters['dateFrom'])

        if filters.get('dateTo'):
            event_query = event_query.filter(Event.end_date <= filters['dateTo'])

        events = event_query.limit(25).all()
        for event in events:
            results.append({
                'id': event.id,
                'type': 'حدث',
                'title': event.title,
                'description': event.description,
                'start_date': event.start_date.isoformat() if event.start_date else None,
                'end_date': event.end_date.isoformat() if event.end_date else None,
                'event_type': event.event_type
            })
    except:
        pass  # تجاهل الأخطاء إذا لم تكن الجداول موجودة

    # البحث في المهام
    try:
        task_query = Task.query.filter(
            Task.title.contains(search_term) |
            Task.description.contains(search_term)
        )

        if filters.get('priority'):
            task_query = task_query.filter(Task.priority == filters['priority'])

        if filters.get('status'):
            task_query = task_query.filter(Task.status == filters['status'])

        tasks = task_query.limit(25).all()
        for task in tasks:
            results.append({
                'id': task.id,
                'type': 'مهمة',
                'title': task.title,
                'description': task.description,
                'due_date': task.due_date.isoformat() if task.due_date else None,
                'priority': task.priority,
                'status': task.status
            })
    except:
        pass  # تجاهل الأخطاء إذا لم تكن الجداول موجودة

    return results[:50]

def export_to_excel(results):
    """تصدير النتائج إلى Excel"""
    try:
        import pandas as pd
        from io import BytesIO

        # تحويل النتائج إلى DataFrame
        df = pd.DataFrame(results)

        # إنشاء ملف Excel في الذاكرة
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='نتائج البحث', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'search_results_{datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    except ImportError:
        return jsonify({'success': False, 'error': 'مكتبة pandas غير متوفرة'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

def export_to_pdf(results):
    """تصدير النتائج إلى PDF"""
    try:
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.lib import colors
        from io import BytesIO

        output = BytesIO()
        doc = SimpleDocTemplate(output, pagesize=A4)

        # إنشاء المحتوى
        story = []
        styles = getSampleStyleSheet()

        # العنوان
        title = Paragraph("نتائج البحث", styles['Title'])
        story.append(title)

        # الجدول
        if results:
            # تحضير البيانات للجدول
            headers = list(results[0].keys())
            data = [headers]

            for result in results:
                row = [str(result.get(header, '')) for header in headers]
                data.append(row)

            table = Table(data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(table)

        doc.build(story)
        output.seek(0)

        return send_file(
            output,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f'search_results_{datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")}.pdf'
        )

    except ImportError:
        return jsonify({'success': False, 'error': 'مكتبة reportlab غير متوفرة'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/clients')
@login_required
def get_clients_api():
    """API لجلب قائمة العملاء للفلاتر"""
    try:
        clients = Client.query.all()
        client_list = []
        for client in clients:
            client_list.append({
                'id': client.id,
                'name': client.name
            })
        return jsonify(client_list)
    except Exception as e:
        return jsonify([])

# ==================== Advanced Search Template Route ====================

@app.route('/advanced-search')
@login_required
def advanced_search_page():
    """صفحة البحث المتقدم"""
    return render_template('search/advanced_search.html')

@app.route('/api/search/stats')
@login_required
def search_stats():
    """API لجلب إحصائيات البحث"""
    try:
        stats = {
            'success': True,
            'cases': Case.query.count(),
            'clients': Client.query.count(),
            'properties': Property.query.count(),
            'financial': Fee.query.count() + Debt.query.count()
        }
        return jsonify(stats)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== Notifications System APIs ====================

@app.route('/notifications')
@login_required
def notifications_center():
    """مركز التنبيهات والإشعارات"""
    return render_template('notifications/notifications_center.html')

@app.route('/api/notifications/stats')
@login_required
def get_notification_stats():
    """الحصول على إحصائيات التنبيهات"""
    try:
        # إحصائيات التنبيهات
        total = Notification.query.filter_by(user_id=current_user.id, is_active=True).count()
        unread = Notification.query.filter_by(user_id=current_user.id, is_active=True, is_read=False).count()
        urgent = Notification.query.filter_by(user_id=current_user.id, is_active=True, priority='urgent').count()
        high = Notification.query.filter_by(user_id=current_user.id, is_active=True, priority='high').count()

        return jsonify({
            'success': True,
            'stats': {
                'total': total,
                'unread': unread,
                'urgent': urgent,
                'high': high
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/notifications/list', methods=['POST'])
@login_required
def get_notifications_list():
    """الحصول على قائمة التنبيهات مع الفلاتر"""
    try:
        data = request.get_json()

        # بناء الاستعلام الأساسي
        query = Notification.query.filter_by(user_id=current_user.id, is_active=True)

        # تطبيق الفلاتر
        if data.get('type'):
            query = query.filter_by(type=data['type'])

        if data.get('priority'):
            query = query.filter_by(priority=data['priority'])

        if data.get('status') == 'unread':
            query = query.filter_by(is_read=False)
        elif data.get('status') == 'read':
            query = query.filter_by(is_read=True)

        if data.get('start_date'):
            start_date = datetime.strptime(data['start_date'], '%Y-%m-%d')
            query = query.filter(Notification.created_date >= start_date)

        if data.get('end_date'):
            end_date = datetime.strptime(data['end_date'], '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59)
            query = query.filter(Notification.created_date <= end_date)

        # ترتيب النتائج
        notifications = query.order_by(Notification.created_date.desc()).all()

        return jsonify({
            'success': True,
            'notifications': [notification.to_dict() for notification in notifications]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/notifications/<int:notification_id>/mark-read', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """تحديد تنبيه كمقروء"""
    try:
        notification = Notification.query.filter_by(
            id=notification_id,
            user_id=current_user.id
        ).first()

        if not notification:
            return jsonify({'success': False, 'error': 'التنبيه غير موجود'})

        notification.is_read = True
        notification.read_date = datetime.now()
        db.session.commit()

        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """تحديد جميع التنبيهات كمقروءة"""
    try:
        notifications = Notification.query.filter_by(
            user_id=current_user.id,
            is_active=True,
            is_read=False
        ).all()

        for notification in notifications:
            notification.is_read = True
            notification.read_date = datetime.now()

        db.session.commit()

        return jsonify({'success': True, 'count': len(notifications)})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/notifications/<int:notification_id>', methods=['DELETE'])
@login_required
def delete_notification(notification_id):
    """حذف تنبيه"""
    try:
        notification = Notification.query.filter_by(
            id=notification_id,
            user_id=current_user.id
        ).first()

        if not notification:
            return jsonify({'success': False, 'error': 'التنبيه غير موجود'})

        notification.is_active = False
        db.session.commit()

        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/notifications/settings')
@login_required
def get_notification_settings():
    """الحصول على إعدادات التنبيهات"""
    try:
        settings = NotificationSettings.query.filter_by(user_id=current_user.id).first()

        if not settings:
            # إنشاء إعدادات افتراضية
            settings = NotificationSettings(
                user_id=current_user.id,
                lease_expiry_enabled=True,
                lease_expiry_days=30,
                payment_due_enabled=True,
                payment_due_days=5,
                maintenance_due_enabled=True,
                maintenance_due_days=7,
                tenant_review_enabled=True,
                tenant_review_months=6,
                email_notifications=True,
                sms_notifications=False,
                push_notifications=True
            )
            db.session.add(settings)
            db.session.commit()

        return jsonify({
            'success': True,
            'settings': {
                'lease_expiry_enabled': settings.lease_expiry_enabled,
                'lease_expiry_days': settings.lease_expiry_days,
                'payment_due_enabled': settings.payment_due_enabled,
                'payment_due_days': settings.payment_due_days,
                'maintenance_due_enabled': settings.maintenance_due_enabled,
                'maintenance_due_days': settings.maintenance_due_days,
                'tenant_review_enabled': settings.tenant_review_enabled,
                'tenant_review_months': settings.tenant_review_months,
                'email_notifications': settings.email_notifications,
                'sms_notifications': settings.sms_notifications,
                'push_notifications': settings.push_notifications
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/notifications/settings', methods=['POST'])
@login_required
def save_notification_settings():
    """حفظ إعدادات التنبيهات"""
    try:
        data = request.get_json()

        settings = NotificationSettings.query.filter_by(user_id=current_user.id).first()

        if not settings:
            settings = NotificationSettings(user_id=current_user.id)
            db.session.add(settings)

        # تحديث الإعدادات
        settings.lease_expiry_enabled = data.get('lease_expiry_enabled', True)
        settings.lease_expiry_days = data.get('lease_expiry_days', 30)
        settings.payment_due_enabled = data.get('payment_due_enabled', True)
        settings.payment_due_days = data.get('payment_due_days', 5)
        settings.maintenance_due_enabled = data.get('maintenance_due_enabled', True)
        settings.maintenance_due_days = data.get('maintenance_due_days', 7)
        settings.tenant_review_enabled = data.get('tenant_review_enabled', True)
        settings.tenant_review_months = data.get('tenant_review_months', 6)
        settings.email_notifications = data.get('email_notifications', True)
        settings.sms_notifications = data.get('sms_notifications', False)
        settings.push_notifications = data.get('push_notifications', True)

        db.session.commit()

        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== Print System ====================

@app.route('/print')
@login_required
def print_dashboard():
    """مركز الطباعة والتصدير"""
    return render_template('print/print_dashboard.html')

@app.route('/api/print/contract/<int:lease_id>')
@login_required
def api_print_contract(lease_id):
    """طباعة عقد إيجار"""
    try:
        lease = Lease.query.get_or_404(lease_id)

        contract_data = {
            'lease_number': lease.lease_number,
            'property_name': lease.property.name,
            'property_address': lease.property.address,
            'property_area': lease.property.area,
            'tenant_name': lease.tenant.full_name,
            'tenant_id': lease.tenant.national_id,
            'tenant_phone': lease.tenant.phone,
            'monthly_rent': lease.monthly_rent,
            'start_date': lease.start_date.strftime('%Y-%m-%d'),
            'end_date': lease.end_date.strftime('%Y-%m-%d'),
            'duration_months': lease.duration_months
        }

        return jsonify({
            'success': True,
            'contract_data': contract_data
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/print/invoice/<int:installment_id>')
@login_required
def api_print_invoice(installment_id):
    """طباعة فاتورة إيجار"""
    try:
        installment = LeaseInstallment.query.get_or_404(installment_id)

        invoice_data = {
            'invoice_number': f"INV-{installment.id:06d}",
            'lease_number': installment.lease.lease_number,
            'property_name': installment.lease.property.name,
            'tenant_name': installment.lease.tenant.full_name,
            'tenant_address': installment.lease.tenant.address,
            'tenant_phone': installment.lease.tenant.phone,
            'amount': installment.amount,
            'due_date': installment.due_date.strftime('%Y-%m-%d'),
            'payment_date': installment.payment_date.strftime('%Y-%m-%d') if installment.payment_date else None,
            'status': installment.status,
            'month_year': installment.due_date.strftime('%B %Y')
        }

        return jsonify({
            'success': True,
            'invoice_data': invoice_data
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/print/receipt/<int:transaction_id>')
@login_required
def api_print_receipt(transaction_id):
    """طباعة إيصال دفع"""
    try:
        transaction = FinancialTransaction.query.get_or_404(transaction_id)

        receipt_data = {
            'receipt_number': f"REC-{transaction.id:06d}",
            'transaction_date': transaction.transaction_date.strftime('%Y-%m-%d'),
            'amount': transaction.amount,
            'description': transaction.description,
            'payment_method': transaction.payment_method,
            'reference_number': transaction.reference_number
        }

        return jsonify({
            'success': True,
            'receipt_data': receipt_data
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== System Testing and Optimization ====================

@app.route('/system/test')
@login_required
def test_dashboard():
    """مركز الاختبار والتحسين"""
    return render_template('system/test_dashboard.html')

@app.route('/api/test/database-connection')
@login_required
def test_database_connection():
    """اختبار اتصال قاعدة البيانات"""
    try:
        # اختبار بسيط للاتصال
        db.session.execute(text('SELECT 1'))
        return jsonify({
            'success': True,
            'message': 'اتصال قاعدة البيانات يعمل بشكل صحيح',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        })

@app.route('/api/test/data-integrity')
@login_required
def test_data_integrity():
    """اختبار سلامة البيانات"""
    try:
        issues = []

        # فحص العقارات بدون مالك
        properties_without_owner = Property.query.filter_by(owner_id=None).count()
        if properties_without_owner > 0:
            issues.append(f"يوجد {properties_without_owner} عقار بدون مالك")

        # فحص المستأجرين بدون عقود
        tenants_without_leases = Tenant.query.filter(~Tenant.leases.any()).count()
        if tenants_without_leases > 0:
            issues.append(f"يوجد {tenants_without_leases} مستأجر بدون عقود")

        # فحص العقود المنتهية الصلاحية
        expired_leases = Lease.query.filter(Lease.end_date < datetime.now().date()).count()

        return jsonify({
            'success': len(issues) == 0,
            'issues': issues,
            'stats': {
                'properties_without_owner': properties_without_owner,
                'tenants_without_leases': tenants_without_leases,
                'expired_leases': expired_leases
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        })

@app.route('/api/test/query-performance')
@login_required
def test_query_performance():
    """اختبار أداء الاستعلامات"""
    try:
        import time

        queries = []

        # اختبار استعلام العقارات
        start_time = time.time()
        Property.query.all()
        properties_time = (time.time() - start_time) * 1000
        queries.append({'query': 'Properties', 'time': properties_time})

        # اختبار استعلام المستأجرين
        start_time = time.time()
        Tenant.query.all()
        tenants_time = (time.time() - start_time) * 1000
        queries.append({'query': 'Tenants', 'time': tenants_time})

        # اختبار استعلام العقود
        start_time = time.time()
        Lease.query.all()
        leases_time = (time.time() - start_time) * 1000
        queries.append({'query': 'Leases', 'time': leases_time})

        avg_time = sum(q['time'] for q in queries) / len(queries)

        return jsonify({
            'success': True,
            'avg_time': avg_time,
            'queries': queries,
            'performance_rating': 'excellent' if avg_time < 100 else 'good' if avg_time < 300 else 'poor',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        })

@app.route('/api/optimize/database', methods=['POST'])
@login_required
def optimize_database():
    """تحسين قاعدة البيانات"""
    try:
        # تنفيذ عمليات تحسين قاعدة البيانات
        db.session.execute(text('VACUUM'))
        db.session.execute(text('ANALYZE'))
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم تحسين قاعدة البيانات بنجاح',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        })

# ==================== Reports System APIs ====================

@app.route('/api/reports/quick', methods=['POST'])
@login_required
def generate_quick_report():
    """API لإنشاء التقارير السريعة"""
    try:
        data = request.get_json()
        report_type = data.get('type')
        filters = data.get('filters', {})

        if report_type == 'cases_summary':
            return generate_cases_summary_report(filters)
        elif report_type == 'clients_analysis':
            return generate_clients_analysis_report(filters)
        elif report_type == 'financial_overview':
            return generate_financial_overview_report(filters)
        elif report_type == 'properties_performance':
            return generate_properties_performance_report(filters)
        elif report_type == 'monthly_summary':
            return generate_monthly_summary_report(filters)
        elif report_type == 'yearly_analysis':
            return generate_yearly_analysis_report(filters)
        elif report_type == 'appointments_analysis':
            return generate_appointments_analysis_report(filters)
        elif report_type == 'tasks_productivity':
            return generate_tasks_productivity_report(filters)
        elif report_type == 'performance_dashboard':
            return generate_performance_dashboard_report(filters)
        elif report_type == 'debts_analysis':
            return generate_debts_analysis_report(filters)
        elif report_type == 'courts_statistics':
            return generate_courts_statistics_report(filters)
        elif report_type == 'fees_collection':
            return generate_fees_collection_report(filters)
        else:
            return jsonify({'success': False, 'error': 'نوع التقرير غير مدعوم'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/reports/custom', methods=['POST'])
@login_required
def generate_custom_report():
    """API لإنشاء التقارير المخصصة"""
    try:
        criteria = request.get_json()

        # بناء التقرير المخصص بناءً على المعايير
        report_data = build_custom_report(criteria)

        return jsonify({
            'success': True,
            'data': report_data['data'],
            'metadata': report_data['metadata'],
            'charts': report_data['charts']
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/reports/export', methods=['POST'])
@login_required
def export_report():
    """API لتصدير التقارير"""
    try:
        data = request.get_json()
        report = data.get('report')
        format_type = data.get('format')

        if format_type == 'pdf':
            return export_report_to_pdf(report)
        elif format_type == 'excel':
            return export_report_to_excel(report)
        else:
            return jsonify({'success': False, 'error': 'صيغة التصدير غير مدعومة'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== Report Generation Functions ====================

def generate_cases_summary_report(filters):
    """إنشاء تقرير ملخص القضايا"""
    try:
        # جلب بيانات القضايا
        cases_query = Case.query

        # تطبيق الفلاتر
        if filters.get('dateFrom'):
            cases_query = cases_query.filter(Case.created_at >= filters['dateFrom'])
        if filters.get('dateTo'):
            cases_query = cases_query.filter(Case.created_at <= filters['dateTo'])
        if filters.get('status'):
            cases_query = cases_query.filter(Case.status == filters['status'])

        cases = cases_query.all()

        # إحصائيات الملخص
        total_cases = len(cases)
        active_cases = len([c for c in cases if c.status == 'active'])
        closed_cases = len([c for c in cases if c.status == 'closed'])
        pending_cases = len([c for c in cases if c.status == 'pending'])

        # بيانات الرسوم البيانية
        status_chart = {
            'type': 'doughnut',
            'title': 'توزيع القضايا حسب الحالة',
            'data': {
                'labels': ['نشطة', 'مغلقة', 'معلقة'],
                'datasets': [{
                    'data': [active_cases, closed_cases, pending_cases],
                    'backgroundColor': ['#28a745', '#dc3545', '#ffc107']
                }]
            }
        }

        # جدول القضايا
        cases_table = {
            'title': 'قائمة القضايا',
            'headers': ['رقم القضية', 'العنوان', 'العميل', 'الحالة', 'تاريخ الإنشاء'],
            'rows': []
        }

        for case in cases[:50]:  # أول 50 قضية
            cases_table['rows'].append([
                case.case_number or f"#{case.id}",
                case.title,
                case.client.name if case.client else 'غير محدد',
                case.status,
                case.created_at.strftime('%Y-%m-%d') if case.created_at else 'غير محدد'
            ])

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'cases', 'label': 'إجمالي القضايا', 'value': total_cases},
                    {'type': 'cases', 'label': 'القضايا النشطة', 'value': active_cases},
                    {'type': 'cases', 'label': 'القضايا المغلقة', 'value': closed_cases},
                    {'type': 'cases', 'label': 'القضايا المعلقة', 'value': pending_cases}
                ],
                'charts': [status_chart],
                'tables': [cases_table]
            },
            'metadata': {
                'title': 'تقرير ملخص القضايا',
                'description': 'تقرير شامل عن حالة وإحصائيات القضايا',
                'type': 'cases_summary',
                'recordCount': total_cases,
                'period': f"{filters.get('dateFrom', 'البداية')} - {filters.get('dateTo', 'النهاية')}"
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_clients_analysis_report(filters):
    """إنشاء تقرير تحليل العملاء"""
    try:
        # جلب بيانات العملاء
        clients_query = Client.query

        if filters.get('dateFrom'):
            clients_query = clients_query.filter(Client.created_at >= filters['dateFrom'])
        if filters.get('dateTo'):
            clients_query = clients_query.filter(Client.created_at <= filters['dateTo'])

        clients = clients_query.all()

        # إحصائيات العملاء
        total_clients = len(clients)
        active_clients = len([c for c in clients if hasattr(c, 'cases') and c.cases])
        new_clients_this_month = len([c for c in clients if c.created_at and c.created_at.month == datetime.now(timezone.utc).month])

        # رسم بياني للعملاء الجدد شهرياً
        monthly_chart = {
            'type': 'line',
            'title': 'العملاء الجدد شهرياً',
            'data': {
                'labels': ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                'datasets': [{
                    'label': 'عملاء جدد',
                    'data': [12, 19, 15, 25, 22, 30],
                    'borderColor': '#007bff',
                    'backgroundColor': 'rgba(0, 123, 255, 0.1)'
                }]
            }
        }

        # جدول العملاء
        clients_table = {
            'title': 'قائمة العملاء',
            'headers': ['الاسم', 'البريد الإلكتروني', 'الهاتف', 'عدد القضايا', 'تاريخ التسجيل'],
            'rows': []
        }

        for client in clients[:50]:
            case_count = len(client.cases) if hasattr(client, 'cases') else 0
            clients_table['rows'].append([
                client.name,
                client.email or 'غير محدد',
                client.phone or 'غير محدد',
                case_count,
                client.created_at.strftime('%Y-%m-%d') if client.created_at else 'غير محدد'
            ])

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'clients', 'label': 'إجمالي العملاء', 'value': total_clients},
                    {'type': 'clients', 'label': 'العملاء النشطون', 'value': active_clients},
                    {'type': 'clients', 'label': 'عملاء جدد هذا الشهر', 'value': new_clients_this_month}
                ],
                'charts': [monthly_chart],
                'tables': [clients_table]
            },
            'metadata': {
                'title': 'تقرير تحليل العملاء',
                'description': 'تحليل شامل لبيانات العملاء والنشاط',
                'type': 'clients_analysis',
                'recordCount': total_clients
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_financial_overview_report(filters):
    """إنشاء تقرير نظرة عامة مالية"""
    try:
        # جلب البيانات المالية
        fees_query = Fee.query
        debts_query = Debt.query

        if filters.get('dateFrom'):
            fees_query = fees_query.filter(Fee.date >= filters['dateFrom'])
            debts_query = debts_query.filter(Debt.date >= filters['dateFrom'])
        if filters.get('dateTo'):
            fees_query = fees_query.filter(Fee.date <= filters['dateTo'])
            debts_query = debts_query.filter(Debt.date <= filters['dateTo'])

        fees = fees_query.all()
        debts = debts_query.all()

        # حساب الإحصائيات
        total_fees = sum(float(fee.amount) for fee in fees)
        total_debts = sum(float(debt.amount) for debt in debts)
        net_income = total_fees - total_debts

        paid_fees = sum(float(fee.amount) for fee in fees if fee.status == 'paid')
        pending_fees = sum(float(fee.amount) for fee in fees if fee.status == 'pending')

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'revenue', 'label': 'إجمالي الرسوم', 'value': f"{total_fees:,.2f} ريال"},
                    {'type': 'expenses', 'label': 'إجمالي الديون', 'value': f"{total_debts:,.2f} ريال"},
                    {'type': 'revenue', 'label': 'صافي الدخل', 'value': f"{net_income:,.2f} ريال"},
                    {'type': 'revenue', 'label': 'الرسوم المدفوعة', 'value': f"{paid_fees:,.2f} ريال"}
                ],
                'charts': [{
                    'type': 'bar',
                    'title': 'المقارنة المالية',
                    'data': {
                        'labels': ['الرسوم', 'الديون', 'صافي الدخل'],
                        'datasets': [{
                            'label': 'المبلغ (ريال)',
                            'data': [total_fees, total_debts, net_income],
                            'backgroundColor': ['#28a745', '#dc3545', '#007bff']
                        }]
                    }
                }]
            },
            'metadata': {
                'title': 'التقرير المالي الشامل',
                'description': 'نظرة عامة على الوضع المالي للمكتب',
                'type': 'financial_overview',
                'recordCount': len(fees) + len(debts)
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_properties_performance_report(filters):
    """إنشاء تقرير أداء العقارات"""
    try:
        properties = Property.query.all()

        total_properties = len(properties)
        rented_properties = len([p for p in properties if p.status == 'rented'])
        available_properties = len([p for p in properties if p.status == 'available'])

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'properties', 'label': 'إجمالي العقارات', 'value': total_properties},
                    {'type': 'properties', 'label': 'العقارات المؤجرة', 'value': rented_properties},
                    {'type': 'properties', 'label': 'العقارات المتاحة', 'value': available_properties}
                ]
            },
            'metadata': {
                'title': 'تقرير أداء العقارات',
                'description': 'تحليل أداء وحالة العقارات',
                'type': 'properties_performance',
                'recordCount': total_properties
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_monthly_summary_report(filters):
    """إنشاء تقرير الملخص الشهري"""
    try:
        current_month = datetime.now(timezone.utc).month
        current_year = datetime.now(timezone.utc).year

        # إحصائيات الشهر الحالي
        monthly_cases = Case.query.filter(
            func.extract('month', Case.created_at) == current_month,
            func.extract('year', Case.created_at) == current_year
        ).count()

        monthly_clients = Client.query.filter(
            func.extract('month', Client.created_at) == current_month,
            func.extract('year', Client.created_at) == current_year
        ).count()

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'cases', 'label': 'قضايا هذا الشهر', 'value': monthly_cases},
                    {'type': 'clients', 'label': 'عملاء جدد هذا الشهر', 'value': monthly_clients}
                ]
            },
            'metadata': {
                'title': 'الملخص الشهري',
                'description': f'ملخص أنشطة شهر {current_month}/{current_year}',
                'type': 'monthly_summary'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_yearly_analysis_report(filters):
    """إنشاء تقرير التحليل السنوي"""
    try:
        current_year = datetime.now(timezone.utc).year

        yearly_cases = Case.query.filter(
            func.extract('year', Case.created_at) == current_year
        ).count()

        yearly_clients = Client.query.filter(
            func.extract('year', Client.created_at) == current_year
        ).count()

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'cases', 'label': f'قضايا عام {current_year}', 'value': yearly_cases},
                    {'type': 'clients', 'label': f'عملاء جدد عام {current_year}', 'value': yearly_clients}
                ]
            },
            'metadata': {
                'title': 'التحليل السنوي',
                'description': f'تحليل شامل لأنشطة عام {current_year}',
                'type': 'yearly_analysis'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def build_custom_report(criteria):
    """بناء تقرير مخصص بناءً على المعايير"""
    try:
        # هذه وظيفة مبسطة للتقارير المخصصة
        # يمكن توسيعها لاحقاً لدعم معايير أكثر تعقيداً

        data = {
            'summary': [
                {'type': 'default', 'label': 'تقرير مخصص', 'value': 'جاري التطوير'}
            ],
            'charts': [],
            'tables': []
        }

        metadata = {
            'title': 'تقرير مخصص',
            'description': 'تقرير مخصص بناءً على معايير محددة',
            'type': 'custom'
        }

        return {
            'data': data,
            'metadata': metadata,
            'charts': []
        }

    except Exception as e:
        raise e

def export_report_to_pdf(report):
    """تصدير التقرير إلى PDF"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet
        from io import BytesIO

        output = BytesIO()
        doc = SimpleDocTemplate(output, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()

        # إضافة عنوان التقرير
        title = Paragraph(report['metadata']['title'], styles['Title'])
        story.append(title)
        story.append(Spacer(1, 20))

        # إضافة الملخص
        if 'summary' in report['data']:
            for item in report['data']['summary']:
                text = f"{item['label']}: {item['value']}"
                para = Paragraph(text, styles['Normal'])
                story.append(para)
                story.append(Spacer(1, 10))

        doc.build(story)
        output.seek(0)

        return send_file(
            output,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f"report_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}.pdf"
        )

    except ImportError:
        return jsonify({'success': False, 'error': 'مكتبة reportlab غير متوفرة'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def export_report_to_excel(report):
    """تصدير التقرير إلى Excel"""
    try:
        import pandas as pd
        from io import BytesIO

        output = BytesIO()

        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # إضافة الملخص
            if 'summary' in report['data']:
                summary_data = []
                for item in report['data']['summary']:
                    summary_data.append({
                        'البيان': item['label'],
                        'القيمة': item['value']
                    })

                df_summary = pd.DataFrame(summary_data)
                df_summary.to_excel(writer, sheet_name='الملخص', index=False)

            # إضافة الجداول
            if 'tables' in report['data']:
                for i, table in enumerate(report['data']['tables']):
                    df_table = pd.DataFrame(table['rows'], columns=table['headers'])
                    sheet_name = f"جدول_{i+1}"
                    df_table.to_excel(writer, sheet_name=sheet_name, index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f"report_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}.xlsx"
        )

    except ImportError:
        return jsonify({'success': False, 'error': 'مكتبة pandas غير متوفرة'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/archive/cases')
@login_required
def get_archived_cases():
    """الحصول على القضايا المؤرشفة"""
    try:
        # فلاتر البحث
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        reason = request.args.get('reason')

        query = ArchivedCase.query

        if date_from:
            query = query.filter(ArchivedCase.archived_date >= datetime.strptime(date_from, '%Y-%m-%d'))
        if date_to:
            query = query.filter(ArchivedCase.archived_date <= datetime.strptime(date_to, '%Y-%m-%d'))
        if reason:
            query = query.filter(ArchivedCase.archive_reason == reason)

        cases = query.order_by(ArchivedCase.archived_date.desc()).all()

        return jsonify({
            'success': True,
            'cases': [case.to_dict() for case in cases]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/clients')
@login_required
def get_archived_clients():
    """الحصول على العملاء المؤرشفين"""
    try:
        clients = ArchivedClient.query.order_by(ArchivedClient.archived_date.desc()).all()

        return jsonify({
            'success': True,
            'clients': [client.to_dict() for client in clients]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/documents')
@login_required
def get_archived_documents():
    """الحصول على المستندات المؤرشفة"""
    try:
        documents = ArchivedDocument.query.order_by(ArchivedDocument.archived_date.desc()).all()

        return jsonify({
            'success': True,
            'documents': [doc.to_dict() for doc in documents]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/transactions')
@login_required
def get_archived_transactions():
    """الحصول على المعاملات المالية المؤرشفة"""
    try:
        # هنا يمكن إضافة منطق للمعاملات المؤرشفة
        # حالياً سنعيد قائمة فارغة
        return jsonify({
            'success': True,
            'transactions': []
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/invoices')
@login_required
def get_archived_invoices():
    """الحصول على الفواتير المؤرشفة"""
    try:
        # هنا يمكن إضافة منطق للفواتير المؤرشفة
        # حالياً سنعيد قائمة فارغة
        return jsonify({
            'success': True,
            'invoices': []
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/statistics')
@login_required
def get_archive_statistics_api():
    """الحصول على إحصائيات الأرشيف"""
    try:
        stats = get_archive_statistics()
        return jsonify(stats)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/system/info')
@login_required
def get_system_info_api():
    """الحصول على معلومات النظام"""
    try:
        info = get_system_info()
        return jsonify(info)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/cases/<int:case_id>/restore', methods=['POST'])
@login_required
def restore_archived_case(case_id):
    """استعادة قضية مؤرشفة"""
    try:
        archived_case = ArchivedCase.query.get_or_404(case_id)

        # إنشاء قضية جديدة من البيانات المؤرشفة
        new_case = Case(
            case_number=archived_case.case_number + '_restored',
            title=archived_case.title,
            description=archived_case.description,
            case_type=archived_case.case_type,
            status='active',
            client_id=archived_case.client_id,
            created_date=datetime.now(timezone.utc)
        )

        db.session.add(new_case)
        db.session.delete(archived_case)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم استعادة القضية بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/cases/<int:case_id>/delete', methods=['DELETE'])
@login_required
def delete_archived_case(case_id):
    """حذف قضية مؤرشفة نهائياً"""
    try:
        archived_case = ArchivedCase.query.get_or_404(case_id)
        db.session.delete(archived_case)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حذف القضية نهائياً'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/clients/<int:client_id>/restore', methods=['POST'])
@login_required
def restore_archived_client(client_id):
    """استعادة عميل مؤرشف"""
    try:
        archived_client = ArchivedClient.query.get_or_404(client_id)

        # إنشاء عميل جديد من البيانات المؤرشفة
        new_client = Client(
            name=archived_client.name,
            id_number=archived_client.id_number,
            phone=archived_client.phone,
            email=archived_client.email,
            address=archived_client.address,
            created_date=datetime.now(timezone.utc)
        )

        db.session.add(new_client)
        db.session.delete(archived_client)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم استعادة العميل بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/system', methods=['POST'])
@login_required
def save_system_settings():
    """حفظ إعدادات النظام"""
    try:
        data = request.get_json()

        # حفظ الإعدادات
        SystemSettings.set_setting('office_name', data.get('office_name', ''), user_id=current_user.id)
        SystemSettings.set_setting('office_address', data.get('office_address', ''), user_id=current_user.id)
        SystemSettings.set_setting('office_phone', data.get('office_phone', ''), user_id=current_user.id)
        SystemSettings.set_setting('office_email', data.get('office_email', ''), user_id=current_user.id)
        SystemSettings.set_setting('currency', data.get('currency', 'JOD'), user_id=current_user.id)

        return jsonify({'success': True, 'message': 'تم حفظ الإعدادات بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/archive', methods=['POST'])
@login_required
def save_archive_settings():
    """حفظ إعدادات الأرشفة"""
    try:
        data = request.get_json()

        # حفظ إعدادات الأرشفة
        SystemSettings.set_setting('auto_archive_days', data.get('auto_archive_days', 365), 'integer', user_id=current_user.id)
        SystemSettings.set_setting('enable_auto_archive', data.get('enable_auto_archive', True), 'boolean', user_id=current_user.id)
        SystemSettings.set_setting('archive_notifications', data.get('archive_notifications', True), 'boolean', user_id=current_user.id)
        SystemSettings.set_setting('backup_frequency', data.get('backup_frequency', 'weekly'), user_id=current_user.id)

        return jsonify({'success': True, 'message': 'تم حفظ إعدادات الأرشفة بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/archive/rules', methods=['POST'])
@login_required
def save_archive_rules():
    """حفظ قواعد الأرشفة"""
    try:
        data = request.get_json()

        # حفظ قواعد الأرشفة
        SystemSettings.set_setting('completed_cases_archive_days', data.get('completed_cases_archive_days', 90), 'integer', user_id=current_user.id)
        SystemSettings.set_setting('cancelled_cases_archive_days', data.get('cancelled_cases_archive_days', 30), 'integer', user_id=current_user.id)
        SystemSettings.set_setting('documents_archive_days', data.get('documents_archive_days', 365), 'integer', user_id=current_user.id)
        SystemSettings.set_setting('archive_empty_folders', data.get('archive_empty_folders', False), 'boolean', user_id=current_user.id)

        return jsonify({'success': True, 'message': 'تم حفظ قواعد الأرشفة بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backup/create', methods=['POST'])
@login_required
def create_backup():
    """إنشاء نسخة احتياطية"""
    try:
        import sqlite3
        import tempfile
        import zipfile
        from flask import send_file

        # إنشاء ملف مؤقت للنسخة الاحتياطية
        backup_file = tempfile.NamedTemporaryFile(delete=False, suffix='.sql')

        # الاتصال بقاعدة البيانات
        db_path = app.config.get('DATABASE_PATH', 'law_office.db')

        # إنشاء النسخة الاحتياطية
        with sqlite3.connect(db_path) as conn:
            with open(backup_file.name, 'w', encoding='utf-8') as f:
                for line in conn.iterdump():
                    f.write('%s\n' % line)

        # تسجيل النسخة الاحتياطية في السجل
        backup_log = BackupLog(
            backup_type='manual',
            backup_path=backup_file.name,
            backup_size=os.path.getsize(backup_file.name),
            backup_status='completed',
            created_by=current_user.id
        )
        db.session.add(backup_log)
        db.session.commit()

        return send_file(backup_file.name, as_attachment=True, download_name=f'backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.sql')

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backup/restore', methods=['POST'])
@login_required
def restore_backup():
    """استعادة نسخة احتياطية"""
    try:
        if 'backup_file' not in request.files:
            return jsonify({'success': False, 'error': 'لم يتم اختيار ملف'}), 400

        file = request.files['backup_file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'لم يتم اختيار ملف'}), 400

        # حفظ الملف مؤقتاً
        temp_path = os.path.join(tempfile.gettempdir(), file.filename)
        file.save(temp_path)

        # استعادة قاعدة البيانات
        import sqlite3
        db_path = app.config.get('DATABASE_PATH', 'law_office.db')

        # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
        backup_current = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(db_path, backup_current)

        # استعادة قاعدة البيانات
        with open(temp_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()

        # حذف قاعدة البيانات الحالية وإنشاء جديدة
        os.remove(db_path)

        with sqlite3.connect(db_path) as conn:
            conn.executescript(sql_script)

        # تنظيف الملف المؤقت
        os.remove(temp_path)

        return jsonify({'success': True, 'message': 'تم استعادة النسخة الاحتياطية بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


# ===== مسارات نظام الإشعارات والتنبيهات =====

@app.route('/notifications')
@login_required
def notifications():
    """صفحة الإشعارات الرئيسية"""
    try:
        return render_template('notifications/list.html')
    except Exception as e:
        flash(f'خطأ في تحميل صفحة الإشعارات: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@app.route('/api/notifications')
@login_required
def api_notifications():
    """API للحصول على إشعارات المستخدم"""
    try:
        limit = request.args.get('limit', 50, type=int)
        unread_only = request.args.get('unread_only', False, type=bool)

        notifications = NotificationService.get_user_notifications(
            user_id=current_user.id,
            limit=limit,
            unread_only=unread_only
        )

        return jsonify({
            'success': True,
            'notifications': [n.to_dict() for n in notifications],
            'unread_count': Notification.get_unread_count(current_user.id)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/new')
@login_required
def api_notifications_new():
    """API للحصول على الإشعارات الجديدة"""
    try:
        since = request.args.get('since', '0')
        since_timestamp = datetime.fromtimestamp(int(since) / 1000, tz=timezone.utc)

        new_notifications = Notification.query.filter(
            Notification.user_id == current_user.id,
            Notification.created_at > since_timestamp
        ).order_by(Notification.created_at.desc()).all()

        return jsonify({
            'success': True,
            'notifications': [n.to_dict() for n in new_notifications]
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/<int:notification_id>/read', methods=['POST'])
@login_required
def api_notification_mark_read(notification_id):
    """تمييز إشعار كمقروء"""
    try:
        success = NotificationService.mark_notification_as_read(
            notification_id=notification_id,
            user_id=current_user.id
        )

        if success:
            return jsonify({'success': True, 'message': 'تم تمييز الإشعار كمقروء'})
        else:
            return jsonify({'success': False, 'error': 'الإشعار غير موجود'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/mark-all-read', methods=['POST'])
@login_required
def api_notifications_mark_all_read():
    """تمييز جميع الإشعارات كمقروءة"""
    try:
        count = NotificationService.mark_all_as_read(current_user.id)

        return jsonify({
            'success': True,
            'message': f'تم تمييز {count} إشعار كمقروء',
            'marked_count': count
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/<int:notification_id>', methods=['DELETE'])
@login_required
def api_notification_delete(notification_id):
    """حذف إشعار"""
    try:
        success = NotificationService.delete_notification(
            notification_id=notification_id,
            user_id=current_user.id
        )

        if success:
            return jsonify({'success': True, 'message': 'تم حذف الإشعار'})
        else:
            return jsonify({'success': False, 'error': 'الإشعار غير موجود'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/clear-all', methods=['POST'])
@login_required
def api_notifications_clear_all():
    """حذف جميع الإشعارات"""
    try:
        count = NotificationService.clear_all_notifications(current_user.id)

        return jsonify({
            'success': True,
            'message': f'تم حذف {count} إشعار',
            'deleted_count': count
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/alerts')
@login_required
def api_alerts():
    """API للحصول على التنبيهات"""
    try:
        alerts = Alert.query.filter_by(user_id=current_user.id)\
                           .order_by(Alert.created_at.desc()).all()

        return jsonify({
            'success': True,
            'alerts': [a.to_dict() for a in alerts]
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/alerts', methods=['POST'])
@login_required
def api_alerts_create():
    """إنشاء تنبيه جديد"""
    try:
        data = request.get_json()

        # تحويل وقت الجدولة
        schedule_time = datetime.fromisoformat(data['schedule_time'].replace('Z', '+00:00'))

        alert = NotificationService.create_scheduled_alert(
            user_id=current_user.id,
            title=data['title'],
            message=data['message'],
            schedule_time=schedule_time,
            type=data.get('type', 'info'),
            priority=data.get('priority', 'normal'),
            repeat_type=data.get('repeat', 'none'),
            url=data.get('url'),
            action=data.get('action'),
            data=data.get('data')
        )

        return jsonify({
            'success': True,
            'message': 'تم إنشاء التنبيه بنجاح',
            'alert': alert.to_dict()
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/alerts/check')
@login_required
def api_alerts_check():
    """فحص التنبيهات المعلقة"""
    try:
        processed_count = NotificationService.process_pending_alerts()

        return jsonify({
            'success': True,
            'processed_count': processed_count,
            'message': f'تم معالجة {processed_count} تنبيه'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/appointments/upcoming')
@login_required
def api_appointments_upcoming():
    """API للحصول على المواعيد القادمة"""
    try:
        # المواعيد في الـ 24 ساعة القادمة
        now = datetime.now(timezone.utc)
        tomorrow = now + timedelta(days=1)

        upcoming_appointments = Event.query.filter(
            Event.start_time >= now,
            Event.start_time <= tomorrow,
            Event.type == 'appointment'
        ).order_by(Event.start_time).all()

        appointments_data = []
        for appointment in upcoming_appointments:
            appointments_data.append({
                'id': appointment.id,
                'title': appointment.title,
                'client_name': appointment.client_name,
                'start_time': appointment.start_time.isoformat(),
                'location': appointment.location,
                'description': appointment.description
            })

        return jsonify({
            'success': True,
            'appointments': appointments_data,
            'count': len(appointments_data)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/overdue-items')
@login_required
def api_overdue_items():
    """API شامل للحصول على جميع العناصر المتأخرة"""
    try:
        now = datetime.now(timezone.utc)
        today = now.date()
        tomorrow = today + timedelta(days=1)

        overdue_items = []

        # المهام المتأخرة
        overdue_tasks = TaskItem.query.filter(
            TaskItem.due_date < now,
            TaskItem.completed == False
        ).order_by(TaskItem.due_date).all()

        for task in overdue_tasks:
            overdue_items.append({
                'id': f'task_{task.id}',
                'type': 'task',
                'title': task.title,
                'description': task.description,
                'due_date': task.due_date.isoformat(),
                'priority': task.priority,
                'category': task.category,
                'days_overdue': (now.date() - task.due_date.date()).days
            })

        # المواعيد المتأخرة أو خلال 24 ساعة
        overdue_appointments = Appointment.query.filter(
            Appointment.date <= tomorrow
        ).order_by(Appointment.date, Appointment.time).all()

        for appointment in overdue_appointments:
            appointment_datetime = datetime.combine(appointment.date, appointment.time or datetime.min.time())
            if appointment_datetime < now:
                status = 'overdue'
                hours_diff = (now - appointment_datetime).total_seconds() / 3600
            else:
                status = 'upcoming'
                hours_diff = (appointment_datetime - now).total_seconds() / 3600

            overdue_items.append({
                'id': f'appointment_{appointment.id}',
                'type': 'appointment',
                'title': f'موعد مع {appointment.client_name}',
                'description': appointment.description,
                'date': appointment.date.isoformat(),
                'time': appointment.time.strftime('%H:%M') if appointment.time else '',
                'location': appointment.location,
                'status': status,
                'hours_diff': round(hours_diff, 1)
            })

        # الاستحقاقات المالية المتأخرة (محاكاة - يمكن ربطها بنظام الفواتير)
        # يمكن إضافة استعلامات للفواتير المتأخرة هنا

        return jsonify({
            'success': True,
            'overdue_items': overdue_items,
            'total_count': len(overdue_items)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/tasks/overdue')
@login_required
def api_tasks_overdue():
    """API للحصول على المهام المتأخرة"""
    try:
        now = datetime.now(timezone.utc)

        overdue_tasks = TaskItem.query.filter(
            TaskItem.due_date < now,
            TaskItem.completed == False
        ).order_by(TaskItem.due_date).all()

        tasks_data = []
        for task in overdue_tasks:
            tasks_data.append({
                'id': task.id,
                'title': task.title,
                'description': task.description,
                'due_date': task.due_date.isoformat(),
                'priority': task.priority,
                'category': task.category,
                'days_overdue': (now - task.due_date).days
            })

        return jsonify({
            'success': True,
            'tasks': tasks_data,
            'count': len(tasks_data)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/settings')
@login_required
def api_notification_settings():
    """API للحصول على إعدادات الإشعارات"""
    try:
        settings = NotificationService.get_notification_settings(current_user.id)

        return jsonify({
            'success': True,
            'settings': settings.to_dict()
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/settings', methods=['POST'])
@login_required
def api_notification_settings_update():
    """تحديث إعدادات الإشعارات"""
    try:
        data = request.get_json()

        settings = NotificationService.update_notification_settings(
            user_id=current_user.id,
            settings_data=data
        )

        return jsonify({
            'success': True,
            'message': 'تم تحديث الإعدادات بنجاح',
            'settings': settings.to_dict()
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/notifications/stats')
@login_required
def api_notification_stats():
    """إحصائيات الإشعارات"""
    try:
        from .notification_models import get_notification_stats

        stats = get_notification_stats(current_user.id)

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500





@app.route('/api/notifications/cleanup', methods=['POST'])
@login_required
def api_notifications_cleanup():
    """تنظيف الإشعارات القديمة"""
    try:
        days_old = request.json.get('days_old', 30)

        cleaned_count = NotificationService.cleanup_old_notifications(days_old)

        return jsonify({
            'success': True,
            'message': f'تم حذف {cleaned_count} إشعار قديم',
            'cleaned_count': cleaned_count
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500





# ==================== Routes للمصروفات والإيرادات ====================

@app.route('/expenses/add', methods=['GET', 'POST'])
@login_required
def add_expense():
    if request.method == 'POST':
        try:
            category = request.form['category']
            subcategory = request.form.get('subcategory', '')
            amount = float(request.form['amount'])
            description = request.form.get('description', '')
            expense_date_str = request.form.get('expense_date')
            payment_method = request.form.get('payment_method', 'نقدي')
            currency = request.form.get('currency', 'شيكل')
            property_id = request.form.get('property_id') if request.form.get('property_id') else None

            expense_date = datetime.strptime(expense_date_str, '%Y-%m-%d') if expense_date_str else datetime.now()

            expense = Expense(
                category=category,
                subcategory=subcategory,
                amount=amount,
                description=description,
                expense_date=expense_date,
                payment_method=payment_method,
                currency=currency,
                property_id=property_id
            )

            db.session.add(expense)
            db.session.commit()
            backup_db_file()

            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': True, 'message': 'تمت إضافة المصروف بنجاح'})

            flash('تمت إضافة المصروف بنجاح', 'success')
            return redirect(url_for('finance_dashboard'))

        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
            flash(f'حدث خطأ أثناء إضافة المصروف: {str(e)}', 'danger')
            return redirect(url_for('finance_dashboard'))

    properties = Property.query.all()
    return render_template('expenses/add.html', properties=properties)

@app.route('/rental_incomes/add', methods=['GET', 'POST'])
@login_required
def add_rental_income():
    if request.method == 'POST':
        try:
            property_id = request.form['property_id']
            tenant_id = request.form.get('tenant_id')
            amount = float(request.form['amount'])
            income_date_str = request.form.get('income_date')
            payment_method = request.form.get('payment_method', 'نقدي')
            currency = request.form.get('currency', 'شيكل')
            description = request.form.get('description', '')
            month_year = request.form.get('month_year', '')

            income_date = datetime.strptime(income_date_str, '%Y-%m-%d') if income_date_str else datetime.now()

            rental_income = RentalIncome(
                property_id=property_id,
                tenant_id=tenant_id,
                amount=amount,
                income_date=income_date,
                payment_method=payment_method,
                currency=currency,
                description=description,
                month_year=month_year
            )

            db.session.add(rental_income)
            db.session.commit()
            backup_db_file()

            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': True, 'message': 'تمت إضافة الإيجار بنجاح'})

            flash('تمت إضافة الإيجار بنجاح', 'success')
            return redirect(url_for('finance_dashboard'))

        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
            flash(f'حدث خطأ أثناء إضافة الإيجار: {str(e)}', 'danger')
            return redirect(url_for('finance_dashboard'))

    properties = Property.query.all()
    tenants = Tenant.query.all()
    return render_template('rental_incomes/add.html', properties=properties, tenants=tenants)

@app.route('/finance/add', methods=['GET', 'POST'])
@login_required
def add_financial_transaction():
    if request.method == 'POST':
        try:
            transaction_type = request.form['type']
            amount = float(request.form['amount'])
            currency = request.form.get('currency', 'شيكل')
            date_str = request.form.get('date')
            client_id = request.form.get('client_id') if request.form.get('client_id') else None
            case_id = request.form.get('case_id') if request.form.get('case_id') else None
            payment_method = request.form.get('payment_method', 'نقدي')
            reference_number = request.form.get('reference_number', '')
            description = request.form['description']
            notes = request.form.get('notes', '')

            transaction_date = datetime.strptime(date_str, '%Y-%m-%d') if date_str else datetime.now()

            transaction = FinancialTransaction(
                type=transaction_type,
                amount=amount,
                currency=currency,
                date=transaction_date,
                client_id=client_id,
                case_id=case_id,
                description=description
            )

            db.session.add(transaction)
            db.session.commit()
            backup_db_file()

            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': True, 'message': 'تمت إضافة السند المالي بنجاح'})

            flash('تمت إضافة السند المالي بنجاح', 'success')
            return redirect(url_for('financial_transactions_list'))

        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-CSRFToken'):
                return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
            flash(f'حدث خطأ أثناء إضافة السند المالي: {str(e)}', 'danger')
            return redirect(url_for('add_financial_transaction'))

    clients = Client.query.all()
    cases = Case.query.all()
    properties = Property.query.all()
    tenants = Tenant.query.all()
    return render_template('finance/add.html', clients=clients, cases=cases, properties=properties, tenants=tenants)

# تم حذف route إضافة عقد الإيجار - سيتم إعادة بناؤه

# ==================== Advanced Settings and Backup System Routes ====================

@app.route('/api/settings/all')
@login_required
def get_all_settings():
    """API لجلب جميع الإعدادات"""
    try:
        settings_service = SettingsService()
        all_settings = settings_service.get_all_settings()

        return jsonify({
            'success': True,
            'settings': all_settings
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/save', methods=['POST'])
@login_required
def save_all_settings():
    """API لحفظ جميع الإعدادات"""
    try:
        data = request.get_json()
        settings_service = SettingsService()

        # حفظ الإعدادات بشكل مجمع
        settings_service.update_settings_batch(data, current_user.id)

        return jsonify({
            'success': True,
            'message': 'تم حفظ جميع الإعدادات بنجاح'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/reset', methods=['POST'])
@login_required
def reset_all_settings():
    """API لإعادة تعيين جميع الإعدادات"""
    try:
        settings_service = SettingsService()
        settings_service.reset_to_defaults(current_user.id)

        return jsonify({
            'success': True,
            'message': 'تم إعادة تعيين الإعدادات بنجاح'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/export')
@login_required
def export_settings():
    """API لتصدير الإعدادات"""
    try:
        settings_service = SettingsService()
        settings_data = settings_service.export_settings()

        # إنشاء ملف JSON للتصدير
        from io import BytesIO
        import json

        output = BytesIO()
        output.write(json.dumps(settings_data, ensure_ascii=False, indent=2).encode('utf-8'))
        output.seek(0)

        return send_file(
            output,
            mimetype='application/json',
            as_attachment=True,
            download_name=f'settings_backup_{datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")}.json'
        )
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/import', methods=['POST'])
@login_required
def import_settings():
    """API لاستيراد الإعدادات"""
    try:
        data = request.get_json()
        settings_service = SettingsService()

        # استيراد الإعدادات
        settings_service.import_settings(data, current_user.id)

        return jsonify({
            'success': True,
            'message': 'تم استيراد الإعدادات بنجاح'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backup/create-manual', methods=['POST'])
@login_required
def create_manual_backup():
    """API لإنشاء نسخة احتياطية يدوية"""
    try:
        backup_service = BackupService()
        backup_result = backup_service.create_manual_backup(current_user.id)

        if backup_result['success']:
            return jsonify({
                'success': True,
                'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
                'backup_id': backup_result['backup_id'],
                'file_path': backup_result['file_path']
            })
        else:
            return jsonify({
                'success': False,
                'error': backup_result['error']
            }), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backup/stats')
@login_required
def get_backup_stats():
    """API لجلب إحصائيات النسخ الاحتياطي"""
    try:
        backup_service = BackupService()
        stats = backup_service.get_backup_statistics()

        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backup/history')
@login_required
def get_backup_history():
    """API لجلب سجل النسخ الاحتياطي"""
    try:
        backup_service = BackupService()
        history = backup_service.get_backup_history()

        return jsonify({
            'success': True,
            'history': history
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/backup/restore-file', methods=['POST'])
@login_required
def restore_backup_file():
    """API لاستعادة نسخة احتياطية"""
    try:
        if 'backup_file' not in request.files:
            return jsonify({'success': False, 'error': 'لم يتم اختيار ملف'}), 400

        file = request.files['backup_file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'لم يتم اختيار ملف'}), 400

        backup_service = BackupService()
        restore_result = backup_service.restore_backup(file, current_user.id)

        if restore_result['success']:
            return jsonify({
                'success': True,
                'message': 'تم استعادة النسخة الاحتياطية بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'error': restore_result['error']
            }), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# ==================== وظائف التقارير المتقدمة الجديدة ====================

def generate_appointments_analysis_report(filters):
    """إنشاء تقرير تحليل المواعيد"""
    try:
        from .calendar_models import Event
        from datetime import datetime, timedelta

        # جلب المواعيد
        appointments = Event.query.filter(
            Event.event_type.in_(['موعد عام', 'جلسة محكمة', 'اجتماع عميل', 'مهمة'])
        ).all()

        # إحصائيات أساسية
        total_appointments = len(appointments)
        completed_appointments = len([a for a in appointments if a.status == 'مكتمل'])
        pending_appointments = len([a for a in appointments if a.status == 'مجدول'])
        cancelled_appointments = len([a for a in appointments if a.status == 'ملغي'])

        # تحليل حسب النوع
        type_stats = {}
        for appointment in appointments:
            type_name = appointment.event_type
            if type_name not in type_stats:
                type_stats[type_name] = 0
            type_stats[type_name] += 1

        # تحليل حسب الأولوية
        priority_stats = {}
        for appointment in appointments:
            priority = appointment.priority or 'متوسطة'
            if priority not in priority_stats:
                priority_stats[priority] = 0
            priority_stats[priority] += 1

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'total', 'label': 'إجمالي المواعيد', 'value': total_appointments},
                    {'type': 'completed', 'label': 'المواعيد المكتملة', 'value': completed_appointments},
                    {'type': 'pending', 'label': 'المواعيد المعلقة', 'value': pending_appointments},
                    {'type': 'cancelled', 'label': 'المواعيد الملغية', 'value': cancelled_appointments}
                ],
                'type_analysis': [{'type': k, 'count': v} for k, v in type_stats.items()],
                'priority_analysis': [{'priority': k, 'count': v} for k, v in priority_stats.items()]
            },
            'metadata': {
                'title': 'تحليل المواعيد',
                'description': 'تحليل شامل لإحصائيات المواعيد والجلسات',
                'type': 'appointments_analysis'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_tasks_productivity_report(filters):
    """إنشاء تقرير إنتاجية المهام"""
    try:
        from .calendar_models import TaskItem
        from datetime import datetime

        # جلب المهام
        tasks = TaskItem.query.all()

        # إحصائيات أساسية
        total_tasks = len(tasks)
        completed_tasks = len([t for t in tasks if t.status == 'مكتملة'])
        in_progress_tasks = len([t for t in tasks if t.status == 'قيد التنفيذ'])
        pending_tasks = len([t for t in tasks if t.status == 'معلقة'])

        # حساب معدل الإنجاز
        completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0

        # تحليل حسب الأولوية
        priority_stats = {}
        for task in tasks:
            priority = task.priority or 'متوسطة'
            if priority not in priority_stats:
                priority_stats[priority] = {'total': 0, 'completed': 0}
            priority_stats[priority]['total'] += 1
            if task.status == 'مكتملة':
                priority_stats[priority]['completed'] += 1

        # تحليل حسب الفئة
        category_stats = {}
        for task in tasks:
            category = task.category or 'عامة'
            if category not in category_stats:
                category_stats[category] = 0
            category_stats[category] += 1

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'total', 'label': 'إجمالي المهام', 'value': total_tasks},
                    {'type': 'completed', 'label': 'المهام المكتملة', 'value': completed_tasks},
                    {'type': 'in_progress', 'label': 'المهام قيد التنفيذ', 'value': in_progress_tasks},
                    {'type': 'completion_rate', 'label': 'معدل الإنجاز', 'value': f'{completion_rate:.1f}%'}
                ],
                'priority_analysis': [{'priority': k, 'total': v['total'], 'completed': v['completed']} for k, v in priority_stats.items()],
                'category_analysis': [{'category': k, 'count': v} for k, v in category_stats.items()]
            },
            'metadata': {
                'title': 'إنتاجية المهام',
                'description': 'تحليل أداء المهام ومعدلات الإنجاز',
                'type': 'tasks_productivity'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_performance_dashboard_report(filters):
    """إنشاء تقرير لوحة الأداء"""
    try:
        from datetime import datetime, timedelta

        # إحصائيات القضايا
        total_cases = Case.query.count()
        active_cases = Case.query.filter(Case.status.in_(['نشطة', 'قيد النظر'])).count()
        closed_cases = Case.query.filter(Case.status == 'مغلقة').count()

        # إحصائيات العملاء
        total_clients = Client.query.count()
        new_clients_month = Client.query.filter(
            Client.created_at >= datetime.now() - timedelta(days=30)
        ).count()

        # إحصائيات مالية
        total_fees = db.session.query(func.sum(Fee.total_amount)).scalar() or 0
        total_paid = db.session.query(func.sum(Fee.paid_amount)).scalar() or 0
        total_remaining = total_fees - total_paid

        # إحصائيات العقارات
        total_properties = Property.query.count()
        active_leases = Lease.query.filter(Lease.end_date >= datetime.now().date()).count()

        return jsonify({
            'success': True,
            'data': {
                'kpis': [
                    {'label': 'إجمالي القضايا', 'value': total_cases, 'icon': 'gavel', 'color': 'primary'},
                    {'label': 'القضايا النشطة', 'value': active_cases, 'icon': 'play-circle', 'color': 'success'},
                    {'label': 'إجمالي العملاء', 'value': total_clients, 'icon': 'users', 'color': 'info'},
                    {'label': 'عملاء جدد (شهر)', 'value': new_clients_month, 'icon': 'user-plus', 'color': 'warning'},
                    {'label': 'إجمالي الأتعاب', 'value': f'{total_fees:,.0f}', 'icon': 'money-bill', 'color': 'success'},
                    {'label': 'المبلغ المحصل', 'value': f'{total_paid:,.0f}', 'icon': 'check-circle', 'color': 'primary'},
                    {'label': 'المبلغ المتبقي', 'value': f'{total_remaining:,.0f}', 'icon': 'clock', 'color': 'danger'},
                    {'label': 'العقارات المؤجرة', 'value': active_leases, 'icon': 'building', 'color': 'info'}
                ]
            },
            'metadata': {
                'title': 'لوحة الأداء',
                'description': 'مؤشرات الأداء الرئيسية للمكتب',
                'type': 'performance_dashboard'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_debts_analysis_report(filters):
    """إنشاء تقرير تحليل الديون"""
    try:
        from datetime import datetime

        # جلب الديون
        debts = Debt.query.all()

        # إحصائيات أساسية
        total_debts = len(debts)
        total_amount = sum([d.amount for d in debts])
        paid_amount = sum([d.paid_amount for d in debts])
        remaining_amount = total_amount - paid_amount

        # تحليل حسب النوع
        type_stats = {}
        for debt in debts:
            debt_type = debt.debt_type or 'عامة'
            if debt_type not in type_stats:
                type_stats[debt_type] = {'count': 0, 'amount': 0}
            type_stats[debt_type]['count'] += 1
            type_stats[debt_type]['amount'] += debt.amount

        # تحليل حسب الحالة
        status_stats = {}
        for debt in debts:
            status = debt.status or 'معلقة'
            if status not in status_stats:
                status_stats[status] = 0
            status_stats[status] += 1

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'total', 'label': 'إجمالي الديون', 'value': total_debts},
                    {'type': 'total_amount', 'label': 'إجمالي المبلغ', 'value': f'{total_amount:,.0f}'},
                    {'type': 'paid_amount', 'label': 'المبلغ المدفوع', 'value': f'{paid_amount:,.0f}'},
                    {'type': 'remaining', 'label': 'المبلغ المتبقي', 'value': f'{remaining_amount:,.0f}'}
                ],
                'type_analysis': [{'type': k, 'count': v['count'], 'amount': v['amount']} for k, v in type_stats.items()],
                'status_analysis': [{'status': k, 'count': v} for k, v in status_stats.items()]
            },
            'metadata': {
                'title': 'تحليل الديون',
                'description': 'تقرير شامل عن الديون والمستحقات',
                'type': 'debts_analysis'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_courts_statistics_report(filters):
    """إنشاء تقرير إحصائيات المحاكم"""
    try:
        # جلب القضايا
        cases = Case.query.all()

        # تحليل حسب المحكمة
        court_stats = {}
        for case in cases:
            court = case.court or 'غير محدد'
            if court not in court_stats:
                court_stats[court] = {'count': 0, 'active': 0, 'closed': 0}
            court_stats[court]['count'] += 1
            if case.status in ['نشطة', 'قيد النظر']:
                court_stats[court]['active'] += 1
            elif case.status == 'مغلقة':
                court_stats[court]['closed'] += 1

        # تحليل حسب نوع القضية
        case_type_stats = {}
        for case in cases:
            case_type = case.case_type or 'عامة'
            if case_type not in case_type_stats:
                case_type_stats[case_type] = 0
            case_type_stats[case_type] += 1

        # تحليل حسب الحالة
        status_stats = {}
        for case in cases:
            status = case.status or 'غير محدد'
            if status not in status_stats:
                status_stats[status] = 0
            status_stats[status] += 1

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'total', 'label': 'إجمالي القضايا', 'value': len(cases)},
                    {'type': 'courts', 'label': 'عدد المحاكم', 'value': len(court_stats)},
                    {'type': 'case_types', 'label': 'أنواع القضايا', 'value': len(case_type_stats)}
                ],
                'court_analysis': [{'court': k, 'total': v['count'], 'active': v['active'], 'closed': v['closed']} for k, v in court_stats.items()],
                'type_analysis': [{'type': k, 'count': v} for k, v in case_type_stats.items()],
                'status_analysis': [{'status': k, 'count': v} for k, v in status_stats.items()]
            },
            'metadata': {
                'title': 'إحصائيات المحاكم',
                'description': 'تحليل القضايا حسب المحاكم والأنواع',
                'type': 'courts_statistics'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_fees_collection_report(filters):
    """إنشاء تقرير تحصيل الأتعاب"""
    try:
        # جلب الأتعاب
        fees = Fee.query.all()

        # إحصائيات أساسية
        total_fees = len(fees)
        total_amount = sum([f.total_amount for f in fees])
        paid_amount = sum([f.paid_amount for f in fees])
        remaining_amount = total_amount - paid_amount
        collection_rate = (paid_amount / total_amount * 100) if total_amount > 0 else 0

        # تحليل حسب العملة
        currency_stats = {}
        for fee in fees:
            currency = fee.currency or 'شيكل'
            if currency not in currency_stats:
                currency_stats[currency] = {'total': 0, 'paid': 0}
            currency_stats[currency]['total'] += fee.total_amount
            currency_stats[currency]['paid'] += fee.paid_amount

        # تحليل حسب طريقة الدفع
        payment_stats = {}
        for fee in fees:
            payment_method = fee.payment_method or 'نقدي'
            if payment_method not in payment_stats:
                payment_stats[payment_method] = 0
            payment_stats[payment_method] += fee.paid_amount

        return jsonify({
            'success': True,
            'data': {
                'summary': [
                    {'type': 'total', 'label': 'إجمالي الأتعاب', 'value': total_fees},
                    {'type': 'total_amount', 'label': 'إجمالي المبلغ', 'value': f'{total_amount:,.0f}'},
                    {'type': 'paid_amount', 'label': 'المبلغ المحصل', 'value': f'{paid_amount:,.0f}'},
                    {'type': 'remaining', 'label': 'المبلغ المتبقي', 'value': f'{remaining_amount:,.0f}'},
                    {'type': 'collection_rate', 'label': 'معدل التحصيل', 'value': f'{collection_rate:.1f}%'}
                ],
                'currency_analysis': [{'currency': k, 'total': v['total'], 'paid': v['paid']} for k, v in currency_stats.items()],
                'payment_analysis': [{'method': k, 'amount': v} for k, v in payment_stats.items()]
            },
            'metadata': {
                'title': 'تحصيل الأتعاب',
                'description': 'تقرير تحصيل الأتعاب والمدفوعات',
                'type': 'fees_collection'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# ==================== مسارات إضافية للتوافق ====================

@app.route('/rentals')
@login_required
def rentals():
    """صفحة الإيجارات - توجيه إلى العقارات"""
    return redirect(url_for('properties_list'))

@app.route('/expenses')
@login_required
def expenses():
    """صفحة المصاريف - توجيه إلى مصاريف المكتب"""
    return redirect(url_for('office_expenses'))


# إضافة رؤوس الحماية
@app.after_request
def add_security_headers(response):
    # منع XSS
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    
    # HTTPS Strict Transport Security
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    
    # Content Security Policy
    response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';"
    
    # منع تخزين المعلومات الحساسة
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    
    return response

# Middleware للحماية الإضافية
@app.before_request
def security_middleware():
    # منع الوصول من IPs مشبوهة
    blocked_ips = []  # قائمة IPs محظورة
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))

    if client_ip in blocked_ips:
        return "Access Denied", 403
    
    # حد معدل الطلبات
    if hasattr(request, 'endpoint') and request.endpoint:
        # تطبيق حد معدل الطلبات للصفحات الحساسة
        sensitive_endpoints = ['login', 'add_user', 'delete_user']
        if request.endpoint in sensitive_endpoints:
            # يمكن إضافة منطق حد معدل الطلبات هنا
            pass

import logging
from datetime import datetime

# إعداد نظام تسجيل الأحداث الأمنية
security_logger = logging.getLogger('security')
security_handler = logging.FileHandler('security.log')
security_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
security_handler.setFormatter(security_formatter)
security_logger.addHandler(security_handler)
security_logger.setLevel(logging.INFO)

def log_security_event(event_type, details, ip_address=None):
    """تسجيل حدث أمني"""
    if not ip_address:
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))

    message = f"{event_type} - IP: {ip_address} - Details: {details}"
    security_logger.info(message)

# ==================== إعدادات النظام ====================

@app.route('/settings')
@login_required
def settings():
    """صفحة الإعدادات"""
    # محاكاة بيانات الإعدادات
    settings_data = {
        'office_name': 'مكتب المحاماة',
        'office_address': '',
        'office_phone': '',
        'office_email': '',
        'default_currency': 'شيقل',
        'enable_notifications': True,
        'email_notifications': False,
        'appointment_reminder': 24,
        'task_reminder': 3,
        'session_timeout': 60,
        'require_strong_password': False,
        'enable_audit_log': True,
        'items_per_page': 25,
        'date_format': 'dd/mm/yyyy',
        'enable_dark_mode': False,
        'auto_backup': True
    }

    # معلومات النظام
    database_size = "15.2 MB"
    user_count = "2"
    last_backup = "2024-01-15 02:00"

    return render_template('settings/settings.html',
                         settings=settings_data,
                         database_size=database_size,
                         user_count=user_count,
                         last_backup=last_backup)

@app.route('/settings/update', methods=['POST'])
@login_required
def update_settings():
    """تحديث الإعدادات العامة"""
    try:
        # هنا يمكن حفظ الإعدادات في قاعدة البيانات
        flash('تم حفظ الإعدادات بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ الإعدادات: {str(e)}', 'danger')

    return redirect(url_for('settings'))

@app.route('/settings/notifications', methods=['POST'])
@login_required
def update_notification_settings():
    """تحديث إعدادات الإشعارات"""
    try:
        # هنا يمكن حفظ إعدادات الإشعارات
        flash('تم حفظ إعدادات الإشعارات بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ إعدادات الإشعارات: {str(e)}', 'danger')

    return redirect(url_for('settings'))

@app.route('/settings/security', methods=['POST'])
@login_required
def update_security_settings():
    """تحديث إعدادات الأمان"""
    try:
        # هنا يمكن حفظ إعدادات الأمان
        flash('تم حفظ إعدادات الأمان بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ إعدادات الأمان: {str(e)}', 'danger')

    return redirect(url_for('settings'))

@app.route('/settings/system', methods=['POST'])
@login_required
def update_system_settings():
    """تحديث إعدادات النظام"""
    try:
        # هنا يمكن حفظ إعدادات النظام
        flash('تم حفظ إعدادات النظام بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ إعدادات النظام: {str(e)}', 'danger')

    return redirect(url_for('settings'))

# ==================== النسخ الاحتياطي ====================

@app.route('/backup')
@login_required
def backup():
    """صفحة النسخ الاحتياطي"""
    # محاكاة بيانات النسخ الاحتياطية
    backups = [
        {
            'id': 1,
            'filename': 'backup_2024_01_15_02_00.sql',
            'created_at': datetime(2024, 1, 15, 2, 0),
            'size': '15.2 MB',
            'type': 'كامل'
        },
        {
            'id': 2,
            'filename': 'backup_2024_01_14_02_00.sql',
            'created_at': datetime(2024, 1, 14, 2, 0),
            'size': '14.8 MB',
            'type': 'كامل'
        }
    ]

    # إعدادات النسخ التلقائي
    auto_backup_enabled = True
    backup_frequency = 'daily'
    backup_time = '02:00'
    keep_backups = 10

    return render_template('settings/backup.html',
                         backups=backups,
                         auto_backup_enabled=auto_backup_enabled,
                         backup_frequency=backup_frequency,
                         backup_time=backup_time,
                         keep_backups=keep_backups)

@app.route('/backup/create', methods=['POST'])
@login_required
def create_backup_form():
    """إنشاء نسخة احتياطية من النموذج"""
    try:
        backup_type = request.form.get('backup_type', 'full')
        export_format = request.form.get('export_format', 'sql')
        include_files = 'include_files' in request.form

        # هنا يمكن تنفيذ عملية إنشاء النسخة الاحتياطية
        # وإرجاع الملف للتحميل

        flash('تم إنشاء النسخة الاحتياطية بنجاح', 'success')
        return redirect(url_for('backup'))

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}', 'danger')
        return redirect(url_for('backup'))

@app.route('/backup/restore', methods=['POST'])
@login_required
def restore_backup_form():
    """استعادة نسخة احتياطية من النموذج"""
    try:
        if 'backup_file' not in request.files:
            flash('لم يتم اختيار ملف', 'danger')
            return redirect(url_for('backup'))

        file = request.files['backup_file']
        if file.filename == '':
            flash('لم يتم اختيار ملف', 'danger')
            return redirect(url_for('backup'))

        # هنا يمكن تنفيذ عملية استعادة النسخة الاحتياطية

        flash('تم استعادة النسخة الاحتياطية بنجاح', 'success')
        return redirect(url_for('backup'))

    except Exception as e:
        flash(f'حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}', 'danger')
        return redirect(url_for('backup'))

@app.route('/backup/download/<int:backup_id>')
@login_required
def download_backup(backup_id):
    """تحميل نسخة احتياطية"""
    try:
        # هنا يمكن تنفيذ عملية تحميل النسخة الاحتياطية
        flash('سيتم تحميل النسخة الاحتياطية قريباً', 'info')
        return redirect(url_for('backup'))
    except Exception as e:
        flash(f'حدث خطأ أثناء تحميل النسخة الاحتياطية: {str(e)}', 'danger')
        return redirect(url_for('backup'))

@app.route('/backup/delete/<int:backup_id>', methods=['DELETE'])
@login_required
def delete_backup(backup_id):
    """حذف نسخة احتياطية"""
    try:
        # هنا يمكن تنفيذ عملية حذف النسخة الاحتياطية
        return jsonify({'success': True, 'message': 'تم حذف النسخة الاحتياطية بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@app.route('/backup/auto-settings', methods=['POST'])
@login_required
def update_auto_backup_settings():
    """تحديث إعدادات النسخ التلقائي"""
    try:
        # هنا يمكن حفظ إعدادات النسخ التلقائي
        flash('تم حفظ إعدادات النسخ التلقائي بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ إعدادات النسخ التلقائي: {str(e)}', 'danger')

    return redirect(url_for('backup'))
