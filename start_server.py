#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل مبسط لنظام إدارة العقارات
"""

import os
import sys
from datetime import datetime

def main():
    """تشغيل الخادم"""
    try:
        print("=" * 60)
        print("🚀 نظام إدارة العقارات - Property Management System")
        print("=" * 60)
        print("📅 التاريخ:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        print("🔧 بدء تشغيل الخادم...")
        print()
        
        # استيراد التطبيق
        from app import app, db
        from app.models import User
        from werkzeug.security import generate_password_hash
        
        # إنشاء مستخدم افتراضي إذا لم يوجد
        with app.app_context():
            try:
                if User.query.count() == 0:
                    admin_user = User(
                        username='admin',
                        password=generate_password_hash('admin123'),
                        email='<EMAIL>',
                        full_name='مدير النظام',
                        role='admin',
                        is_active=True,
                        created_at=datetime.now()
                    )
                    db.session.add(admin_user)
                    db.session.commit()
                    print("✅ تم إنشاء المستخدم الافتراضي")
                    print("   👤 اسم المستخدم: admin")
                    print("   🔐 كلمة المرور: admin123")
                    print()
            except Exception as e:
                print(f"⚠️ تحذير: {e}")
        
        print("🌐 روابط النظام:")
        print("   🏠 الصفحة الرئيسية: http://localhost:5000")
        print("   🔐 تسجيل الدخول: http://localhost:5000/lawyersameh")
        print("=" * 60)
        print("✅ الخادم يعمل... اضغط Ctrl+C للإيقاف")
        print("=" * 60)
        
        # تشغيل الخادم
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
