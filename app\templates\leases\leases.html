{% extends "base.html" %}

{% block title %}إدارة عقود الإيجار{% endblock %}

{% block extra_css %}
<style>
    .lease-card {
        border: 1px solid #e0e0e0;
        border-radius: 15px;
        transition: all 0.3s ease;
        background: white;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 20px;
    }
    
    .lease-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }
    
    .lease-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        position: relative;
    }
    
    .lease-status {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-active { background: #28a745; color: white; }
    .status-expired { background: #dc3545; color: white; }
    .status-pending { background: #ffc107; color: #212529; }
    .status-terminated { background: #6c757d; color: white; }
    
    .lease-info {
        padding: 20px;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f1f1f1;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
    }
    
    .info-value {
        color: #6c757d;
        font-size: 0.9rem;
        text-align: right;
    }
    
    .lease-actions {
        padding: 15px 20px;
        background: #f8f9fa;
        display: flex;
        gap: 10px;
        justify-content: center;
    }
    
    .btn-lease-action {
        flex: 1;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.85rem;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 8px;
    }
    
    .stats-label {
        font-size: 1rem;
        opacity: 0.9;
    }
    
    .filter-section {
        background: #f8f9fa;
        padding: 25px;
        border-radius: 15px;
        margin-bottom: 25px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    
    .btn-add-lease {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-add-lease:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        color: white;
    }
    
    .search-box {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 12px 20px;
        transition: all 0.3s ease;
    }
    
    .search-box:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    
    .expiry-warning {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: white;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .property-info {
        background: #e3f2fd;
        padding: 10px 15px;
        border-radius: 8px;
        margin-bottom: 10px;
    }
    
    .tenant-info {
        background: #f3e5f5;
        padding: 10px 15px;
        border-radius: 8px;
        margin-bottom: 10px;
    }
    
    .payment-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
    }
    
    .payment-current { background: #28a745; }
    .payment-overdue { background: #dc3545; }
    .payment-upcoming { background: #ffc107; }
    
    .lease-timeline {
        position: relative;
        padding: 10px 0;
    }
    
    .timeline-progress {
        height: 4px;
        background: #e9ecef;
        border-radius: 2px;
        position: relative;
        overflow: hidden;
    }
    
    .timeline-fill {
        height: 100%;
        background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
        border-radius: 2px;
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-0">
                <i class="fas fa-file-contract text-primary"></i>
                إدارة عقود الإيجار
            </h2>
            <p class="text-muted">إدارة شاملة ومتطورة لجميع عقود الإيجار</p>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-add-lease" data-bs-toggle="modal" data-bs-target="#addLeaseModal">
                <i class="fas fa-plus"></i>
                إنشاء عقد جديد
            </button>
        </div>
    </div>

    <!-- Expiry Warning -->
    {% if expiring_leases_count > 0 %}
    <div class="expiry-warning">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>تنبيه:</strong> يوجد {{ expiring_leases_count }} عقد/عقود ستنتهي خلال الشهر القادم
        <a href="{{ url_for('leases', status='expiring') }}" class="btn btn-light btn-sm ms-2">عرض العقود</a>
    </div>
    {% endif %}

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <div class="stats-number">{{ total_leases or 0 }}</div>
                <div class="stats-label">إجمالي العقود</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="stats-number">{{ active_leases or 0 }}</div>
                <div class="stats-label">عقود نشطة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <div class="stats-number">{{ expiring_leases or 0 }}</div>
                <div class="stats-label">عقود منتهية الصلاحية</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                <div class="stats-number">{{ total_monthly_income or 0 }}</div>
                <div class="stats-label">الدخل الشهري (JOD)</div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <form method="GET" id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <input type="text" class="form-control search-box" name="search" 
                           placeholder="البحث في العقود..." value="{{ request.args.get('search', '') }}">
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="نشط" {{ 'selected' if request.args.get('status') == 'نشط' }}>نشط</option>
                        <option value="منتهي" {{ 'selected' if request.args.get('status') == 'منتهي' }}>منتهي</option>
                        <option value="معلق" {{ 'selected' if request.args.get('status') == 'معلق' }}>معلق</option>
                        <option value="ملغي" {{ 'selected' if request.args.get('status') == 'ملغي' }}>ملغي</option>
                        <option value="expiring" {{ 'selected' if request.args.get('status') == 'expiring' }}>ينتهي قريباً</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="property_type">
                        <option value="">جميع أنواع العقارات</option>
                        {% for property_type in property_types %}
                        <option value="{{ property_type }}" {{ 'selected' if request.args.get('property_type') == property_type }}>{{ property_type }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="payment_status">
                        <option value="">جميع حالات الدفع</option>
                        <option value="current" {{ 'selected' if request.args.get('payment_status') == 'current' }}>محدث</option>
                        <option value="overdue" {{ 'selected' if request.args.get('payment_status') == 'overdue' }}>متأخر</option>
                        <option value="upcoming" {{ 'selected' if request.args.get('payment_status') == 'upcoming' }}>قادم</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
                <div class="col-md-1">
                    <a href="{{ url_for('leases') }}" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Leases Grid -->
    <div class="row">
        {% if leases %}
            {% for lease in leases %}
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="lease-card">
                    <div class="lease-header">
                        <div class="lease-status status-{{ 'active' if lease.status == 'نشط' else 'expired' if lease.status == 'منتهي' else 'pending' if lease.status == 'معلق' else 'terminated' }}">
                            {{ lease.status }}
                        </div>
                        
                        <h6 class="mb-2">
                            <i class="fas fa-file-contract"></i>
                            عقد رقم: {{ lease.lease_number or lease.id }}
                        </h6>
                        
                        <div class="property-info">
                            <i class="fas fa-home"></i>
                            {{ lease.property.name if lease.property else 'عقار محذوف' }}
                        </div>
                        
                        <div class="tenant-info">
                            <i class="fas fa-user"></i>
                            {{ lease.tenant.full_name if lease.tenant else 'مستأجر محذوف' }}
                        </div>
                    </div>
                    
                    <div class="lease-info">
                        <div class="info-row">
                            <span class="info-label">تاريخ البداية:</span>
                            <span class="info-value">{{ lease.start_date.strftime('%Y-%m-%d') if lease.start_date }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">تاريخ الانتهاء:</span>
                            <span class="info-value">{{ lease.end_date.strftime('%Y-%m-%d') if lease.end_date }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">الإيجار الشهري:</span>
                            <span class="info-value">{{ lease.monthly_rent or 0 }} {{ lease.currency or 'JOD' }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">مدة العقد:</span>
                            <span class="info-value">{{ lease.lease_duration_months or 0 }} شهر</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">حالة الدفع:</span>
                            <span class="info-value">
                                {% if lease.payment_status == 'current' %}
                                    <span class="payment-indicator payment-current"></span>محدث
                                {% elif lease.payment_status == 'overdue' %}
                                    <span class="payment-indicator payment-overdue"></span>متأخر
                                {% else %}
                                    <span class="payment-indicator payment-upcoming"></span>قادم
                                {% endif %}
                            </span>
                        </div>
                        
                        <!-- Timeline Progress -->
                        {% if lease.status == 'نشط' %}
                        <div class="lease-timeline mt-3">
                            <div class="timeline-progress">
                                <div class="timeline-fill" style="width: {{ lease.progress_percentage or 0 }}%"></div>
                            </div>
                            <small class="text-muted">تقدم العقد: {{ lease.progress_percentage or 0 }}%</small>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="lease-actions">
                        <a href="{{ url_for('lease_details', id=lease.id) }}" 
                           class="btn btn-outline-primary btn-lease-action">
                            <i class="fas fa-eye"></i> عرض
                        </a>
                        <a href="{{ url_for('edit_lease', id=lease.id) }}" 
                           class="btn btn-outline-warning btn-lease-action">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <button class="btn btn-outline-success btn-lease-action" 
                                onclick="addPayment({{ lease.id }})">
                            <i class="fas fa-money-bill"></i> دفع
                        </button>
                        <button class="btn btn-outline-danger btn-lease-action" 
                                onclick="terminateLease({{ lease.id }})">
                            <i class="fas fa-ban"></i> إنهاء
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-file-contract fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">لا يوجد عقود إيجار</h4>
                    <p class="text-muted">ابدأ بإنشاء عقد إيجار جديد</p>
                    <button class="btn btn-add-lease" data-bs-toggle="modal" data-bs-target="#addLeaseModal">
                        <i class="fas fa-plus"></i>
                        إنشاء عقد جديد
                    </button>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if leases.pages > 1 %}
    <nav aria-label="تنقل الصفحات">
        <ul class="pagination justify-content-center">
            {% if leases.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('leases', page=leases.prev_num, **request.args) }}">السابق</a>
            </li>
            {% endif %}
            
            {% for page_num in leases.iter_pages() %}
                {% if page_num %}
                    {% if page_num != leases.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('leases', page=page_num, **request.args) }}">{{ page_num }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if leases.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('leases', page=leases.next_num, **request.args) }}">التالي</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- Add Lease Modal -->
{% include 'leases/modals/add_lease_modal.html' %}

{% endblock %}

{% block extra_js %}
<script>
function addPayment(leaseId) {
    window.location.href = `/leases/${leaseId}/payments/add`;
}

function terminateLease(leaseId) {
    if (confirm('هل أنت متأكد من إنهاء هذا العقد؟')) {
        fetch(`/leases/${leaseId}/terminate`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء إنهاء العقد: ' + data.message);
            }
        });
    }
}

// Auto-submit filter form on change
document.querySelectorAll('#filterForm select').forEach(select => {
    select.addEventListener('change', () => {
        document.getElementById('filterForm').submit();
    });
});
</script>
{% endblock %}
