{% extends "base.html" %}

{% block title %}لوحة التحكم{% endblock %}
{% block page_title %}لوحة التحكم{% endblock %}

{% block content %}
<style>
    /* ===== تصميم لوحة التحكم الاحترافية ===== */
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-2xl);
    }
    
    .stats-card {
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-md);
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
    }
    
    .stats-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }
    
    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
        border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    }
    
    .stats-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-lg);
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-2xl);
        color: var(--text-white);
        background: var(--primary-gradient);
        box-shadow: var(--shadow-md);
    }
    
    .stats-info h3 {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-secondary);
        margin: 0 0 var(--spacing-xs) 0;
    }
    
    .stats-number {
        font-size: var(--text-4xl);
        font-weight: 900;
        color: var(--text-primary);
        margin: 0;
        line-height: 1;
    }
    
    .stats-change {
        font-size: var(--text-sm);
        font-weight: 500;
        margin-top: var(--spacing-xs);
    }
    
    .stats-change.positive {
        color: var(--success-color);
    }
    
    .stats-change.negative {
        color: var(--danger-color);
    }
    
    /* ===== ألوان مختلفة للبطاقات ===== */
    .stats-card.clients .stats-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .stats-card.cases .stats-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .stats-card.properties .stats-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .stats-card.finance .stats-icon {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
    
    /* ===== قسم الإجراءات السريعة ===== */
    .quick-actions {
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-md);
        margin-bottom: var(--spacing-2xl);
    }
    
    .quick-actions h2 {
        font-size: var(--text-2xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-lg);
    }
    
    .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-xl);
        background: var(--bg-tertiary);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        text-decoration: none;
        color: var(--text-primary);
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
    }
    
    .action-btn:hover {
        background: var(--primary-color);
        color: var(--text-white);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
    
    .action-btn i {
        font-size: var(--text-3xl);
        transition: var(--transition-normal);
    }
    
    .action-btn:hover i {
        transform: scale(1.1);
    }
    
    .action-btn span {
        font-weight: 600;
        text-align: center;
    }
    
    /* ===== قسم التنبيهات ===== */
    .alerts-section {
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-md);
        margin-bottom: var(--spacing-2xl);
    }
    
    .alerts-section h2 {
        font-size: var(--text-2xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .alert-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        background: var(--bg-tertiary);
        border-radius: var(--radius-md);
        margin-bottom: var(--spacing-md);
        border-right: 4px solid var(--warning-color);
        transition: all var(--transition-fast);
    }

    .alert-item:hover {
        background: var(--bg-secondary);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
    
    .alert-item.urgent {
        border-right-color: var(--danger-color);
        background: rgba(220, 38, 38, 0.05);
    }
    
    .alert-item i {
        font-size: var(--text-lg);
        color: var(--warning-color);
    }
    
    .alert-item.urgent i {
        color: var(--danger-color);
        animation: pulse 2s infinite;
    }
    
    /* ===== الرسوم المتحركة ===== */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.1);
            opacity: 0.8;
        }
    }
    
    /* تطبيق الحركات */
    .stats-card {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;
    }
    
    .stats-card:nth-child(1) { animation-delay: 0.1s; }
    .stats-card:nth-child(2) { animation-delay: 0.2s; }
    .stats-card:nth-child(3) { animation-delay: 0.3s; }
    .stats-card:nth-child(4) { animation-delay: 0.4s; }
    
    .quick-actions {
        animation: fadeInUp 0.6s ease-out 0.5s both;
    }
    
    .alerts-section {
        animation: fadeInUp 0.6s ease-out 0.6s both;
    }
    
    /* ===== الاستجابة للشاشات الصغيرة ===== */
    @media (max-width: 768px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }
        
        .actions-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .stats-number {
            font-size: var(--text-3xl);
        }
    }
    
    @media (max-width: 480px) {
        .actions-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<!-- الإحصائيات الرئيسية -->
<div class="dashboard-grid">
    <div class="stats-card clients">
        <div class="stats-header">
            <div class="stats-info">
                <h3>إجمالي العملاء</h3>
                <div class="stats-number">{{ clients_count }}</div>
                <div class="stats-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +5% من الشهر الماضي
                </div>
            </div>
            <div class="stats-icon">
                <i class="fas fa-users"></i>
            </div>
        </div>
    </div>
    
    <div class="stats-card cases">
        <div class="stats-header">
            <div class="stats-info">
                <h3>القضايا النشطة</h3>
                <div class="stats-number">{{ cases_count }}</div>
                <div class="stats-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +12% من الشهر الماضي
                </div>
            </div>
            <div class="stats-icon">
                <i class="fas fa-gavel"></i>
            </div>
        </div>
    </div>
    
    <div class="stats-card properties">
        <div class="stats-header">
            <div class="stats-info">
                <h3>العقارات المدارة</h3>
                <div class="stats-number">{{ properties_count }}</div>
                <div class="stats-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +3% من الشهر الماضي
                </div>
            </div>
            <div class="stats-icon">
                <i class="fas fa-building"></i>
            </div>
        </div>
    </div>
    
    <div class="stats-card finance">
        <div class="stats-header">
            <div class="stats-info">
                <h3>الإيرادات الشهرية</h3>
                <div class="stats-number">{{ total_revenue|default('0') }}</div>
                <div class="stats-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +8% من الشهر الماضي
                </div>
            </div>
            <div class="stats-icon">
                <i class="fas fa-chart-line"></i>
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="quick-actions">
    <h2>
        <i class="fas fa-bolt"></i>
        الإجراءات السريعة
    </h2>
    <div class="actions-grid">
        <a href="{{ url_for('add_client') }}" class="action-btn">
            <i class="fas fa-user-plus"></i>
            <span>إضافة عميل جديد</span>
        </a>
        <a href="{{ url_for('add_case') }}" class="action-btn">
            <i class="fas fa-plus-circle"></i>
            <span>إضافة قضية جديدة</span>
        </a>
        <a href="{{ url_for('add_property') }}" class="action-btn">
            <i class="fas fa-home"></i>
            <span>إضافة عقار</span>
        </a>
        <a href="{{ url_for('add_financial_transaction') }}" class="action-btn">
            <i class="fas fa-money-bill-wave"></i>
            <span>إضافة سند مالي</span>
        </a>
        <a href="{{ url_for('add_task') }}" class="action-btn">
            <i class="fas fa-tasks"></i>
            <span>إضافة مهمة</span>
        </a>
        <a href="{{ url_for('add_appointment') }}" class="action-btn">
            <i class="fas fa-calendar-plus"></i>
            <span>حجز موعد</span>
        </a>
        <a href="{{ url_for('add_lease') }}" class="action-btn">
            <i class="fas fa-file-contract"></i>
            <span>إضافة عقد إيجار</span>
        </a>
        <a href="{{ url_for('add_tenant') }}" class="action-btn">
            <i class="fas fa-user-tie"></i>
            <span>إضافة مستأجر</span>
        </a>
        <a href="{{ url_for('add_debt') }}" class="action-btn">
            <i class="fas fa-credit-card"></i>
            <span>إضافة دين</span>
        </a>
        <a href="{{ url_for('add_expense') }}" class="action-btn">
            <i class="fas fa-receipt"></i>
            <span>إضافة مصروف</span>
        </a>
        <a href="{{ url_for('add_rental_income') }}" class="action-btn">
            <i class="fas fa-coins"></i>
            <span>إضافة إيراد إيجار</span>
        </a>
        <a href="{{ url_for('add_user') }}" class="action-btn">
            <i class="fas fa-user-shield"></i>
            <span>إضافة مستخدم</span>
        </a>
    </div>
</div>

<!-- التنبيهات والإشعارات -->
<div class="alerts-section">
    <h2>
        <i class="fas fa-exclamation-triangle"></i>
        التنبيهات والإشعارات
    </h2>
    
    {% if overdue_tasks or overdue_appointments or overdue_payments %}
        {% for task in overdue_tasks %}
        <div class="alert-item urgent" onclick="window.location.href='/tasks';" style="cursor: pointer;">
            <i class="fas fa-clock"></i>
            <div class="flex-grow-1">
                <strong>مهمة متأخرة:</strong> {{ task.title }}
                <br><small>تاريخ الاستحقاق: {{ task.due_date.strftime('%Y-%m-%d') if task.due_date else 'غير محدد' }}</small>
            </div>
            <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); window.location.href='/tasks';">
                عرض
            </button>
        </div>
        {% endfor %}

        {% for appointment in overdue_appointments %}
        <div class="alert-item urgent" onclick="window.location.href='/appointments';" style="cursor: pointer;">
            <i class="fas fa-calendar-times"></i>
            <div class="flex-grow-1">
                <strong>موعد:</strong> {{ appointment.client_name or appointment.subject or 'موعد' }}
                <br><small>التاريخ: {{ appointment.date.strftime('%Y-%m-%d') if appointment.date else 'غير محدد' }}</small>
            </div>
            <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); window.location.href='/appointments';">
                عرض
            </button>
        </div>
        {% endfor %}

        {% for payment in overdue_payments %}
        <div class="alert-item urgent" onclick="window.location.href='/reports';" style="cursor: pointer;">
            <i class="fas fa-money-bill-wave"></i>
            <div class="flex-grow-1">
                <strong>دفعة متأخرة:</strong> {{ payment.description }}
                <br><small>المبلغ: {{ payment.amount }} - تاريخ الاستحقاق: {{ payment.due_date.strftime('%Y-%m-%d') if payment.due_date else 'غير محدد' }}</small>
            </div>
            <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); window.location.href='/reports';">
                عرض
            </button>
        </div>
        {% endfor %}
    {% else %}
        <div class="alert-item">
            <i class="fas fa-check-circle"></i>
            <div>
                <strong>ممتاز!</strong> لا توجد مهام أو مواعيد متأخرة
            </div>
        </div>
    {% endif %}
</div>

<!-- ويجت التقويم -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                        التقويم السريع
                    </h5>
                    <a href="{{ url_for('calendar') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>
                        عرض التقويم الكامل
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div id="dashboardCalendar" class="dashboard-calendar">
                    <!-- سيتم إنشاء التقويم هنا بواسطة JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.dashboard-calendar {
    max-width: 100%;
    overflow-x: auto;
}

.calendar-mini {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 15px;
}

.calendar-day-header {
    background: #e9ecef;
    padding: 8px 4px;
    text-align: center;
    font-weight: bold;
    font-size: 0.8rem;
    color: #495057;
    border-radius: 4px;
}

.calendar-day {
    background: white;
    min-height: 40px;
    padding: 4px;
    text-align: center;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    font-size: 0.85rem;
}

.calendar-day:hover {
    background: #e3f2fd;
}

.calendar-day.today {
    background: #2196f3;
    color: white;
    font-weight: bold;
}

.calendar-day.has-events {
    border-right: 3px solid #28a745;
}

.calendar-day.has-overdue {
    border-right: 3px solid #dc3545;
}

.calendar-day.other-month {
    background: #f8f9fa;
    color: #6c757d;
}

.event-dot {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #28a745;
}

.event-dot.overdue {
    background: #dc3545;
}

.calendar-legend {
    display: flex;
    justify-content: center;
    gap: 15px;
    font-size: 0.8rem;
    margin-top: 10px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.legend-color {
    width: 10px;
    height: 10px;
    border-radius: 2px;
}

@media (max-width: 768px) {
    .calendar-mini {
        font-size: 0.7rem;
    }

    .calendar-day {
        min-height: 30px;
        font-size: 0.7rem;
    }

    .calendar-legend {
        flex-direction: column;
        gap: 8px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    renderDashboardCalendar();
});

function renderDashboardCalendar() {
    const container = document.getElementById('dashboardCalendar');
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();

    // أسماء الأيام
    const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

    // أسماء الأشهر
    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    let html = `
        <div class="text-center mb-3">
            <h6 class="mb-0">${monthNames[month]} ${year}</h6>
        </div>
        <div class="calendar-mini">
    `;

    // إضافة رؤوس الأيام
    dayNames.forEach(day => {
        html += `<div class="calendar-day-header">${day}</div>`;
    });

    // الحصول على أول يوم في الشهر
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    // إنشاء أيام التقويم
    for (let i = 0; i < 42; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);

        const isCurrentMonth = date.getMonth() === month;
        const isToday = isDateToday(date);
        const hasEvents = false; // سيتم ربطها بالبيانات الحقيقية لاحقاً
        const hasOverdue = false; // سيتم ربطها بالبيانات الحقيقية لاحقاً

        let classes = 'calendar-day';
        if (!isCurrentMonth) classes += ' other-month';
        if (isToday) classes += ' today';
        if (hasEvents) classes += ' has-events';
        if (hasOverdue) classes += ' has-overdue';

        html += `
            <div class="${classes}" onclick="goToCalendarDate('${date.toISOString().split('T')[0]}')">
                ${date.getDate()}
                ${hasEvents ? '<div class="event-dot"></div>' : ''}
                ${hasOverdue ? '<div class="event-dot overdue"></div>' : ''}
            </div>
        `;
    }

    html += `
        </div>
        <div class="calendar-legend">
            <div class="legend-item">
                <div class="legend-color" style="background: #28a745;"></div>
                <span>مواعيد ومهام</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #dc3545;"></div>
                <span>متأخرة</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #2196f3;"></div>
                <span>اليوم</span>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

function isDateToday(date) {
    const today = new Date();
    return date.toDateString() === today.toDateString();
}

function goToCalendarDate(dateStr) {
    window.location.href = `{{ url_for('calendar') }}?date=${dateStr}`;
}

// نظام التنبيهات المنبثقة
function showDashboardAlerts() {
    // بيانات التنبيهات الحقيقية
    const alerts = [
        {
            type: 'overdue_task',
            title: 'مهمة متأخرة',
            message: 'مراجعة ملف القضية رقم 123',
            date: '2024-01-15',
            priority: 'high'
        },
        {
            type: 'upcoming_appointment',
            title: 'موعد قريب',
            message: 'اجتماع مع العميل أحمد محمد',
            date: '2024-01-20',
            priority: 'medium'
        },
        {
            type: 'payment_due',
            title: 'دفعة مستحقة',
            message: 'أتعاب القضية رقم 456',
            amount: '5000 شيقل',
            date: '2024-01-18',
            priority: 'high'
        }
    ];

    if (alerts.length > 0) {
        showAlertModal(alerts);
    }
}

function showAlertModal(alerts) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'dashboardAlertsModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تنبيهات مهمة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        لديك ${alerts.length} تنبيه مهم يتطلب انتباهك
                    </div>
                    <div class="row">
                        ${alerts.map(alert => `
                            <div class="col-md-6 mb-3">
                                <div class="card border-${alert.priority === 'high' ? 'danger' : 'warning'}">
                                    <div class="card-body">
                                        <h6 class="card-title text-${alert.priority === 'high' ? 'danger' : 'warning'}">
                                            <i class="fas fa-${getAlertIcon(alert.type)} me-1"></i>
                                            ${alert.title}
                                        </h6>
                                        <p class="card-text">${alert.message}</p>
                                        ${alert.amount ? `<p class="text-success"><strong>${alert.amount}</strong></p>` : ''}
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            ${alert.date}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="goToNotifications()">
                        <i class="fas fa-bell me-1"></i>
                        عرض جميع الإشعارات
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

function getAlertIcon(type) {
    switch(type) {
        case 'overdue_task': return 'tasks';
        case 'upcoming_appointment': return 'calendar-check';
        case 'payment_due': return 'money-bill-wave';
        default: return 'bell';
    }
}

function goToNotifications() {
    window.location.href = '{{ url_for("notifications") }}';
}

// عرض التنبيهات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قصير لضمان تحميل الصفحة بالكامل
    setTimeout(showDashboardAlerts, 2000);
});
</script>
{% endblock %}
