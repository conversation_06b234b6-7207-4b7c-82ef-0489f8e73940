{% extends 'base.html' %}
{% block title %}إعدادات النظام{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-cog"></i> إعدادات النظام</h1>
                    <p>إدارة إعدادات النظام والتفضيلات العامة</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- إعدادات عامة -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sliders-h text-primary me-2"></i>
                        الإعدادات العامة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('update_settings') }}">
                        <div class="mb-3">
                            <label class="form-label">اسم المكتب</label>
                            <input type="text" name="office_name" class="form-control" 
                                   value="{{ settings.office_name if settings else 'مكتب المحاماة' }}" 
                                   placeholder="اسم المكتب">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">عنوان المكتب</label>
                            <textarea name="office_address" class="form-control" rows="3" 
                                      placeholder="عنوان المكتب">{{ settings.office_address if settings else '' }}</textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="text" name="office_phone" class="form-control" 
                                   value="{{ settings.office_phone if settings else '' }}" 
                                   placeholder="رقم الهاتف">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" name="office_email" class="form-control" 
                                   value="{{ settings.office_email if settings else '' }}" 
                                   placeholder="البريد الإلكتروني">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العملة الافتراضية</label>
                            <select name="default_currency" class="form-select">
                                <option value="شيقل" {{ 'selected' if settings and settings.default_currency == 'شيقل' else '' }}>شيقل</option>
                                <option value="دينار" {{ 'selected' if settings and settings.default_currency == 'دينار' else '' }}>دينار</option>
                                <option value="دولار" {{ 'selected' if settings and settings.default_currency == 'دولار' else '' }}>دولار</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ الإعدادات
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- إعدادات الإشعارات -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell text-warning me-2"></i>
                        إعدادات الإشعارات
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('update_notification_settings') }}">
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="enable_notifications" 
                                       id="enableNotifications" {{ 'checked' if settings and settings.enable_notifications else '' }}>
                                <label class="form-check-label" for="enableNotifications">
                                    تفعيل الإشعارات
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="email_notifications" 
                                       id="emailNotifications" {{ 'checked' if settings and settings.email_notifications else '' }}>
                                <label class="form-check-label" for="emailNotifications">
                                    إشعارات البريد الإلكتروني
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">تذكير المواعيد (بالساعات)</label>
                            <select name="appointment_reminder" class="form-select">
                                <option value="1" {{ 'selected' if settings and settings.appointment_reminder == 1 else '' }}>ساعة واحدة</option>
                                <option value="2" {{ 'selected' if settings and settings.appointment_reminder == 2 else '' }}>ساعتان</option>
                                <option value="24" {{ 'selected' if settings and settings.appointment_reminder == 24 else '' }}>24 ساعة</option>
                                <option value="48" {{ 'selected' if settings and settings.appointment_reminder == 48 else '' }}>48 ساعة</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">تذكير المهام (بالأيام)</label>
                            <select name="task_reminder" class="form-select">
                                <option value="1" {{ 'selected' if settings and settings.task_reminder == 1 else '' }}>يوم واحد</option>
                                <option value="3" {{ 'selected' if settings and settings.task_reminder == 3 else '' }}>3 أيام</option>
                                <option value="7" {{ 'selected' if settings and settings.task_reminder == 7 else '' }}>أسبوع</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-bell me-2"></i>حفظ إعدادات الإشعارات
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- إعدادات الأمان -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt text-success me-2"></i>
                        إعدادات الأمان
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('update_security_settings') }}">
                        <div class="mb-3">
                            <label class="form-label">مدة انتهاء الجلسة (بالدقائق)</label>
                            <select name="session_timeout" class="form-select">
                                <option value="30" {{ 'selected' if settings and settings.session_timeout == 30 else '' }}>30 دقيقة</option>
                                <option value="60" {{ 'selected' if settings and settings.session_timeout == 60 else '' }}>ساعة واحدة</option>
                                <option value="120" {{ 'selected' if settings and settings.session_timeout == 120 else '' }}>ساعتان</option>
                                <option value="480" {{ 'selected' if settings and settings.session_timeout == 480 else '' }}>8 ساعات</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="require_strong_password" 
                                       id="requireStrongPassword" {{ 'checked' if settings and settings.require_strong_password else '' }}>
                                <label class="form-check-label" for="requireStrongPassword">
                                    طلب كلمة مرور قوية
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="enable_audit_log" 
                                       id="enableAuditLog" {{ 'checked' if settings and settings.enable_audit_log else '' }}>
                                <label class="form-check-label" for="enableAuditLog">
                                    تفعيل سجل العمليات
                                </label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-shield-alt me-2"></i>حفظ إعدادات الأمان
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- إعدادات النظام -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools text-info me-2"></i>
                        إعدادات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('update_system_settings') }}">
                        <div class="mb-3">
                            <label class="form-label">عدد العناصر في الصفحة</label>
                            <select name="items_per_page" class="form-select">
                                <option value="10" {{ 'selected' if settings and settings.items_per_page == 10 else '' }}>10</option>
                                <option value="25" {{ 'selected' if settings and settings.items_per_page == 25 else '' }}>25</option>
                                <option value="50" {{ 'selected' if settings and settings.items_per_page == 50 else '' }}>50</option>
                                <option value="100" {{ 'selected' if settings and settings.items_per_page == 100 else '' }}>100</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">التاريخ الافتراضي</label>
                            <select name="date_format" class="form-select">
                                <option value="dd/mm/yyyy" {{ 'selected' if settings and settings.date_format == 'dd/mm/yyyy' else '' }}>يوم/شهر/سنة</option>
                                <option value="mm/dd/yyyy" {{ 'selected' if settings and settings.date_format == 'mm/dd/yyyy' else '' }}>شهر/يوم/سنة</option>
                                <option value="yyyy-mm-dd" {{ 'selected' if settings and settings.date_format == 'yyyy-mm-dd' else '' }}>سنة-شهر-يوم</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="enable_dark_mode" 
                                       id="enableDarkMode" {{ 'checked' if settings and settings.enable_dark_mode else '' }}>
                                <label class="form-check-label" for="enableDarkMode">
                                    تفعيل الوضع الليلي افتراضياً
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="auto_backup" 
                                       id="autoBackup" {{ 'checked' if settings and settings.auto_backup else '' }}>
                                <label class="form-check-label" for="autoBackup">
                                    النسخ الاحتياطي التلقائي
                                </label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-tools me-2"></i>حفظ إعدادات النظام
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات النظام -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle text-secondary me-2"></i>
                        معلومات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-code fa-2x text-primary mb-2"></i>
                                <h6>إصدار النظام</h6>
                                <span class="badge bg-primary">v2.0.0</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-database fa-2x text-success mb-2"></i>
                                <h6>حجم قاعدة البيانات</h6>
                                <span class="badge bg-success">{{ database_size if database_size else '---' }}</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-users fa-2x text-info mb-2"></i>
                                <h6>عدد المستخدمين</h6>
                                <span class="badge bg-info">{{ user_count if user_count else '---' }}</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-calendar fa-2x text-warning mb-2"></i>
                                <h6>آخر نسخة احتياطية</h6>
                                <span class="badge bg-warning">{{ last_backup if last_backup else 'لا توجد' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
