<form method="POST" action="{{ url_for('add_tenant') }}" class="modal-form" id="addTenantForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-user"></i> المعلومات الشخصية
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                <input type="text" name="name" class="form-control" required 
                       placeholder="الاسم الكامل للمستأجر">
            </div>
            <div class="col-md-6">
                <label class="form-label">رقم الهوية</label>
                <input type="text" name="national_id" class="form-control" 
                       placeholder="رقم الهوية الوطنية">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                <input type="tel" name="phone" class="form-control" required 
                       placeholder="رقم الهاتف">
            </div>
            <div class="col-md-6">
                <label class="form-label">البريد الإلكتروني</label>
                <input type="email" name="email" class="form-control" 
                       placeholder="البريد الإلكتروني">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">تاريخ الميلاد</label>
                <input type="date" name="birth_date" class="form-control">
            </div>
            <div class="col-md-6">
                <label class="form-label">الجنسية</label>
                <input type="text" name="nationality" class="form-control" 
                       placeholder="الجنسية">
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-map-marker-alt"></i> معلومات العنوان
        </div>
        <div class="mb-3">
            <label class="form-label">العنوان الحالي</label>
            <textarea name="current_address" class="form-control" rows="2" 
                      placeholder="العنوان الحالي للمستأجر"></textarea>
        </div>
        <div class="mb-3">
            <label class="form-label">العنوان الدائم</label>
            <textarea name="permanent_address" class="form-control" rows="2" 
                      placeholder="العنوان الدائم للمستأجر"></textarea>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-briefcase"></i> المعلومات المهنية
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">المهنة</label>
                <input type="text" name="occupation" class="form-control" 
                       placeholder="المهنة أو الوظيفة">
            </div>
            <div class="col-md-6">
                <label class="form-label">جهة العمل</label>
                <input type="text" name="employer" class="form-control" 
                       placeholder="اسم جهة العمل">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">الراتب الشهري</label>
                <input type="number" name="monthly_income" class="form-control" 
                       step="0.01" min="0" placeholder="الراتب الشهري">
            </div>
            <div class="col-md-6">
                <label class="form-label">هاتف العمل</label>
                <input type="tel" name="work_phone" class="form-control" 
                       placeholder="رقم هاتف العمل">
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-users"></i> معلومات الأسرة
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">الحالة الاجتماعية</label>
                <select name="marital_status" class="form-control">
                    <option value="">اختر الحالة</option>
                    <option value="أعزب">أعزب</option>
                    <option value="متزوج">متزوج</option>
                    <option value="مطلق">مطلق</option>
                    <option value="أرمل">أرمل</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">عدد أفراد الأسرة</label>
                <input type="number" name="family_members" class="form-control" 
                       min="1" placeholder="عدد أفراد الأسرة">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-md-6">
                <label class="form-label">عدد الأطفال</label>
                <input type="number" name="children_count" class="form-control" 
                       min="0" placeholder="عدد الأطفال">
            </div>
            <div class="col-md-6">
                <label class="form-label">هل يوجد حيوانات أليفة؟</label>
                <select name="has_pets" class="form-control">
                    <option value="لا">لا</option>
                    <option value="نعم">نعم</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-star"></i> التقييم والحالة
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">التقييم الائتماني</label>
                <select name="credit_score" class="form-control">
                    <option value="ممتاز">ممتاز</option>
                    <option value="جيد جداً">جيد جداً</option>
                    <option value="جيد">جيد</option>
                    <option value="مقبول">مقبول</option>
                    <option value="ضعيف">ضعيف</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">حالة المستأجر</label>
                <select name="status" class="form-control">
                    <option value="نشط">نشط</option>
                    <option value="غير نشط">غير نشط</option>
                    <option value="محظور">محظور</option>
                </select>
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-user-friends"></i> معلومات الطوارئ
        </div>
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">اسم جهة الاتصال في الطوارئ</label>
                <input type="text" name="emergency_contact_name" class="form-control" 
                       placeholder="اسم جهة الاتصال">
            </div>
            <div class="col-md-6">
                <label class="form-label">هاتف جهة الاتصال في الطوارئ</label>
                <input type="tel" name="emergency_contact_phone" class="form-control" 
                       placeholder="رقم هاتف جهة الاتصال">
            </div>
        </div>
        <div class="row g-3 mt-2">
            <div class="col-12">
                <label class="form-label">صلة القرابة</label>
                <input type="text" name="emergency_contact_relation" class="form-control" 
                       placeholder="صلة القرابة مع جهة الاتصال">
            </div>
        </div>
    </div>

    <div class="form-section">
        <div class="form-section-title">
            <i class="fa fa-sticky-note"></i> ملاحظات
        </div>
        <div class="mb-3">
            <label class="form-label">ملاحظات عامة</label>
            <textarea name="notes" class="form-control" rows="3" 
                      placeholder="ملاحظات عامة حول المستأجر..."></textarea>
        </div>
    </div>

    <div class="text-center mt-4">
        <button type="submit" class="btn btn-success px-4">
            <i class="fa fa-save"></i> حفظ المستأجر
        </button>
        <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
            إلغاء
        </button>
    </div>
</form>

<script>
document.getElementById('addTenantForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            if (modal) modal.hide();
            showAlert('تم إضافة المستأجر بنجاح', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(data.message || 'حدث خطأ أثناء إضافة المستأجر', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
});

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
    document.body.appendChild(alertDiv);
    setTimeout(() => alertDiv.remove(), 5000);
}
</script>

<style>
.modal-form .form-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.modal-form .form-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
}

.modal-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.modal-form .text-danger {
    color: #dc3545 !important;
}

.modal-form .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.modal-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
</style>
