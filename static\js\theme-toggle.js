/**
 * نظام تبديل الوضع الليلي/النهاري
 */
class ThemeToggle {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        this.applyTheme();
        this.createToggleButton();
        this.bindEvents();
        this.addToggleEffect();
    }

    applyTheme() {
        const body = document.body;
        const navbar = document.querySelector('.navbar');
        const cards = document.querySelectorAll('.card');
        const tables = document.querySelectorAll('.table');
        const modals = document.querySelectorAll('.modal-content');
        const forms = document.querySelectorAll('.form-control, .form-select');
        const buttons = document.querySelectorAll('.btn-outline-secondary');

        if (this.currentTheme === 'dark') {
            body.classList.add('dark-theme');
            
            // تطبيق الوضع المظلم على العناصر
            if (navbar) {
                navbar.classList.remove('navbar-light', 'bg-light');
                navbar.classList.add('navbar-dark', 'bg-dark');
            }

            cards.forEach(card => {
                card.classList.add('bg-dark', 'text-light');
                card.style.borderColor = '#495057';
            });

            tables.forEach(table => {
                table.classList.add('table-dark');
            });

            modals.forEach(modal => {
                modal.classList.add('bg-dark', 'text-light');
            });

            forms.forEach(form => {
                form.classList.add('bg-dark', 'text-light');
                form.style.borderColor = '#495057';
            });

            buttons.forEach(btn => {
                btn.classList.remove('btn-outline-secondary');
                btn.classList.add('btn-outline-light');
            });

        } else {
            body.classList.remove('dark-theme');
            
            // إزالة الوضع المظلم
            if (navbar) {
                navbar.classList.remove('navbar-dark', 'bg-dark');
                navbar.classList.add('navbar-light', 'bg-light');
            }

            cards.forEach(card => {
                card.classList.remove('bg-dark', 'text-light');
                card.style.borderColor = '';
            });

            tables.forEach(table => {
                table.classList.remove('table-dark');
            });

            modals.forEach(modal => {
                modal.classList.remove('bg-dark', 'text-light');
            });

            forms.forEach(form => {
                form.classList.remove('bg-dark', 'text-light');
                form.style.borderColor = '';
            });

            buttons.forEach(btn => {
                btn.classList.remove('btn-outline-light');
                btn.classList.add('btn-outline-secondary');
            });
        }

        // إضافة CSS مخصص للوضع المظلم
        this.addDarkModeStyles();
    }

    addDarkModeStyles() {
        const existingStyle = document.getElementById('dark-mode-styles');
        if (existingStyle) {
            existingStyle.remove();
        }

        const style = document.createElement('style');
        style.id = 'dark-mode-styles';
        
        if (this.currentTheme === 'dark') {
            style.textContent = `
                .dark-theme {
                    background-color: #121212 !important;
                    color: #ffffff !important;
                }
                
                .dark-theme .bg-white {
                    background-color: #1e1e1e !important;
                    color: #ffffff !important;
                }
                
                .dark-theme .border-bottom {
                    border-color: #495057 !important;
                }
                
                .dark-theme .text-dark {
                    color: #ffffff !important;
                }
                
                .dark-theme .alert-warning {
                    background-color: #664d03 !important;
                    border-color: #997404 !important;
                    color: #ffecb5 !important;
                }
                
                .dark-theme .alert-success {
                    background-color: #0f5132 !important;
                    border-color: #146c43 !important;
                    color: #d1e7dd !important;
                }
                
                .dark-theme .alert-info {
                    background-color: #055160 !important;
                    border-color: #087990 !important;
                    color: #b6effb !important;
                }
                
                .dark-theme .btn-primary {
                    background-color: #0d6efd !important;
                    border-color: #0d6efd !important;
                }
                
                .dark-theme .btn-secondary {
                    background-color: #6c757d !important;
                    border-color: #6c757d !important;
                }
                
                .dark-theme .dropdown-menu {
                    background-color: #2d2d2d !important;
                    border-color: #495057 !important;
                }
                
                .dark-theme .dropdown-item {
                    color: #ffffff !important;
                }
                
                .dark-theme .dropdown-item:hover {
                    background-color: #495057 !important;
                }
                
                .dark-theme .list-group-item {
                    background-color: #2d2d2d !important;
                    border-color: #495057 !important;
                    color: #ffffff !important;
                }
                
                .dark-theme .sidebar {
                    background-color: #1e1e1e !important;
                }
                
                .dark-theme .sidebar .nav-link {
                    color: #ffffff !important;
                }
                
                .dark-theme .sidebar .nav-link:hover {
                    background-color: #495057 !important;
                }
                
                .dark-theme input, .dark-theme select, .dark-theme textarea {
                    background-color: #2d2d2d !important;
                    border-color: #495057 !important;
                    color: #ffffff !important;
                }
                
                .dark-theme input:focus, .dark-theme select:focus, .dark-theme textarea:focus {
                    background-color: #2d2d2d !important;
                    border-color: #0d6efd !important;
                    color: #ffffff !important;
                    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
                }
            `;
        }

        document.head.appendChild(style);
    }

    createToggleButton() {
        // البحث عن زر التبديل الموجود في الشريط العلوي
        const existingToggle = document.getElementById('theme-toggle');
        if (existingToggle) {
            // استخدام الزر الموجود
            this.updateToggleButton();
            return;
        }

        // إنشاء زر التبديل الجديد في الشريط العلوي
        const topBar = document.querySelector('.bg-white.border-bottom');
        if (topBar) {
            const leftSection = topBar.querySelector('.d-flex.align-items-center.gap-3');
            if (leftSection) {
                const toggleButton = document.createElement('button');
                toggleButton.id = 'theme-toggle';
                toggleButton.className = 'btn btn-outline-secondary btn-sm';
                toggleButton.innerHTML = '<i class="fas fa-moon"></i>';
                toggleButton.title = 'تبديل الوضع الليلي/النهاري';
                
                leftSection.appendChild(toggleButton);
                this.updateToggleButton();
            }
        }
    }

    updateToggleButton() {
        const toggleButton = document.getElementById('theme-toggle');
        if (toggleButton) {
            const icon = toggleButton.querySelector('i');
            if (this.currentTheme === 'dark') {
                icon.className = 'fas fa-sun';
                toggleButton.title = 'تبديل إلى الوضع النهاري';
            } else {
                icon.className = 'fas fa-moon';
                toggleButton.title = 'تبديل إلى الوضع الليلي';
            }
        }
    }

    bindEvents() {
        const toggleButton = document.getElementById('theme-toggle');
        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        localStorage.setItem('theme', this.currentTheme);
        this.applyTheme();
        this.updateToggleButton();
    }

    addToggleEffect() {
        const toggleButton = document.getElementById('theme-toggle');
        if (toggleButton) {
            toggleButton.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });
        }
    }
}

// تهيئة نظام تبديل الوضع عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    new ThemeToggle();
});
