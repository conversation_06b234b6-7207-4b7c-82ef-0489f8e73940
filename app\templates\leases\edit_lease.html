{% extends "base.html" %}

{% block title %}تعديل العقد - {{ lease.lease_number or lease.id }}{% endblock %}

{% block extra_css %}
<style>
    .edit-header {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 0 0 25px 25px;
    }
    
    .form-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
    }
    
    .form-section h5 {
        color: #495057;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 12px;
        margin-bottom: 20px;
        font-weight: bold;
    }
    
    .nav-tabs .nav-link {
        border-radius: 10px 10px 0 0;
        border: none;
        background: #f8f9fa;
        color: #6c757d;
        margin-right: 5px;
        transition: all 0.3s ease;
    }
    
    .nav-tabs .nav-link.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
    }
    
    .nav-tabs .nav-link:hover {
        background: #e9ecef;
        border: none;
    }
    
    .nav-tabs .nav-link.active:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
        color: white;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        color: white;
    }
    
    .btn-cancel {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
        color: white;
        transition: all 0.3s ease;
    }
    
    .btn-cancel:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
        color: white;
    }
    
    .required-field {
        color: #dc3545;
    }
    
    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }
    
    .auto-calculate {
        background-color: #f8f9fa !important;
        cursor: not-allowed;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="edit-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-2">
                        <i class="fas fa-edit"></i>
                        تعديل العقد - {{ lease.lease_number or lease.id }}
                    </h2>
                    <p class="mb-0 opacity-75">تعديل معلومات وتفاصيل العقد</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('lease_details', id=lease.id) }}" class="btn btn-light">
                        <i class="fas fa-arrow-right"></i>
                        العودة للتفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form method="POST" id="editLeaseForm">
        {{ csrf_token() }}
        
        <!-- Navigation Tabs -->
        <div class="form-section">
            <ul class="nav nav-tabs mb-4" id="leaseTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" 
                            type="button" role="tab" aria-controls="basic" aria-selected="true">
                        <i class="fas fa-info-circle"></i> المعلومات الأساسية
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="financial-tab" data-bs-toggle="tab" data-bs-target="#financial" 
                            type="button" role="tab" aria-controls="financial" aria-selected="false">
                        <i class="fas fa-dollar-sign"></i> المعلومات المالية
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="terms-tab" data-bs-toggle="tab" data-bs-target="#terms" 
                            type="button" role="tab" aria-controls="terms" aria-selected="false">
                        <i class="fas fa-file-alt"></i> الشروط والأحكام
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="additional-tab" data-bs-toggle="tab" data-bs-target="#additional" 
                            type="button" role="tab" aria-controls="additional" aria-selected="false">
                        <i class="fas fa-plus-circle"></i> معلومات إضافية
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="leaseTabContent">
                <!-- Basic Information Tab -->
                <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="lease_number" class="form-label">رقم العقد</label>
                                <input type="text" class="form-control" id="lease_number" name="lease_number" 
                                       value="{{ lease.lease_number or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="property_id" class="form-label">العقار <span class="required-field">*</span></label>
                                <select class="form-select" id="property_id" name="property_id" required>
                                    <option value="">اختر العقار</option>
                                    {% for property in available_properties %}
                                    <option value="{{ property.id }}" {{ 'selected' if property.id == lease.property_id }}>
                                        {{ property.name }} - {{ property.address }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tenant_id" class="form-label">المستأجر <span class="required-field">*</span></label>
                                <select class="form-select" id="tenant_id" name="tenant_id" required>
                                    <option value="">اختر المستأجر</option>
                                    {% for tenant in available_tenants %}
                                    <option value="{{ tenant.id }}" {{ 'selected' if tenant.id == lease.tenant_id }}>
                                        {{ tenant.full_name }} - {{ tenant.phone }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="lease_type" class="form-label">نوع العقد <span class="required-field">*</span></label>
                                <select class="form-select" id="lease_type" name="lease_type" required>
                                    <option value="">اختر نوع العقد</option>
                                    {% for type in ['سكني', 'تجاري', 'مكتبي', 'صناعي', 'مختلط'] %}
                                    <option value="{{ type }}" {{ 'selected' if type == lease.lease_type }}>{{ type }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">تاريخ بداية العقد <span class="required-field">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date" 
                                       value="{{ lease.start_date.strftime('%Y-%m-%d') if lease.start_date }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">تاريخ انتهاء العقد <span class="required-field">*</span></label>
                                <input type="date" class="form-control" id="end_date" name="end_date" 
                                       value="{{ lease.end_date.strftime('%Y-%m-%d') if lease.end_date }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="lease_duration_months" class="form-label">مدة العقد (بالأشهر)</label>
                                <input type="number" class="form-control auto-calculate" id="lease_duration_months" 
                                       name="lease_duration_months" value="{{ lease.lease_duration_months or '' }}" 
                                       min="1" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">حالة العقد</label>
                                <select class="form-select" id="status" name="status">
                                    {% for status in ['معلق', 'نشط', 'منتهي', 'ملغي'] %}
                                    <option value="{{ status }}" {{ 'selected' if status == lease.status }}>{{ status }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financial Information Tab -->
                <div class="tab-pane fade" id="financial" role="tabpanel" aria-labelledby="financial-tab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="monthly_rent" class="form-label">الإيجار الشهري <span class="required-field">*</span></label>
                                <input type="number" step="0.01" class="form-control" id="monthly_rent" name="monthly_rent" 
                                       value="{{ lease.monthly_rent or '' }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="currency" class="form-label">العملة</label>
                                <select class="form-select" id="currency" name="currency">
                                    {% for curr in ['JOD', 'USD', 'EUR'] %}
                                    <option value="{{ curr }}" {{ 'selected' if curr == lease.currency }}>
                                        {{ 'دينار أردني' if curr == 'JOD' else 'دولار أمريكي' if curr == 'USD' else 'يورو' }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="security_deposit" class="form-label">مبلغ التأمين</label>
                                <input type="number" step="0.01" class="form-control" id="security_deposit" 
                                       name="security_deposit" value="{{ lease.security_deposit or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="commission_amount" class="form-label">مبلغ العمولة</label>
                                <input type="number" step="0.01" class="form-control" id="commission_amount" 
                                       name="commission_amount" value="{{ lease.commission_amount or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="payment_frequency" class="form-label">دورية الدفع</label>
                                <select class="form-select" id="payment_frequency" name="payment_frequency">
                                    {% for freq in ['شهري', 'ربع سنوي', 'نصف سنوي', 'سنوي'] %}
                                    <option value="{{ freq }}" {{ 'selected' if freq == lease.payment_frequency }}>{{ freq }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="payment_due_day" class="form-label">يوم استحقاق الدفع</label>
                                <input type="number" class="form-control" id="payment_due_day" name="payment_due_day" 
                                       min="1" max="31" value="{{ lease.payment_due_day or 1 }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="late_fee_amount" class="form-label">رسوم التأخير</label>
                                <input type="number" step="0.01" class="form-control" id="late_fee_amount" 
                                       name="late_fee_amount" value="{{ lease.late_fee_amount or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="grace_period_days" class="form-label">فترة السماح (بالأيام)</label>
                                <input type="number" class="form-control" id="grace_period_days" name="grace_period_days" 
                                       min="0" value="{{ lease.grace_period_days or 5 }}">
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="total_lease_value" class="form-label">إجمالي قيمة العقد</label>
                                <input type="number" step="0.01" class="form-control auto-calculate" id="total_lease_value" 
                                       name="total_lease_value" value="{{ lease.total_lease_value or '' }}" readonly>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Terms and Conditions Tab -->
                <div class="tab-pane fade" id="terms" role="tabpanel" aria-labelledby="terms-tab">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="terms_and_conditions" class="form-label">الشروط والأحكام</label>
                                <textarea class="form-control" id="terms_and_conditions" name="terms_and_conditions" 
                                          rows="6">{{ lease.terms_and_conditions or '' }}</textarea>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="special_conditions" class="form-label">شروط خاصة</label>
                                <textarea class="form-control" id="special_conditions" name="special_conditions" 
                                          rows="4">{{ lease.special_conditions or '' }}</textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="pets_allowed" name="pets_allowed" 
                                           {{ 'checked' if lease.pets_allowed }}>
                                    <label class="form-check-label" for="pets_allowed">
                                        السماح بالحيوانات الأليفة
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="smoking_allowed" name="smoking_allowed" 
                                           {{ 'checked' if lease.smoking_allowed }}>
                                    <label class="form-check-label" for="smoking_allowed">
                                        السماح بالتدخين
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="subletting_allowed" name="subletting_allowed" 
                                           {{ 'checked' if lease.subletting_allowed }}>
                                    <label class="form-check-label" for="subletting_allowed">
                                        السماح بالإيجار من الباطن
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto_renewal" name="auto_renewal" 
                                           {{ 'checked' if lease.auto_renewal }}>
                                    <label class="form-check-label" for="auto_renewal">
                                        التجديد التلقائي
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Tab -->
                <div class="tab-pane fade" id="additional" role="tabpanel" aria-labelledby="additional-tab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="utilities_included" class="form-label">الخدمات المشمولة</label>
                                <textarea class="form-control" id="utilities_included" name="utilities_included" 
                                          rows="3">{{ lease.utilities_included or '' }}</textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="maintenance_responsibility" class="form-label">مسؤولية الصيانة</label>
                                <select class="form-select" id="maintenance_responsibility" name="maintenance_responsibility">
                                    <option value="">اختر المسؤول</option>
                                    {% for resp in ['المالك', 'المستأجر', 'مشتركة'] %}
                                    <option value="{{ resp }}" {{ 'selected' if resp == lease.maintenance_responsibility }}>{{ resp }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parking_spaces" class="form-label">عدد مواقف السيارات</label>
                                <input type="number" class="form-control" id="parking_spaces" name="parking_spaces" 
                                       min="0" value="{{ lease.parking_spaces or 0 }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="furnished_status" class="form-label">حالة الأثاث</label>
                                <select class="form-select" id="furnished_status" name="furnished_status">
                                    <option value="">اختر الحالة</option>
                                    {% for status in ['مفروش', 'نصف مفروش', 'غير مفروش'] %}
                                    <option value="{{ status }}" {{ 'selected' if status == lease.furnished_status }}>{{ status }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات إضافية</label>
                                <textarea class="form-control" id="notes" name="notes" 
                                          rows="4">{{ lease.notes or '' }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="text-center mb-4">
            <button type="submit" class="btn btn-save me-3">
                <i class="fas fa-save"></i> حفظ التغييرات
            </button>
            <a href="{{ url_for('lease_details', id=lease.id) }}" class="btn btn-cancel">
                <i class="fas fa-times"></i> إلغاء
            </a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Calculate lease duration when dates change
function calculateLeaseDuration() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = Math.abs(end - start);
        const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30.44)); // Average days per month
        
        document.getElementById('lease_duration_months').value = diffMonths;
        calculateTotalLeaseValue();
    }
}

// Calculate total lease value
function calculateTotalLeaseValue() {
    const monthlyRent = parseFloat(document.getElementById('monthly_rent').value) || 0;
    const duration = parseInt(document.getElementById('lease_duration_months').value) || 0;
    const total = monthlyRent * duration;
    
    document.getElementById('total_lease_value').value = total.toFixed(2);
}

// Event listeners
document.getElementById('start_date').addEventListener('change', calculateLeaseDuration);
document.getElementById('end_date').addEventListener('change', calculateLeaseDuration);
document.getElementById('monthly_rent').addEventListener('input', calculateTotalLeaseValue);

// Form validation
document.getElementById('editLeaseForm').addEventListener('submit', function(e) {
    const requiredFields = ['property_id', 'tenant_id', 'lease_type', 'start_date', 'end_date', 'monthly_rent'];
    let isValid = true;
    
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        // Switch to first tab with errors
        document.getElementById('basic-tab').click();
    }
});

// Remove validation styling on input
document.querySelectorAll('.form-control, .form-select').forEach(field => {
    field.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });
});

// Calculate initial values
calculateLeaseDuration();
</script>
{% endblock %}
